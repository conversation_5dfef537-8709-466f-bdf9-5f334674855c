import { NativeModules, Platform } from 'react-native';

/**
 * React Navigation
 */
export const WELCOME_SCREEN = 'Welcome';
export const MY_TEAM_INVITE = 'Subcontractor Invite';
export const BOTTOM_TABS_SCREEN = 'BottomTabNavigation';
export const REVIEWS_SCREEN = 'Reviews';
export const REVIEWS_OVERVIEW = 'Overview';
export const REVIEWS_REQUEST = 'Request';
export const REVIEWS_INSIGHTS = 'Insights';
export const JOBS_SCREEN = 'Jobs';
export const JOB_INFORMATION_SCREEN = 'Job Information';
export const ARCHIVED_JOBS_SCREEN = 'Archived';
export const CAMPAIGNS_SCREEN = 'Campaigns';
export const MARKETPLACE_JOB_DETAILS_SCREEN = 'Marketplace Job Details';
export const PHOTOS_SCREEN = 'Photos';
export const ALBUM_SCREEN = 'Album';
export const CREATE_ALBUM_SCREEN = 'Create album';
export const EDIT_ALBUM_SCREEN = 'Edit album';
export const MY_MEMBERSHIP_SCREEN = 'My membership';
export const SWITCH_PLAN_SCREEN = 'Switch plan';
export const INVOICES_SCREEN = 'Invoices';
export const DIRECT_DEBIT_SCREEN = 'Direct Debit';
export const INVOICE_DETAILS_SCREEN = 'Invoice';
export const INVOICE_CAMPAIGN_DETAILS_SCREEN = 'Invoice campaign details';
export const INBOX_SCREEN = 'Chats';
export const INACTIVE_CHATS_SCREEN = 'Inactive chats';
export const CHANNEL_SCREEN = 'Channel';
export const CHANNEL_APPOINTMENTS_SCREEN = 'Appointments';
export const ADD_APPOINTMENT_SCREEN = 'Appointment';
export const UPDATE_APPOINTMENT_SCREEN = 'Update Appointment';
export const MORE_LINKS_SCREEN = 'More';
export const PROFILE_SCREEN = 'Profile';
export const MY_DETAILS_SCREEN = 'My details';
export const PROFILE_DESCRIPTION_SCREEN = 'Profile description';
export const PROFILE_BUSINESS_OFFERINGS_SCREEN = 'Business offerings';
export const FEATURED_PROJECTS_SCREEN = 'Featured projects';
export const FEATURED_PROJECTS_ALL_JOBS_SCREEN = 'All jobs';
export const FEATURED_PROJECTS_SELECT_FROM_ALBUM =
  'Featured projects album select';
export const FEATURED_PROJECT_DETAILS = 'Featured project details';
export const SEARCH_CATEGORIES_SCREEN = 'Search categories';
export const SUBCONTRACTING_SCREEN = 'Subcontracting';
export const SPONSORED_LISTINGS_SCREEN = 'Sponsored listings';
export const CREATE_SPONSORED_LISTING_SCREEN = 'Create sponsored listing';
export const WORK_RADIUS_SCREEN = 'Work radius';
export const PAGE_NOT_FOUND = 'Page not found';
export const AVAILABILITY_SCREEN = 'Availability';
export const MY_INSIGHTS_SCREEN = 'Insights';
export const INSURANCE_SCREEN = 'Insurance (PLI)';
export const ACCREDITATIONS_SCREEN = 'Accreditations';
export const ACCREDITATIONS_UPDATE_SCREEN = 'Accreditation';
export const ACCREDITATIONS_CREATE_SCREEN = 'Add new Accreditation';
export const MARKETING_MATERIALS_SCREEN = 'Marketing materials';
export const HOME_SCREEN = 'Home';

export const ADVERTISE_IN_A_DIRECTORY_SCREEN = 'Advertise in a directory';
export const REFER_AND_EARN_SCREEN = 'Refer and earn';
export const REFER_AND_EARN_REGIONAL_PROMOTION_SCREEN = 'Regional promotion';
export const MY_TEAM_SCREEN = 'My team';
export const MY_TEAM_ADD_EMPLOYEE_SCREEN = 'Add an onsite staff';
export const MY_TEAM_ONSITE_STAFF_TAB = 'Onsite staff';
export const MY_TEAM_SUBCONTRACTORS_TAB = 'Subcontractors';
export const MY_TEAM_CONTRACTORS_TAB = 'Contractors';
export const MY_TEAM_EDIT_EMPLOYEE_SCREEN = 'Employee details';
export const MY_TEAM_VIEW_EMPLOYEE_SCREEN = 'View employee';
export const MY_TEAM_TRADE_ACCREDITATIONS_SCREEN = 'Trade accreditations';
export const MY_TEAM_TRADE_ACCREDITATIONS_CREATE_SCREEN =
  'Create trade accreditation';
export const MY_TEAM_TRADE_ACCREDITATIONS_UPDATE_SCREEN =
  'Update trade accreditation';
export const MY_TEAM_TRADE_ACCREDITATIONS_DOCUMENT_PREVIEW_SCREEN =
  'Trade accreditation document preview';
export const MY_TEAM_ADD_EMPLOYEE_SUCCESS_SCREEN = 'Add employee success';
export const MY_VETTING_CHECKS = 'My vetting checks';
export const OFFERS_AND_DISCOUNTS_SCREEN = 'Offers & discounts';

export const PAY_CHECKATRADE_SCREEN = 'Pay Checkatrade';

export const NOTIFICATION_PREFERENCES_SCREEN = 'Notification preferences';
export const JOB_NOTIFICATIONS_PREFERENCES_SCREEN = 'Job notifications';
export const PUSH_NOTIFICATION_PREFERENCES_SCREEN = 'Push notifications';

export const MARKETING_PREFERENCES_SCREEN = 'Marketing preferences';
export const PRIVACY_PREFERENCES_SCREEN = 'Privacy preferences';
export const DEMO_SCREEN = 'Demo';
export const DEMO_CLOUD_STORAGE = 'Cloud Storage';
export const DEMO_DOC_UPLOAD = 'Doc Upload';
export const DEMO_MAP = 'Map';
export const DEMO_DISTRICTS_MAP = 'Map Districts';
export const DEMO_TIME_PICKER = 'Time Picker';
export const DEMO_EXPO_CALENDAR = 'Expo Calendar Demo';
export const DEMO_TOAST = 'Toast';
export const DEMO_DIRECT_DEBIT = 'Direct Debit Demo';

export const DEMO_CHARTS = 'Charts';

export const QUOTES_SCREEN = 'Quotes';
export const QUOTE_JOB_LIST_SCREEN = 'Quote Job List';
export const SAVED_QUOTE_SCREEN = 'Saved Quote';
export const QUOTES_AND_INVOICES_INVOICE_DETAILS_SCREEN =
  'Quotes and invoices - Invoice Details';
export const LEAD_QUOTE_SCREEN = 'Lead Quote';
export const EXISTING_QUOTE_SCREEN = 'Existing Quote';
export const DUPLICATE_QUOTE_SCREEN = 'Duplicate Quote';
export const BLANK_QUOTE_SCREEN = 'Blank Quote';

export const SAVED_INVOICE_SCREEN = 'Saved Invoice';

// Job payments navigation
export const JOB_PAYMENTS_SCREEN = 'Payments';

export const QUOTE_PAYMENTS_LIST_SCREEN = 'Quote payments';
export const JOB_PAYMENTS_ONBOARDING_PRIVACY_SCREEN =
  'Payments Onboarding Privacy';
export const JOB_PAYMENTS_CANCEL_REQUEST_SCREEN = 'Job Payments Cancel Request';
export const JOB_PAYMENTS_SETUP_HELP_SCREEN = 'Job Payments Setup Help';
export const JOB_PAYMENTS_TAX_INFORMATION_SCREEN =
  'Job Payments Tax Information';
export const JOB_PAYMENTS_NEW_REQUEST_SCREEN = 'New payment request';
export const JOB_PAYMENTS_PAYMENT_DETAILS_SCREEN = 'Payment details';
export const JOB_PAYMENTS_OFF_PLATFORM_NEW_REQUEST_SCREEN =
  'Off platform payment request';
export const JOB_PAYMENTS_PAY_BY_PHONE_SCREEN = 'Take payment by phone';
export const JOB_PAYMENTS_ENABLE_TAP_SCREEN = 'Enable tap to pay';
export const JOB_PAYMENTS_CHOOSE_JOB_REQUEST_SCREEN = 'Choose job';
export const JOB_PAYMENTS_ENABLE_TAP_EDUCATION_VIDEO_SCREEN =
  'Tap to pay education video';
export const JOB_PAYMENTS_EXPORT_STATEMENT_SCREEN = 'Export statement';
export const JOB_PAYMENTS_PAYMENT_REQUEST_EDUCATION = 'How to use payments';
export const JOB_PAYMENTS_TAP_TO_PAY_ENABLED = 'Tap to Pay enabled';
export const JOB_PAYMENTS_QUOTE_NEW_PAYMENT_SCREEN =
  'Quote new payment request';

// My campaigns navigation
export const CREATE_CAMPAIGN_SCREEN = 'Create campaign';
export const EDIT_CAMPAIGN_SCREEN = 'Edit campaign';
export const VIEW_LEADS_SCREEN = 'View leads';
export const VIEW_LEAD_DETAILS_SCREEN = 'Lead details';
export const REPORT_LEAD_SCREEN = 'Report';
export const PAUSE_CAMPAIGN_SCREEN = 'Pause campaign';
export const MY_CAMPAIGNS_SCREEN = 'Campaigns';
export const UPGRADE_CAMPAIGN_SCREEN = 'Upgrade fixed plan';
export const UPGRADE_CAMPAIGN_SUMMARY_SCREEN = 'Upgrade summary';

// Jobs top tabs navigation
export const ALL_JOBS = 'All Jobs';
export const INTERESTED = 'Interested';
export const DECLINED = 'Inactive';
export const COMPLETED = 'Completed';

// Marketplace Jobs top tabs navigation
export const MARKETPLACE_JOBS_ALL_JOBS = 'All Jobs';
export const MARKETPLACE_JOBS_NEW_REQUESTS = 'New';
export const MARKETPLACE_JOBS_INTERESTED = 'Interested';
export const MARKETPLACE_JOBS_COMPLETED = 'Completed';
export const MARKETPLACE_JOBS_ARCHIVE = 'Inactive';
export const MARKETPLACE_JOBS_CALLS_SMS = 'Calls-SMS';
export const MARKETPLACE_JOBS_BOOKED = 'Booked';

// My Labs navigation
export const LABS_SCREEN = 'Labs';

// Override Feature Flags navigation
export const OVERRIDE_FEATURE_FLAGS_SCREEN = 'Override feature flags';

// Freemium / onboarding navigation
export const FREEMIUM_ONBOARDING_SCREEN = 'Essentials Terms';

// Bookable Services
export const BOOKABLE_SERVICES_SCREEN = 'Services';
export const CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN = 'Create a service';
export const CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_LIST_SCREEN =
  'Choose a service to copy';
export const CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN =
  'Create service from copy';
export const CREATE_BOOKABLE_SERVICES_SCREEN = 'New service';
export const BOOKABLE_SERVICE_DETAIL_SCREEN = 'Service details';
export const EDIT_BOOKABLE_SERVICE_SCREEN = 'Edit service';
export const BOOKABLE_SERVICES_FAQ_GUIDE_SCREEN = 'Services guide';

// Loans
export const LOANS_SCREEN = 'Loans';

// Help and Support
export const HELP_AND_SUPPORT_SCREEN = 'Help & support';
export const ACCOUNT_TIPS_SCREEN = 'AccountTips';

/**
 * Platform / Devices
 */
export const IS_ANDROID = Platform.OS === 'android';
export const IS_IOS = Platform.OS === 'ios';
export const IS_WEB = Platform.OS === 'web';

export const IS_ABOVE_IOS_14 =
  IS_IOS && parseInt(Platform.Version as string, 10) >= 14;

export const IS_ABOVE_IOS_17 =
  IS_IOS && parseInt(Platform.Version as string, 10) >= 17;

export const IS_ABOVE_ANDROID_13 =
  Platform.OS === 'android' && Platform.Version >= 33;

/**
 * @deprecated - use useMediaQuery() instead
 * Using the user agent is labelled as unreliable and is not recommended
 * However, it is the only way to detect if the user is on a mobile web environment
 * https://developer.mozilla.org/en-US/docs/Web/API/Navigator/userAgent
 */
export const IS_MOBILE_WEB =
  IS_WEB &&
  /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    window.navigator.userAgent,
  );

const getEmulatorServicesHostname = (): string => {
  if (!__DEV__ || Platform.OS === 'web') {
    return 'localhost';
  }

  // Infer the Firebase Emulator hostname from the Script Host URL
  const hostUrl = NativeModules.SourceCode.scriptURL || 'http://localhost';
  const { hostname } = new URL(hostUrl);
  return hostname;
};

export const LOCAL_EMULATOR_SERVICES_HOSTNAME =
  getEmulatorServicesHostname() || 'localhost';
export const LOCAL_EMULATOR_HOSTNAME = `http://${LOCAL_EMULATOR_SERVICES_HOSTNAME}`;

/*
 * Urls
 */
export const PROD_WEB_URL = 'https://membersapp.checkatrade.com';
const IOS_APP_STORE_URL =
  'https://apps.apple.com/gb/app/checkatrade-trades/id1498194074?mt=8';
const ANDROID_APP_STORE_URL =
  'https://play.google.com/store/apps/details?id=com.checkatrade.tradeapp';
export const APP_STORE_URL = Platform.select({
  ios: IOS_APP_STORE_URL,
  android: ANDROID_APP_STORE_URL,
});
export const NO_UNIVERSAL_LINKS_FRAGMENT = '#no_universal_links';
export const WEB_ONLY_QUERY_PARAM = '$web_only=true';

export const DIRECTORIES_CALLBACK_FORM_URL =
  'https://forms.checkatrade.com/form/6f271d24-d265-4f22-b622-2297945fa2df';
export const CHECKATRADE_COM_URL = 'https://www.checkatrade.com';
export const SIGN_UP_URL = 'https://join.checkatrade.com';
export const LOGIN_HELP_URL =
  'https://support.checkatrade.com/s/article/How-to-login-to-the-Checkatrade-for-Trades-App-Member-Area';
export const TERMS_AND_CONDITIONS_URL = `${CHECKATRADE_COM_URL}/blog/terms-and-conditions`;
export const TERMS_OF_USE_URL = `${CHECKATRADE_COM_URL}/platform-terms-of-use`;
export const MEMBERSHIP_TERMS_AND_CONDITIONS_URL = `${CHECKATRADE_COM_URL}/membership-terms`;
export const PRIVACY_NOTICE_URL = `${CHECKATRADE_COM_URL}/trade-privacy-notice`;
export const COOKIE_POLICY_URL =
  'https://apply.checkatrade.com/cookie-policy.aspx';
export const SUPPORT_URL = 'https://support.checkatrade.com/s/tradesperson';
export const QUOTING_AND_INVOICING_FAQ_URL = `${CHECKATRADE_COM_URL}/blog/quoting-invoicing/`;
export const LEAD_PRICING_FAQ_URL = `${CHECKATRADE_COM_URL}/blog/lead-prices/`;

export const GIVE_FEEDBACK_URL = `${CHECKATRADE_COM_URL}/give-feedback`;
export const REVIEWS_POLICY_URL = `${CHECKATRADE_COM_URL}/blog/notice-and-takedown/`;
export const REPLY_TO_REVIEWS_URL = `${CHECKATRADE_COM_URL}/blog/trade/business-management/how-to-reply-to-negative-reviews/`;

export const GOOGLE_MAPS_URL = 'https://www.google.co.uk/maps/search/';

export const APPLE_TAP_TO_PAY_EDUCATIONAL_VIDEO_URL =
  'https://storage.googleapis.com/education_apple_tap_to_pay/Iphone-Tap-to-Pay-Explainer-Video.mp4';

/**
 * Tanstack Query Keys
 */
export const QUERY_KEY_HAS_RECENT_REVIEWS = 'hasRecentReviews';
export const QUERY_KEY_HAS_RECENT_PHOTOS = 'hasRecentPhotos';
export const QUERY_KEY_CATEGORIES = 'categories';
export const QUERY_KEY_REGIONS = 'regions';
export const QUERY_KEY_POSTCODE_AREAS = 'postcodeAreas';
export const QUERY_KEY_HAS_ARCHIVED_JOBS = 'hasArchivedJobs';
export const QUERY_KEY_SECURE_CONTACTS = 'secureContacts';
export const QUERY_KEY_MEMBER_INFO = 'memberInfo';

/**
 * Phone numbers
 * Please add all phone numbers to this section, without spaces
 */
export const TEL_AVAILABILITY_NUMBER = '***********';
export const TEL_CROSS_SALES = '***********';
export const TEL_SALES_TEAM = '***********';
export const TEL_IMAGE_REVIEW_NUMBER = '01323834774';
export const TEL_RETENTION = '02394 219576';
export const TEL_MEMBERSHIP_EXP = '0333 0146190';
export const TEL_WHATSAPP = '07410583043';
export const TEL_RENEWALS = '02394 219599';

/**
 * Emails
 */
export const PRIVACY_EMAIL = '<EMAIL>';
export const MEMBERSHIP_ADVICE_EMAIL = '<EMAIL>';
export const MEMBERSHIP_EMAIL = '<EMAIL>';

/**
 * Breakpoints
 */
export const FONT_SCALING_BREAKPOINT = 412;

/**
 * Content
 */
export const ERROR_MESSAGE_SESSION =
  "Sorry, there's a problem with your session.\n Try signing in again.";
export const NO_JOBS_TO_SHOW = 'No jobs to show';
export const JOBS_ERROR_MESSAGE = 'Error loading jobs data.';
export const REVIEWS_ERROR_MESSAGE = 'Error loading reviews data.';
export const REVIEWS_REVIEWING_REPLY = "We're processing your reply";

/**
 * AsyncStorage keys
 */
export const STORAGE_KEY_REFRESH_TOKEN = '@CAT_TRADE_APP_AUTH_REFRESH_TOKEN';
export const STORAGE_KEY_ACCESS_TOKEN = '@CAT_TRADE_APP_AUTH_ACCESS_TOKEN';
export const STORAGE_KEY_ID_TOKEN = '@CAT_TRADE_APP_AUTH_ID_TOKEN';
export const STORAGE_KEY_AUTH_SERVICE_TYPE = '@CAT_TRADE_APP_AUTH_SERVICE_TYPE';
export const STORAGE_KEY_TRACKING_PREF = '@CAT_TRADE_TRACKING_PREF';
export const STORAGE_KEY_BACKGROUND_DATE_TIME = 'backgroundDateTime';
export const STORAGE_KEY_SESSION_COUNT = 'sessionCount';
export const STORAGE_KEY_DEVICE_ID = '@CAT_TRADE_APP_DEVICE_ID';
export const STORAGE_KEY_JOB_SURVEY_DATA = 'declineReasonForJobId';
export const STORAGE_KEY_JOB_LE_SURVEY = '@CAT_TRADE_APP_JOB_LE_SURVEY';
export const STORAGE_KEY_JOB_SURVEY_OTHER_REASON_TEXT =
  'declineOtherReasonForJobId';
export const STORAGE_KEY_SESSION_ID = '@CAT_TRADE_APP_SESSION_ID';
export const STORAGE_KEY_FCM_TOKEN = '@CAT_TRADE_FCM_TOKEN';
export const STORAGE_KEY_SHOW_INSIGHTS_AS_INITIAL_TAB =
  'shouldShowInsightsAsInitialTab';
export const STORAGE_KEY_BRAZE_EXTERNAL_ID = '@CAT_BRAZE_EXTERNAL_ID';
export const STORAGE_KEY_FIREBASE_TOKEN_LAST_UPDATED =
  '@FIREBASE_CUSTOM_TOKEN_LAST_UPDATED';
export const STORAGE_KEY_SELECTED_COMPANY_ID = '@CAT_SELECTED_COMPANY_ID';

export const STORAGE_KEY_JOBS_MAP_TOOLTIP_OPENED =
  '@CAT_JOBS_MAP_TOOLTIP_OPENED';
export const STORAGE_KEY_JOB_PAYMENTS_TAP_TO_PAY_ENABLED =
  'jobPaymentsTapToPayEnabled';
export const STORAGE_KEY_JOB_REVIEW_REPLY_SKIP_TIPS =
  '@CAT_REVIEW_REPLY_SKIP_TIPS';

export const STORAGE_KEY_SHOW_CHAT_CACHE = '@CAT_SHOW_CHAT_CACHE';

export const STORAGE_KEY_FEATURED_PROJECTS_DRAFT_STATE =
  '@CAT_FEATURED_PROJECTS_DRAFT_STATE';

/**
 * New feature modal asyncStorage keys
 */
export const STORAGE_KEY_QUOTESANDINVOICES_GUIDE_VIEWED =
  'quotesAndInvoicesGuideViewed';
export const STORAGE_KEY_QUOTESANDINVOICES_V2_GUIDE_VIEWED =
  'quotesAndInvoicesGuideV2Viewed';
export const STORAGE_KEY_ONBOARDING_ITEM_COMPLETION_QUOTE =
  'onboardingItemCompletionQuote';
export const STORAGE_KEY_ONBOARDING_ITEM_COMPLETION_REVIEW =
  'onboardingItemCompletionReview';
export const STORAGE_KEY_ONBOARDING_ITEM_COMPLETION_DESCRIPTION =
  'onboardingItemCompletionDescription';
export const STORAGE_KEY_ONBOARDING_ITEM_COMPLETION_PLI =
  'onboardingItemCompletionPLI';
export const STORAGE_KEY_ONBOARDING_ITEM_COMPLETION_PHOTOS =
  'onboardingItemCompletionPhotos';
export const STORAGE_KEY_ONBOARDING_ITEM_COMPLETION_ACCREDS =
  'onboardingItemCompletionAccreds';
export const STORAGE_KEY_IN_SHOW_LIFE_OPTIMISATION_COMPLETED =
  'inLifeOptimisationWidgetCompleted';
export const STORAGE_KEY_CHAT_GUIDE_VIEWED = 'chatGuide2Viewed';
export const STORAGE_KEY_OVERRIDE_FEATURES_CONFIG =
  '@CAT_OVERRIDE_FEATURES_CONFIG';
export const STORAGE_KEY_NAV_BADGE_STATE = '@CAT_NAV_BADGE_STATE_27_11_24';
export const STORAGE_KEY_JOB_PAYMENTS_GUIDE_VIEWED = 'jobPaymentsGuideViewed';

/**
 * Internal Review Prompt
 */
export const MAX_SESSION_LENGTH = 60;
export const MAX_SESSION_COUNT = 5;
export const MAX_MINUTES_INACTIVE = 30;

/**
 * Hook Error Types
 */
export enum HOOK_STATE {
  TIMED_OUT = 'timed out',
  DATA_ERROR = 'data error',
  LOADING = 'loading',
}

/* Global Banner Storage Keys */
export const BANNER_LAST_SEEN_DATE = 'BANNER_LAST_SEEN_DATE';
export const BANNER_LAST_ACTION = 'BANNER_LAST_ACTION';

export const JOB_QUERY_KEY = 'query-key-single-job';
export const JOBS_QUERY_KEY = 'query-key-jobs';
export const ACTIVE_ONBOARDING_KEY = 'query-key-active-onboarding';
export const JOBS_CANCELLATION_REASONS_KEY = 'jobs-cancellation-reasons';
export const JOBS_REJECTION_REASONS_KEY = 'jobs-rejection-reasons';
export const NEW_JOB_COUNT_QUERY_KEY = 'new-job-count';
export const JOB_APPOINTMENTS_QUERY_KEY = 'job-appointments';
export const JOB_PROPERTY_FACTS_QUERY_KEY = 'job-property-facts';
export const APPOINTMENT_QUERY_KEY = 'appointment';
export const APPOINTMENT_TYPES_QUERY_KEY = 'appointment-types';
export const GET_REVIEW_REQUESTS_QUERY_KEY = 'get-review-requests';
export const GET_REVIEWS_QUERY_KEY = 'get-reviews';
export const GET_REVIEW_QUERY_KEY = 'get-review';
export const GET_REVIEW_METRICS_QUERY_KEY = 'get-review-metrics';
export const GET_JOB_REVIEW_REQUESTS_QUERY_KEY = 'get-job-review-requests';
export const GET_REVIEWS_SUMMARY_QUERY_KEY = 'get-reviews-summary';
export const COMPANY_ACCOUNTS_QUERY_KEY = 'company-accounts';
export const ARCHIVED_JOB_QUERY = 'archived-job';
export const ARCHIVED_JOBS_QUERY = 'archived-jobs';
export const CAMPAIGN_PRICE_RANGES_QUERY_KEY = 'campaignPriceRanges';
export const USE_CAMPAIGNS_V2_QUERY_KEY = 'campaigns-v2';
export const CAMPAIGNS_DETAILS_V2_QUERY_KEY = 'campaign-details-v2';
export const CAMPAIGN_STATISTICS_QUERY_KEY = 'campaign-statistics';
export const CAMPAIGNS_IN_USE_CATEGORIES_QUERY_KEY =
  'campaigns-in-use-categories';
export const GET_RECOMMENDED_SUBCATEGORIES_QUERY_KEY =
  'get-recommended-subcategories';
export const GET_CAMPAIGN_UPGRADE_OPTIONS_QUERY_KEY =
  'campaign-upgrade-options';
export const GET_QUOTE_REQUEST = 'quote-request';
export const GET_MANUAL_REVIEW_REQUESTS_QUERY_KEY =
  'get-manual-review-requests';
export const TEAM_PERSON_QUERY_KEY = 'team-person';
export const TEAM_PERSON_ACCREDITATIONS_QUERY_KEY =
  'team-person-accreditations';
export const MEMBER_DETAILS_QUERY_KEY = 'member-details';
export const BOOKABLE_SERVICES_QUERY_KEY = 'bookable-services';
export const BOOKABLE_SERVICE_VERSION_QUERY_KEY = 'bookable-service-version';
export const TRADE_INSIGHTS_QUERY_KEY = 'trade-insights';
/**
 * Payment Request
 */
export const MIN_PAYMENT_AMOUNT_PENCE = 100;
export const MAX_PAYMENT_AMOUNT_PENCE = 250000;

/**
 * Taxes
 */
export const VAT_RATE = 1.2;

/**
 * Stream
 */
export const HAS_OWN_MESSAGE_QUERY_KEY = 'has-own-message';

// Business Jobs
export const BUSINESS_JOB_CHANNEL_SCREEN = 'Business job channel';
export const BUSINESS_JOB_DETAILS_SCREEN = 'Business job details';
export const BUSINESS_JOB_INFORMATION_SCREEN = 'Business job information';
export const BUSINESS_JOB_QUERY_KEY = 'query-key-single-business-job';
export const BUSINESS_JOBS_QUERY_KEY = 'query-key-business-jobs';
