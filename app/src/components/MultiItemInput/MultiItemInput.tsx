import React, { useEffect, useRef } from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';
import {
  createMortarStyles,
  createTestIds,
  palette as staticPalette,
  spacing as staticSpacing,
} from '@cat-home-experts/react-native-utilities';
import {
  InputField,
  Typography,
} from '@cat-home-experts/react-native-components';
import { MinusCircle, Plus } from '@cat-home-experts/mortar-iconography-native';

interface MultiItemInputProps {
  addItemLabel?: string;
  addNewItemLabel?: string;
  itemPlaceholder?: string;
  items: string[];
  onChange: (index: number, item: string) => void;
  onAddItem: () => void;
  onRemoveItem: (index: number) => void;
}

const TEST_IDS = createTestIds('string-array-input', {
  INPUT: 'input',
  ADD_ITEM_BUTTON: 'add-item-button',
  REMOVE_ICON: 'remove-icon',
});

export const MultiItemInput: React.NativeFC<
  MultiItemInputProps,
  typeof TEST_IDS
> = ({
  addItemLabel = 'Add an item (optional)',
  addNewItemLabel = 'Add new item',
  itemPlaceholder = 'New item',
  items,
  onChange,
  onAddItem,
  onRemoveItem,
}) => {
  const inputRefs = useRef<Array<TextInput | null>>([]);

  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, items.length);
  }, [items]);

  const handleAddItem = () => {
    onAddItem();
    setTimeout(() => {
      inputRefs.current[items.length]?.focus();
    }, 0);
  };

  return (
    <View style={styles.root} testID={TEST_IDS.ROOT}>
      {items.map((item, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <View key={index}>
          <InputField
            ref={(ref) => (inputRefs.current[index] = ref)}
            label={`Item ${index + 1}`}
            hideLabel
            value={item}
            onChangeText={(text) => onChange(index, text)}
            placeholder={itemPlaceholder}
            hideFocusBorder
            rightIcon={MinusCircle}
            rightIconProps={{
              size: staticSpacing(2),
              color: staticPalette.mortarV3.tokenNeutral600,
              testID: `${TEST_IDS.REMOVE_ICON}-${index}`,
            }}
            showRightIconOnBlur
            onRightIconPress={() => onRemoveItem(index)}
            testID={`${TEST_IDS.INPUT}-${index}`}
          />
        </View>
      ))}
      <TouchableOpacity
        style={styles.addItemButton}
        onPress={handleAddItem}
        testID={TEST_IDS.ADD_ITEM_BUTTON}
      >
        <Plus
          size={staticSpacing(2)}
          color={staticPalette.mortarV3.tokenDefault700}
        />
        <Typography useVariant="bodyRegular" isMuted>
          {items.length === 0 ? addItemLabel : addNewItemLabel}
        </Typography>
      </TouchableOpacity>
    </View>
  );
};

MultiItemInput.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing }) => ({
  root: {
    gap: spacing(2),
  },
  addItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing(1.5),
  },
}));
