import { JobStatus } from 'src/data/schemas/api/capi/jobs';
import { SubscriptionPauseReason } from 'src/data/schemas/firestore/membershipSwitch';
import { AccreditationStatus } from 'src/screens/Accreditations/constants';
import { PhotosValidationErrorReason } from 'src/screens/Photos/useUploadPhotos/validationError';
import type { EventTypeKey } from 'src/services/analytics/logEmitter';
import { ArchivedLeadStatus } from './data/schemas/api/capi/archived-jobs';
import { TimePeriodsEnum } from './data/schemas/firestore/companies';

export enum EVENT_TYPE {
  ACTIVE = 'active',
  ALSP_PREFIX = 'alsp',
  APP_IN_FOREGROUND = 'app_in_foreground',
  APP_INITIAL_OPEN = 'app_open',
  APP_CLOSE = 'app_close',
  APP_LOADED = 'app_loaded',
  AVAILABILITY_TOGGLE_OFF = 'availability_toggle_off',
  AVAILABILITY_TOGGLE_ON = 'availability_toggle_on',
  AVAILABILITY_TOGGLE_UNAVAILABLE = 'availability_toggle_unavailable',
  CALL_BUTTON_SALES = 'membership_sales_phone',
  CALL_BUTTON_RETENTION = 'membership_retention_phone',
  DIRECTORIES_CALL_US = 'directories_call_us_clicked',
  DIRECTORIES_REQUEST_CALLBACK = 'directories_callback_request_clicked',
  DISCUSS_LSP_BREAK_EMAIL = 'lsp_membership_switch_email',
  EXTEND_LSP_BREAK_EMAIL = 'lsp_extend_break_email',
  INSURANCE_DATE_INPUT = 'date_input',
  INSURER_NAME_INPUT = 'insurer_name_input',
  PLI_PREFIX = 'pli',
  PLI_SELF_CERTIFY = 'pli_self_certify',
  MORE_SCREEN = 'more_screen',
  NEED_WORK_MODAL = 'need_work',
  NO_NETWORK_SCREEN_VIEWED = 'no_network_screen_viewed',
  REVIEW_MODAL = 'app_review',
  REVIEW_MODAL_FLOW_COMPLETE = 'post_review_flow_complete',
  REVIEW_MODAL_REVIEW_ERROR = 'post_review_failure',
  SIGN_IN_FAILURE_FIREBASE_AUTH = 'sign_in_failure_firebase_auth',
  SIGN_IN_BUTTON_CLICKED = 'sign_in_button_clicked',
  SIGN_IN_SUCCESS = 'sign_in_success',
  SIGN_IN_DISMISSED = 'sign_in_dismissed',
  SIGN_IN_FAILURE_NO_ACCOUNTS = 'sign_in_failure_no_accounts',
  SIGN_IN_SIGN_UP_CLICKED = 'sign_in_sign_up_clicked',
  SIGN_IN_HELP_CLICKED = 'sign_in_help_clicked',
  STATUS = 'status',
  USER_SIGNOUT_AUTOMATIC = 'user_signout_automatic',
  USER_SIGNOUT_EXPIRED_TOKEN = 'user_signout_expired_token',
  USER_SIGNOUT_MANUAL = 'user_signout_manual',
  VIEW_LSP_ON_BREAK = 'lsp_on_break',
  VIEW_MEMBERSHIPS = 'view_memberships',
  PAGE_VIEW = 'page_view',
  COPY_REVIEW_LINK = 'reviews_request_copy_link',
  REVIEWS_REQUEST_MORE = 'reviews_request_more_reviews_clicked',
  REVIEWS_REQUEST_SENT = 'reviews_send_request',
  REVIEWS_LIST_LEAD_REQUEST_SENT = 'reviews_list_send_request',
  REVIEWS_REMINDER_SENT = 'reviews_list_send_request_reminder',
  REVIEWS_OPEN_MENU = 'reviews_open_review_overflow',
  REVIEWS_CLOSE_MENU = 'reviews_close_review_overflow',
  REVIEWS_REPORT_CLICKED = 'reviews_report_review_clicked',
  REVIEWS_REPORT_SUBMITTED = 'reviews_review_report_submitted',
  REVIEWS_REPLY_CLICKED = 'reviews_reply_review_clicked',
  REVIEWS_REPLY_SUBMITTED = 'reviews_review_reply_submitted',
  REVIEWS_SHOW_BREAKDOWN = 'reviews_review_breakdown',
  REVIEWS_HIDE_BREAKDOWN = 'reviews_hide_breakdown',
  REVIEWS_NEW_FIELDS = 'reviews_new_fields',
  REVIEWS_WIDGET_COPY_CODE = 'reviews_widget_copy_code',

  JOBS_OPEN_ALL_JOBS = 'jobs_open_all_jobs',
  JOBS_OPEN_NEW_REQUESTS = 'jobs_open_new_requests',
  JOBS_OPEN_INTERESTED_JOBS = 'jobs_open_interested_jobs',
  JOBS_OPEN_COMPLETED = 'jobs_open_completed',
  JOBS_OPEN_ARCHIVE = 'jobs_open_archive',
  JOBS_ARCHIVE_TRAY = 'jobs_archive_tray',
  JOBS_OPEN_JOB = 'jobs_open_job',
  JOBS_JOB_ACCEPTED = 'jobs_job_accepted',
  JOBS_JOB_COMPLETED = 'jobs_job_completed',
  JOBS_JOB_BOOKED = 'jobs_job_booked',
  JOBS_JOB_BOOKED_UNDO = 'jobs_job_booked_undo',
  JOBS_JOB_COMPLETED_UNDO = 'jobs_job_completed_undo',
  JOBS_CALL_HOMEOWNER = 'jobs_call_homeowner',
  JOBS_EMAIL_HOMEOWNER = 'jobs_email_homeowner',
  JOBS_SMS_HOMEOWNER = 'jobs_sms_homeowner',
  JOBS_CHAT_HOMEOWNER = 'jobs_chat_homeowner',
  JOBS_WHATSAPP_HOMEOWNER = 'jobs_whatsapp_homeowner',
  JOBS_JOB_DECLINED = 'jobs_job_declined',
  JOBS_JOB_UNDO_TOAST_DECLINED = 'jobs_job_undo_toast_declined',
  JOBS_JOB_UNDO_TOAST_CANCELLED = 'jobs_job_undo_toast_cancelled',

  // Job accepted status events
  JOBS_JOB_STATUS_DROPDOWN = 'jobs_job_status_dropdown',
  JOBS_JOB_STATUS_LOST_SUBMITTED = 'jobs_job_status_lost_submitted',
  JOBS_JOB_LOST_OPEN = 'jobs_job_lost_opens',

  JOBS_DECLINE_SURVEY_SHOW = 'jobs_decline_survey_show',
  JOBS_DECLINE_SURVEY_SUBMIT = 'jobs_decline_survey_submit',
  JOBS_JOB_REFINEMENT_SURVEY_SUBMIT = 'jobs_job_refinement_survey_submit',
  JOBS_NOTIFICATIONS_SHOW = 'jobs_notifications_show',
  JOBS_NOTIFICATIONS_CHANGE_SAVED = 'jobs_notifications_change_saved',
  JOBS_YOUR_NOTES_SAVED = 'jobs_your_notes_saved',
  JOBS_REQUEST_REVIEW_REMINDER_CLICKED = 'reviews_job_request_reminder_clicked',
  JOBS_REQUEST_REVIEW_CLICKED = 'reviews_job_request_clicked',
  JOBS_REQUEST_REVIEW_FAILED = 'reviews_job_request_failed',
  JOBS_REQUEST_REVIEW_SUCCEEDED = 'reviews_job_request_succeeded',

  JOBS_DETAILS_PAGE = 'jobs_details_page',
  JOBS_MAP_CLICKED = 'job_map_clicked',
  JOBS_MAP_OPENED = 'jobs_map_opened',
  JOBS_MAP_COPY_ADDRESS = 'jobs_map_copy_address',
  JOBS_DIRECTIONS_BUTTON = 'jobs_directions_button',
  JOBS_CONTACT_OPTION_MENU = 'jobs_contact_option_menu',
  JOBS_JOB_INFORMATION_SCREEN = 'jobs_information_screen',
  JOBS_PROPERTY_FACTS_TOOLTIP = 'jobs_property_facts_tooltip',
  JOBS_JOB_REQUEST_PAYMENT = 'jobs_job_request_payment',

  SEARCH_CAT_PRIMARY_OPEN = 'search_cat_primary_open',
  SEARCH_CAT_PRIMARY_CLOSE = 'search_cat_primary_close',
  SEARCH_CAT_CATEGORY_ADD = 'search_cat_category_add',
  SEARCH_CAT_CATEGORY_REMOVE = 'search_cat_category_remove',
  SEARCH_CAT_REMOVE_MODAL = 'search_cat_remove_cat_modal',
  SEARCH_CAT_REMOVE_MODAL_YES = 'search_cat_remove_cat_modal_yes',
  SEARCH_CAT_REMOVE_MODAL_NO = 'search_cat_remove_cat_modal_no',
  SEARCH_CAT_WARN = 'search_cat_one_cat_warn',
  JOBS_JOB_LOCATION_MAP = 'jobs_job_location_map',
  PHOTOS_CREATE_ALBUM = 'photos_create_album',
  PHOTOS_EDIT_ALBUM = 'photos_edit_album',
  PHOTOS_DELETE_ALBUMS = 'photos_delete_albums',
  PHOTOS_REORDER_ALBUMS = 'photos_reorder_albums',
  PHOTOS_ADD_PHOTOS = 'photos_add_photos',
  PHOTOS_UPLOAD_FAILED = 'photos_upload_failed',
  PHOTOS_VALIDATION_ERROR = 'photos_validation_error',
  PHOTOS_DELETE_PHOTOS = 'photos_delete_photos',
  PHOTOS_REORDER_PHOTOS = 'photos_reorder_photos',
  PHOTOS_ADD_CAPTION = 'photos_add_caption',
  PHOTOS_ADD_TAG = 'photos_add_tag',
  PHOTOS_ROTATE_PHOTO = 'photos_rotate_photo',
  PUSH_NOTIFICATION_OPENED = 'cat_notification_opened',
  PHOTOS_UPLOAD_COMPLETE = 'photos_upload_complete',
  PAYMENTS_PAYMENT_SUCCESS = 'payments_payment_success',
  PAYMENTS_PAYMENT_FAILURE = 'payments_payment_failure',
  PAYMENTS_RETRY_BUTTON_CLICKED = 'payments_retry_payment_button_clicked',
  QUOTE_CREATE_QUOTE = 'quote_create_quote',
  QUOTE_ADD_LINE_ITEM = 'quote_add_line_item',
  QUOTE_EDITED_LINE_ITEM = 'quote_edited_line_item',
  QUOTE_DELETED_LINE_ITEM = 'quote_deleted_line_item',
  QUOTE_SAVED = 'quote_saved',
  QUOTE_SHARED_QUOTE = 'quote_shared_quote',
  QUOTE_RESHARE = 'quote_reshare',
  QUOTE_DELETED = 'quote_deleted',
  QUOTE_CONVERT_TO_INVOICE = 'quote_convert_to_invoice',
  QUOTE_CONVERT_TO_INVOICE_MODAL = 'quote_convert',
  QUOTE_EDIT_QUOTE = 'quote_edit_quote',
  QUOTE_EDITED = 'quote_edited',
  QUOTE_PAYMENTS = 'quote_payments',
  INVOICE_PAYMENTS = 'quote_invoice_payments',
  QUOTE_PAYMENTS_LIST_NEW_PAYMENT = 'quote_payments_list_new_payment',
  QUOTE_CONVERTED_TO_INVOICE = 'quote_converted_to_invoice',
  QUOTE_INVOICE_MARK_AS_PAID = 'quote_invoice_mark_as_paid',
  QUOTE_INVOICE_MARK_AS_PAID_MODAL = 'invoice_paid',
  QUOTE_INVOICE_SHARED = 'quote_invoice_shared',
  QUOTE_INVOICE_RESHARED = 'quote_invoice_reshared',
  QUOTE_INVOICE_REVERT_TO_QUOTE = 'quote_invoice_rev',
  PLI_UPLOAD_VIEWED = 'pli_upload_viewed',
  PLI_UPLOAD_SAVED = 'pli_upload_saved',
  PLI_UPLOAD_SAVED_FAILED = 'pli_upload_saved_failed',
  PLI_UPLOAD_ADD_DOCUMENT_CLICKED = 'pli_upload_add_document_clicked',
  PLI_UPLOAD_ADD_ANOTHER_DOC_CLICKED = 'pli_upload_add_another_doc_clicked',
  PLI_UPLOAD_INSURER_INPUT_CLICKED = 'pli_upload_insurer_input_clicked',
  PLI_UPLOAD_DATE_INPUT_CLICKED = 'pli_upload_date_input_clicked',
  PLI_UPLOAD_DOC_RETRY_CLICKED = 'pli_upload_doc_retry_clicked',
  PLI_UPLOAD_DOC_ADDED = 'pli_upload_doc_added',
  PLI_UPLOAD_DOC_REMOVED = 'pli_upload_doc_removed',
  PLI_UPLOAD_DOC_FAILED = 'pli_upload_doc_failed',
  PLI_UPLOAD_PERMISSIONS = 'pli_upload_perm',
  ACCREDITATIONS_DOC_UPLOAD_ADDED = 'accreditations_upload_doc_added',
  ACCREDITATIONS_DOC_UPLOAD_FAILED = 'accreditations_upload_doc_failed',
  ACCREDITATIONS_UPLOAD_PERMISSIONS = 'accreditations_upload_perm',
  WORK_RADIUS_SAVED = 'work_radius_saved',
  GUIDE_HOWTO = 'quote_howto',
  GUIDE_SKIPPED = 'quote_howto_skipped',
  GUIDE_FINISHED = 'quote_howto_finished',
  ONBOARDING_WIDGET = 'onboarding_widget',
  ONBOARDING_TODO_REVIEW = 'onboarding_todo_review',
  ONBOARDING_TODO_QUOTE = 'onboarding_todo_quote',
  // ONBOARDING_TODO_DESCRIPTIONS refers to "Profile Description" only
  ONBOARDING_TODO_DESCRIPTIONS = 'onboarding_todo_descriptions',
  ONBOARDING_TODO_PLI = 'onboarding_todo_pli',
  ONBOARDING_TODO_PHOTOS = 'onboarding_todo_photos',
  ONBOARDING_TODO_ACCREDS = 'onboarding_todo_accreds',
  LEAD_CHANNEL_FOMO_WIDGET = 'lead_channel_fomo_widget',
  LEAD_CHANNEL_FOMO_UPGRADE = 'lead_channel_fomo_upgrade',
  LEAD_CHANNEL_TIME_PERIOD = 'lead_channel_time_period',
  LEAD_CHANNEL_UNREAD_JOBS = 'lead_channel_unread_jobs',
  IN_LIFE_OPTIMISATION_WIDGET = 'in_life_optimisation_widget',
  IN_LIFE_OPTIMISATION_REVIEW = 'in_life_optimisation_review',
  IN_LIFE_OPTIMISATION_PLI = 'in_life_optimisation_pli',
  IN_LIFE_OPTIMISATION_PHOTOS = 'in_life_optimisation_photos',
  IN_LIFE_OPTIMISATION_COVER_IMAGE = 'in_life_optimisation_cover_image',
  CHANNEL_PERFORMANCE_WIDGET = 'channel_performance_widget',
  // Accreditations
  ACCREDITATIONS_ADD_CHANGED_DROPDOWN = 'accreditations_add_changed_dropdown',
  ACCREDITATIONS_ADD_ADDED_DOCUMENT = 'accreditations_add_added_document',
  ACCREDITATIONS_ADD_REMOVED_DOCUMENT = 'accreditations_add_removed_document',
  ACCREDITATIONS_ADD_CHANGED_EXPIRY = 'accreditations_add_changed_expiry',
  ACCREDITATIONS_ADD_MATCH_FOUND = 'accreditations_add_match_found',
  ACCREDITATIONS_ADD_SUBMIT = 'accreditations_add_submit',
  ACCREDITATIONS_VIEW = 'accreditations_view',
  ACCREDITATIONS_VIEW_ADDED_DOCUMENT = 'accreditations_view_added_document',
  ACCREDITATIONS_VIEW_REMOVED_DOCUMENT = 'accreditations_view_removed_document',
  ACCREDITATIONS_VIEW_CHANGED_EXPIRY = 'accreditations_view_changed_expiry',
  ACCREDITATIONS_VIEW_DELETED = 'accreditations_view_deleted',
  ACCREDITATIONS_VIEW_SUBMIT = 'accreditations_view_submit',

  // Youtube
  YOUTUBE_OPEN = 'youtube_open',
  YOUTUBE_CLOSE = 'youtube_close',
  YOUTUBE_SUBMIT = 'youtube_submit',

  // Direct Debit
  DIRECT_DEBIT_SCREEN_VIEWED = 'direct_debit_screen_viewed',
  DIRECT_DEBIT_UPDATE_BUTTON_CLICKED = 'direct_debit_update_button_clicked',
  DIRECT_DEBIT_SUBMIT_BUTTON_CLICKED = 'direct_debit_submit_button_clicked',
  DIRECT_DEBIT_UPDATE_SUCCESS = 'direct_debit_update_success',
  DIRECT_DEBIT_UPDATE_FAILURE = 'direct_debit_update_failure',
  DIRECT_DEBIT_ERROR = 'direct_debit_error',

  // Services
  SERVICES_VIEW = 'services_view',
  SERVICES_CHANGE_SUCCESS = 'services_change_success',
  SERVICES_CHANGE_FAILURE = 'services_change_failure',

  // Subcontracting
  SUBCONTRACTING_VIEW = 'subcontracting_view',
  SUBCONTRACTING_UPDATE_SUCCESS = 'subcontracting_update_success',
  SUBCONTRACTING_UPDATE_FAILURE = 'subcontracting_update_failure',

  // Profile
  PROFILE_COMPANY_LOGO_UPLOAD_SUCCESS = 'profile_company_logo_upload_success',
  PROFILE_COMPANY_LOGO_UPLOAD_FAILURE = 'profile_company_logo_upload_failure',
  PROFILE_COMPANY_LOGO_OBJECT_UPDATED = 'profile_company_logo_object_updated',
  PROFILE_COMPANY_LOGO_OBJECT_REMOVED = 'profile_company_logo_object_removed',
  PROFILE_COVER_PHOTO_UPLOAD_SUCCESS = 'profile_cover_photo_upload_success',
  PROFILE_COVER_PHOTO_UPLOAD_FAILURE = 'profile_cover_photo_upload_failure',
  PROFILE_COVER_PHOTO_OBJECT_UPDATED = 'profile_cover_photo_object_updated',
  PROFILE_COVER_PHOTO_OBJECT_REMOVED = 'profile_cover_photo_object_removed',
  PROFILE_TRADE_PREVIEW_CLICKED = 'profile_trade_preview_clicked',

  // Campaigns
  CAMPAIGNS_PAGE = 'campaigns_page',
  CAMPAIGN_ENQUIRE = 'campaign_enquire',
  CAMPAIGN_CREATE = 'campaign_create',
  CAMPAIGN_EDIT = 'campaign_edit',
  CAMPAIGN_VIEW_LEAD = 'campaign_view_lead',
  CAMPAIGN_PAUSE = 'campaign_pause',
  CAMPAIGN_PAUSE_SUCCESS = 'campaign_pause_success',
  CAMPAIGN_PAUSE_SUCCESS_SUBMITTED = 'campaign_pause_success_submitted',
  CAMPAIGN_RESUME = 'campaign_resume',
  CAMPAIGN_REMOVE = 'campaign_remove',
  CAMPAIGN_UPGRADE = 'campaign_upgrade',
  CAMPAIGN_CATEGORIES_PAGE_CONTINUE = 'campaign_categories_continue',
  CAMPAIGN_CATEGORIES_PAGE_REQUEST = 'campaign_categories_request',
  CAMPAIGN_TYPES_OF_WORK_PAGE_CONTINUE = 'campaign_types_of_work_continue',
  CAMPAIGN_WORK_AREAS_PAGE_CONTINUE = 'campaign_work_areas_continue',
  CAMPAIGN_WORK_AREAS_MAP_SELECTION = 'campaign_postcode_map',
  CAMPAIGN_WORK_AREAS_LIST_SELECTION = 'campaign_postcode_list',
  CAMPAIGN_REVIEW_AND_CONFIRM_PAGE_CONTINUE = 'campaign_review_continue',
  CAMPAIGN_REVIEW_AND_CONFIRM_PAGE_CONTINUE_CLICKED = 'campaign_review_continue_clicked',
  CAMPAIGN_SET_YOUR_MONTHLY_BUDGET_PAGE_CONTINUE = 'campaign_monthly_budget_continue',
  CAMPAIGN_DOES_NOT_HAVE_MINIMUM_CREDIT = 'campaign_does_not_have_minimum_credit',
  CAMPAIGN_UPGRADES_ERROR_NO_UPGRADES_AVAILABLE = 'campaign_upgrades_no_upgrades_available',
  CAMPAIGN_UPGRADES_ERROR_UPGRADES_NOT_ALLOWED = 'campaign_upgrades_not_allowed',
  CAMPAIGN_UPGRADES_ERROR_UPGRADES_NOT_ALLOWED_EMAIL = 'campaign_upgrades_not_allowed_email',
  CAMPAIGN_UPGRADES_ERROR_UPGRADES_NOT_ALLOWED_IN_DEBT = 'campaign_upgrades_not_allowed_in_debt',
  CAMPAIGN_UPGRADES_SELECT_UPGRADE = 'campaign_upgrades_select_upgrade',
  CAMPAIGN_UPGRADES_SUCCESS = 'campaign_upgrades_success',
  CAMPAIGN_CREATION_NO_PLANS_AVAILABLE = 'campaign_creation_no_plans_available',
  CAMPAIGN_CREATION_PLANS_AVAILABLE = 'campaign_creation_plans_available',
  CAMPAIGN_CREATION_SELECT_PLAN = 'campaign_creation_select_plan',
  CAMPAIGN_CREATION_SUCCESS = 'campaign_creation_success',
  CAMPAIGN_CREATE_ERROR_IN_DEBT = 'campaign_create_error_in_debt',
  CAMPAIGN_RECOMMENDED_LOCATION_CLICKED = 'campaign_recommended_location_clicked',
  CAMPAIGN_RENEWAL_WINDOW_MODAL_VIEWED = 'campaign_renewal_window_modal_viewed',

  // Sponsored Listings
  SPONSORED_LISTINGS_VIEWED = 'sponsored_listings_viewed',
  SPONSORED_LISTINGS_CREATE_BUTTON_CLICKED = 'sponsored_listings_create_button_clicked',
  SPONSORED_LISTINGS_CATEGORY_SELECTED = 'sponsored_listings_category_selected',
  SPONSORED_LISTINGS_SUBCATEGORIES_AND_GEOS_STEP_CONTINUE_BUTTON_CLICKED = 'sponsored_listings_subcategories_and_geos_step_continue_button_clicked',
  SPONSORED_LISTINGS_BUDGET_AND_BID_STRATEGY_STEP_CONTINUE_BUTTON_CLICKED = 'sponsored_listings_budget_and_bid_strategy_step_continue_button_clicked',
  SPONSORED_LISTINGS_CREATED = 'sponsored_listings_created',
  SPONSORED_CAMPAIGN_PAGE_REQUEST = 'sponsored_campaign_request',

  // Offers and Discounts
  OFFERS_AND_DISCOUNTS_VIEWED = 'offers_discounts_viewed',
  OFFERS_ONSI_LAUNCH_CLICKED = 'offers_onsi_launch_clicked',
  OFFERS_ONSI_SIGN_UP_CLICKED = 'offers_onsi_sign_up_clicked',
  OFFERS_ONSI_LEARN_MORE_CLICKED = 'offers_onsi_learn_more_clicked',

  // Beta
  BETA_INTERVIEW = 'beta_interview',
  BETA_TERMS_CLICKED = 'beta_interview_modal_terms_clicked',

  // Feedback
  FEEDBACK_MODAL = 'feedback_modal',

  // Inbox
  INBOX_CHANNEL_OPEN = '2wm_inbox_channel_open',
  INBOX_CHANNEL_BACK = '2wm_inbox_channel_back',
  INBOX_CHANNEL_MESSAGE_SENT = '2wm_inbox_channel_message_sent',
  INBOX_JOB_INTERESTED = '2wm_inbox_job_interested',
  INBOX_JOB_NOT_INTERESTED = '2wm_inbox_job_not_interested',
  INBOX_GUIDE = '2wm_inbox_guide',
  INBOX_GUIDE_SKIPPED = '2wm_inbox_guide_skipped',
  INBOX_GUIDE_FINISHED = '2wm_inbox_guide_finished',
  INBOX_CALL_HOMEOWNER = '2wm_inbox_channel_call_homeowner',
  INBOX_COPY_HOMEOWNER_NUMBER = '2wm_inbox_channel_copy_number_homeowner',
  INBOX_CHANNEL_REQUEST_ADDRESS_SENT = '2wm_inbox_channel_request_address_sent',
  INBOX_CHANNEL_JOB_PAYMENTS_GUIDE = '2wm_inbox_job_payments_guide',

  // Appointments
  APPOINTMENT_CANCELLED = 'appointment_cancelled',
  APPOINTMENT_CREATED = 'appointment_created',
  APPOINTMENT_UPDATED = 'appointment_updated',

  // Referral
  REFERRAL_CAMPAIGN_VIEWED = 'referral_campaign_viewed',
  REFERRAL_COPY_LINK_CLICKED = 'referral_copy_link_clicked',

  // Marketing Preferences
  MARKETING_PREFERENCES_TOGGLE = 'marketing_preferences_toggle',
  MARKETING_PREFERENCES_LOADED = 'marketing_preferences_loaded',
  MARKETING_PREFERENCES_ERROR = 'marketing_preferences_error',

  // Marketing Materials
  MARKETING_MATERIALS_LAUNCH_SHOPIFY = 'marketing_materials_launch_shopify',

  // Job Payments
  JOB_PAYMENTS_GUIDE = 'job_payments_guide',

  PAYMENTS_SET_UP_CONTACT_US = 'payments_set_up_contact_us',
  PAYMENTS_SET_UP_LEARN_MORE = 'payments_set_up_learn_more',
  PAYMENTS_SET_UP_SETUP = 'payments_set_up_setup',
  PAYMENTS_SET_UP_START = 'payments_set_up_start',
  PAYMENTS_GUIDE_SET_UP_START = 'payments_guide_set_up_start',
  PAYMENTS_SET_UP_PAUSED_TRY_AGAIN = 'payments_set_up_paused_try_again',
  PAYMENTS_SET_UP_PAUSED_RESUME = 'payments_set_up_paused_resume',
  PAYMENTS_SET_UP_TERMS_CONSENT = 'payments_set_up_terms_consent',
  PAYMENTS_SET_UP_HMRC_DETAILS = 'payments_set_up_hmrc_details',
  PAYMENTS_2WM_REQUEST_DETAILS = '2mw_payments_request_details',

  PAYMENTS_AREA_NEW_PAYMENT = 'payments_area_new_payment',
  PAYMENTS_AREA_ACTIVITY = 'payments_area_activity',
  PAYMENTS_AREA_PAYMENT = 'payments_area_payment',
  PAYMENTS_REQUEST_CREATE = 'payments_request_create',
  PAYMENT_REQUEST_FROM_CHAT = '2wm_overflow_request_payment',
  PAYMENTS_REQUEST_NEW_REQUEST = 'payments_request_new_request',
  PAYMENTS_REQUEST_COPY_LINK = 'payments_request_copy_link',
  PAYMENTS_REQUEST_CANCEL_REQUEST = 'payments_request_cancel_request',
  PAYMENTS_REQUEST_OVERFLOW = 'payments_request_overflow',
  PAYMENTS_REQUEST_OVERFLOW_FAQ = 'payments_request_overflow_faq',

  PAYMENTS_NEW_REQUEST_SEND_REQUEST = 'payments_new_request_send_request',
  PAYMENTS_NEW_REQUEST_JOB = 'payments_new_request_job',

  PAYMENTS_OVERFLOW = 'payments_overflow',
  PAYMENTS_OVERFLOW_FAQ = 'payments_overflow_faq',
  PAYMENTS_OVERFLOW_LEARN_MORE = 'payments_overflow_learn_more',
  PAYMENTS_TAP_EDUCATION = 'payments_tap_education',

  PAYMENTS_CANCEL = 'payments_cancel',
  PAYMENTS_CANCEL_REASON = 'payments_cancel_reason',

  PAYMENTS_STATEMENT_REPORT_EXPORT = 'payments_statement_report_export',

  // Off Platform Job Payments
  PAYMENTS_AREA_CHOOSE_JOB = 'payments_area_choose_job', // Click new payment request will take you to choose job screen
  PAYMENTS_AREA_NEW_OFF_PLATFORM_JOB_PAYMENT = 'payments_area_off_platform_job', // click create new job

  // Auth Error Screen
  AUTH_ERROR_SCREEN_VIEWED = 'auth_error_screen_viewed',

  // Checkatrade Academy
  CHECKATRADE_ACADEMY_VISITED = 'checkatrade_academy_visited',

  // Onboarding/Freemium
  ONBOARDING_SIGNIN_SUCCESS_ESSENTIALS = 'signin_success_essentials',
  ONBOARDING_PAGE_VIEWED = 'onboarding_page_viewed',
  ONBOARDING_TERMS_AND_CONDITIONS_CHECKED = 'onboarding_terms_and_conditions_checked',
  ONBOARDING_TERMS_AND_CONDITIONS_UNCHECKED = 'onboarding_terms_and_conditions_unchecked',
  ONBOARDING_TERMS_ACCEPT_CLICKED = 'freemium_terms_accept_clicked',
  ONBOARDING_CHECKBOX_ACTION_CLICKED = 'onboarding_checkbox_action_clicked',
  ONBOARDING_VIEW_PHOTOS_CLICKED = 'onboarding_view_photos_clicked',
  ONBOARDING_VIEW_PROFILE_CLICKED = 'onboarding_view_profile_clicked',
  ONBOARDING_VIEW_REVIEWS_CLICKED = 'onboarding_view_reviews_clicked',
  ONBOARDING_VETTING_AREA_CLICKED = 'onboarding_vetting_area_clicked',
  ONBOARDING_BANNER_SETUP_PROFILE_CLICKED = 'onboarding_banner_setup_profile_clicked',
  FREEMIUM_TERMS_SIGNOUT_CLICKED = 'freemium_terms_user_signout_clicked',
  FREEMIUM_ONBOARDING = 'freemium_onboarding',
  FREEMIUM_ONBOARDING_SKIPPED = 'freemium_onboarding_skipped',
  FREEMIUM_TERMS_AND_CONDITIONS_VIEWED = 'freemium_terms_terms_cond_viewed',
  FREEMIUM_TERMS_OF_USE_VIEWED = 'freemium_terms_terms_of_use_viewed',
  FREEMIUM_PRIVACY_POLICY_VIEWED = 'freemium_terms_privacy_viewed',
  FREEMIUM_COOKIE_POLICY_VIEWED = 'freemium_terms_cookie_viewed',

  // Loan Card
  LOAN_CARD_CLICK = 'loan_card_click',

  // MyTeams
  CMA_TERMS_MODAL_VIEWED = 'cma_terms_modal_viewed',
  CMA_TERMS_MODAL_SAVED = 'cma_terms_modal_saved',
  CMA_TERMS_TOGGLE_CLICKED = 'cma_terms_toggle_clicked',
  CMA_TERMS_ARTICLE_VIEWED = 'cma_terms_article_viewed',
  MY_TEAM_EMPLOYEE_DETAILS_UPDATE_SUPPORT_MODAL_VIEWED = 'my_team_employee_details_update_support_modal_viewed',
  MY_TEAM_REMOVE_SUBCONTRACTOR = 'remove_subcontractor',
  MY_TEAM_ADD_EMPLOYEE_VALIDATION_ERROR_MODAL_VIEWED = 'my_team_add_employee_validation_error_modal_viewed',
  MY_TEAM_ADD_EMPLOYEE_ALREADY_EXISTS_MODAL_VIEWED = 'my_team_add_employee_already_exists_modal_viewed',
  MY_TEAM_INVITE_ERROR_MODAL_VIEWED = 'my_team_invite_error_modal_viewed',
  MY_TEAM_INVITE_ALREADY_ACCEPTED_MODAL_VIEWED = 'my_team_invite_already_accepted',
  MY_TEAM_VIEWED = 'my_team_viewed',
  MY_TEAM_ONSITE_STAFF_TAB_VIEWED = 'onsite_staff_tab_viewed',
  MY_TEAM_ADD_OSS_CLICKED = 'add_oss_clicked',
  MY_TEAM_UPDATE_EMPLOYEE_CLICK = 'update_employee_click',
  MY_TEAM_REMOVE_EMPLOYEE_CLICKED = 'remove_employee_clicked',
  MY_TEAM_CONTACT_SUPPORT_MODAL_VIEWED = 'contact_support',
  MY_TEAM_MANAGE_ACCREDITATIONS_CLICKED = 'manage_accreditations_clicked',
  MY_TEAM_SEARCH_FOR_OSS = 'search_for_oss',
  MY_TEAM_SUBCONTRACTORS_TAB_VIEWED = 'subcontractors_tab_viewed',
  MY_TEAM_INVITE_SHARE_CLICKED = 'share_invite_clicked',
  MY_TEAM_RESEND_INVITE_CLICKED = 'resend_invite_clicked',
  MY_TEAM_ADD_OSS_SCREEN_VIEWED = 'add_oss_screen_viewed',
  MY_TEAM_ADD_SERVICE_CLICKED = 'add_service_clicked',
  MY_TEAM_OSS_EXISTS_MODAL_VIEWED = 'oss_exists_modal',
  MY_TEAM_OSS_ERROR_MODAL_VIEWED = 'oss_error_viewed',
  MY_TEAM_TRADE_ACCREDITATIONS_SCREEN_VIEWED = 'my_team_accreditations_page_viewed',
  MY_TEAM_NEW_ACCREDITATION_SCREEN_VIEWED = 'my_team_new_accreditation_page_viewed',
  MY_TEAM_NEW_ACCREDITATION_SAVED = 'my_team_new_accreditation_saved',
  MY_TEAM_ACCREDITATION_DELETED = 'my_team_accreditation_deleted',
  MY_TEAM_CONTRACTORS_TAB_VIEWED = 'contractors_tab_viewed',
  MY_TEAM_INVITE_MODAL_VIEWED = 'invite_modal_viewed',
  MY_TEAM_INVITE_MODAL_ACCEPT_CLICKED = 'invite_modal_accept',
  MY_TEAM_INVITE_MODAL_DECLINE_CLICKED = 'invite_modal_decline',
  MY_TEAM_SELECT_ACCOUNT_MODAL_VIEWED = 'select_account_modal_viewed',
  MY_TEAM_SELECT_ACCOUNT_MODAL_ACCEPT = 'select_account_modal_accept',
  MY_TEAM_SELECT_ACCOUNT_MODAL_DECLINE = 'select_account_modal_decline',
  MY_TEAM_VETTING_CHECKS_SCREEN_VIEWED = 'my_vetting_viewed',
  MY_TEAM_VETTING_CONSENT_APP_CLICKED = 'vetting_consent_app_clicked',
  MY_TEAM_ACCREDS_CTA_CLICKED = 'accreds_cta_clicked',
  MY_TEAM_REMOVE_CONTRACTOR_CONFIRMED = 'remove_contractor_confirmed',

  // Toolbelt
  TOOLBELT_VIEWED = 'toolbelt_viewed',
  TOOLBELT_ACTION_CLICKED = 'toolbelt_clicked',

  // Alert Header
  ALERT_HEADER_ACC_EXPIRE_CLICKED = 'alert_header_acc_expire_clicked',
  ALERT_HEADER_ACC_SOON_EXPIRE_CLICKED = 'alert_header_acc_soon_expire_clicked',
  ALERT_HEADER_PLI_EXPIRE_CLICKED = 'alert_header_pli_expire_clicked',
  ALERT_HEADER_NEW_REVIEW_CLICKED = 'alert_header_new_review_clicked',
  ALERT_HEADER_MISSING_REVIEW_CLICKED = 'alert_header_missing_review_clicked',
  ALERT_HEADER_APP_UPGRADE_CLICKED = 'alert_header_app_upgrade_clicked',
  ALERT_HEADER_CAMPAIGN_PAUSED_CLICKED = 'alert_header_campaign_paused_clicked',
  ALERT_HEADER_REVET_PLI_CLICKED = 'alert_header_revet_pli_clicked',
  ALERT_HEADER_REVET_ACCREDITATIONS_CLICKED = 'alert_header_revet_accreditations_clicked',
  ALERT_HEADER_REVET_MITEK_CLICKED = 'alert_header_revet_mitek_clicked',

  // My Team Invite
  INVITE_TERMS_AND_CONDITIONS_CLICKED = 'invite_terms_clicked',
  SUBBIE_INVITE_CREATE_ACCOUNT_CLICKED = 'subbie_invite_create_account_clicked',
  SUBBIE_INVITE_LOGIN_CLICKED = 'subbie_invite_login_clicked',
  INVITE_TERMS_OF_USE_CLICKED = 'invite_terms_of_use_clicked',
  INVITE_PRIVACY_POLICY_CLICKED = 'invite_privacy_policy_clicked',
  INVITE_COOKIE_POLICY_CLICKED = 'invite_cookie_policy_clicked',
  INVITE_PAGE_SHOW = 'invite_page_show',

  ADD_SUBCONTRACTOR_EMAIL_CLICKED = 'add_subcontractor_email_clicked',
  ADD_OSS_FORM_CONTINUE = 'add_oss_form_continue',
  OSS_ADD_SUCCESS = 'oss_add_success',

  // Active Home - My Jobs
  MY_JOBS_TAB_CHANGED = 'my_jobs_tab_changed',
  MY_JOBS_SINGLE_JOB_CLICKED = 'my_jobs_single_job_clicked',
  MY_JOBS_ALL_JOBS_CLICKED = 'my_jobs_all_jobs_clicked',

  // Active Home - Insights
  INSIGHTS_VIEWED = 'insights_viewed',
  INSIGHTS_TIME_PERIOD_SELECTED = 'insights_time_period_selected',

  // Bookable Services
  BOOKABLE_SERVICES_FAQ_HELPER = 'bookable_services_faq_helper', // Click on helper icon on manage bookable services page
  BOOKABLE_SERVICES_FORM = 'bookable_service_form',
  BOOKABLE_SERVICES_EARLY_ACCESS = 'bookable_services_early_access',
  BOOKABLE_SERVICES_MANAGE_ADD_SERVICE = 'bookable_services_add_service',
  BOOKABLE_SERVICES_MANAGE_DETAIL = 'bookable_services_manage_detail',
  BOOKABLE_SERVICES_CREATE_FROM_DUPLICATE = 'bookable_services_duplicate',
  BOOKABLE_SERVICES_CREATE_SUCCESS = 'bookable_services_create_success',
  BOOKABLE_SERVICES_EDIT_SUCCESS = 'bookable_services_edit_success',
  BOOKABLE_SERVICES_DELETE_SUCCESS = 'bookable_services_delete_success',
  BOOKABLE_SERVICES_PUBLISH_SUCCESS = 'bookable_services_publish_success',
  BOOKABLE_SERVICES_HIDE_SUCCESS = 'bookable_services_hide_success',
  BOOKABLE_SERVICES_SUBMIT_FOR_REVIEW_SUCCESS = 'bookable_services_submit_review_success',
  BOOKABLE_SERVICES_DUPLICATE_SUCCESS = 'bookable_services_duplicate_success',

  // My Details
  MY_DETAILS_SCREEN_VIEWED = 'my_details_screen_viewed',

  MY_DETAILS_COMPANY_NAME_SUBMIT_REQUEST = 'company_name_submit_request',
  MY_DETAILS_COMPANY_NAME_SUBMIT_SUCCESS = 'company_name_submit_success',
  MY_DETAILS_COMPANY_NAME_SUBMIT_FAILURE = 'company_name_submit_failure',

  MY_DETAILS_ACCOUNT_PHONE_SUBMIT_REQUEST = 'account_phone_submit_request',
  MY_DETAILS_ACCOUNT_PHONE_SUBMIT_SUCCESS = 'account_phone_submit_success',
  MY_DETAILS_ACCOUNT_PHONE_SUBMIT_FAILURE = 'account_phone_submit_failure',

  MY_DETAILS_ACCOUNT_EMAIL_SUBMIT_REQUEST = 'account_email_submit_request',
  MY_DETAILS_ACCOUNT_EMAIL_SUBMIT_SUCCESS = 'account_email_submit_success',
  MY_DETAILS_ACCOUNT_EMAIL_SUBMIT_FAILURE = 'account_email_submit_failure',

  MY_DETAILS_ACCOUNT_TRADING_ADDRESS_SUBMIT_REQUEST = 'account_trd_add_submit_request',
  MY_DETAILS_ACCOUNT_TRADING_ADDRESS_SUBMIT_SUCCESS = 'account_trd_add_submit_success',
  MY_DETAILS_ACCOUNT_TRADING_ADDRESS_SUBMIT_FAILURE = 'account_trd_add_submit_failure',

  MY_DETAILS_ACCOUNT_ADMIN_ADDRESS_SUBMIT_REQUEST = 'account_admin_add_submit_request',
  MY_DETAILS_ACCOUNT_ADMIN_ADDRESS_SUBMIT_SUCCESS = 'account_admin_add_submit_success',
  MY_DETAILS_ACCOUNT_ADMIN_ADDRESS_SUBMIT_FAILURE = 'account_admin_add_submit_failure',

  MY_DETAILS_UPDATE_CONTACT_SUBMIT_REQUEST = 'update_contact_submit_request',
  MY_DETAILS_UPDATE_CONTACT_SUBMIT_SUCCESS = 'update_contact_submit_success',
  MY_DETAILS_UPDATE_CONTACT_SUBMIT_FAILURE = 'update_contact_submit_failure',

  MY_DETAILS_DELETE_MODAL_VIEWED = 'delete_contact_modal_viewed',
  MY_DETAILS_DELETE_CONTACT_SUBMIT_REQUEST = 'delete_contact_submit_request',
  MY_DETAILS_DELETE_CONTACT_SUBMIT_SUCCESS = 'delete_contact_submit_success',
  MY_DETAILS_DELETE_CONTACT_SUBMIT_FAILURE = 'delete_contact_submit_failure',

  TROUBLESHOOTING_HELP_CENTRE_LINK_CLICKED = 'more_screen_support_clicked',
  TROUBLESHOOTING_TERMS_AND_CONDITIONS_CLICKED = 'more_screen_terms_conditions_clicked',
  TROUBLESHOOTING_ACCOUNT_TIPS_CLICKED = 'troubleshooting_account_tips_clicked',
  TROUBLESHOOTING_ACCREDITATIONS_CLICKED = 'troubleshooting_accreditations_clicked',
  TROUBLESHOOTING_INSURANCE_CLICKED = 'troubleshooting_insurance_clicked',
  TROUBLESHOOTING_CAMPAIGNS_CLICKED = 'troubleshooting_campaigns_clicked',
  TROUBLESHOOTING_REVIEWS_CLICKED = 'troubleshooting_reviews_clicked',
  TROUBLESHOOTING_PHOTOS_CLICKED = 'troubleshooting_photos_clicked',
  TROUBLESHOOTING_PROFILE_DESCRIPTION_CLICKED = 'troubleshooting_profile_description_clicked',
  TROUBLESHOOTING_PROFILE_PHOTO_CLICKED = 'troubleshooting_profile_photo_clicked',
  TROUBLESHOOTING_COVER_PHOTO_CLICKED = 'troubleshooting_cover_photo_clicked',
  TROUBLESHOOTING_BUSINESS_OFFERINGS_CLICKED = 'troubleshooting_business_offerings_clicked',
  TROUBLESHOOTING_SUBCATEGORIES_CLICKED = 'troubleshooting_subcategories_clicked',
  TROUBLESHOOTING_POST_CODES_CLICKED = 'troubleshooting_post_codes_clicked',

  // Featured Projects
  FEATURED_PROJ_CREATE_CLICKED = 'featured_proj_create_clicked',
  FEATURED_PROJ_DELETE_CLICKED = 'featured_proj_delete_clicked',
  FEATURED_PROJ_VIEW_ALL_JOBS_CLICKED = 'featured_proj_view_all_jobs_clicked',
  FEATURED_PROJ_ADD_PHOTOS_CLICKED = 'featured_proj_add_photos_clicked',
  FEATURED_PROJ_ALBUM_SELECTION_CLICKED = 'featured_proj_album_selection_clicked',
  FEATURED_PROJ_ADD_REVIEW_CLICKED = 'featured_proj_add_review_clicked',
  FEATURED_PROJ_PUBLISH_STAGE_CLICKED = 'featured_proj_publish_stage_clicked',
  FEATURED_PROJ_PUBLISH_CLICKED = 'featured_proj_publish_clicked',
  FEATURED_PROJ_GO_BACK_EDIT = 'featured_proj_go_back_edit',

  // My Insights
  MY_INSIGHTS_VIEWED = 'my_insights_viewed',
  MY_INSIGHTS_ENGAGEMENT_TITLE_CLICKED = 'my_insights_engagement_title_clicked',
  MY_INSIGHTS_ENGAGEMENT_FOOTER_CLICKED = 'my_insights_engagement_footer_clicked',
}

export enum QUOTE_SOURCE_TYPE {
  MESSAGE_ACTION = 'message action',
  SHARE_BUTTON = 'share button',
  MAGIC_LINK_CLIPBOARD = 'magic link - saved to clipboard',
  MAGIC_LINK_URL_WITH_MESSAGE = 'magic link - url with message',
  DOWNLOAD = 'downloaded pdf',
}

export enum COMPONENT_TYPE {
  INPUT = 'input',
  SAVE_BUTTON = 'save_button',
  MODAL = 'modal',
  SCREEN = 'screen',
}

export enum ANALYTICS_ACTION_TYPE {
  CLICKED = 'clicked',
  SELECTED = 'selected',
  VIEWED = 'viewed',
  SUBMITTED = 'submitted',
  CONTINUED = 'continued',
  DISCARDED = 'discarded',
  SAVED = 'saved',
  GOTO = 'goto',
  CLOSE = 'close',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  FINISHED = 'finished',
  SEARCHED = 'searched',
}

export enum ALSP_ACTION_TYPE {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade',
}

export type MESSAGE_TYPE = {
  screen: string;
  name?: string;
  action: ANALYTICS_ACTION_TYPE;
};

export type ALSP_MESSAGE_TYPE = {
  currentTier?: string;
  newTier?: string;
  action?: ALSP_ACTION_TYPE;
  downgradeReason?: SubscriptionPauseReason;
};

export type NOTIFICATION_PREF_CHANGE_TYPE =
  | 'sms_updated'
  | 'email_updated'
  | 'sms_toggle_on'
  | 'sms_toggle_off';

export enum PLI_DOC_UPLOAD_FAILED_REASON {
  UNSUPPORTED = 'unsupported',
  FILE_SIZE = 'file-size',
  OTHER = 'other',
}

export enum DocUploadFailedReason {
  Unsupported = 'unsupported',
  FileSize = 'file-size',
  Other = 'other',
}

export type JOB_MESSAGE_JOB_TYPE = {
  id: string;
  status?: ArchivedLeadStatus;
};

export type MARKETPLACE_JOB_MESSAGE_JOB_TYPE = {
  id: string;
  status?: JobStatus;
};

export type JOB_MESSAGE_TYPE = {
  job_id?: string;
  job_ids?: string[];
  job_ids_v2?: string[];
  jobs?: JOB_MESSAGE_JOB_TYPE[];
  status?: string;
  response?: string;
  changes?: string[];
  source?: string;
  option?: string;
  previous_state?: string;
  next_state?: string;
  tab_name?: string;
};

export type MARKETPLACE_JOB_MESSAGE_TYPE = {
  job_id_v2?: string;
  job_ids_v2?: string[];
  jobs?: MARKETPLACE_JOB_MESSAGE_JOB_TYPE[];
  status?: string;
  response?: string;
  changes?: string[];
  source?: string;
  option?: string;
  previous_state?: string;
  next_state?: string;
  tab_name?: string;
};

export type MARKETPLACE_JOB_CONTACT_TYPE = {
  job_id_v2: string;
};

export type REVIEWS_MESSAGE_TYPE = {
  review_id?: string;
  report_type?: string;
  homeowner_id?: string;
};

export type SEARCH_CATEGORIES_MESSAGE_TYPE = {
  productId: string;
  subCategoryId?: string;
};

export type SUBCONTRACTING_MESSAGE_TYPE = {
  available: boolean;
  dailyRate?: number;
};

export type PHOTOS_MESSAGE_TYPE = {
  albumId?: string;
  albumItemId?: string;
  numberOfAlbums?: number;
  numberOfPhotos?: number;
  photoIds?: string[];
  flaggedPhotoIds?: string[];
  reason?: PhotosValidationErrorReason;
};

type TRACK_NAVIGATION_TYPE = {
  currentPath: string;
  previousPath?: string;
  queryParams?: Record<string, string>;
};

type TRACK_PUSH_NOTIFICATION_TYPE = {
  message_id?: string;
  message_url?: string;
};

export type PLI_MESSAGE_TYPE = {
  pliStatus?: string;
};

export type PAYMENTS_MESSAGE_TYPE = {
  outstandingBalance?: number;
  amount?: number;
  reason?: string;
};

export type PLI_UPLOAD_TYPE = {
  pliStatus?: string;
  failedReason?: string;
};

export type QUOTE_MESSAGE_TYPE = {
  quote_id?: string;
  item_type?: string;
  vat?: number;
  from_favourites?: boolean;
  job_value?: number;
  drop_off_screen?: string;
  source?: QUOTE_SOURCE_TYPE;
};

export type ONBOARDING_MESSAGE_TYPE = {
  has_no_required_accreditations: boolean;
};

export type CHANNEL_PERFORMANCE_MESSAGE_TYPE = {
  time_period: string;
};

export type EVENT_LOG_TYPE = {
  eventName: EventTypeKey;
};

export type ACCREDITATION_EVENT_TYPE = {
  status?: AccreditationStatus;
  canExpire?: boolean;
  requiresApproval?: boolean;
};

export type CAMPAIGNS_EVENT_TYPE = {
  pausedUntil?: Date;
  isEditMode?: boolean;
  categoryName?: string;
  selectedTypesOfWorkCount?: number;
  selectedDistrictsCount?: number;
  budget?: number;
  campaignId?: string;
  oldBudget?: number;
  action?: string;
};

export type CAMPAIGN_CATEGORIES_PAGE_REQUEST_TYPE = {
  categoryId: number;
  categoryName: string;
  planCount: number;
  url: string;
  imageUrl: string;
};

export type CAMPAIGN_CREATION_PLANS_AVAILABLE_TYPE = {
  categoryId: number;
  planCount: number;
  hasPplPlan: boolean;
  hasFixedPlan: boolean;
};

export type CAMPAIGN_CREATION_SELECT_PLAN_TYPE = {
  planType: string;
};

export type CAMPAIGN_CREATION_SUCCESS_TYPE = {
  campaignId: string;
  campaignType: string;
  quoteId?: string;
};

export type CAMPAIGN_RECOMMENDED_LOCATION_CLICKED_TYPE = {
  campaignId: string;
  companyId: number;
  postcode: string;
  demandScore: number;
};

export type CAMPAIGN_UPGRADES_TYPE = {
  campaignId: string;
  upgradeId: string;
};

export type FEEDBACK_MODAL_TYPE = {
  eventName: EventTypeKey;
  rating: 1 | 2 | 3 | 4 | 5;
  feedback: string;
};

export type CHANNEL_OPEN_TYPE = {
  channelId: string;
  correlationId?: string;
  jobId: string;
};

export type GENERIC_ERROR_TYPE = {
  error: string;
};

export type OFFERS_AND_DISCOUNTS_ITEM_TYPE = {
  offerId: string;
  url: string;
};

export type MARKETING_PREF_TOGGLE_TYPE = {
  preferenceToToggle: string;
  preferenceGroup: string;
};

export type APPOINTMENT_UPDATED_TYPE = {
  appointmentId: string;
};

export type APPOINTMENT_CANCELLED_TYPE = {
  appointmentId: string;
  reason?: string;
};

export type CHAT_MESSAGE_TYPE = {
  job_id_v2?: string;
};

export type DIRECT_DEBIT_TYPE = {
  zuoraId?: string;
};

export type JOB_PAYMENTS_TYPE = {
  activity_type?: string;
  job_id_v2?: string;
  payment_request_id?: string;
  payment_status?: string;
  tax_type?: string;
  screen_name?: string;
  reason?: string;
  reason_detail?: string;
  channel_id?: string;
};

export type JOB_DETAILS_V2_TYPE = {
  isJobDetailsV2: boolean;
  mapAppName: string;
};

type BOOKABLE_SERVICE_TYPE = {
  serviceId: string;
  versionId: string;
  categoryId?: number;
};

type SIGN_IN_FAILURE_NO_ACCOUNTS_TYPE = {
  catIdUid: string;
  accountsCount: number;
  accountsPendingCount?: number;
  accountsActiveCount: number;
};

type SHARE_REFERRAL_LINK_TYPE = {
  referralCode: string;
  campaignId: number;
};

type LOAN_BANNER_CAMPAIGN_ID = {
  loanCampaignId: string;
};

type JOBS_LE_SURVEY_SUBMIT = {
  job_id_v2: string;
  feedback: string;
  rating: number;
  isJobDetailsV2: boolean;
};

type ONBOARDING_UNCHECKED_ACTIONS_TYPE = {
  uncheckedActions: OnboardingAnalyticsKeys[];
};

type ONBOARDING_ACTION_TYPE = {
  checkboxAction: OnboardingAnalyticsKeys;
};

type CMA_TERMS_MODAL_TYPE = {
  hasOnSiteStaff: boolean;
  hasOnSiteSubcontractors: boolean;
};

type MY_TEAM_INVITE_TYPE = {
  inviteId: string;
};

type MY_DETAILS_REQUEST_TYPE = {
  correlationId: string;
  companyId: number;
};

// Active Home - My Jobs
type ACTIVE_HOME_MY_JOBS_TYPE = {
  jobTab: string;
  jobId?: string;
};

// Active Home - Insights
type ACTIVE_HOME_INSIGHTS_TYPE = {
  timePeriod: TimePeriodsEnum;
};

// Report date range
type REPORT_DATE_RANGE_TYPE = {
  from_date: string;
  to_date: string;
  company_id: number;
};

type FEATURED_PROJECTS_TYPE = {
  featuredProjectId: string;
};

export type EVENT_MESSAGE_TYPE =
  | EVENT_LOG_TYPE
  | MESSAGE_TYPE
  | ALSP_MESSAGE_TYPE
  | JOB_MESSAGE_TYPE
  | MARKETPLACE_JOB_MESSAGE_TYPE
  | JOB_DETAILS_V2_TYPE
  | DIRECT_DEBIT_TYPE
  | MARKETPLACE_JOB_CONTACT_TYPE
  | REVIEWS_MESSAGE_TYPE
  | PHOTOS_MESSAGE_TYPE
  | TRACK_NAVIGATION_TYPE
  | TRACK_PUSH_NOTIFICATION_TYPE
  | PLI_MESSAGE_TYPE
  | SEARCH_CATEGORIES_MESSAGE_TYPE
  | PAYMENTS_MESSAGE_TYPE
  | PLI_UPLOAD_TYPE
  | QUOTE_MESSAGE_TYPE
  | ONBOARDING_MESSAGE_TYPE
  | CHANNEL_PERFORMANCE_MESSAGE_TYPE
  | ACCREDITATION_EVENT_TYPE
  | CAMPAIGNS_EVENT_TYPE
  | CAMPAIGN_CATEGORIES_PAGE_REQUEST_TYPE
  | CAMPAIGN_CREATION_PLANS_AVAILABLE_TYPE
  | CAMPAIGN_CREATION_SUCCESS_TYPE
  | CAMPAIGN_CREATION_SELECT_PLAN_TYPE
  | CAMPAIGN_RECOMMENDED_LOCATION_CLICKED_TYPE
  | CAMPAIGN_UPGRADES_TYPE
  | FEEDBACK_MODAL_TYPE
  | CHANNEL_OPEN_TYPE
  | SUBCONTRACTING_MESSAGE_TYPE
  | GENERIC_ERROR_TYPE
  | OFFERS_AND_DISCOUNTS_ITEM_TYPE
  | MARKETING_PREF_TOGGLE_TYPE
  | APPOINTMENT_UPDATED_TYPE
  | APPOINTMENT_CANCELLED_TYPE
  | CHAT_MESSAGE_TYPE
  | JOB_PAYMENTS_TYPE
  | SIGN_IN_FAILURE_NO_ACCOUNTS_TYPE
  | SHARE_REFERRAL_LINK_TYPE
  | LOAN_BANNER_CAMPAIGN_ID
  | JOBS_LE_SURVEY_SUBMIT
  | ONBOARDING_UNCHECKED_ACTIONS_TYPE
  | ONBOARDING_ACTION_TYPE
  | CMA_TERMS_MODAL_TYPE
  | MY_TEAM_INVITE_TYPE
  | ACTIVE_HOME_MY_JOBS_TYPE
  | ACTIVE_HOME_INSIGHTS_TYPE
  | MY_DETAILS_REQUEST_TYPE
  | REPORT_DATE_RANGE_TYPE
  | BOOKABLE_SERVICE_TYPE
  | FEATURED_PROJECTS_TYPE;

export type PhotoActionAnalyticsKeys =
  | 'photos_create_album'
  | 'photos_add_4_images'
  | 'photos_add_tags';

export type ReviewsActionAnalyticsKeys =
  | 'reviews_request'
  | 'reviews_request_2_per_month'
  | 'reviews_reply_to_all';

export type MyProfileActionAnalyticsKeys =
  | 'profile_upload_company_logo'
  | 'profile_add_accreditations'
  | 'profile_add_pli'
  | 'profile_add_business_offering'
  | 'profile_add_description';

export type OnboardingAnalyticsKeys =
  | PhotoActionAnalyticsKeys
  | ReviewsActionAnalyticsKeys
  | MyProfileActionAnalyticsKeys;
