export const REFERRAL_TEXT = {
  TITLE: 'Get £300. Give £300. Everybody wins.',
  DESCRIPTION: {
    A: "Refer a fellow tradesperson to Checkatrade this month and you could both pocket a tidy cash bonus. It's our way of saying thanks for bringing good work our way.",
  },
  HOW_DOES_IT_WORK: {
    TITLE: 'How Does It Work?',
    ITEMS: [
      '✅  *Share your unique referral link*',
      '✅  *They sign up* for either a Fixed Plan or a Pay-as-you-go Campaign.',
      '✅  *You both get paid* once they pass our checks, and you set up your payments account.',
    ],
    FOOTER: '',
  },
  WHAT_DO_THEY_NEED_TO_DO: {
    TITLE: 'To qualify for the reward, your mate needs to:',
    ITEMS: [
      '•  Use your referral link or quote your code on the phone by *30 June 2025*',
      '•  Pass our checks and pay their first month by *14 July 2025*',
      '•  You both need to have a payments account by *14 July 2025* so we can send you the reward',
    ],
    FOOTER:
      "Our offers are subject to change so *don't delay* or you'll miss out! Share your link today.",
  },
  WHAT_YOU_EARN: {
    TITLE: "What you'll earn:",
    ITEMS: [
      '💰  *£300 each* when they join on a Fixed Plan.',
      '💰  *£90 each* if they go for Pay-as-you-go.',
    ],
    FOOTER:
      "Our offers are subject to change so *don't delay* or you'll miss out! Share your link today.",
  },
  SHARE_MODAL: {
    TITLE: 'Share referral link',
  },
  BOTTOM_LINKS_TEXT: {
    TERMS: 'Terms and Conditions',
    REFER: 'Your referrals',
  },
};

export const CAMPAIGN_NOT_FOUND = {
  TITLE: 'Referral campaign not found',
  DESCRIPTION: 'Please try again later',
};

export const REFERRAL_TERMS_LINK =
  'https://join.checkatrade.com/referral-june2025';
