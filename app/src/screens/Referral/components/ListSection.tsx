import React from 'react';
import { Typography } from '@cat-home-experts/react-native-components';
import { type StyleProp, View, type ViewStyle, Text } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';

interface Props {
  title: string;
  listItems: string[];
  footer?: string;
  style?: StyleProp<ViewStyle>;
}

const parseTextWithBold = (text: string) => {
  // Split text by asterisks while keeping the delimiters
  const parts = text.split(/(\*[^*]+\*)/);

  return parts.map((part, index) => {
    if (part.startsWith('*') && part.endsWith('*')) {
      // Remove asterisks and render as bold
      const boldText = part.slice(1, -1);
      return (
        // eslint-disable-next-line react/no-array-index-key
        <Typography key={`bold-${part}-${index}`} useVariant="bodySemiBold">
          {boldText}
        </Typography>
      );
    }

    // Render regular text
    // eslint-disable-next-line react/no-array-index-key
    return <Text key={`regular-${part}-${index}`}>{part}</Text>;
  });
};

export const ListSection: React.FC<Props> = ({
  title,
  listItems,
  footer,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <Typography useVariant="subHeadingSemiBold">{title}</Typography>
      <View style={styles.bulletPoints}>
        {listItems.map((item, itemIndex) => (
          // eslint-disable-next-line react/no-array-index-key
          <Typography useVariant="bodyRegular" key={`${item}-${itemIndex}`}>
            {parseTextWithBold(item)}
          </Typography>
        ))}
      </View>
      {footer && (
        <Typography useVariant="bodyRegular">
          {parseTextWithBold(footer)}
        </Typography>
      )}
    </View>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    marginBottom: spacing(2),
  },
  bulletPoints: {
    marginVertical: spacing(2),
    marginLeft: spacing(1),
    gap: spacing(1.5),
  },
}));
