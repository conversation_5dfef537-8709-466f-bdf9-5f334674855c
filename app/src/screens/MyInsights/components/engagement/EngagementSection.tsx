import React, { useCallback } from 'react';
import { View } from 'react-native';
import { isNumber } from 'lodash';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Typography } from '@cat-home-experts/react-native-components';
import { useNavigation } from '@react-navigation/native';
import { JOBS_SCREEN } from 'src/constants';
import { TradeInsightsType } from 'src/data/schemas/api/trade-app-bff/trade-insights/tradeInsightsResponse';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { InsightsHeader } from '../../InsightsHeader';
import { EnagementProgressResponse } from './EnagementProgressResponse';
import { EnagementProgressAcceptance } from './EnagementProgressAcceptance';
import { InsightsFooterLink } from '../../InsightsFooterLink';

const INSIGHTS_FOOTER_LINK = 'Respond to unanswered leads';
const DESCRIPTION =
  "How you're responding to customer job requests on the Checkatrade platform";
const SUB_DESCRIPTION = 'Data in this section excludes calls';
const TITLE = 'Engagement';

interface EngagementSectionProps {
  tradeInsights: TradeInsightsType;
}

export const EngagementSection: React.FC<EngagementSectionProps> = ({
  tradeInsights,
}: EngagementSectionProps) => {
  const navigation = useNavigation();

  const onNavigatePress = useCallback(() => {
    navigation.navigate(JOBS_SCREEN, {
      screen: 'New',
    });
  }, [navigation]);

  const handleFooterLinkPress = useCallback(() => {
    logEvent(EVENT_TYPE.MY_INSIGHTS_ENGAGEMENT_FOOTER_CLICKED);
    onNavigatePress();
  }, [onNavigatePress]);

  const handleTitlePress = useCallback(() => {
    logEvent(EVENT_TYPE.MY_INSIGHTS_ENGAGEMENT_TITLE_CLICKED);
    onNavigatePress();
  }, [onNavigatePress]);

  return (
    <View style={styles.engagementContainer} testID="ENGAGEMENT_SECTION">
      <View style={styles.containerTitleDescription}>
        <InsightsHeader title={TITLE} onPress={handleTitlePress} />
        <Typography useVariant="bodyRegular">{DESCRIPTION}</Typography>
        <Typography useVariant="labelSemiBold">{SUB_DESCRIPTION}</Typography>
      </View>

      <View style={styles.progressContainer}>
        {isNumber(
          tradeInsights.engagement?.engagementThirtyDays?.respondedPercent,
        ) &&
        isNumber(
          tradeInsights.engagement?.engagementThirtyDays?.unansweredPercent,
        ) ? (
          <EnagementProgressResponse
            respondedPercent={
              tradeInsights.engagement?.engagementThirtyDays?.respondedPercent
            }
            unansweredPercent={
              tradeInsights.engagement?.engagementThirtyDays?.unansweredPercent
            }
          />
        ) : null}

        {isNumber(
          tradeInsights.engagement?.engagementThirtyDays?.acceptedPercent,
        ) &&
        isNumber(
          tradeInsights.engagement?.engagementThirtyDays?.declinedPercent,
        ) ? (
          <EnagementProgressAcceptance
            acceptedPercent={
              tradeInsights.engagement?.engagementThirtyDays?.acceptedPercent
            }
            declinedPercent={
              tradeInsights.engagement?.engagementThirtyDays?.declinedPercent
            }
          />
        ) : null}
      </View>
      <InsightsFooterLink
        title={INSIGHTS_FOOTER_LINK}
        onPress={handleFooterLinkPress}
      />
    </View>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  containerTitleDescription: {
    gap: spacing(1),
  },
  engagementContainer: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    padding: spacing(2),
  },
  progressContainer: {
    alignSelf: 'flex-start',
  },
}));
