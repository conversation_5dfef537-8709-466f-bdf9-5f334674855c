import React, { ReactElement, useEffect } from 'react';
import { ScrollView } from 'react-native';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Loader } from '@cat-home-experts/react-native-components';
import { useUserContext } from 'src/hooks/useUser';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { useTradeInsights } from './hooks/useTradeInsights';
import { EngagementSection } from './components/engagement/EngagementSection';
import { ErrorScreen } from '../ErrorScreen';

export function MyInsights(): ReactElement | null {
  const { companyId } = useUserContext();
  const { tradeInsights, loading, error } = useTradeInsights(companyId);

  useEffect(() => {
    logEvent(EVENT_TYPE.MY_INSIGHTS_VIEWED);
  }, []);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return <ErrorScreen testID="ERROR_SCREEN" />;
  }

  return (
    <ScrollView style={styles.container} testID="MY_INSIGHTS">
      {tradeInsights?.engagement?.engagementThirtyDays ? (
        <EngagementSection tradeInsights={tradeInsights} />
      ) : null}
    </ScrollView>
  );
}

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    paddingVertical: spacing(2),
    gap: spacing(1),
    alignSelf: 'center',
  },
}));
