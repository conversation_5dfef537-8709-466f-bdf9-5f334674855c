import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { useUserContext } from 'src/hooks/useUser';
import { MyInsights } from './MyInsights';
import { useTradeInsights } from './hooks/useTradeInsights';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

jest.mock('src/components/CircleProgress/CircleProgress', () => ({
  CircularProgress: jest.fn(() => {
    return null;
  }),
}));

jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

jest.mock('src/hooks/useUser');
jest.mock('./hooks/useTradeInsights');

describe('MyInsights tests', () => {
  afterEach(() => {
    cleanup();
  });
  it('renders loading indicator when loading', () => {
    (useUserContext as jest.Mock).mockReturnValue({ companyId: '123' });
    (useTradeInsights as jest.Mock).mockReturnValue({
      loading: true,
      error: undefined,
    });

    const { getByTestId } = render(<MyInsights />);

    expect(getByTestId('loader')).toBeDefined();
  });

  it('renders error screen when error', () => {
    (useUserContext as jest.Mock).mockReturnValue({ companyId: '123' });
    (useTradeInsights as jest.Mock).mockReturnValue({
      loading: false,
      error: new Error('Test error'),
    });

    const { getByTestId } = render(<MyInsights />);

    expect(getByTestId('ERROR_SCREEN')).toBeDefined();
  });

  it('renders MY_INSIGHTS text when not loading and no error', () => {
    (useUserContext as jest.Mock).mockReturnValue({ companyId: '123' });
    (useTradeInsights as jest.Mock).mockReturnValue({
      loading: false,
      error: undefined,
    });

    const { getByTestId } = render(<MyInsights />);

    expect(getByTestId('MY_INSIGHTS')).toBeDefined();
  });

  it('renders insights without engagement section when engagement not set', () => {
    (useUserContext as jest.Mock).mockReturnValue({ companyId: '123' });
    (useTradeInsights as jest.Mock).mockReturnValue({
      loading: false,
      error: undefined,
      tradeInsights: {
        engagement: null,
      },
    });

    const { getByTestId, queryByTestId } = render(<MyInsights />);

    expect(getByTestId('MY_INSIGHTS')).toBeDefined();

    expect(queryByTestId('ENGAGEMENT_SECTION')).toBeNull();
  });

  it('renders engagement when engagement exists ', () => {
    (useUserContext as jest.Mock).mockReturnValue({ companyId: '123' });
    (useTradeInsights as jest.Mock).mockReturnValue({
      loading: false,
      error: undefined,
      tradeInsights: {
        engagement: {
          engagementThirtyDays: {
            respondedPercent: 50,
            unansweredPercent: 50,
            acceptedPercent: 70,
            declinedPercent: 30,
          },
        },
      },
    });

    const { getByTestId } = render(<MyInsights />);

    expect(getByTestId('MY_INSIGHTS')).toBeDefined();

    expect(getByTestId('ENGAGEMENT_SECTION')).toBeTruthy();
  });
});
