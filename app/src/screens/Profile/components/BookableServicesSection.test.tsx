import React from 'react';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import { useNavigation } from '@react-navigation/native';
import { BOOKABLE_SERVICES_SCREEN } from 'src/constants';
import { useBookableServices } from 'src/screens/BookableServices/hooks/useBookableServices';
import { useClickedBookableService } from 'src/screens/BookableServices/servicesState';
import { createQueryClientWrapper } from 'src/utilities/tanstack-query/tanstack-test-utils';
import { BookableServicesSection } from './BookableServicesSection';
import { BOOKABLE_SERVICES_DETAILS } from '../constants';

jest.mock('src/screens/BookableServices/servicesState', () => ({
  useClickedBookableService: jest.fn(() => [false, jest.fn()]),
}));
jest.mock('src/screens/BookableServices/hooks/useBookableServices', () => ({
  useBookableServices: jest.fn(() => ({
    services: [],
    count: 0,
    isLoading: false,
  })),
}));

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');

  return {
    ...actualNav,
    useNavigation: () => {
      return {
        navigate: mockNavigate,
      };
    },
  };
});

const wrapper = createQueryClientWrapper();

describe('Screens | Profile | Components | BookableServicesSection', () => {
  beforeEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders Bookable services section', () => {
    const { getByTestId, getByText } = render(<BookableServicesSection />, {
      wrapper,
    });

    expect(getByText(BOOKABLE_SERVICES_DETAILS.HEADER)).toBeDefined();
    const button = getByTestId(BookableServicesSection.testIds.ADD_BUTTON);
    expect(button).toBeVisible();
  });

  it('calls navigation when add is pressed', () => {
    const navigation = useNavigation();

    const { getByTestId } = render(<BookableServicesSection />, {
      wrapper,
    });

    const button = getByTestId(BookableServicesSection.testIds.ADD_BUTTON);
    fireEvent.press(button);

    expect(navigation.navigate).toHaveBeenCalledWith(BOOKABLE_SERVICES_SCREEN);
  });

  it('shows the "New" badge when hasClickedBookableService is false', () => {
    (useClickedBookableService as jest.Mock).mockReturnValue([false]);

    const { getByText } = render(<BookableServicesSection />, {
      wrapper,
    });

    expect(getByText('New')).toBeDefined();
  });

  it('does not show the "New" badge when hasClickedBookableService is true', () => {
    (useClickedBookableService as jest.Mock).mockReturnValue([true]);

    const { queryByText } = render(<BookableServicesSection />, {
      wrapper,
    });

    expect(queryByText('New')).toBeNull();
  });

  it('calls navigation to BOOKABLE_SERVICES_SCREEN when add is pressed', () => {
    const { getByTestId } = render(<BookableServicesSection />, {
      wrapper,
    });

    const button = getByTestId(BookableServicesSection.testIds.ADD_BUTTON);
    fireEvent.press(button);

    expect(mockNavigate).toHaveBeenCalledWith(BOOKABLE_SERVICES_SCREEN);
  });

  it('shows the "Add services" text when there are no bookable services', () => {
    (useBookableServices as jest.Mock).mockReturnValue({
      services: [],
      count: 0,
      isLoading: false,
    });

    const { getByText, queryByText } = render(<BookableServicesSection />, {
      wrapper,
    });

    expect(getByText(BOOKABLE_SERVICES_DETAILS.ADD)).toBeDefined();
    expect(queryByText(BOOKABLE_SERVICES_DETAILS.CHANGE)).toBeNull();
    expect(
      getByText(BOOKABLE_SERVICES_DETAILS.ADD_SERVICES_INTRO),
    ).toBeDefined();
  });

  it('shows the "Change services" text and service names when there are bookable services', () => {
    (useBookableServices as jest.Mock).mockReturnValue({
      services: [
        {
          id: '1',
          name: 'Service 1',
        },
        {
          id: '2',
          name: 'Service 2',
        },
      ],
      count: 2,
      isLoading: false,
    });

    const { getByText, queryByText } = render(<BookableServicesSection />, {
      wrapper,
    });

    expect(getByText(BOOKABLE_SERVICES_DETAILS.CHANGE)).toBeDefined();
    expect(queryByText(BOOKABLE_SERVICES_DETAILS.ADD)).toBeNull();
    expect(getByText('Service 1, Service 2')).toBeDefined();
  });
});
