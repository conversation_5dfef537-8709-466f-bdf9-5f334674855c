import React, { ReactElement } from 'react';
import { ProfileSection } from 'src/screens/Profile/components/ProfileSection';
import { createTestIds } from '@cat-home-experts/react-native-utilities';
import { BOOKABLE_SERVICES_DETAILS } from 'src/screens/Profile/constants';
import { Typography } from '@cat-home-experts/react-native-components';
import { useNavigation } from '@react-navigation/native';
import { BOOKABLE_SERVICES_SCREEN } from 'src/constants';
import { useClickedBookableService } from 'src/screens/BookableServices/servicesState';
import { useBookableServices } from 'src/screens/BookableServices/hooks/useBookableServices';

const TEST_IDS = createTestIds('profile_bookable_services', {
  PREVIEW: 'preview-text',
  ADD_BUTTON: 'add-button',
});

export const BookableServicesSection = (): ReactElement => {
  const { services } = useBookableServices();
  const navigation = useNavigation();
  const [hasClickedBookableService] = useClickedBookableService();

  const handleButtonPress = () => {
    navigation.navigate(BOOKABLE_SERVICES_SCREEN);
  };

  return (
    <ProfileSection
      title={BOOKABLE_SERVICES_DETAILS.HEADER}
      onPress={handleButtonPress}
      buttonTestID={TEST_IDS.ADD_BUTTON}
      buttonText={
        services.length > 0
          ? BOOKABLE_SERVICES_DETAILS.CHANGE
          : BOOKABLE_SERVICES_DETAILS.ADD
      }
      showNewFeatureBadge={!hasClickedBookableService}
    >
      <Typography
        useVariant="labelRegular"
        testID={TEST_IDS.PREVIEW}
        numberOfLines={2}
      >
        {services.length > 0
          ? services.map((service) => service.name).join(', ')
          : BOOKABLE_SERVICES_DETAILS.ADD_SERVICES_INTRO}
      </Typography>
    </ProfileSection>
  );
};

BookableServicesSection.testIds = TEST_IDS;
