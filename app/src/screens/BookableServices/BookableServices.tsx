import React, { ReactElement, useEffect } from 'react';
import { View, Pressable, Platform, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { QuestionCircle } from '@cat-home-experts/mortar-iconography-native';
import {
  createMortarStyles,
  palette as staticPalette,
  spacing as staticSpacing,
} from '@cat-home-experts/react-native-utilities';
import { Icon } from '@cat-home-experts/react-native-components';
import {
  BOOKABLE_SERVICES_ACCESSABILITY_LABEL,
  BOOKABLE_SERVICES_STRINGS,
} from 'src/screens/BookableServices/constants';
import { createTestIds } from 'src/utilities/testIds';
import { BookableServiceDetailType } from 'src/data/schemas/api/capi/service-catalog/service';
import {
  BOOKABLE_SERVICES_FAQ_GUIDE_SCREEN,
  BOOKABLE_SERVICE_DETAIL_SCREEN,
  CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN,
} from 'src/constants';
import { logEvent } from 'src/services/analytics';
import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { useBookableServices } from 'src/screens/BookableServices/hooks/useBookableServices';
import { useUserContext } from 'src/hooks/useUser';
import { useFeatureFlag } from 'src/hooks/useFeatureFlag';
import { openURL } from 'src/utilities/linking';
import { AllScreensParamList } from 'src/navigation/routes';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { BookableServicesEarlyAccess } from './components/BookableServicesEarlyAccess';
import { BookableServicesList } from './components/BookableServicesList';
import { BookableServicesHeader } from './components/BookableServicesHeader';

const TEST_IDS = createTestIds('bookable-services', {
  TITLE: 'title',
  BUTTON: 'primary-button',
  PLUS_BUTTON: 'plus-button',
  QUESTION_BUTTON: 'question-button',
});

export function BookableServices(): ReactElement {
  const navigation = useNavigation<StackNavigationProp<AllScreensParamList>>();
  const { services, isLoading, isFetched, ...rest } = useBookableServices();
  const { companyId } = useUserContext();
  const isDesktop = useDesktopMediaQuery();

  const isBookableServiceCreationEnabled = useFeatureFlag(
    'enable_bookable_service_creation',
    {
      isFeatureComplete: true,
      enabledInDev: true,
    },
  );

  if (!companyId) {
    throw new Error('Missing company data');
  }

  const handleAddService = () => {
    logEvent(
      `${EVENT_TYPE.BOOKABLE_SERVICES_MANAGE_ADD_SERVICE}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
    );

    if (isBookableServiceCreationEnabled) {
      navigation.navigate(CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN);
    } else {
      openURL(`${BOOKABLE_SERVICES_STRINGS.CTA_BUTTON_URL}=${companyId}`);
    }
  };

  const handleEarlyAccessPress = () => {
    logEvent(
      `${EVENT_TYPE.BOOKABLE_SERVICES_EARLY_ACCESS}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
    );

    handleAddService();
  };

  const onServicePress = (service: BookableServiceDetailType) => {
    logEvent(
      `${EVENT_TYPE.BOOKABLE_SERVICES_MANAGE_DETAIL}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
    );
    navigation.navigate(BOOKABLE_SERVICE_DETAIL_SCREEN, {
      id: service.serviceId,
      versionId: service.id,
    });
  };

  useEffect(() => {
    const handleFaqNavigation = () => {
      logEvent(
        `${EVENT_TYPE.BOOKABLE_SERVICES_FAQ_HELPER}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
      );
      navigation.navigate(BOOKABLE_SERVICES_FAQ_GUIDE_SCREEN);
    };

    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity
          onPress={handleFaqNavigation}
          testID={TEST_IDS.QUESTION_BUTTON}
          style={styles.questionButton}
        >
          <QuestionCircle
            size={staticSpacing(2.4)}
            color={staticPalette.mortarV3.tokenNeutral900}
          />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  if (!services?.length && !isLoading && isFetched) {
    return <BookableServicesEarlyAccess onPress={handleEarlyAccessPress} />;
  }

  return (
    <View style={styles.root} testID={TEST_IDS.ROOT}>
      <BookableServicesList
        services={services}
        isLoading={isLoading}
        isFetched={isFetched}
        {...rest}
        onServicePress={onServicePress}
        renderHeader={
          isDesktop
            ? () => <BookableServicesHeader onPress={handleAddService} />
            : undefined
        }
      />

      {!isDesktop && (
        <Pressable
          style={styles.newServiceButton}
          onPress={handleAddService}
          accessibilityRole="button"
          testID={TEST_IDS.PLUS_BUTTON}
          accessibilityLabel={
            BOOKABLE_SERVICES_ACCESSABILITY_LABEL.NEW_SERVICE_BUTTON
          }
        >
          <View style={styles.iconContainer}>
            <Icon
              name="plus"
              color={staticPalette.mortar.tokenColorPrimaryWhite}
            />
          </View>
        </Pressable>
      )}
    </View>
  );
}

BookableServices.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => {
  return {
    root: {
      flex: 1,
      width: '100%',
    },
    questionButton: {
      ...Platform.select({
        web: {
          marginRight: spacing(2),
        },
      }),
    },

    newServiceButton: {
      backgroundColor: palette.mortar.tokenColorPrimaryBlue,
      width: 60,
      height: 60,
      borderRadius: 50,
      position: 'absolute',
      right: 24,
      zIndex: 1,
      bottom: 28,
      ...Platform.select({
        web: {
          bottom: 70,
        },
      }),
    },
    iconContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
});
