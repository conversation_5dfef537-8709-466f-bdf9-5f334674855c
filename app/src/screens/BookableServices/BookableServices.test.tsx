import React, { PropsWithChildren } from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { BookableServices } from 'src/screens/BookableServices/BookableServices';
import { openURL } from 'src/utilities/linking';
import { useBookableServices } from 'src/screens/BookableServices/hooks/useBookableServices';
import {
  Quantifier,
  DayOfTheWeek,
  BookingServiceStatusType,
} from 'src/data/schemas/api/capi/service-catalog/service';
import { useFeatureFlag } from 'src/hooks/useFeatureFlag';
import { CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN } from 'src/constants';
import { logEvent } from 'src/services/analytics';
import { BOOKABLE_SERVICES_ACCESSABILITY_LABEL } from './constants';

jest.mock('src/utilities/linking', () => ({
  openURL: jest.fn(),
}));

const mockNavigate = jest.fn();

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');

  return {
    ...actualNav,

    useNavigation: () => ({
      navigate: mockNavigate,
      addListener: jest.fn(),
      setOptions: jest.fn(),
    }),
  };
});

jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

jest.mock('src/screens/BookableServices/hooks/useBookableServices', () => ({
  useBookableServices: jest.fn(),
}));

jest.mock('src/hooks/useUser', () => ({
  useUserContext: jest.fn(() => ({ companyId: '12345' })),
}));

jest.mock('src/hooks/useFeatureFlag', () => ({
  useFeatureFlag: jest.fn(),
}));

const wrapper = ({ children }: PropsWithChildren) => (
  <NavigationContainer>{children}</NavigationContainer>
);

const mockServices = [
  {
    id: '123',
    name: 'Test Service 1',
    description: 'Test description 1',
    categoryId: 1,
    categoryName: 'Plumbing',
    serviceId: '123',
    lineItems: [
      {
        notes: 'Test notes',
        priceInPence: 10000,
        quantifier: Quantifier.PerJob,
      },
    ],
    days: [
      {
        dayOfTheWeek: DayOfTheWeek.Monday,
        startTime: '08:00',
        endTime: '17:00',
      },
    ],
    homeownerNotes: 'Test notes',
    whatIsIncluded: ['Item 1', 'Item 2'],
    whatIsNotIncluded: ['Not included 1', 'Not included 2'],
    status: BookingServiceStatusType.PublishedActive,
  },
];

describe('BookableServices', () => {
  const mockLogEvent = jest.fn();

  beforeEach(() => {
    (useBookableServices as jest.Mock).mockReturnValue({
      services: mockServices,
      isLoading: false,
    });

    jest.mocked(useFeatureFlag).mockReturnValue(false);
    (logEvent as jest.Mock).mockImplementation(mockLogEvent);
  });

  afterEach(cleanup);

  it('renders the plus button for adding new services', () => {
    const { getByTestId } = render(<BookableServices />, { wrapper });

    const plusButton = getByTestId(BookableServices.testIds.PLUS_BUTTON);
    expect(plusButton).toBeOnTheScreen();

    expect(plusButton.props.accessibilityLabel).toBe(
      BOOKABLE_SERVICES_ACCESSABILITY_LABEL.NEW_SERVICE_BUTTON,
    );
  });

  it('opens URL when CTA button is pressed with feature flag disabled', () => {
    const { getByTestId } = render(<BookableServices />, { wrapper });

    const ctaButton = getByTestId(BookableServices.testIds.PLUS_BUTTON);

    fireEvent.press(ctaButton);

    expect(openURL).toHaveBeenCalled();
  });

  it('calls navigate when CTA button is pressed with feature flag enabled', () => {
    jest.mocked(useFeatureFlag).mockReturnValue(true);

    const { getByTestId } = render(<BookableServices />, { wrapper });

    const ctaButton = getByTestId(BookableServices.testIds.PLUS_BUTTON);

    fireEvent.press(ctaButton);

    expect(mockNavigate).toHaveBeenCalledWith(
      CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN,
    );
  });

  it('renders service items correctly', () => {
    const { queryAllByTestId } = render(<BookableServices />, { wrapper });

    const serviceTestIds = Object.keys(queryAllByTestId(/^booking-service-/));

    expect(serviceTestIds.length).toBeGreaterThan(0);
  });

  it('renders service with status pill', () => {
    const serviceWithStatus = {
      ...mockServices[0],
      status: BookingServiceStatusType.PublishedActive,
    };

    (useBookableServices as jest.Mock).mockReturnValue({
      services: [serviceWithStatus],
      isLoading: false,
    });

    const { getByText } = render(<BookableServices />, { wrapper });

    expect(getByText('Published')).toBeOnTheScreen();
  });

  it('renders service with different status pill', () => {
    const serviceWithDraftStatus = {
      ...mockServices[0],
      status: BookingServiceStatusType.Draft,
    };

    (useBookableServices as jest.Mock).mockReturnValue({
      services: [serviceWithDraftStatus],
      isLoading: false,
    });

    const { getByText } = render(<BookableServices />, { wrapper });

    expect(getByText('Draft')).toBeOnTheScreen();
  });
});
