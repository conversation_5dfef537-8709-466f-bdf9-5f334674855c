import { Quantifier } from 'src/data/schemas/api/capi/service-catalog/service';

export const BOOKABLE_SERVICES_SETUP_INFO = {
  LIST: [
    {
      title: 'Define your services precisely',
      body: 'List each service you offer, from standard jobs to specialised projects.',
    },
    {
      title: 'Provide detailed descriptions and set clear expectations',
      body: "Explain what each service entails and clearly specify what's included and excluded.",
    },
    {
      title: 'Set your prices',
      body: 'Clearly display your pricing structure for each service.',
    },
  ],
};

export const BOOKABLE_SERVICES_STRINGS = {
  TITLE: 'Services (Early access)',
  DESCRIPTION:
    'Want to make it easier for customers to understand your services? With Services you can create a clear, detailed catalogue of what you offer, right within the Trade app. This means you can:',
  CTA_BUTTON_URL:
    'https://forms.office.com/Pages/ResponsePage.aspx?id=O7ac5TGHtEGAMkA-be4iR_g1YN2-jD9MnG8MvgBc8AVUMjYxVkk2M1RKVlFGQjIwSFVBNUVDMU0zSy4u&rfc0a6b5a7837475c908f395ecb6149f6',
  BUTTON_CTA: 'Add a Bookable service',
  REGISTER_INFO_TITLE: 'Early access: Set up your services before they go live',
  REGISTER_INFO_TEXT:
    "Your services won't be live on your profile just yet, but getting them ready now means you'll be one step ahead when the feature goes live.",
};

export const MANAGE_BOOKABLE_SERVICES_STRINGS = {
  TITLE: 'Your services',
  DESCRIPTION_INTRO:
    "If you've submitted a Bookable service, we're reviewing it and will notify you once it's approved. This process may take a few days. \n \nIn the meantime, you can expand your offerings by adding more services.",
  NEED_SUPPORT_TEXT:
    'For questions about your Bookable services submission, contact us at ',
  EMAIL_CTA: '<EMAIL>',
  BUTTON_CTA: 'Add service',
  HEADER_TITLE: 'Help customers understand your work',
  HEADER_DESCRIPTION:
    'Add service listings with pricing and descriptions to make booking easier.',
};

export const BOOKABLE_SERVICES_ACCESSABILITY_LABEL = {
  NEW_SERVICE_BUTTON: 'New service button',
};

export const BOOKABLE_SERVICES_DETAIL_STRINGS = {
  TITLE: 'Tap repair and new tap installation',
  DESCRIPTION_INTRO:
    'Repair of leaking or faulty taps, including the installation of a new tap supplied by the homeowner.',
  INCLUDED_TITLE: "What's included",
  NOT_INCLUDED_TITLE: "What's not included",
  INCLUDED_LIST: [
    'Diagnosis and repair of dripping, leaking, or faulty taps (faucets).',
    'Replacement of worn-out washers, seals, and cartridges.',
    'Tightening of loose connections.',
    'Minor adjustments to tap handles and spouts.',
  ],
  NOT_INCLUDED_LIST: [
    'Replacement of entire taps.',
    'Repair of damage caused by water leaks (e.g., water damage to walls, floors).',
    'Plumbing work beyond the tap itself (e.g., pipe repairs, rerouting).',
    'Emergency repairs or call-outs outside of standard working hours.',
    'Supply of new taps.',
  ],
  NOTES_TITLE: 'Important notes',
  NOTES: ['Please ensure the water supply is turned off before my arrival.'],
  PRICING_DETAILS_TITLE: 'Pricing details',
  PRICING_DETAILS:
    'Price is for labor only. Any additional parts will be charged separately. VAT included.',
  LINK_URL: 'https://www.checkatrade.com',
  SUBMIT_FOR_REVIEW_BUTTON: 'Submit for review',
  EDIT_BUTTON: 'Edit',
  EDIT_AND_RESUBMIT_BUTTON: 'Edit and resubmit',
  DELETE_BUTTON: 'Delete',
  DELETE_SERVICE_TITLE: 'Delete service?',
  DELETE_SERVICE_SUBTITLE: (serviceName?: string): string =>
    `${serviceName ?? 'This service'} will be permanently removed from the Services. This action cannot be undone.`,
  CANCEL_BUTTON: 'Cancel',
};

export const CREATE_BOOKABLE_SERVICE_STRINGS = {
  SEARCH_CATEGORY: 'Search category',
  SERVICE_TITLE: 'Service title',
  SERVICE_DESCRIPTION_PLACEHOLDER:
    'Describe the work you will carry out, including the type of service, common tasks involved, and any key details customers should be aware of.',
  PRICING_DETAILS_TITLE: 'Pricing details',
  AMOUNT_LABEL: 'Amount (incl. VAT)',
  AMOUNT_HINT:
    'Add your usual price, including VAT. Customers will see it displayed as “From £” to reflect variations.',
  UNIT_LABEL: 'Unit',
  PRICE_NOTE_LABEL: 'Price note',
  PRICE_NOTE_PLACEHOLDER: 'Price note (optional)',

  WHAT_IS_INCLUDED_TITLE: "What's included",
  WHAT_IS_NOT_INCLUDED_TITLE: "What's not included",
  NOTES: 'Important notes',
  IMPORTANT_NOTES_LABEL: 'Important notes',
  IMPORTANT_NOTES_PLACEHOLDER: 'Important notes (optional)',

  MIN_CHARACTERS: (min: number): string => `Min characters: ${min}`,
  ADD_ITEM: 'Add an item (optional)',

  BUTTON_TEXT: 'Done',

  DUPLICATE_SERVICE_HEADER:
    "You're creating a new service based on an existing one. Edits won't affect the original.",
};

export const BOOKABLE_SERVICES_CREATE_OPTIONS_STRINGS = {
  START_FROM_SCRATCH: 'Start from scratch',
  START_FROM_SCRATCH_DESCRIPTION: 'Enter all details manually',
  START_FROM_DUPLICATE: 'Duplicate an existing service',
  START_FROM_DUPLICATE_DESCRIPTION: 'Select a service to copy and edit',
};

export const BOOKABLE_SERVICE_VISIBILITY_STRINGS = {
  VISIBILITY_TITLE: 'Service visibility',
  PUBLISHED: 'Published',
  PUBLISHED_DESCRIPTION:
    'Your service is visible on your profile, and customers can book it.',
  HIDDEN: 'Hidden',
  HIDDEN_DESCRIPTION:
    'Your service is hidden from your profile, and customers cannot book it.',
};

export const BOOKABLE_SERVICE_BANNER_STRINGS = {
  IN_REVIEW_TITLE: 'In review',
  IN_REVIEW_DESCRIPTION:
    "We've received your service submission and it's now under review. You'll hear from us within 1 week, and we may share suggestions to help improve your listing.",
  NOT_APPROVED_TITLE: 'Not approved',
  NOT_APPROVED_DESCRIPTION: 'Your service has been rejected by our team.', // TODO: Add reason when available
};

export const BOOKABLE_SERVICES_GUIDE_HEADER_TITLE = 'Bookable services guide';
export const BOOKABLE_SERVICES_GENERAL_INFORMATION_TITLE =
  'General Information';
export const BUILDING_YOUR_BOOKABLE_SERVICES_CATALOGUE_TITLE =
  "Setting up 'Your services'";
export const BOOKABLE_SERVICES_SUPPORT_HEADER_TITLE = 'Support';

export const BOOKABLE_SERVICES_GENERAL_INFORMATION_FAQ_GUIDE = {
  LIST: [
    {
      title: "What is 'Your services'?",
      body: 'Your services is a new feature that allows you to create a detailed catalogue of the services you offer, directly within the Trade app. This catalogue will eventually be visible to customers on your Checkatrade profile, enabling them to book your services instantly. Right now, you can start building your catalogue in preparation for the full launch.',
    },
    {
      title: "How does 'Your services' help me win more work?",
      body: 'Your services helps you get hired faster by giving customers all the details they need upfront. This reduces back-and-forth communication and ensures you receive serious, high-intent inquiries.',
    },
    {
      title: "When will 'Your services' go live on my profile?",
      body: "We're preparing to launch this feature soon and will share the official date in the app and via email. For now, services you add won't appear on your profile or be visible to customers—this is your chance to get set up early and be ready when it goes live. \n\nOnce live, your services will be visible in search results and on your public profile, where customers will be able to view and request them directly.",
    },
  ],
};

export const BUILDING_BOOKABLE_SERVICES_INFORMATION_FAQ_GUIDE = {
  LIST: [
    {
      title: 'How do I add a service?',
      body: 'You can add a service using the Service form. The form will guide you through defining your service, setting a price, and outlining what is included and excluded. Submit the form each time you want to add a new service.',
    },
    {
      title: 'How many services can I add?',
      body: "There's no limit—you can add as many services as you need.",
    },
    {
      title: 'Can I edit or delete a service?',
      body: "Currently, there's no direct way to edit or delete services. This functionality will be available after the full launch.",
    },
    {
      title: 'How do I set my prices?',
      body: "Set prices for each service individually. Customers won't be charged at booking—you'll confirm the final price with them directly, so aim to be as precise as possible. Specify the value and unit of measure (e.g., per hour, per job, per room) and include any key pricing notes (e.g., VAT inclusion, surcharges for out-of-hours work).",
    },
  ],
};

export const BOOKABLE_SERVICES_SUPPORT_INFORMATION_FAQ_GUIDE = {
  LIST: [
    {
      title: 'Who can I contact for help?',
      body: "For assistance with 'Your services', contact our support team at",
    },
  ],
};

export const BOOKABLE_SERVICES_FORM_URL = 'Service form.';
export const BOOKABLE_SERVICES_FORM_URL_LINK =
  'https://forms.office.com/e/cFXU4DGX0d';
export const EMAIL_SUBMISSION_BODY =
  "I'd like to discuss my bookable services submission";
export const EMAIL_SUPPORT_BODY =
  "Support request: I'd like to be contacted with regards to my services";

export const BOOKABLE_SERVICE_UNIT_OPTIONS: {
  label: string;
  value: Quantifier;
}[] = [
  { label: 'Per job', value: Quantifier.PerJob },
  { label: 'Per hour', value: Quantifier.PerHour },
  { label: 'Per day', value: Quantifier.PerDay },
  { label: 'Per room', value: Quantifier.PerRoom },
  { label: 'Per square metre', value: Quantifier.PerSquareMetre },
  { label: 'Per cubic metre', value: Quantifier.PerCubicMetre },
  { label: 'Per linear metre', value: Quantifier.PerLinearMetre },
  { label: 'Per brick/block', value: Quantifier.PerBrickBlock },
  { label: 'Per fixture', value: Quantifier.PerFixture },
  { label: 'Per item', value: Quantifier.PerItem },
  { label: 'Per tree', value: Quantifier.PerTree },
  { label: 'Per visit', value: Quantifier.PerVisit },
  { label: 'Per appliance', value: Quantifier.PerAppliance },
  { label: 'Other', value: Quantifier.Other },
];
