import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { BookableServicesHeader } from './BookableServicesHeader';
import { MANAGE_BOOKABLE_SERVICES_STRINGS } from '../constants';

jest.mock('src/hooks/useMediaQuery', () => ({
  useDesktopMediaQuery: jest.fn(),
}));

describe('BookableServicesHeader', () => {
  const mockOnPress = jest.fn();
  const { HEADER, DESCRIPTION, BUTTON, ROOT } = BookableServicesHeader.testIds;

  beforeEach(() => {
    jest.clearAllMocks();
    (useDesktopMediaQuery as jest.Mock).mockReturnValue(true);
  });

  afterEach(() => {
    cleanup();
  });

  it('renders correctly', () => {
    const { getByTestId, getByText } = render(
      <BookableServicesHeader onPress={mockOnPress} />,
    );

    // Verify component renders
    expect(getByTestId(ROOT)).toBeOnTheScreen();
    expect(getByTestId(HEADER)).toBeOnTheScreen();
    expect(getByTestId(DESCRIPTION)).toBeOnTheScreen();
    expect(getByTestId(BUTTON)).toBeOnTheScreen();

    // Verify text content
    expect(
      getByText(MANAGE_BOOKABLE_SERVICES_STRINGS.HEADER_TITLE),
    ).toBeOnTheScreen();
    expect(
      getByText(MANAGE_BOOKABLE_SERVICES_STRINGS.HEADER_DESCRIPTION),
    ).toBeOnTheScreen();
    expect(
      getByText(MANAGE_BOOKABLE_SERVICES_STRINGS.BUTTON_CTA),
    ).toBeOnTheScreen();
  });

  it('calls onPress when button is pressed', () => {
    const { getByTestId } = render(
      <BookableServicesHeader onPress={mockOnPress} />,
    );

    fireEvent.press(getByTestId(BUTTON));

    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
});
