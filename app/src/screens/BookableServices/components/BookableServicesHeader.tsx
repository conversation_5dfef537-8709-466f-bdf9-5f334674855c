import React, { ReactElement } from 'react';
import { View } from 'react-native';
import { Button, Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { createTestIds } from 'src/utilities/testIds';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { ContentSegment } from 'src/components/ContentSegment';
import { MANAGE_BOOKABLE_SERVICES_STRINGS } from '../constants';

const TEST_IDS = createTestIds('bookable-services-header', {
  DESCRIPTION: 'description',
  BUTTON: 'button',
  HEADER: 'header',
});

interface BookableServicesHeaderProps {
  onPress: () => void;
}
export function BookableServicesHeader({
  onPress,
}: BookableServicesHeaderProps): ReactElement {
  const isLargeScreen = useDesktopMediaQuery();

  return (
    <ContentSegment
      testID={TEST_IDS.ROOT}
      style={[styles.container, isLargeScreen && styles.containerDesktop]}
    >
      <View>
        <Typography useVariant="bodySemiBold" testID={TEST_IDS.HEADER}>
          {MANAGE_BOOKABLE_SERVICES_STRINGS.HEADER_TITLE}
        </Typography>
        <Typography use="bodyRegular" testID={TEST_IDS.DESCRIPTION}>
          {MANAGE_BOOKABLE_SERVICES_STRINGS.HEADER_DESCRIPTION}
        </Typography>
      </View>
      <Button
        block={!isLargeScreen}
        label={MANAGE_BOOKABLE_SERVICES_STRINGS.BUTTON_CTA}
        testID={TEST_IDS.BUTTON}
        variant="secondary"
        onPress={onPress}
        style={styles.button}
        iconStart="plus"
      />
    </ContentSegment>
  );
}

BookableServicesHeader.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    padding: spacing(3),
    marginBottom: 0,
    width: '100%',
    maxWidth: spacing(100),
    alignSelf: 'center',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderBottomWidth: 1,
    borderColor: palette.system.tokenLegacyColorBorderDecorative,
  },
  containerDesktop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: spacing(4),
  },
  button: {
    marginVertical: spacing(2),
  },
}));
