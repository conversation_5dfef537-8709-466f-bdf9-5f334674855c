import React from 'react';
import { View } from 'react-native';
import { Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';

interface CreateBookableServiceHeaderProps {
  title: string;
}

const TEST_IDS = createTestIds('create-bookable-service-header', {});

export const CreateBookableServiceHeader: React.NativeFC<
  CreateBookableServiceHeaderProps,
  typeof TEST_IDS
> = ({ title }) => {
  const isDesktop = useDesktopMediaQuery();

  return (
    <View style={[styles.root, isDesktop && styles.desktopRoot]}>
      <View style={styles.content}>
        <Typography useVariant="bodySMRegular">{title}</Typography>
      </View>
    </View>
  );
};

CreateBookableServiceHeader.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  root: {
    paddingHorizontal: spacing(2),
    marginTop: spacing(2),
  },
  desktopRoot: {
    paddingHorizontal: 0,
    marginTop: 0,
  },
  content: {
    backgroundColor: palette.mortarV3.tokenDefault200,
    borderRadius: spacing(1),
    padding: spacing(1.5),
  },
}));
