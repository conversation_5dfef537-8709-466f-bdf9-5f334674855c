import React from 'react';
import { View } from 'react-native';
import { render, cleanup, fireEvent } from '@testing-library/react-native';
import { LoadingGuard } from '@cat-home-experts/react-native-components';
import {
  BookableServiceDetailType,
  BookingServiceStatusType,
} from 'src/data/schemas/api/capi/service-catalog/service';
import { BookableServicesList } from './BookableServicesList';

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: jest.fn().mockReturnValue({ bottom: 10 }),
}));

describe('BookableServicesList', () => {
  const mockServices: BookableServiceDetailType[] = [
    {
      id: '1',
      serviceId: '1',
      name: 'Service 1',
      categoryName: 'Category 1',
      status: BookingServiceStatusType.PublishedActive,
      description: 'Description 1',
      categoryId: 1,
      lineItems: [],
      days: [],
      homeownerNotes: '',
      whatIsIncluded: [],
      whatIsNotIncluded: [],
    },
    {
      id: '2',
      name: 'Service 2',
      categoryName: 'Category 1',
      status: BookingServiceStatusType.PublishedInactive,
      description: 'Description 2',
      categoryId: 1,
      serviceId: '2',
      lineItems: [],
      days: [],
      homeownerNotes: '',
      whatIsIncluded: [],
      whatIsNotIncluded: [],
    },
    {
      id: '3',
      name: 'Service 3',
      categoryName: 'Category 2',
      status: BookingServiceStatusType.PublishedActive,
      description: 'Description 3',
      categoryId: 2,
      serviceId: '3',
      lineItems: [],
      days: [],
      homeownerNotes: '',
      whatIsIncluded: [],
    },
  ];

  const mockProps = {
    services: mockServices,
    isLoading: false,
    hasNextPage: true,
    fetchNextPage: jest.fn(),
    onServicePress: jest.fn(),
    count: 3,
    isFetched: true,
  };

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders correctly with services', () => {
    const { getByText, getAllByTestId } = render(
      <BookableServicesList {...mockProps} />,
    );

    // Check if categories are rendered
    expect(getByText('Category 1')).toBeOnTheScreen();
    expect(getByText('Category 2')).toBeOnTheScreen();

    // Check if services are rendered
    expect(getByText('Service 1')).toBeOnTheScreen();
    expect(getByText('Service 2')).toBeOnTheScreen();
    expect(getByText('Service 3')).toBeOnTheScreen();

    // Check if category containers are rendered
    const categoryTitles = getAllByTestId(
      /bookable-services-list-category-title/,
    );
    expect(categoryTitles.length).toBe(2);
  });

  it('renders header when provided', () => {
    const mockHeader = () => <View testID="mock-header">{'Header'}</View>;
    const { getByTestId } = render(
      <BookableServicesList {...mockProps} renderHeader={mockHeader} />,
    );

    expect(getByTestId('mock-header')).toBeOnTheScreen();
  });

  it('renders only loading guard when loading', () => {
    const { queryByText, getByTestId } = render(
      <BookableServicesList {...mockProps} isLoading={true} />,
    );

    expect(queryByText('Category 1')).toBeNull();
    expect(queryByText('Service 1')).toBeNull();

    expect(getByTestId(LoadingGuard.testIds.ROOT)).toBeOnTheScreen();
  });

  it('does not render anything when there are no services', () => {
    const { queryByText } = render(
      <BookableServicesList {...mockProps} services={[]} />,
    );

    expect(queryByText('Category 1')).toBeNull();
    expect(queryByText('Service 1')).toBeNull();
  });

  it('calls onServicePress when a service is pressed', () => {
    const { getAllByTestId } = render(<BookableServicesList {...mockProps} />);

    // Find all service items
    const serviceItems = getAllByTestId(
      /bookable-services-list-item-container/,
    );

    fireEvent.press(serviceItems[0]);

    expect(mockProps.onServicePress).toHaveBeenCalledWith(
      expect.objectContaining({
        id: mockServices[0].id,
        name: mockServices[0].name,
      }),
    );
  });

  it('calls fetchNextPage when end is reached and hasNextPage is true', () => {
    render(<BookableServicesList {...mockProps} />);

    expect(mockProps.fetchNextPage).toHaveBeenCalled();
  });

  it('does not call fetchNextPage when hasNextPage is false', () => {
    render(<BookableServicesList {...mockProps} hasNextPage={false} />);

    expect(mockProps.fetchNextPage).not.toHaveBeenCalled();
  });
});
