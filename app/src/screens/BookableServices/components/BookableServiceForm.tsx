import React, { ReactElement, useState } from 'react';
import { View } from 'react-native';
import { useFormErrors, useFormValue } from '@childishforces/zod-form';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import {
  PoundSterling,
  PlusCircle,
  InformationCircle,
  MinusCircle,
} from '@cat-home-experts/mortar-iconography-native';
import {
  InputField,
  TextArea,
  Typography,
} from '@cat-home-experts/react-native-components';
import { FormGroup, FormGroupScreen } from 'src/components/FormGroup';
import { TextFormatter } from 'src/components/TextFormatter';
import { MultiItemInput } from 'src/components/MultiItemInput';
import { CampaignCategoryInput } from 'src/screens/Campaigns/components/CampaignCategoryInput';
import { CreateBookableServiceSchema } from 'src/data/schemas/api/capi/service-catalog/service';
// TODO: This includes a fix on Android for the CurrencyInput component. Import directly from `@cat-home-experts/react-native-components` when possible.
import { CurrencyInput } from 'src/screens/JobPayments/components/CurrencyInput';
import {
  BOOKABLE_SERVICE_UNIT_OPTIONS,
  CREATE_BOOKABLE_SERVICE_STRINGS,
} from '../constants';
import { UseBookableServiceForm } from '../hooks/useBookableServiceForm';
import { BookableServiceUnitSelect } from './BookableServiceUnitSelect';

const TEST_IDS = createTestIds('create-bookable-service', {
  SERVICE_TITLE: 'service-title',
});

interface BookableServiceFormProps {
  form: UseBookableServiceForm;
  isEditMode: boolean;
  autoFocusMultiInputs?: boolean;
  onSubmit: (service: CreateBookableServiceSchema) => Promise<void>;
  renderHeader?: () => ReactElement;
}

type MultiItemInputKey = 'whatIsIncluded' | 'whatIsNotIncluded';

export const BookableServiceForm = ({
  form,
  onSubmit,
  isEditMode,
  renderHeader,
}: BookableServiceFormProps): ReactElement => {
  const { state, valid } = useFormValue(form);
  const { errors, showForKey } = useFormErrors(form);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const onChangeMultiItemInput = (
    key: MultiItemInputKey,
    index: number,
    item: string,
  ) => {
    const currentItems = [...(state[key] ?? [])];
    currentItems[index] = item;
    form.createBasicSetter(key)(currentItems);
  };

  const onAddMultiItemInput = (key: MultiItemInputKey) => {
    form.createBasicSetter(key)([...(state[key] ?? []), '']);
  };

  const onRemoveMultiItemInput = (key: MultiItemInputKey, index: number) => {
    const currentItems = [...(state[key] ?? [])];
    currentItems.splice(index, 1);
    form.createBasicSetter(key)(currentItems);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onSubmit(state as CreateBookableServiceSchema);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormGroupScreen
      buttonProps={{
        label: CREATE_BOOKABLE_SERVICE_STRINGS.BUTTON_TEXT,
        isDisabled: !valid || isSubmitting,
        onPress: handleSubmit,
        isLoading: isSubmitting,
      }}
    >
      {renderHeader?.()}

      <FormGroup>
        <CampaignCategoryInput
          value={state.categoryId}
          onChange={form.createBasicSetter('categoryId')}
          isDisabled={isEditMode}
        />

        <View>
          <TextArea
            testID={TEST_IDS.SERVICE_TITLE}
            label={CREATE_BOOKABLE_SERVICE_STRINGS.SERVICE_TITLE}
            placeholder={CREATE_BOOKABLE_SERVICE_STRINGS.SERVICE_TITLE}
            onChangeText={form.createBasicSetter('name')}
            onBlur={showForKey('name')}
            value={state.name}
            error={errors.name}
            submitBehavior="blurAndSubmit"
            hideFocusBorder
            returnKeyType="next"
            maxLength={80}
            multiline
            numberOfLines={2}
            style={styles.textArea}
            inputStyle={styles.textArea}
            containerStyle={styles.textArea}
          />
          <View style={styles.inputNote}>
            <Typography useVariant="labelRegular" isMuted>
              {CREATE_BOOKABLE_SERVICE_STRINGS.MIN_CHARACTERS(15)}
            </Typography>
            <Typography useVariant="labelRegular" isMuted>
              {`${state.name?.length}/80`}
            </Typography>
          </View>
        </View>

        <View>
          <TextFormatter
            value={state.description ?? ''}
            onChange={form.createBasicSetter('description')}
            onBlur={showForKey('description')}
            error={errors.description}
            placeholder={
              CREATE_BOOKABLE_SERVICE_STRINGS.SERVICE_DESCRIPTION_PLACEHOLDER
            }
            label={
              CREATE_BOOKABLE_SERVICE_STRINGS.SERVICE_DESCRIPTION_PLACEHOLDER
            }
            hideLabel
          />
          <View style={styles.inputNote}>
            <Typography useVariant="labelRegular" isMuted>
              {CREATE_BOOKABLE_SERVICE_STRINGS.MIN_CHARACTERS(100)}
            </Typography>
            <Typography
              useVariant="labelRegular"
              isMuted={(state.description?.length ?? 0) <= 1200}
              isBrandedSecondary={(state.description?.length ?? 0) > 1200}
            >
              {`${state.description?.length}/1200`}
            </Typography>
          </View>
        </View>
      </FormGroup>

      <FormGroup
        header={{
          icon: PoundSterling,
          title: CREATE_BOOKABLE_SERVICE_STRINGS.PRICING_DETAILS_TITLE,
        }}
      >
        <CurrencyInput
          label={CREATE_BOOKABLE_SERVICE_STRINGS.AMOUNT_LABEL}
          value={state.priceInPence ?? 0}
          onChange={form.createBasicSetter('priceInPence')}
          onBlur={showForKey('priceInPence')}
          error={errors.priceInPence}
          hintMessage={CREATE_BOOKABLE_SERVICE_STRINGS.AMOUNT_HINT}
        />

        <BookableServiceUnitSelect
          placeholder={CREATE_BOOKABLE_SERVICE_STRINGS.UNIT_LABEL}
          value={
            BOOKABLE_SERVICE_UNIT_OPTIONS.find(
              (option) => option.value === state.quantifier,
            ) ?? null
          }
          onChange={({ value }) => form.createBasicSetter('quantifier')(value)}
        />

        <InputField
          label={CREATE_BOOKABLE_SERVICE_STRINGS.PRICE_NOTE_LABEL}
          placeholder={CREATE_BOOKABLE_SERVICE_STRINGS.PRICE_NOTE_PLACEHOLDER}
          value={state.homeownerPriceRelatedNotes ?? ''}
          onChangeText={form.createBasicSetter('homeownerPriceRelatedNotes')}
          onBlur={showForKey('homeownerPriceRelatedNotes')}
          hideFocusBorder
        />
      </FormGroup>

      <FormGroup
        header={{
          icon: PlusCircle,
          title: CREATE_BOOKABLE_SERVICE_STRINGS.WHAT_IS_INCLUDED_TITLE,
        }}
      >
        <MultiItemInput
          items={state.whatIsIncluded ?? []}
          onChange={(index, item) =>
            onChangeMultiItemInput('whatIsIncluded', index, item)
          }
          onAddItem={() => onAddMultiItemInput('whatIsIncluded')}
          onRemoveItem={(index) =>
            onRemoveMultiItemInput('whatIsIncluded', index)
          }
        />
      </FormGroup>

      <FormGroup
        header={{
          icon: MinusCircle,
          title: CREATE_BOOKABLE_SERVICE_STRINGS.WHAT_IS_NOT_INCLUDED_TITLE,
        }}
      >
        <MultiItemInput
          items={state.whatIsNotIncluded ?? []}
          onChange={(index, item) =>
            onChangeMultiItemInput('whatIsNotIncluded', index, item)
          }
          onAddItem={() => onAddMultiItemInput('whatIsNotIncluded')}
          onRemoveItem={(index) =>
            onRemoveMultiItemInput('whatIsNotIncluded', index)
          }
        />
      </FormGroup>

      <FormGroup
        header={{
          icon: InformationCircle,
          title: CREATE_BOOKABLE_SERVICE_STRINGS.NOTES,
        }}
      >
        <TextArea
          label={CREATE_BOOKABLE_SERVICE_STRINGS.IMPORTANT_NOTES_LABEL}
          placeholder={
            CREATE_BOOKABLE_SERVICE_STRINGS.IMPORTANT_NOTES_PLACEHOLDER
          }
          value={state.homeownerNotes ?? ''}
          onChangeText={form.createBasicSetter('homeownerNotes')}
          onBlur={showForKey('homeownerNotes')}
          error={errors.homeownerNotes}
          style={styles.textArea}
          inputStyle={styles.textArea}
          containerStyle={styles.textArea}
          hideFocusBorder
          multiline
        />
      </FormGroup>
    </FormGroupScreen>
  );
};

BookableServiceForm.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing }) => ({
  inputNote: {
    paddingTop: spacing(1),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  textArea: {
    height: 'auto',
    minHeight: 50,
    lineHeight: 22,
    fontSize: 16,
  },
}));
