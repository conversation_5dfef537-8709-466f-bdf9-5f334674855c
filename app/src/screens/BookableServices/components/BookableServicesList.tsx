import React, { ReactElement, useCallback } from 'react';
import { Platform, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import { noop } from 'lodash';
import {
  LoadingGuard,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { BookableServiceDetailType } from 'src/data/schemas/api/capi/service-catalog/service';
import { BookingServiceListItem } from './BookingServiceListItem';
import { UseBookableServicesReturn } from '../hooks/useBookableServices';

interface ExtendedBookingServiceDetail extends BookableServiceDetailType {
  isLastItemInCategory: boolean;
}

type CategoryItem = {
  type: 'category';
  name: string;
};

type BookingServiceListItemType =
  | string
  | CategoryItem
  | ExtendedBookingServiceDetail;

interface BookableServicesListProps extends UseBookableServicesReturn {
  onServicePress: (service: BookableServiceDetailType) => void;
  renderHeader?: () => ReactElement;
}

const TEST_IDS = createTestIds('bookable-services-list', {
  CATEGORY_CONTAINER: 'category-container',
  CATEGORY_TITLE: 'category-title',
  ITEM_CONTAINER: 'item-container',
});

export const BookableServicesList: React.NativeFC<
  BookableServicesListProps,
  typeof TEST_IDS
> = ({
  services,
  isLoading,
  hasNextPage,
  fetchNextPage,
  onServicePress,
  renderHeader,
}) => {
  const { bottom } = useSafeAreaInsets();

  const renderItem: ListRenderItem<BookingServiceListItemType> = useCallback(
    ({ item }) => {
      if (
        typeof item === 'object' &&
        'type' in item &&
        item.type === 'category'
      ) {
        return (
          <View style={[styles.categoryContainer, styles.itemContainer]}>
            <Typography
              useVariant="bodySMSemiBold"
              style={styles.categoryTitle}
              testID={`${TEST_IDS.CATEGORY_TITLE}-${item.name}`}
            >
              {item.name}
            </Typography>
          </View>
        );
      }

      if (typeof item === 'object' && 'isLastItemInCategory' in item) {
        return (
          <View style={styles.itemContainer}>
            <BookingServiceListItem
              service={item}
              onPress={() => onServicePress(item)}
              isLastInGroup={item.isLastItemInCategory}
              testID={`${TEST_IDS.ITEM_CONTAINER}-${item.name}`}
            />
          </View>
        );
      }

      return null;
    },
    [onServicePress],
  );

  const renderHeaderComponent = useCallback(() => {
    if (!renderHeader) {
      return null;
    }

    return <View style={styles.headerContainer}>{renderHeader()}</View>;
  }, [renderHeader]);

  const groupedServices = services.reduce(
    (acc, service) => {
      if (!acc[service.categoryName]) {
        acc[service.categoryName] = [];
      }

      acc[service.categoryName].push(service);
      return acc;
    },
    {} as Record<string, BookableServiceDetailType[]>,
  );

  const flatListData: BookingServiceListItemType[] = Object.entries(
    groupedServices,
  ).flatMap(([categoryName, categoryServices]) => {
    return [
      { type: 'category' as const, name: categoryName },
      ...categoryServices.map((service, index) => ({
        ...service,
        isLastItemInCategory: index === categoryServices.length - 1,
      })),
    ];
  });

  return (
    <LoadingGuard spinnerSize={40} style={styles.root}>
      {!isLoading && flatListData.length > 0 && (
        <FlashList
          data={flatListData}
          onEndReached={hasNextPage ? fetchNextPage : noop}
          renderItem={renderItem}
          estimatedItemSize={75}
          keyExtractor={(item, index) =>
            `${typeof item === 'string' ? item : item.name}-${index}`
          }
          contentContainerStyle={{ paddingBottom: bottom }}
          ListHeaderComponent={renderHeaderComponent}
        />
      )}
    </LoadingGuard>
  );
};

BookableServicesList.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  root: {
    flex: 1,
  },
  headerContainer: {
    paddingVertical: spacing(2),
  },
  itemContainer: {
    flex: 1,
    ...Platform.select({
      web: {
        width: '100%',
        maxWidth: spacing(100),
        alignSelf: 'center',
      },
    }),
  },
  categoryContainer: {
    backgroundColor: palette.mortar.tokenColorLightBlue,
  },
  categoryTitle: {
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(1.5),
  },
}));
