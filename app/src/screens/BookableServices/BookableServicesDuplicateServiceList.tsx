import React, { ReactElement } from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { createTestIds } from 'src/utilities/testIds';
import { BookableServiceDetailType } from 'src/data/schemas/api/capi/service-catalog/service';
import {
  BOOKABLE_SERVICES_SCREEN,
  CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN,
} from 'src/constants';
import { logEvent } from 'src/services/analytics';
import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { useBookableServices } from 'src/screens/BookableServices/hooks/useBookableServices';
import { AllScreensParamList } from 'src/navigation/routes';
import { BookableServicesList } from './components/BookableServicesList';
import { PageNotFound } from '../PageNotFound';

const TEST_IDS = createTestIds('bookable-services-duplicate-service-list', {});

export function BookableServicesDuplicateServiceList(): ReactElement {
  const navigation = useNavigation<StackNavigationProp<AllScreensParamList>>();
  const { services, isLoading, isFetched, ...rest } = useBookableServices();

  const onServicePress = (service: BookableServiceDetailType) => {
    logEvent(
      `${EVENT_TYPE.BOOKABLE_SERVICES_CREATE_FROM_DUPLICATE}_${ANALYTICS_ACTION_TYPE.CLICKED}`,
    );
    navigation.navigate(
      CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN,
      {
        id: service.serviceId,
        versionId: service.id,
      },
    );
  };

  if (!services?.length && !isLoading && isFetched) {
    return <PageNotFound fallbackScreenToNavigate={BOOKABLE_SERVICES_SCREEN} />;
  }

  return (
    <View style={styles.flex} testID={TEST_IDS.ROOT}>
      <BookableServicesList
        services={services}
        isLoading={isLoading}
        isFetched={isFetched}
        {...rest}
        onServicePress={onServicePress}
      />
    </View>
  );
}

BookableServicesDuplicateServiceList.testIds = TEST_IDS;

const styles = createMortarStyles(() => {
  return {
    flex: {
      flex: 1,
    },
  };
});
