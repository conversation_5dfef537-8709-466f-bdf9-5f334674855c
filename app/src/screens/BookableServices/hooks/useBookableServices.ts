import { useInfiniteQuery } from '@tanstack/react-query';
import { useUserContext } from 'src/hooks/useUser';
import { tradeAppBff } from 'src/data/api/trade-app-bff';
import {
  BookableServiceDetailType,
  BookableServiceSchemaResponse,
  BookingServiceStatusType,
} from 'src/data/schemas/api/capi/service-catalog/service';
import { useMemo } from 'react';
import { useLastTruthyValue } from 'src/hooks/useLastTruthyValue';
import { BOOKABLE_SERVICES_QUERY_KEY } from 'src/constants';

type InfiniteQueryReturn = ReturnType<
  typeof useInfiniteQuery<BookableServiceSchemaResponse>
>;
export interface UseBookableServicesReturn {
  services: BookableServiceDetailType[];
  count: number | null;
  isLoading: InfiniteQueryReturn['isLoading'];
  isFetched: InfiniteQueryReturn['isFetched'];
  fetchNextPage: InfiniteQueryReturn['fetchNextPage'];
  hasNextPage: InfiniteQueryReturn['hasNextPage'];
}

export const useBookableServices = (): UseBookableServicesReturn => {
  const { companyId } = useUserContext();

  const {
    data: servicesResponse,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetched,
  } = useInfiniteQuery({
    queryFn: async ({ pageParam }) => {
      if (!companyId) {
        throw new Error('companyId is undefined');
      }

      const { data } = await tradeAppBff.serviceCatalog.getServices({
        pageNumber: pageParam.page,
        pageSize: pageParam.limit,
        companyId,
      });
      return data;
    },
    queryKey: [BOOKABLE_SERVICES_QUERY_KEY, companyId],
    initialPageParam: { page: 1, limit: 40 },
    getNextPageParam: ({ pagination }) => {
      const totalPages = Math.ceil((pagination.total || 0) / pagination.size);
      if (pagination.page === totalPages) {
        return undefined;
      }

      return { page: pagination.page + 1, limit: pagination.size };
    },
    refetchOnMount: 'always',
    refetchInterval: 1000 * 30,
  });

  const flatData = useMemo<BookableServiceDetailType[]>(() => {
    return (servicesResponse?.pages ?? [])
      .flatMap(({ data }) => data)
      .filter((data) => data.status !== BookingServiceStatusType.Archived);
  }, [servicesResponse]);

  const count = useLastTruthyValue(
    useMemo(() => {
      if (!servicesResponse) {
        return null;
      }

      const [lastPage] = servicesResponse.pages.slice(-1);
      return lastPage.pagination.total || 0;
    }, [servicesResponse]),
    null,
  );

  return {
    services: flatData,
    count,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetched,
  };
};
