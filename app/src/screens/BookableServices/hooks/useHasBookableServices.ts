import { isTruthy } from '@cat-home-experts/react-native-utilities';
import { useBookableServices } from './useBookableServices';

type UseHasBookableServicesReturn = {
  hasBookableServices: boolean;
  isLoading: boolean;
};

export const useHasBookableServices = (): UseHasBookableServicesReturn => {
  const { services, isLoading } = useBookableServices();

  return {
    hasBookableServices: isTruthy(services) && services.length > 0,
    isLoading,
  };
};
