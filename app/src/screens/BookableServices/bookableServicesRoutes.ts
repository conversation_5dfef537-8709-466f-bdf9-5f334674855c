import {
  B<PERSON><PERSON><PERSON><PERSON>_SERVICE_DETAIL_SCREEN,
  <PERSON>OOK<PERSON>LE_SERVICES_SCREEN,
  B<PERSON><PERSON><PERSON>LE_SERVICES_FAQ_GUIDE_SCREEN,
  CREATE_BOOKABLE_SERVICES_SCREEN,
  EDIT_BOOKABLE_SERVICE_SCREEN,
  CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN,
  CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_LIST_SCREEN,
  CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN,
} from 'src/constants';
import type { RouteItem } from 'src/navigation/types/routeTypes';

import { BookableServices } from './BookableServices';
import { BookableServicesDetail } from './BookableServicesDetail';
import { BookableServicesFaqGuide } from './BookableServiceFaqGuide';
import { CreateBookableService } from './CreateBookableService';
import { EditBookableService } from './EditBookableService';
import { BookableServicesCreateOptions } from './BookableServicesCreateOptions';
import { BookableServicesDuplicateServiceList } from './BookableServicesDuplicateServiceList';
import { CreateBookableServiceFromDuplicate } from './CreateBookableServiceFromDuplicate';

export type BookableServicesParamList = {
  [BOOKABLE_SERVICES_SCREEN]: undefined;
  [CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN]: undefined;
  [CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_LIST_SCREEN]: undefined;
  [CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN]: {
    id: string;
    versionId: string;
  };
  [CREATE_BOOKABLE_SERVICES_SCREEN]: undefined;
  [BOOKABLE_SERVICE_DETAIL_SCREEN]: {
    id: string;
    versionId: string;
  };
  [EDIT_BOOKABLE_SERVICE_SCREEN]: {
    id: string;
    versionId: string;
  };
  [BOOKABLE_SERVICES_FAQ_GUIDE_SCREEN]: undefined;
};

export const bookableServicesRoutes = ({
  enableBookableServices,
}: {
  enableBookableServices: boolean;
}): RouteItem[] => {
  if (!enableBookableServices) {
    return [];
  }

  return [
    {
      key: 'bookable-services',
      name: BOOKABLE_SERVICES_SCREEN,
      component: BookableServices,
      path: 'bookable-services',
      navGroup: 'bookable-services',
    },
    {
      key: 'create-bookable-services-options',
      name: CREATE_BOOKABLE_SERVICES_OPTIONS_SCREEN,
      component: BookableServicesCreateOptions,
      path: 'bookable-services/create-options',
      navGroup: 'bookable-services',
    },
    {
      key: 'create-bookable-services-from-duplicate-service-list',
      name: CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_LIST_SCREEN,
      component: BookableServicesDuplicateServiceList,
      path: 'bookable-services/create-duplicate-services',
      navGroup: 'bookable-services',
    },
    {
      key: 'create-bookable-services-from-duplicate-service-details',
      name: CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN,
      component: CreateBookableServiceFromDuplicate,
      path: 'bookable-services/create/from-service',
      navGroup: 'bookable-services',
    },
    {
      key: 'create-bookable-services',
      name: CREATE_BOOKABLE_SERVICES_SCREEN,
      component: CreateBookableService,
      path: 'bookable-services/create',
      navGroup: 'bookable-services',
    },
    {
      key: 'manage-bookable-services-faq-guide',
      name: BOOKABLE_SERVICES_FAQ_GUIDE_SCREEN,
      component: BookableServicesFaqGuide,
      path: 'bookable-services/faq',
      navGroup: 'bookable-services',
    },
    {
      key: 'bookable-services-detail',
      name: BOOKABLE_SERVICE_DETAIL_SCREEN,
      component: BookableServicesDetail,
      path: 'bookable-services/:id',
      navGroup: 'bookable-services',
    },
    {
      key: 'bookable-services-edit',
      name: EDIT_BOOKABLE_SERVICE_SCREEN,
      component: EditBookableService,
      path: 'bookable-services/:id/edit',
      navGroup: 'bookable-services',
    },
  ];
};
