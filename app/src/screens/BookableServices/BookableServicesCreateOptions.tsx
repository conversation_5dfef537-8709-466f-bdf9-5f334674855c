import React, { ReactElement } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  createMortarStyles,
  createTestIds,
  isTruthy,
  palette as staticPalette,
} from '@cat-home-experts/react-native-utilities';
import {
  ChevronRightSmall,
  ClipboardEdit,
  ContentCopy,
} from '@cat-home-experts/mortar-iconography-native';
import { Typography } from '@cat-home-experts/react-native-components';
import { AllScreensParamList } from 'src/navigation/routes';
import {
  CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_LIST_SCREEN,
  CREATE_BOOKABLE_SERVICES_SCREEN,
} from 'src/constants';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { BOOKABLE_SERVICES_CREATE_OPTIONS_STRINGS } from './constants';
import { useBookableServices } from './hooks/useBookableServices';

const TEST_IDS = createTestIds('bookable-services-create-options', {});

export const BookableServicesCreateOptions = (): ReactElement => {
  const navigation = useNavigation<StackNavigationProp<AllScreensParamList>>();
  const { count } = useBookableServices();
  const useIsDesktop = useDesktopMediaQuery();

  const options = [
    {
      title: BOOKABLE_SERVICES_CREATE_OPTIONS_STRINGS.START_FROM_SCRATCH,
      description:
        BOOKABLE_SERVICES_CREATE_OPTIONS_STRINGS.START_FROM_SCRATCH_DESCRIPTION,
      onPress: () => navigation.navigate(CREATE_BOOKABLE_SERVICES_SCREEN),
      Icon: ClipboardEdit,
    },
    (count || 0) > 0 && {
      title: BOOKABLE_SERVICES_CREATE_OPTIONS_STRINGS.START_FROM_DUPLICATE,
      description:
        BOOKABLE_SERVICES_CREATE_OPTIONS_STRINGS.START_FROM_DUPLICATE_DESCRIPTION,
      onPress: () =>
        navigation.navigate(
          CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_LIST_SCREEN,
        ),
      Icon: ContentCopy,
    },
  ].filter(isTruthy);

  return (
    <View style={[styles.root, useIsDesktop && styles.rootDesktop]}>
      {options.map((option) => {
        const Icon = option.Icon;
        return (
          <TouchableOpacity
            key={option.title}
            onPress={option.onPress}
            style={styles.option}
          >
            <View style={styles.iconContainer}>
              <Icon size={20} color={staticPalette.mortarV3.tokenDefault700} />
            </View>
            <View style={styles.textContainer}>
              <Typography useVariant="bodySMSemiBold">
                {option.title}
              </Typography>
              <Typography useVariant="bodySMRegular">
                {option.description}
              </Typography>
            </View>
            <ChevronRightSmall
              size={16}
              color={staticPalette.mortarV3.tokenNeutral900}
            />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

BookableServicesCreateOptions.testIds = TEST_IDS;

const styles = createMortarStyles(({ spacing, palette }) => ({
  root: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    width: '100%',
    maxWidth: spacing(100),
    alignSelf: 'center',
  },
  rootDesktop: {
    borderRadius: spacing(1),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
    marginTop: spacing(2),
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(2),
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: spacing(5),
    height: spacing(5),
    borderRadius: spacing(3),
    backgroundColor: palette.mortarV3.tokenDefault200,
  },
  textContainer: {
    flex: 1,
    paddingLeft: spacing(1.5),
  },
}));
