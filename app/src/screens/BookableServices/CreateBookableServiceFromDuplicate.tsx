import React, { ReactElement } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { LoadingGuard } from '@cat-home-experts/react-native-components';
import {
  CreateBookableServiceSchema,
  Quantifier,
} from 'src/data/schemas/api/capi/service-catalog/service';
import { AllScreensParamList } from 'src/navigation/routes';
import {
  BOOKABLE_SERVICE_DETAIL_SCREEN,
  CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN,
} from 'src/constants';
import { captureException } from 'src/services/datadog';
import { showToast } from 'src/components';
import { EVENT_TYPE } from 'src/constants.events';
import { logEvent } from 'src/services/analytics';
import { useBookableServiceForm } from './hooks/useBookableServiceForm';
import { BookableServiceForm } from './components/BookableServiceForm';
import { useBookableServiceVersion } from './hooks/useBookableServiceVersion';
import { useCreateBookableService } from './hooks/useCreateBookableService';
import { CreateBookableServiceHeader } from './components/CreateBookableServiceHeader';
import { CREATE_BOOKABLE_SERVICE_STRINGS } from './constants';

const TEST_IDS = createTestIds('create-bookable-service-from-duplicate', {});

export const CreateBookableServiceFromDuplicate = (): ReactElement => {
  const navigation = useNavigation<StackNavigationProp<AllScreensParamList>>();
  const { params } =
    useRoute<
      RouteProp<
        AllScreensParamList,
        typeof CREATE_BOOKABLE_SERVICES_FROM_DUPLICATE_SERVICE_SCREEN
      >
    >();

  const { id, versionId } = params;

  const { bookableServiceVersion, isLoading } = useBookableServiceVersion(
    id,
    versionId,
  );

  const { mutateAsync: createService } = useCreateBookableService();

  const form = useBookableServiceForm({
    initialValues: {
      name: bookableServiceVersion?.name
        ? `${bookableServiceVersion.name} (copy)`
        : '',
      description: bookableServiceVersion?.description ?? '',
      categoryId: bookableServiceVersion?.categoryId ?? 0,
      priceInPence: bookableServiceVersion?.lineItems?.[0].priceInPence ?? 0,
      quantifier:
        bookableServiceVersion?.lineItems?.[0].quantifier ?? Quantifier.PerJob,
      homeownerPriceRelatedNotes:
        bookableServiceVersion?.lineItems?.[0]?.notes ?? '',
      whatIsIncluded: bookableServiceVersion?.whatIsIncluded ?? [],
      whatIsNotIncluded: bookableServiceVersion?.whatIsNotIncluded ?? [],
      homeownerNotes: bookableServiceVersion?.homeownerNotes ?? '',
    },
  });

  const onSubmit = async (service: CreateBookableServiceSchema) => {
    try {
      const response = await createService(service);
      logEvent(EVENT_TYPE.BOOKABLE_SERVICES_DUPLICATE_SUCCESS, {
        serviceId: response.serviceId,
        versionId: response.id,
        categoryId: service.categoryId,
      });
      try {
        navigation.replace(BOOKABLE_SERVICE_DETAIL_SCREEN, {
          id: response.serviceId,
          versionId: response.id,
        });
      } catch (e) {
        navigation.navigate(BOOKABLE_SERVICE_DETAIL_SCREEN, {
          id: response.serviceId,
          versionId: response.id,
        });
      }
    } catch (error) {
      captureException(error, {
        source: 'CreateBookableServiceFromDuplicate',
        method: 'onSubmit',
      });
      showToast({
        text1: 'Error creating service',
        type: 'error',
      });
    }
  };

  const renderHeader = () => {
    return (
      <CreateBookableServiceHeader
        title={CREATE_BOOKABLE_SERVICE_STRINGS.DUPLICATE_SERVICE_HEADER}
      />
    );
  };

  return (
    <LoadingGuard style={styles.container}>
      {!isLoading && (
        <BookableServiceForm
          form={form}
          isEditMode={false}
          onSubmit={onSubmit}
          renderHeader={renderHeader}
        />
      )}
    </LoadingGuard>
  );
};

CreateBookableServiceFromDuplicate.testIds = TEST_IDS;

const styles = createMortarStyles(() => ({
  container: {
    flex: 1,
  },
}));
