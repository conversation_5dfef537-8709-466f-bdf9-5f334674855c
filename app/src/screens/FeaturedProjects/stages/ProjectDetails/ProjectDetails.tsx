import { noop } from 'lodash';
import React, { useEffect, useMemo } from 'react';
import { View } from 'react-native';

import {
  Button,
  CurrencyInput,
  InputField,
  Spinner,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
  spacing,
} from '@cat-home-experts/react-native-utilities';

import { Controller, useForm } from 'react-hook-form';
import { ContentSegment } from 'src/components/ContentSegment';
import { DatePicker } from 'src/components/primitives/DatePicker';
import { showToast } from 'src/components/primitives/Toaster';
import { useDesktopMediaQuery } from 'src/hooks/useMediaQuery';
import { ScreenBreakpoints } from 'src/hooks/useMediaQuery/ScreenBreakpoints';
import { useMarketplaceJobDetails } from 'src/screens/MarketplaceJobs/hooks';
import { INVALID_FORM_TOAST } from 'src/screens/MyTeam/constants';
import { SafeAreaView } from 'react-native-safe-area-context';
import { addDays, addYears, subYears } from 'date-fns';
import { zodResolver } from '@hookform/resolvers/zod';
import { useUserContext } from 'src/hooks/useUser';
import { EVENT_TYPE } from 'src/constants.events';
import { logEvent } from 'src/services/analytics';
import { FeaturedProjectRadioCardList } from '../../components/FeaturedProjectJobCard';
import { FEATURED_PROJECT_STAGES } from '../../constants';
import { useFeaturedProjectsStages } from '../../hooks/useFeaturedProjectsStages/useFeaturedProjectsStages';
import { useFeaturedProjectsDraft } from '../../hooks/useFeaturedProjectsDraft';
import {
  COST_INSTRUCTION_TEXT,
  GIVE_PROJECT_NAME_INSTRUCTION_TEXT,
  WHICH_JOB_INSTRUCTION_TEXT,
} from './constants';
import {
  ProjectDetailsFormDataSchema,
  type ProjectDetailsFormDataType,
} from '../types';
import { useCompletedJobs } from '../../hooks/useCompletedJobs/useCompletedJobs';

const TEST_IDS = createTestIds('project-details', {
  PROJECT_NAME_INPUT: 'project-name-input',
  START_DATE_INPUT: 'start-date-input',
  END_DATE_INPUT: 'end-date-input',
  COST_INPUT: 'cost-input',
});

export const ProjectDetails: React.FC = () => {
  const isDesktop = useDesktopMediaQuery();
  const { companyId } = useUserContext();
  const { updateActiveStage } = useFeaturedProjectsStages();
  const { draftState, updateStage } = useFeaturedProjectsDraft();

  const projectDetailsDraftState =
    draftState?.stages?.[FEATURED_PROJECT_STAGES.PROJECT_DETAILS];

  const { data: completedJobs, isLoading: isLoadingCompletedJobs } =
    useCompletedJobs();

  // Temporary: This (filter) will eventually need to be completely done on the backend.
  const allJobsNotAssignedToProjects = useMemo(() => {
    return completedJobs?.filter((job) => !job.isAssignedToProject).slice(0, 5);
  }, [completedJobs]);

  const minDate = subYears(new Date(), 50);
  const maxDate = addYears(new Date(), 10);

  const { control, handleSubmit, setValue, formState, reset, watch } = useForm({
    resolver: zodResolver(ProjectDetailsFormDataSchema),
    defaultValues: {
      companyId,
      startDate: new Date(),
      endDate: new Date(),
      cost: 0,
      projectName: '',
      jobId: undefined,
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (projectDetailsDraftState) {
      const formattedDraft = {
        ...projectDetailsDraftState,
        // Convert dates to Date objects to prevent validation error.
        startDate: new Date(projectDetailsDraftState.startDate),
        endDate: new Date(projectDetailsDraftState.endDate),
      };

      reset(formattedDraft);
    }

    if (companyId) {
      setValue('companyId', companyId);
    }
  }, [companyId, projectDetailsDraftState, reset, setValue]);

  const { jobId: selectedJobId } = watch();

  const { isValid, errors } = formState;
  const { job: selectedJobDetails } = useMarketplaceJobDetails(selectedJobId);

  const onSubmit = (data: ProjectDetailsFormDataType) => {
    updateStage(FEATURED_PROJECT_STAGES.PROJECT_DETAILS, data);
    updateActiveStage(FEATURED_PROJECT_STAGES.ADD_PHOTOS);
    logEvent(EVENT_TYPE.FEATURED_PROJ_ADD_PHOTOS_CLICKED);
  };

  const onSubmitWithError = () => {
    showToast({
      text1: INVALID_FORM_TOAST.TITLE,
      text2: INVALID_FORM_TOAST.DESCRIPTION,
      type: 'error',
    });
  };

  useEffect(() => {
    // Pre-populate form fields based on job selection.
    if (!projectDetailsDraftState && selectedJobDetails) {
      setValue('projectName', selectedJobDetails.category.label);
      setValue('startDate', new Date(selectedJobDetails.createdAt));
      setValue('endDate', addDays(selectedJobDetails.createdAt, 2));
    }
  }, [selectedJobId, selectedJobDetails, setValue, projectDetailsDraftState]);

  return (
    <SafeAreaView
      edges={['bottom']}
      style={isDesktop ? styles.containerDesktop : styles.container}
    >
      <Controller
        rules={{ required: true }}
        control={control}
        name="jobId"
        render={({ field }) => (
          <ContentSegment>
            <Typography useVariant="bodySemiBold">
              {'Which job was this?'}
            </Typography>
            <Typography
              style={styles.helpText}
              useVariant={isDesktop ? 'bodySMRegular' : 'bodySmall'}
            >
              {WHICH_JOB_INSTRUCTION_TEXT}
            </Typography>
            {isLoadingCompletedJobs ? (
              <View style={styles.spinnerContainer}>
                <Spinner size={48} />
              </View>
            ) : (
              <FeaturedProjectRadioCardList
                selectedJobId={field.value}
                onChange={field.onChange}
                jobs={allJobsNotAssignedToProjects}
              />
            )}
          </ContentSegment>
        )}
      />

      <Controller
        rules={{ required: true }}
        control={control}
        name="projectName"
        render={({ field }) => (
          <ContentSegment>
            <Typography useVariant="bodySemiBold">
              {'Give the project a name'}
            </Typography>
            <Typography
              useVariant={isDesktop ? 'bodySMRegular' : 'bodySmall'}
              style={styles.helpText}
            >
              {GIVE_PROJECT_NAME_INSTRUCTION_TEXT}
            </Typography>
            <InputField
              testID={TEST_IDS.PROJECT_NAME_INPUT}
              onChangeText={field.onChange}
              value={field.value}
              label="Project name"
              placeholder="Enter name here"
              error={errors.projectName?.message}
            />
          </ContentSegment>
        )}
      />

      <ContentSegment>
        <Typography useVariant="bodySemiBold" style={styles.title}>
          {'When did this project start and end?'}
        </Typography>
        <View style={styles.datePickerContainer}>
          <Controller
            rules={{ required: true }}
            control={control}
            name="startDate"
            render={({ field }) => (
              <DatePicker
                testID={TEST_IDS.START_DATE_INPUT}
                label="Start Date"
                placeholder="Start Date"
                value={field.value ? new Date(field.value) : new Date()}
                onDateChanged={field.onChange}
                onDatePickerShown={noop}
                isInvalid={false}
                minimumDate={minDate}
                maximumDate={maxDate}
              />
            )}
          />
          <Controller
            rules={{ required: true }}
            control={control}
            name="endDate"
            render={({ field }) => (
              <DatePicker
                testID={TEST_IDS.END_DATE_INPUT}
                label="End Date"
                placeholder="End Date"
                value={field.value ? new Date(field.value) : new Date()}
                onDateChanged={field.onChange}
                onDatePickerShown={noop}
                isInvalid={false}
                minimumDate={minDate}
                maximumDate={maxDate}
              />
            )}
          />
        </View>
      </ContentSegment>

      <ContentSegment>
        <Typography useVariant="bodySemiBold">
          {'Approximately how much did the customer pay for the project?'}
        </Typography>
        <Typography
          style={styles.helpText}
          useVariant={isDesktop ? 'bodySMRegular' : 'bodySmall'}
        >
          {COST_INSTRUCTION_TEXT}
        </Typography>
        <Controller
          rules={{ required: true }}
          control={control}
          name="cost"
          render={({ field }) => (
            <CurrencyInput
              testID={TEST_IDS.COST_INPUT}
              label="Total cost"
              onChange={field.onChange}
              value={field.value}
              error={errors.cost?.message}
              isLargeUnits={true}
              useWholeIfPossible={true}
            />
          )}
        />
      </ContentSegment>

      <View style={isDesktop ? styles.footerDesktop : styles.footer}>
        <Button
          isDisabled={!isValid}
          iconStart="arrow-right"
          label="Next: Add photos"
          variant="secondary"
          onPress={handleSubmit(onSubmit, onSubmitWithError)}
        />
      </View>
    </SafeAreaView>
  );
};

ProjectDetails.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette }) => ({
  container: {
    flex: 1,
    backgroundColor: palette.mortarV3.tokenNeutral100,
  },
  containerDesktop: {
    maxWidth: ScreenBreakpoints.Medium,
    marginHorizontal: 'auto',
    width: '100%',
  },
  title: {
    marginBottom: spacing(3),
  },
  datePickerContainer: {
    gap: spacing(2),
  },
  buttonContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  footer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingVertical: spacing(2),
    paddingHorizontal: spacing(3),
  },
  footerDesktop: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingVertical: spacing(2),
  },
  helpText: {
    marginVertical: spacing(1),
    marginBottom: spacing(2.5),
  },
  spinnerContainer: {
    paddingVertical: spacing(3),
    backgroundColor: palette.mortarV3.tokenNeutral0,
    alignItems: 'center',
    justifyContent: 'center',
  },
}));
