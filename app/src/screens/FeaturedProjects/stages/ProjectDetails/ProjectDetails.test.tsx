import {
  cleanup,
  fireEvent,
  render,
  waitFor,
  within,
} from '@testing-library/react-native';
import React from 'react';
import { FulfilmentType, JobStatus } from 'src/data/schemas/api/capi/jobs';
import { BasicCurrencyInput } from '@cat-home-experts/react-native-components';
import { EVENT_TYPE } from 'src/constants.events';
import { createQueryClientWrapper } from 'src/utilities/tanstack-query/tanstack-test-utils';
import { ProjectDetails } from './ProjectDetails';
import { FeaturedProjectsProvider } from '../../hooks/useFeaturedProjectsStages/useFeaturedProjectsStages';
import { FeaturedProjectJobCard } from '../../components/FeaturedProjectJobCard';
import { FEATURED_PROJECT_STAGES } from '../../constants';

const mockUpdateActiveStage = jest.fn();
const mockUpdateStage = jest.fn();
const mockClear = jest.fn();
const mockNavigate = jest.fn();
const mockLogEvent = jest.fn();

const mockJobDetails = {
  id: '123',
  status: JobStatus.COMPLETED,
  address: { postcode: '12345' },
  category: { id: 1, label: 'The category' },
  channelId: '123',
  consumer: { id: '123' },
  createdAt: new Date().toISOString(),
  description: 'test',
  title: 'test',
  tradeMarkedBooked: true,
  tradeViewed: true,
  tradeViewedAt: new Date().toISOString(),
  fulfilmentType: FulfilmentType.ENQUIRY,
} as const;

const mockJob = {
  title: 'test',
  id: '123',
  category: 'The category',
  date: new Date().toISOString(),
  isAssignedToProject: false,
};

jest.mock(
  '../../hooks/useFeaturedProjectsStages/useFeaturedProjectsStages',
  () => ({
    useFeaturedProjectsStages: () => ({
      updateActiveStage: mockUpdateActiveStage,
    }),
    FeaturedProjectsProvider: ({ children }: { children: React.ReactNode }) => (
      <>{children}</>
    ),
  }),
);

jest.mock('src/hooks/useUser', () => ({
  useUserContext: () => ({ companyId: 123456 }),
}));

jest.mock('src/components/primitives/Toaster', () => ({
  showToast: jest.fn(),
}));

jest.mock('src/screens/MarketplaceJobs/hooks', () => ({
  useMarketplaceJobDetails: jest.fn(() => ({
    job: mockJobDetails,
    isLoading: false,
  })),
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({ navigate: mockNavigate }),
}));

jest.mock(
  '../../hooks/useFeaturedProjectsDraft/useFeaturedProjectsDraft',
  () => ({
    useFeaturedProjectsDraft: () => ({
      draftState: null,
      updateStage: mockUpdateStage,
      clear: mockClear,
    }),
  }),
);

jest.mock('../../hooks/useCompletedJobs/useCompletedJobs', () => ({
  useCompletedJobs: () => ({
    data: [mockJob],
    isLoading: false,
  }),
}));

jest.mock('src/services/analytics', () => ({
  logEvent: (...args: unknown[]) => mockLogEvent(...args),
}));

const QueryClientProvider = createQueryClientWrapper();
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider>
    <FeaturedProjectsProvider>{children}</FeaturedProjectsProvider>
  </QueryClientProvider>
);

describe('Screens | FeaturedProjects | Stages | ProjectDetails', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('should render all form fields', () => {
    const { getByText } = render(<ProjectDetails />, { wrapper });
    expect(getByText('Which job was this?')).toBeOnTheScreen();
    expect(getByText('Give the project a name')).toBeOnTheScreen();
    expect(getByText('When did this project start and end?')).toBeOnTheScreen();
    expect(
      getByText('Approximately how much did the customer pay for the project?'),
    ).toBeOnTheScreen();
    expect(getByText('Next: Add photos')).toBeOnTheScreen();
  });

  it('should navigate to next stage when form is valid', async () => {
    const { getByTestId, getAllByTestId, getByText } = render(
      <ProjectDetails />,
      {
        wrapper,
      },
    );

    const singleJobCard = getAllByTestId(
      FeaturedProjectJobCard.testIds!.CONTAINER,
    )[0];

    fireEvent.press(singleJobCard);
    fireEvent.changeText(
      getByTestId(ProjectDetails.testIds!.PROJECT_NAME_INPUT),
      'Test Project',
    );
    fireEvent.changeText(
      getByTestId(ProjectDetails.testIds!.START_DATE_INPUT),
      '2023-01-01',
    );
    fireEvent.changeText(
      getByTestId(ProjectDetails.testIds!.END_DATE_INPUT),
      '2023-01-01',
    );

    const costInput = getByTestId(ProjectDetails.testIds!.COST_INPUT);
    fireEvent.changeText(
      within(costInput).getByTestId(BasicCurrencyInput.testIds.INPUT),
      '1000_00',
    );

    expect(getByText('£1,000')).toBeOnTheScreen();

    const nextButton = getByText('Next: Add photos');

    await waitFor(() => {
      expect(nextButton).not.toBeDisabled();
    });

    fireEvent.press(nextButton);

    await waitFor(() => {
      expect(mockUpdateActiveStage).toHaveBeenCalledWith(
        FEATURED_PROJECT_STAGES.ADD_PHOTOS,
      );
      expect(mockLogEvent).toHaveBeenCalledWith(
        EVENT_TYPE.FEATURED_PROJ_ADD_PHOTOS_CLICKED,
      );
    });
  });
});
