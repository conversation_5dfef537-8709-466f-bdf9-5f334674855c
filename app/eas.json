{"cli": {"version": ">= 10.0.0", "requireCommit": true, "promptToConfigurePushNotifications": false, "appVersionSource": "remote"}, "build": {"base": {"node": "22.14.0", "resourceClass": "large", "android": {"image": "sdk-52"}, "ios": {"image": "macos-sonoma-14.6-xcode-16.1"}}, "dev-client-simulator": {"extends": "base", "env": {"EXPO_PUBLIC_ENVIRONMENT": "staging", "IS_DEV_CLIENT": "false"}, "developmentClient": true, "distribution": "internal", "ios": {"simulator": true}}, "dev-client": {"extends": "base", "env": {"EXPO_PUBLIC_ENVIRONMENT": "staging", "IS_DEV_CLIENT": "false"}, "developmentClient": true, "distribution": "internal"}, "dev-client-tap-to-pay": {"extends": "dev-client"}, "staging": {"extends": "base", "env": {"EXPO_PUBLIC_ENVIRONMENT": "staging"}, "distribution": "internal"}, "production_internal": {"extends": "base", "env": {"EXPO_PUBLIC_ENVIRONMENT": "production"}, "distribution": "internal", "channel": "production"}, "production": {"extends": "base", "env": {"EXPO_PUBLIC_ENVIRONMENT": "production", "IS_APP_STORE_BUILD": "true"}, "autoIncrement": true, "distribution": "store", "channel": "production"}}, "submit": {"production": {"android": {"track": "internal", "applicationId": "com.checkatrade.tradeapp"}, "ios": {"bundleIdentifier": "com.cat.trade", "ascAppId": "1498194074", "appleTeamId": "Y7Q766WK4L"}}}}