variable "organization" {
  type    = string
  default = "organizations/535868630468"
}

variable "project_name" {
  type    = string
  default = ""
}

variable "environment" {
  type    = string
  default = ""
}

variable "region" {
  type        = string
  description = "(Optional) The region the project will sit in."
  default     = "europe-west2"
}

variable "apis" {
  type    = list(string)
  default = []
}

variable "project_static_permissions" {
  type        = map(list(string))
  description = "A map of roles to their list of IAM groups."
  default     = {}
}

variable "project_team" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "project_team_admin" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}



variable "viewers" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "everyone" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

# variable "cloud_build_triggers" {
#   type = map(object({
#     name                         = string
#     description                  = string
#     disabled                     = bool
#     push_trigger_enabled         = bool
#     pull_request_trigger_enabled = bool
#     owner                        = string
#     repo_name                    = string
#     branch_regex                 = string
#     invert_regex                 = bool
#     filename                     = string
#     env_variables                = map(string)
#     included_files_filter        = optional(list(string))
#     excluded_files_filter        = optional(list(string))
#   }))
# }
