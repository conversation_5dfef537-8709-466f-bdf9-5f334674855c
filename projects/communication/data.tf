data "google_active_folder" "cloudbuild_current" {
  display_name = var.folder
  parent       = "organizations/535868630468"
}

data "google_projects" "campaigns_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:campaign*"
}

data "google_projects" "jobs_management_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:jobs-management*"
}

data "google_projects" "email_service_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:email-service*"
}

data "google_projects" "reviews_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:reviews*"
}

data "google_projects" "trade_experience_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:trade-experience*"
}

data "google_projects" "comsumer_area_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:consumer-area*"
}

data "google_projects" "jobs_board_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:jobs-board*"
}

data "google_projects" "salesforce_integration_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:salesforce-integ*"
}
data "google_projects" "directories_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:directories*"
}
data "google_projects" "data_stats_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:data-stats*"
}
data "google_projects" "data_streams_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:data-streams*"
}
data "google_projects" "retool_projects" {
  filter = "name:retool-${module.project.env}*"
}
data "google_service_account" "retool_service_account" {
  account_id = "ext-retool-to-api@${data.google_projects.retool_projects.projects[0].project_id}.iam.gserviceaccount.com"
}