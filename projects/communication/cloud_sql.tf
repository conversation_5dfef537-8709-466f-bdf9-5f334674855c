locals {
  db_name      = "${var.project_name}-db"
  db_user_name = local.db_name
}

resource "random_password" "db" {
  length  = 16
  special = false

  lifecycle {
    ignore_changes = all
  }
}

module "postgres_database" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_sql?ref=3.106.2"

  instance_name    = var.project_name
  database_name    = local.db_name
  database_version = "POSTGRES_16"
  edition          = "ENTERPRISE_PLUS"
  project          = module.project
  region           = var.region

  tier              = var.sql_db_params.tier
  availability_type = "REGIONAL"

  notification_targets            = module.project.default_slack_channel_alerts.datadog_formatted
  cpu_monitor                     = {}
  database_instance_state_monitor = {}

  ipv4_enabled       = false
  private_network_id = module.vpc.network_id

  user_name           = local.db_user_name
  user_password       = random_password.db.result
  disk_size           = var.sql_db_params.disk_size
  disk_autoresize     = true
  backup_enabled      = false
  deletion_protection = true

  depends_on = [module.vpc]
}
