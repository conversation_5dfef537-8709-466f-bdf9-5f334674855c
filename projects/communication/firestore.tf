module "firestore" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/firestore?ref=3.62.1"

  project                   = module.project
  main_notification_targets = module.project.default_slack_channel_alerts.datadog_formatted

  firestore_indexes = []

  backup_enabled = false
  backup_datadog_monitors = [
    {
      notification_targets = module.project.default_slack_channel_alerts.datadog_formatted
    }
  ]

  api_request_count_monitor = {
    enabled = false
  }

  depends_on = [module.app_engine_app]
}