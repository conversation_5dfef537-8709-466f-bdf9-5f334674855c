locals {
  scheduler_jobs = {
    onsiSyncMembers = {
      name                 = "onsi-sync-members"
      description          = "Sync Onsi members"
      schedule             = "0 0 * * *"
      topic_name_id        = "projects/${module.project.id}/topics/onsi-sync-members"
      topic_data_in_base64 = base64encode("{\"page\": 0,\"pageSize\": 1000,\"total\": null}")
      topic_attributes     = { "messageType" = "OnsiSyncMembers" }
    }
    jobSyncCategories = {
      name                 = "job-sync-categories"
      description          = "Sync job categories"
      schedule             = "0 0 * * *"
      topic_name_id        = "projects/${module.project.id}/topics/job-sync-categories"
      topic_data_in_base64 = base64encode("{}")
      topic_attributes     = { "messageType" = "JobSyncCategories" }
    }
    brazeTradeHardBounceSegmentExport = {
      name                 = "braze-trade-hard-bounce-segment-export"
      description          = "Daily export of Trade hard bounce segment"
      schedule             = "0 8 * * *"
      topic_name_id        = "projects/${module.project.id}/topics/braze-segment-events"
      topic_data_in_base64 = base64encode("{\"segment_id\":\"37e47d6d-6951-42af-a3e8-0fb9eb50ef27\",\"fields_to_export\":[\"email\",\"custom_attributes\",\"external_id\",\"first_name\",\"last_name\"],\"workspace\":\"trade\"}")
      topic_attributes     = { "messageType" = "BrazeTradeHardBounceSegmentExport" }
    }
  }
}

module "cloud_scheduler" {
  for_each = local.scheduler_jobs
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_scheduler?ref=3.62.1"

  project     = module.project
  name        = each.value["name"]
  description = each.value["description"]
  schedule    = each.value["schedule"]

  http_target = false

  topic_name_id        = each.value["topic_name_id"]
  topic_data_in_base64 = each.value["topic_data_in_base64"]
  topic_attributes     = each.value["topic_attributes"]
}
