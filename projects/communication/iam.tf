locals {

  service_accounts_for_cloudrun = [
    for srv in local.services : "serviceAccount:run-${lower(srv.name)}@${module.project.id}.iam.gserviceaccount.com"
  ]

  project_computed_permissions = {
    "roles/secretmanager.secretAccessor" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${google_service_account.cloud_build.email}",
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloudrun,
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com"
    ]),
    "roles/iam.serviceAccountUser" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${google_service_account.cloud_build.email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloudrun,
    ]),
    "roles/datastore.indexAdmin" : [
      "serviceAccount:${google_service_account.cloud_build.email}",
    ],
    "roles/datastore.importExportAdmin" : [
      "serviceAccount:${google_service_account.cloud_build.email}",
    ],
    "roles/run.admin" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${google_service_account.cloud_build.email}",
    ],
    "roles/run.invoker" : concat([
      "serviceAccount:campaigns-to-comms-service@${data.google_projects.campaigns_projects.projects[0].project_id}.iam.gserviceaccount.com",
      "serviceAccount:consumer-area-to-comms@${data.google_projects.comsumer_area_projects.projects[0].project_id}.iam.gserviceaccount.com",
      "serviceAccount:${module.project.name}-subscriber@${data.google_projects.trade_experience_projects.projects[0].project_id}.iam.gserviceaccount.com",
      "serviceAccount:${module.project.name}-sub@${data.google_projects.comsumer_area_projects.projects[0].project_id}.iam.gserviceaccount.com",
      "serviceAccount:${module.project.name}-sub@${data.google_projects.salesforce_integration_projects.projects[0].project_id}.iam.gserviceaccount.com",
      "serviceAccount:data-stats-service-publisher@${var.data_stats_project_id}.iam.gserviceaccount.com",
      "serviceAccount:trade-exp-to-comms-service@${data.google_projects.trade_experience_projects.projects[0].project_id}.iam.gserviceaccount.com",
      "serviceAccount:${data.google_service_account.retool_service_account.email}",
    ], compact([local.capi_sa, local.capi_sa_chat, local.capi_sa_new_chat, local.capi_sa_job, local.capi_sa_consumer_delete, local.capi_sa_bff_trade_app, local.capi_shared_sa_bff_trade_app, local.capi_sa_trade_app_bff, local.capi_shared_sa_trade_app_bff, local.capi_sa_bff_consumer_app, local.capi_sa_chat_flag_message, local.capi_sa_firebase_core, local.capi_sa_review_verification_message])),
    "roles/pubsub.publisher" : flatten(concat([
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloudrun,
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:service-${module.project.number}@gs-project-accounts.iam.gserviceaccount.com",
      compact([local.capi_sa_bff_consumer_public]),
    ])),
    "roles/pubsub.subscriber" : flatten([
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloudrun,
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
    ]),
    "roles/artifactregistry.writer" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${google_service_account.cloud_build.email}",
    ],
    "roles/datastore.viewer" : [
      "serviceAccount:${google_service_account.data_insights_airflow.email}",
      "group:<EMAIL>"
    ],
    "roles/storage.objectViewer" : [
      "serviceAccount:${google_service_account.comms_cloud_run.email}",
    ]
    "roles/apigateway.admin" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${google_service_account.cloud_build.email}",
    ],
    "roles/cloudtasks.enqueuer" : flatten([
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloudrun,
    ]),
    "roles/cloudfunctions.developer" : [
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ],
    "roles/firebase.admin" : flatten([
      local.service_accounts_for_cloudrun,
      "serviceAccount:${module.project.number}-<EMAIL>",
    ]),
    "roles/logging.logWriter" : flatten([
      local.service_accounts_for_cloudrun,
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:build-communication-api@${module.project.id}.iam.gserviceaccount.com",
      "serviceAccount:build-deployment-metrics@${module.project.id}.iam.gserviceaccount.com",
      "serviceAccount:build-slack-notifier@${module.project.id}.iam.gserviceaccount.com",
    ]),
    "roles/datastore.owner" : flatten([
      local.service_accounts_for_cloudrun,
      "serviceAccount:${module.project.number}-<EMAIL>",
    ]),
    "roles/cloudprofiler.agent" : flatten([
      local.service_accounts_for_cloudrun,
      "serviceAccount:${module.project.number}-<EMAIL>",
    ]),
    "roles/iam.serviceAccountTokenCreator" : flatten([
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com"
    ])
    "roles/cloudsql.client" : flatten([
      "serviceAccount:${module.project.number}-<EMAIL>",
    ]),
  }
}

module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project  = module.project
  humans   = var.project_static_permissions
  machines = local.project_computed_permissions
}

resource "google_service_account_iam_binding" "slack_notifier" {
  members            = ["serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com"]
  role               = "roles/iam.serviceAccountUser"
  service_account_id = "projects/${module.project.id}/serviceAccounts/${module.project.number}-<EMAIL>"
}

resource "google_service_account" "data_insights_airflow" {
  project      = module.project.id
  account_id   = "data-insights-airflow"
  display_name = "Used by Airflow pipelines"
}
resource "google_service_account" "comms_cloud_run" {
  project      = module.project.id
  account_id   = "cloud-run-communication-api"
  display_name = "cloud-run-communication-api"
}

resource "google_project_iam_custom_role" "braze_message_archiving_role_create_objects" {
  project     = module.project.id
  role_id     = "BrazeMessageArchivingRole"
  title       = "Custom role for Braze Message Archiving"
  description = "To allow Braze to archive messages we set up a custom role that allows the service account to create objects in the bucket and get buckets"
  permissions = ["storage.buckets.get", "storage.objects.create"]
}

resource "google_service_account" "braze_message_archiving_service_account" {
  project      = module.project.id
  account_id   = "braze-message-archiving"
  display_name = "braze-message-archiving"
}

resource "google_service_account_iam_binding" "braze_message_archiving" {
  members            = ["serviceAccount:${google_service_account.braze_message_archiving_service_account.email}"]
  role               = google_project_iam_custom_role.braze_message_archiving_role_create_objects.name
  service_account_id = google_service_account.braze_message_archiving_service_account.id
  depends_on = [
    google_project_iam_custom_role.braze_message_archiving_role_create_objects,
    google_project_iam_custom_role.braze_message_archiving_role_create_objects
  ]
}
