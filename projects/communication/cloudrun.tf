locals {
  default_docker_image = "us-docker.pkg.dev/cloudrun/container/hello"
  default_cloud_run_env_variables = [
    {
      name  = "PROJECT_ID"
      value = module.project.id
    },
    {
      name  = "GCP_REGION"
      value = "europe-west2"
    },
    {
      name  = "VOUCHER_BUCKET_NAME"
      value = module.bucket_vouchers_upload.name
    },
  ]

  services = {
    communication-api = {
      name           = "communication-api"
      repo_name      = "gcp-communication"
      container_port = 8080
      secret_variables = [
        {
          name      = "KALEYRA_PASSWORD"
          secret_id = "kaleyra-password"
        },
        {
          name      = "SENDINBLUE_API_KEY"
          secret_id = "sendinblue-api-key"
        },
        {
          name      = "BRAZE_API_KEY"
          secret_id = "braze-api-key"
        },
        {
          name      = "BRAZE_CONSUMER_API_KEY"
          secret_id = "braze-consumer-api-key"
        },
        {
          name      = "BRAZE_UNSUBSCRIBE_SALT"
          secret_id = "braze-unsubscribe-salt"
        },
        {
          name      = "SALESFORCE_CLIENT_SECRET"
          secret_id = "salesforce-client-secret"
        },
        {
          name      = "SALESFORCE_PASSWORD"
          secret_id = "salesforce-password"
        },
        {
          name      = "SALESFORCE_SECURITY_TOKEN"
          secret_id = "salesforce-security-token"
        },
        {
          name      = "BRAZE_CONNECTED_CONTENT_USER_PASSWORD"
          secret_id = "braze-connected-content-user-password"
        },
        {
          name      = "ONSI_API_KEY"
          secret_id = "onsi-api-key"
        },
        {
          name      = "ENTRA_TENANT_ID"
          secret_id = "entra-tenant-id"
        },
        {
          name      = "ENTRA_CLIENT_ID"
          secret_id = "entra-client-id"
        },
        {
          name      = "ENTRA_CLIENT_SECRET"
          secret_id = "entra-client-secret"
        },
        {
          name      = "BRAZE_INTERNAL_API_KEY"
          secret_id = "braze-internal-api-key"
        },
        {
          name      = "BRAZE_SERVICES_API_KEY"
          secret_id = "braze-services-api-key"
        },
        {
          name      = "DATADOG_API_KEY"
          secret_id = "datadog-api-key"
        },
        {
          name      = "POSTGRES_CONNECTION_STRING"
          secret_id = "db-connection-string"
        },
        {
          name      = "BOB_API_USER_ID"
          secret_id = "bob-api-user-id"
        },
        {
          name      = "BOB_API_TOKEN"
          secret_id = "bob-api-token"
        },
        {
          name      = "BOB_WEBHOOK_SECRET"
          secret_id = "bob-webhook-secret"
        }
      ]
      env_variables = [
        {
          name  = "EVENT_TOPIC"
          value = "event"
        },
        {
          name  = "SEND_EMAIL_TOPIC"
          value = "send-email"
        },
        {
          name  = "SEND_SMS_TOPIC"
          value = "send-sms"
        },
        {
          name  = "SEND_PUSH_TOPIC"
          value = "send-push"
        },
        {
          name  = "TRADE_USER_EVENT_TOPIC"
          value = "trade-user-event"
        },
        {
          name  = "EMAIL_SERVICE_PROJECT_ID"
          value = "${data.google_projects.email_service_projects.projects[0].project_id}"
        },
        {
          name  = "TRADE_EXPERIENCE_PROJECT_ID"
          value = "${data.google_projects.trade_experience_projects.projects[0].project_id}"
        },
        {
          name  = "EMAIL_SERVICE_TOPIC"
          value = "send-email"
        },
        {
          name  = "SMS_STATUS_TOPIC"
          value = "sms-status"
        },
        {
          name  = "EMAIL_STATUS_TOPIC"
          value = "email-status"
        },
        {
          name  = "TASKS_QUEUE_NAME"
          value = "communication-tasks"
        },
        {
          name  = "TASKS_IMPORT_QUEUE_NAME"
          value = "communication-import-tasks"
        },
        {
          name  = "TASKS_IMPORT_PROCESSOR_QUEUE_NAME"
          value = "communication-import-tasks-processor"
        },
        {
          name  = "APP_SERVICE_ACCOUNT_EMAIL"
          value = "${module.project.number}-<EMAIL>"
        },
        {
          name  = "TRUSTPILOT_EMAIL",
          value = "<EMAIL>"
        },
        {
          name  = "TRUSTPILOT_DAILY_MAX_EMAILS",
          value = 2001
        },
        {
          name  = "TEST_FLIGHT_USER_DATA_BUCKET_NAME",
          value = module.bucket_test_flight_user_data.name
        },
        {
          name  = "EMAIL_SUBSCRIBE_MIGRATION_USER_DATA_BUCKET_NAME",
          value = module.bucket_email_subscribe_migration_user_data.name
        },
        {
          name  = "IMPORT_SALESFORCE_LEADS_DATA_BUCKET_NAME",
          value = module.bucket_import_salesforce_leads_user_data.name
        },
        {
          name  = "SALESFORCE_EVENT_TOPIC"
          value = "salesforce"
        },
        {
          name  = "UNSUBSCRIBE_EVENT_TOPIC"
          value = "unsubscribe-requests"
        }
      ]
    }
    braze-processor = {
      name           = "braze-processor"
      repo_name      = "gcp-communication"
      container_port = 8080
      secret_variables = [
        {
          name      = "KALEYRA_PASSWORD"
          secret_id = "kaleyra-password"
        },
        {
          name      = "SENDINBLUE_API_KEY"
          secret_id = "sendinblue-api-key"
        },
        {
          name      = "BRAZE_API_KEY"
          secret_id = "braze-api-key"
        },
        {
          name      = "BRAZE_CONSUMER_API_KEY"
          secret_id = "braze-consumer-api-key"
        },
        {
          name      = "BRAZE_UNSUBSCRIBE_SALT"
          secret_id = "braze-unsubscribe-salt"
        },
        {
          name      = "SALESFORCE_CLIENT_SECRET"
          secret_id = "salesforce-client-secret"
        },
        {
          name      = "SALESFORCE_PASSWORD"
          secret_id = "salesforce-password"
        },
        {
          name      = "SALESFORCE_SECURITY_TOKEN"
          secret_id = "salesforce-security-token"
        },
        {
          name      = "BRAZE_CONNECTED_CONTENT_USER_PASSWORD"
          secret_id = "braze-connected-content-user-password"
        },
        {
          name      = "ONSI_API_KEY"
          secret_id = "onsi-api-key"
        },
        {
          name      = "ENTRA_TENANT_ID"
          secret_id = "entra-tenant-id"
        },
        {
          name      = "ENTRA_CLIENT_ID"
          secret_id = "entra-client-id"
        },
        {
          name      = "ENTRA_CLIENT_SECRET"
          secret_id = "entra-client-secret"
        },
        {
          name      = "BRAZE_INTERNAL_API_KEY"
          secret_id = "braze-internal-api-key"
        },
        {
          name      = "BRAZE_SERVICES_API_KEY"
          secret_id = "braze-services-api-key"
        },
        {
          name      = "DATADOG_API_KEY"
          secret_id = "datadog-api-key"
        },
        {
          name      = "POSTGRES_CONNECTION_STRING"
          secret_id = "db-connection-string"
        },
        {
          name      = "BOB_API_USER_ID"
          secret_id = "bob-api-user-id"
        },
        {
          name      = "BOB_API_TOKEN"
          secret_id = "bob-api-token"
        },
        {
          name      = "BOB_WEBHOOK_SECRET"
          secret_id = "bob-webhook-secret"
        }
      ]
      env_variables = [
        {
          name  = "EVENT_TOPIC"
          value = "event"
        },
        {
          name  = "SEND_EMAIL_TOPIC"
          value = "send-email"
        },
        {
          name  = "SEND_SMS_TOPIC"
          value = "send-sms"
        },
        {
          name  = "SEND_PUSH_TOPIC"
          value = "send-push"
        },
        {
          name  = "TRADE_USER_EVENT_TOPIC"
          value = "trade-user-event"
        },
        {
          name  = "EMAIL_SERVICE_PROJECT_ID"
          value = "${data.google_projects.email_service_projects.projects[0].project_id}"
        },
        {
          name  = "TRADE_EXPERIENCE_PROJECT_ID"
          value = "${data.google_projects.trade_experience_projects.projects[0].project_id}"
        },
        {
          name  = "EMAIL_SERVICE_TOPIC"
          value = "send-email"
        },
        {
          name  = "SMS_STATUS_TOPIC"
          value = "sms-status"
        },
        {
          name  = "EMAIL_STATUS_TOPIC"
          value = "email-status"
        },
        {
          name  = "TASKS_QUEUE_NAME"
          value = "communication-tasks"
        },
        {
          name  = "TASKS_IMPORT_QUEUE_NAME"
          value = "communication-import-tasks"
        },
        {
          name  = "TASKS_IMPORT_PROCESSOR_QUEUE_NAME"
          value = "communication-import-tasks-processor"
        },
        {
          name  = "APP_SERVICE_ACCOUNT_EMAIL"
          value = "${module.project.number}-<EMAIL>"
        },
        {
          name  = "TRUSTPILOT_EMAIL",
          value = "<EMAIL>"
        },
        {
          name  = "TRUSTPILOT_DAILY_MAX_EMAILS",
          value = 2001
        },
        {
          name  = "TEST_FLIGHT_USER_DATA_BUCKET_NAME",
          value = module.bucket_test_flight_user_data.name
        },
        {
          name  = "EMAIL_SUBSCRIBE_MIGRATION_USER_DATA_BUCKET_NAME",
          value = module.bucket_email_subscribe_migration_user_data.name
        },
        {
          name  = "IMPORT_SALESFORCE_LEADS_DATA_BUCKET_NAME",
          value = module.bucket_import_salesforce_leads_user_data.name
        },
        {
          name  = "SALESFORCE_EVENT_TOPIC"
          value = "salesforce"
        },
        {
          name  = "UNSUBSCRIBE_EVENT_TOPIC"
          value = "unsubscribe-requests"
        }
      ]
    }
  }
}

# Start Cloud Run instance(s)
module "cloud_run" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars?ref=3.74.0"

  for_each = local.services

  name                  = each.value["name"]
  gcr_image             = local.default_docker_image
  container_port        = each.value["container_port"]
  project               = module.project
  region                = var.region
  memory                = var.cloud_run_parameters[each.key].memory
  cpu                   = var.cloud_run_parameters[each.key].cpu
  container_concurrency = var.cloud_run_parameters[each.key].container_concurrency

  env_variables    = concat(each.value["env_variables"], local.default_cloud_run_env_variables, var.cloud_run_env_variables[each.key])
  secret_variables = each.value["secret_variables"]
  template_metadata_annotations = {
    "autoscaling.knative.dev/initialScale"    = var.cloud_run_parameters[each.key].initial_scale
    "autoscaling.knative.dev/maxScale"        = var.cloud_run_parameters[each.key].max_scale
    "autoscaling.knative.dev/minScale"        = var.cloud_run_parameters[each.key].min_scale
    "run.googleapis.com/cloudsql-instances"   = module.postgres_database.instance.connection_name
    "run.googleapis.com/vpc-access-connector" = module.serverless_connector.connector_id
    "run.googleapis.com/vpc-access-egress"    = "private-ranges-only"
  }

  # autodeploy_build_trigger_names = {
  #   development = {
  #     trigger_name = "Feature"
  #   }
  #   staging = {
  #     trigger_name = "PR-to-Main"
  #   }
  #   production = {
  #     trigger_name = "Main"
  #   }
  # }

  custom_labels = {
    "managed-by"   = "gcp-cloud-build-deploy-cloud-run",
    "repo-name"    = each.value["repo_name"],
    "service-name" = each.value["name"]
  }
}

module "cloud_run_mem_alert" {
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/memory_alerts?ref=3.74.0"
  for_each = module.cloud_run

  alert_duration_memory  = var.alert_duration_memory
  threshold_value_memory = var.threshold_value_memory
  trigger_count_memory   = var.trigger_count_memory
  project                = module.project
  service_name           = each.value["service_name"]
  env                    = var.environment
}

module "cloud_run_cpu_alert" {
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/cpu_alerts?ref=3.74.0"
  for_each = module.cloud_run

  alert_duration_cpu  = var.alert_duration_cpu
  threshold_value_cpu = var.threshold_value_cpu
  trigger_count_cpu   = var.trigger_count_cpu
  project             = module.project
  service_name        = each.value["service_name"]
  env                 = var.environment
}

module "cloud_run_response_codes_alert" {
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/response_code_alerts?ref=3.74.0"
  for_each = module.cloud_run

  alert_duration_response_codes  = var.alert_duration_response_codes
  threshold_value_response_codes = var.threshold_value_response_codes
  trigger_count_response_codes   = var.trigger_count_response_codes
  project                        = module.project
  service_name                   = each.value["service_name"]
  env                            = var.environment
}
