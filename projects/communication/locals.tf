locals {
  sa_map = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa = lookup(local.sa_map, var.environment, "")
  sa_map_chat = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_chat = lookup(local.sa_map_chat, var.environment, "")
  sa_map_new_chat = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_new_chat = lookup(local.sa_map_new_chat, var.environment, "")
  sa_map_chat_flag_message = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_chat_flag_message = lookup(local.sa_map_chat_flag_message, var.environment, "")
  sa_map_job = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_job = lookup(local.sa_map_job, var.environment, "")
  sa_map_consumer_delete = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_consumer_delete = lookup(local.sa_map_consumer_delete, var.environment, "")
  sa_map_bff_trade_app = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  shared_sa_map_core_consumer_bff = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_bff_trade_app        = lookup(local.sa_map_bff_trade_app, var.environment, "")
  capi_shared_sa_bff_trade_app = lookup(local.shared_sa_map_core_consumer_bff, var.environment, "")
  sa_map_trade_app_bff = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  shared_sa_map_core_trade_bff = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_trade_app_bff        = lookup(local.sa_map_trade_app_bff, var.environment, "")
  capi_shared_sa_trade_app_bff = lookup(local.shared_sa_map_core_trade_bff, var.environment, "")
  sa_map_bff_consumer_app = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_bff_consumer_app = lookup(local.sa_map_bff_consumer_app, var.environment, "")
  sa_map_bff_consumer_public = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_bff_consumer_public = lookup(local.sa_map_bff_consumer_public, var.environment, "")
  sa_map_firebase_core = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_firebase_core = lookup(local.sa_map_firebase_core, var.environment, "")

  capi_chat_topics = {
    staging    = "projects/capi-staging-27323/topics/chat-new-message"
    production = "projects/capi-production-21756/topics/chat-new-message"
  }
  capi_chat_topic = lookup(local.capi_chat_topics, var.environment, "")

  consumer_profile_topics = {
    staging    = "projects/capi-staging-27323/topics/consumer-profile"
    production = "projects/capi-production-21756/topics/consumer-profile"
  }
  consumer_profile_topic = lookup(local.consumer_profile_topics, var.environment, "")

  consumer_profile_delete_topics = {
    staging    = "projects/capi-staging-27323/topics/consumer-delete"
    production = "projects/capi-production-21756/topics/consumer-delete"
  }
  consumer_profile_delete_topic = lookup(local.consumer_profile_delete_topics, var.environment, "")

  payment_trade_onboarding_status_topics = {
    staging    = "projects/capi-staging-27323/topics/payment-trade-onboarding-status"
    production = "projects/capi-production-21756/topics/payment-trade-onboarding-status"
  }
  payment_trade_onboarding_status_topic = lookup(local.payment_trade_onboarding_status_topics, var.environment, "")

  payment_request_status_topics = {
    staging    = "projects/capi-staging-27323/topics/payment-request-status"
    production = "projects/capi-production-21756/topics/payment-request-status"
  }
  payment_request_status_topic = lookup(local.payment_request_status_topics, var.environment, "")

  payment_request_reminders_topics = {
    staging    = "projects/capi-staging-27323/topics/payment-request-reminders"
    production = "projects/capi-production-21756/topics/payment-request-reminders"
  }
  payment_request_reminders_topic = lookup(local.payment_request_reminders_topics, var.environment, "")

  payment_payout_status_topics = {
    staging    = "projects/capi-staging-27323/topics/payment-payout-status"
    production = "projects/capi-production-21756/topics/payment-payout-status"
  }
  payment_payout_status_topic = lookup(local.payment_payout_status_topics, var.environment, "")

  job_offers_topics = {
    staging    = "projects/capi-staging-27323/topics/offers"
    production = "projects/capi-production-21756/topics/offers"
  }
  job_offers_topic = lookup(local.job_offers_topics, var.environment, "")

  reviews_topics = {
    staging    = "projects/capi-staging-27323/topics/reviews"
    production = "projects/capi-production-21756/topics/reviews"
  }
  reviews_topic = lookup(local.reviews_topics, var.environment, "")

  review_request_topics = {
    staging    = "projects/capi-staging-27323/topics/review-request"
    production = "projects/capi-production-21756/topics/review-request"
  }
  review_request_topic = lookup(local.review_request_topics, var.environment, "")

  secure_contacts_review_request_topics = {
    staging    = "projects/capi-staging-27323/topics/secure-contacts-review-request"
    production = "projects/capi-production-21756/topics/secure-contacts-review-request"
  }
  secure_contacts_review_request_topic = lookup(local.secure_contacts_review_request_topics, var.environment, "")

  mdp_campaign_events_topics = {
    staging    = "projects/capi-staging-27323/topics/mdp-campaign-events"
    production = "projects/capi-production-21756/topics/mdp-campaign-events"
  }
  mdp_campaign_events_topic = lookup(local.mdp_campaign_events_topics, var.environment, "")

  sa_map_review_verification_message = {
    staging    = "serviceAccount:<EMAIL>"
    production = "serviceAccount:<EMAIL>"
  }
  capi_sa_review_verification_message = lookup(local.sa_map_review_verification_message, var.environment, "")

  handyman_labs_notifications_topics = {
    staging    = "projects/capi-staging-27323/topics/core-handyman-labs-notifications"
    production = "projects/capi-production-21756/topics/core-handyman-labs-notifications"
  }
  handyman_labs_notifications_topic = lookup(local.handyman_labs_notifications_topics, var.environment, "")
  employee_consent_topics = {
    staging    = "projects/capi-staging-27323/topics/employee-consent"
    production = "projects/capi-production-21756/topics/employee-consent"
  }
  employee_consent_topic = lookup(local.employee_consent_topics, var.environment, "")
}
