resource "google_cloud_tasks_queue" "default" {
  name     = "communication-tasks"
  location = "europe-west2"
  project  = module.project.id

  rate_limits {
    max_concurrent_dispatches = 400
    max_dispatches_per_second = 400
  }
}

resource "google_cloud_tasks_queue" "imports" {
  name     = "communication-import-tasks"
  location = "europe-west2"
  project  = module.project.id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }
}

resource "google_cloud_tasks_queue" "import-processor" {
  name     = "communication-import-tasks-processor"
  location = "europe-west2"
  project  = module.project.id

  rate_limits {
    max_concurrent_dispatches = 400
    max_dispatches_per_second = 400
  }
}
