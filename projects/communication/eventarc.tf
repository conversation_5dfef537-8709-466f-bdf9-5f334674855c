locals {
  cloud_run_eventarc_trigger = {
    communication-api-test-flight-data = {
      cloud_run_name    = module.cloud_run["communication-api"].service_name
      cloud_run_path    = "/consumer/test-flight-data-added"
      payload_source    = "storage"
      eventarc_name     = "${module.cloud_run["communication-api"].service_name}-test-flight"
      eventarc_location = module.bucket_test_flight_user_data.location
      cloud_run_region  = var.region
      iam_audit_enabled = false
      iam_audit_name    = ""
      matching_criteria = {
        type   = "google.cloud.storage.object.v1.finalized"
        bucket = module.bucket_test_flight_user_data.name
      }
    }
    communication-api-import-salesforce-leads = {
      cloud_run_name    = module.cloud_run["communication-api"].service_name
      cloud_run_path    = "/lead-batch-import"
      payload_source    = "storage"
      eventarc_name     = "${module.cloud_run["communication-api"].service_name}-import-salesforce-leads"
      eventarc_location = module.bucket_import_salesforce_leads_user_data.location
      cloud_run_region  = var.region
      iam_audit_enabled = false
      iam_audit_name    = ""
      matching_criteria = {
        type   = "google.cloud.storage.object.v1.finalized"
        bucket = module.bucket_import_salesforce_leads_user_data.name
      }
    }
    communication-api-email-subscribe-migration = {
      cloud_run_name    = module.cloud_run["communication-api"].service_name
      cloud_run_path    = "/consumer/email-subscribe-migration-user-data-added"
      payload_source    = "storage"
      eventarc_name     = "${module.cloud_run["communication-api"].service_name}-email-subscribe-migration"
      eventarc_location = module.bucket_email_subscribe_migration_user_data.location
      cloud_run_region  = var.region
      iam_audit_enabled = false
      iam_audit_name    = ""
      matching_criteria = {
        type   = "google.cloud.storage.object.v1.finalized"
        bucket = module.bucket_email_subscribe_migration_user_data.name
      }
    }
    communication-api-archived-braze-messages = {
      cloud_run_name    = module.cloud_run["braze-processor"].service_name
      cloud_run_path    = "/braze/archived-messages"
      payload_source    = "storage"
      eventarc_name     = "${module.cloud_run["braze-processor"].service_name}-archived-braze-messages"
      eventarc_location = var.region
      cloud_run_region  = var.region
      iam_audit_enabled = false
      iam_audit_name    = ""
      matching_criteria = {
        type               = "google.cloud.storage.object.v1.finalized"
        bucket             = "${module.project.id}.appspot.com"
        object_name_prefix = "braze/trades/archive/sent_messages/"
      }
      matching_criteria = {
        type               = "google.cloud.storage.object.v1.finalized"
        bucket             = "${module.project.id}.appspot.com"
        object_name_prefix = "braze/consumers/archive/sent_messages/"
      }
    }
    communication-api-directories-upload = {
      cloud_run_name    = module.cloud_run["communication-api"].service_name
      cloud_run_path    = "/directories/upload"
      payload_source    = "storage"
      eventarc_name     = "${module.cloud_run["communication-api"].service_name}-directories-upload"
      eventarc_location = module.bucket_directories_upload.location
      cloud_run_region  = var.region
      iam_audit_enabled = false
      iam_audit_name    = ""
      matching_criteria = {
        type   = "google.cloud.storage.object.v1.finalized"
        bucket = module.bucket_directories_upload.name
      }
    }
    communication-api-vouchers-upload = {
      cloud_run_name    = module.cloud_run["communication-api"].service_name
      cloud_run_path    = "/vouchers/upload"
      payload_source    = "storage"
      eventarc_name     = "${module.cloud_run["communication-api"].service_name}-vouchers-upload"
      eventarc_location = module.bucket_vouchers_upload.location
      cloud_run_region  = var.region
      iam_audit_enabled = false
      iam_audit_name    = ""
      matching_criteria = {
        type   = "google.cloud.storage.object.v1.finalized"
        bucket = module.bucket_vouchers_upload.name
      }
    }
    braze-segments-export = {
      cloud_run_name    = module.cloud_run["braze-processor"].service_name
      cloud_run_path    = "/braze/segment-export/upload"
      payload_source    = "storage"
      eventarc_name     = "${module.cloud_run["braze-processor"].service_name}-segment-export-upload"
      eventarc_location = var.region
      cloud_run_region  = var.region
      iam_audit_enabled = false
      iam_audit_name    = ""
      matching_criteria = {
        type   = "google.cloud.storage.object.v1.finalized"
        bucket = "${module.project.id}.appspot.com"
      }
    }
  }
}

module "event_arc" {
  source                   = "git::**************:cat-home-experts/terraform-modules.git//gcp/eventarc?ref=3.63.5"
  for_each                 = local.cloud_run_eventarc_trigger
  iam_audit_enabled        = each.value["iam_audit_enabled"]
  receiver_service_account = "${module.project.number}-<EMAIL>"
  project                  = module.project
  destination = {
    payload_source    = each.value["payload_source"]
    cloud_run_name    = each.value["cloud_run_name"]
    cloud_run_path    = each.value["cloud_run_path"]
    cloud_run_region  = each.value["cloud_run_region"]
    matching_criteria = each.value["matching_criteria"]
  }
  iam_audit_name    = each.value["iam_audit_name"]
  eventarc_name     = "${each.value["eventarc_name"]}-trigger"
  eventarc_location = each.value["eventarc_location"]
}
