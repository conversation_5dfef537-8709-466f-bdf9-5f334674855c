locals {
  substitutions = {
    _GCP_REGION                  = "europe-west2"
    _SERVICE_NAME                = "communication-api"
    _TRADE_EXPERIENCE_PROJECT_ID = data.google_projects.trade_experience_projects.projects[0].project_id
  }
}

module "cloud_build_triggers" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_build?ref=3.69.1"

  for_each = var.cloud_build_triggers

  project = module.project

  description                  = each.value["description"]
  name                         = each.value["name"]
  create_service_account       = false
  service_account_key          = "build-communication-api"
  env_variables                = merge(local.substitutions, each.value.env_variables)
  disabled                     = each.value["disabled"]
  push_trigger_enabled         = each.value["push_trigger_enabled"]
  pull_request_trigger_enabled = each.value["pull_request_trigger_enabled"]
  owner                        = each.value["owner"]
  repo_name                    = each.value["repo_name"]
  branch_regex                 = each.value["branch_regex"]
  invert_regex                 = each.value["invert_regex"]
  filename                     = each.value["filename"]
  included_files_filter        = each.value["included_files_filter"]
  excluded_files_filter        = each.value["excluded_files_filter"]
}

module "slackbot_trigger" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/slackbot_trigger?ref=3.65.1"

  project = module.project

  env_variables = {
    _GCP_REGION   = "europe-west2"
    _TRIGGER_NAME = "slack-notifier"
  }
}

module "deployment_metrics_trigger" {
  source = "git::**************:cat-home-experts/terraform-modules.git//checkatrade/deployment_metrics_trigger?ref=3.63.4"

  project = module.project
}