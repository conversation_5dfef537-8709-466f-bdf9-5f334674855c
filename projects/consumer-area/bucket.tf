module "firebase_auth_export_storage_bucket" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/storage_bucket?ref=3.67.2"

  project = module.project

  legacy_name                         = "${module.project.id}-firebase-auth-export"
  location                            = "EU"
  storage_class                       = "STANDARD"
  force_destroy                       = false
  versioning_enabled                  = false
  backup_enabled                      = false
  uniform_bucket_level_access_enabled = false
  lifecycle_rules = [{
    condition = {
      age = 30
    }
  }]

  custom_labels = {
    terraform = "true"
  }
}

module "sib_contact_export_storage_bucket" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/storage_bucket?ref=3.67.2"

  project = module.project

  legacy_name                         = "${module.project.id}-sib-contact-export"
  location                            = "EU"
  storage_class                       = "STANDARD"
  force_destroy                       = false
  versioning_enabled                  = false
  uniform_bucket_level_access_enabled = false
  backup_enabled                      = false
  lifecycle_rules = [{
    condition = {
      age = 30
    }
  }]

  custom_labels = {
    terraform = "true"
  }
}
