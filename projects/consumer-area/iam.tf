locals {
  # Transitioning to the new v2 IAM module below
  service_accounts_for_cloud_build_triggers = [
    for trg in var.cloudbuild_triggers : "serviceAccount:build-${lower(trg.name)}@${module.project.id}.iam.gserviceaccount.com"
  ]
  service_accounts_for_cloud_run_services = [
    for service in module.cloud_run : "serviceAccount:run-${lower(service.service_name)}@${module.project.id}.iam.gserviceaccount.com"
  ]

  project_computed_permissions = {
    "roles/iam.serviceAccountUser" : flatten([
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.slackbot_trigger.cloud_build_sa_email}",
      local.service_accounts_for_cloud_build_triggers,
      local.service_accounts_for_cloud_run_services
    ]),
    "roles/run.admin" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
    ],
    "roles/serviceusage.apiKeysAdmin" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
    ],
    "roles/pubsub.admin" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
    ],
    "roles/secretmanager.secretAccessor" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/secretmanager.secretVersionAdder" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/cloudfunctions.developer" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/cloudfunctions.invoker" : flatten([
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloud_run_services,
    ]),
    "roles/run.invoker" : flatten([
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${var.reviews_project_number}-<EMAIL>",
      local.service_accounts_for_cloud_run_services,
      "serviceAccount:run-reviews-backend@${data.google_projects.reviews_projects.projects[0].project_id}.iam.gserviceaccount.com",
    ]),
    "roles/pubsub.publisher" : flatten([
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloud_run_services,
    ]),
    "roles/pubsub.subscriber" : [
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
    ],
    "roles/iam.serviceAccountTokenCreator" : flatten([
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      local.service_accounts_for_cloud_run_services,
      "serviceAccount:${google_service_account.firebase_core.email}"
    ]),
    "roles/datastore.user" : [
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
    ],
    "roles/datastore.owner" : [
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
    ],
    "roles/datastore.viewer" : [
      # Setting this perm here is temporary because of our use of two authoritative modules
      #   and datastore.viewer needing to be granted to a "machine" and a "human" (i.e. a group)
      # We should remove it when we remove the use of the group_authoritative module
      "serviceAccount:${google_service_account.data_insights_airflow.email}",
      "serviceAccount:run-communication-api@${data.google_projects.communication_projects.projects[0].project_id}.iam.gserviceaccount.com",
      "group:<EMAIL>"
    ],
    "roles/datastore.importExportAdmin" : [
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com"
    ],
    "roles/storage.admin" : flatten([
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/identityplatform.viewer" : [
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
    ],
    "roles/appengine.appAdmin" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/serviceusage.serviceUsageAdmin" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/cloudbuild.builds.builder" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.slackbot_trigger.cloud_build_sa_email}",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/apigateway.admin" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/firebase.admin" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      local.service_accounts_for_cloud_run_services,
      local.service_accounts_for_cloud_build_triggers,
      var.capi_consumer_app_service_accounts
    ]),
    "roles/serviceusage.serviceUsageConsumer" : flatten([
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
      local.service_accounts_for_cloud_build_triggers,
      "serviceAccount:${google_service_account.firebase_core.email}"
    ]),
    "roles/servicemanagement.admin" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com"
    ],
    "roles/logging.logWriter" : flatten([
      "serviceAccount:${module.slackbot_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      local.service_accounts_for_cloud_build_triggers
    ]),
    "roles/firebase.viewer" : [
      "serviceAccount:${google_service_account.firebase_core.email}"
    ],
    "roles/iam.workloadIdentityUser" : var.core_firebase_service_accounts
  }

  cloud_function_invokers = {
    "cloudGroup-optInToMarketing" : [
      "allUsers"
    ],
    "cloudGroup-updateUserData" : [
      "serviceAccount:content-api-to-consumer-area@${var.content_api_project_id}.iam.gserviceaccount.com",
      "serviceAccount:reviews-publisher@${var.reviews_project_id}.iam.gserviceaccount.com"
    ]
  }
}

# At the end, there should be only one module to rule them all (the roles).
# Migration path: each time a role is being added under this module sight, it needs to be removed from anywhere else.
# This role can be listed in 2 places though:
#   - in the 'var.project_static_permissions' map
#   - in the 'local.project_computed_permissions' map
module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project  = module.project
  humans   = var.project_static_permissions
  machines = local.project_computed_permissions

}

module "cloud_functions_iam_authoritative_invokers" {
  for_each = merge(local.cloud_function_invokers, var.cloud_function_invokers)
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/cloud_function_authoritative?ref=3.62.1"

  project        = module.project
  cloud_function = each.key
  role           = "roles/cloudfunctions.invoker"
  members        = each.value
}

resource "google_service_account" "data_insights_airflow" {
  project      = module.project.id
  account_id   = "data-insights-airflow"
  display_name = "Used by Airflow pipelines"
}

resource "google_service_account" "firebase_core" {
  project      = module.project.id
  account_id   = "firebase-core"
  display_name = "Used by Core platform to provide Firebase tokens for frontend"
}
