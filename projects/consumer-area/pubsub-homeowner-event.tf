locals {
  homeowner_event_max_delivery_attempts = 10
  homeowner_event_retry_policy          = { minimum_backoff = "10s", maximum_backoff = "600s" }

  topic = "homeowner-event"
  subscriptions = [
    {
      name                 = "communication-homeowner-event"
      filter               = "attributes.messageType = \"HomeownerVerificationRequested\""
      notification_targets = var.communication_api_notification_targets

      push_to = {
        project           = data.google_projects.communication_projects.projects[0]
        cloud_run_service = "communication-api",
        path              = "/handle-event"
      }
    },
    {
      name                 = "homeowner-review-verification-event"
      filter               = "attributes.ce-type = \"HomeownerVerificationRequested\""
      notification_targets = var.communication_api_notification_targets

      push_to = {
        project           = data.google_projects.communication_projects.projects[0]
        cloud_run_service = "communication-api",
        path              = "/handle-event"
      }
    },
    {
      name                 = "homeowner-trade-interested-event"
      filter               = "attributes.ce-type = \"TradeExpressInterest.v1\""
      notification_targets = var.communication_api_notification_targets

      push_to = {
        project           = data.google_projects.communication_projects.projects[0]
        cloud_run_service = "communication-api",
        path              = "/handle-event"
      }
    }
  ]
}

module "homeowner_event_pubsub_topic" {
  source  = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/topic?ref=3.62.1"
  project = module.project
  name    = local.topic
}

resource "google_service_account" "homeowner_event_subscriber_service_accounts" {
  for_each     = toset([for s in local.subscriptions : s.push_to.project.name])
  project      = module.project.id
  account_id   = "${each.value}-sub"
  display_name = "Cloud run invoker for ${each.value}"
  description  = "Used to subscribe to ${local.topic} and invoke ${each.value}"
}

data "google_cloud_run_service" "homeowner_event_push" {
  for_each = { for s in local.subscriptions : s.name => s }
  name     = each.value.push_to.cloud_run_service
  location = var.region
  project  = each.value.push_to.project.project_id
}

module "pub_sub_subscriptions_for_homeowner_event" {
  for_each = { for s in local.subscriptions : s.name => s }

  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt?ref=3.62.1"

  project                    = module.project
  subscription_name          = each.key
  topic_name                 = local.topic
  filter                     = each.value.filter
  service_account            = google_service_account.homeowner_event_subscriber_service_accounts[each.value.push_to.project.name].email
  dead_letter_policy_enabled = true
  dead_letter_queue_name     = "${each.key}-dead-letter"
  max_delivery_attempts      = contains(keys(each.value), "max_delivery_attempts") ? each.value.max_delivery_attempts : local.homeowner_event_max_delivery_attempts
  retry_policy               = contains(keys(each.value), "retry_policy") ? each.value.retry_policy : local.homeowner_event_retry_policy

  push_config = {
    push_endpoint = "${data.google_cloud_run_service.homeowner_event_push[each.key].status[0].url}${each.value.push_to.path}"
    audience      = data.google_cloud_run_service.homeowner_event_push[each.key].status[0].url
  }

  main_notification_targets = each.value.notification_targets
  env                       = var.environment
}
