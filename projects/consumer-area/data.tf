data "google_active_folder" "cloudbuild_current" {
  display_name = var.project_folder
  parent       = "organizations/535868630468"
}

data "google_projects" "communication_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:communication*"
}

data "google_projects" "jobs_board_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:jobs-board*"
}

data "google_projects" "reviews_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:reviews*"
}


data "terraform_remote_state" "search" {
  backend = "gcs"

  config = {
    bucket = "cathex-tf-states"
    prefix = "${var.project_folder}/search"
  }
}
