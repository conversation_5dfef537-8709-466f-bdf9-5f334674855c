variable "project_name" {
  type    = string
  default = ""
}

variable "project_folder" {
  type = string
}

variable "apis" {
  type    = list(string)
  default = []
}
variable "environment" {
  type    = string
  default = ""
}

variable "cloudbuild_triggers" {
  type = map(object({
    name                         = string
    description                  = string
    disabled                     = bool
    push_trigger_enabled         = bool
    pull_request_trigger_enabled = bool
    owner                        = string
    repo_name                    = string
    branch_regex                 = string
    invert_regex                 = bool
    comment_control              = string
    filename                     = string
    env_variables                = map(string)
    included_files_filter        = list(string)
  }))
}

variable "pub_sub_topics" {
  type        = list(any)
  description = "(optional) describe your variable"
  default     = []
}

variable "pub_sub_subscriptions" {
  type = map(object({
    subscription_name          = string
    topic_name                 = string
    create_service_account     = bool
    service_account            = string
    dead_letter_policy_enabled = bool
    dead_letter_queue_name     = string
    push_config                = map(string)
  }))
  default = {}
}

variable "viewer_access" {
  type        = list(string)
  description = "A list of account (group) emails to which viewer access will be given."
  default     = []
}

variable "owner_access" {
  type        = list(string)
  description = "A list of account (group) emails to which owner and invoker access will be given."
  default     = []
}

variable "cloud_function_invokers" {
  type        = map(list(string))
  default     = {}
  description = "A list of members permission set on a per cloud function basis. If used, the list per cloud function overrides the list if defined in the main config, so be sure to include all required."
}

variable "team_access" {
  type        = list(string)
  default     = []
  description = "A list of team accounts (groups) to which general team access will be given."
}

variable "appengine_location" {
  description = "Location for app engine application"
  default     = "europe-west2"
}

# Pubsub alert variables
variable "alert_duration" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"

}

variable "threshold_value" {
  type        = number
  description = "Number of unacked messages before threshold is considered breached"
}

variable "trigger_count" {
  type        = number
  description = "Number of times the threshold must be breached before triggering the alert"
}

variable "content_api_project_id" {
  type        = string
  description = "Content API project id"
}

variable "jobs_management_project_id" {
  type        = string
  description = "Jobs Management project id"
}
variable "reviews_project_id" {
  type        = string
  description = "Reviews project id"
}

variable "reviews_project_number" {
  type        = string
  description = "Reviews project number"
}

variable "region" {
  type        = string
  description = "Project region"
  default     = "europe-west2"
}

variable "cloud_run_env_variables" {
  type = map(list(any))
}

variable "cloud_run_parameters" {
  type = map(object({
    cpu                   = string
    memory                = string
    max_scale             = number
    min_scale             = number
    initial_scale         = number
    container_concurrency = number
  }))
}

variable "firebase_auth_export_schedule" {
  type        = string
  description = "(Optional) Cron schedule for firebase auth export"
  default     = "0 1 * * *"
}

# Cloud Run memory alert variables
variable "alert_duration_memory" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"
}

variable "threshold_value_memory" {
  type        = string
  description = "Number of unacked messages before threshold is considered breached"
}

variable "trigger_count_memory" {
  type        = string
  description = "Number of times the threshold must be breached before triggering the alert"
}

# Cloud Run cpu alert variables
variable "alert_duration_cpu" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"
}

variable "threshold_value_cpu" {
  type        = string
  description = "Percent of cpu utilisation before threshold is considered breached"
}

variable "trigger_count_cpu" {
  type        = string
  description = "Number of times the threshold must be breached before triggering the alert"
}

# Cloud Run Response Code alert variables
variable "alert_duration_response_codes" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"
}

variable "threshold_value_response_codes" {
  type        = string
  description = "Rate per second of response codes received in the given range before threshold is considered breached"
}

variable "trigger_count_response_codes" {
  type        = string
  description = "Number of times the threshold must be breached before triggering the alert"
}

variable "project_static_permissions" {
  type        = map(list(string))
  description = "A map of roles to their list of IAM groups."
  default     = {}
}

variable "jobs_board_notification_targets" {
  type        = string
  description = "Additional notification targets for Datadog Monitors also concerning Jobs Board."
}

variable "communication_api_notification_targets" {
  type        = string
  description = "Notification targets for Datadog Monitors concerning Communication API."
}

variable "capi_consumer_app_service_accounts" {
  type        = list(string)
  description = "Service accounts of Core platform's component able to access Firebase resources"
  default     = []
}

variable "core_firebase_service_accounts" {
  type        = list(string)
  description = "Service accounts of Core platform's component able to impersonate to Firebase SA"
  default     = []
}

variable "capi_reviews_public_endpoint" {
  type        = string
  description = "Reviews CAPI service URL"
}

