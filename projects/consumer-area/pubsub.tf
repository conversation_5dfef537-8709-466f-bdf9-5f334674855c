locals {
  max_delivery_attempts = 5
  retry_policy          = { minimum_backoff = "1s", maximum_backoff = "600s" }

  pub_sub_topics = [
    "marketing-opt-in",
    "marketing-opt-out",
    "marketing-prefs-updated",
    "firebase-auth-export",
    "verify-review",
    "verify-review-reply",
    "review-verified",
    "consumer-updated"
  ]
  pub_sub_subscriptions = {
    marketing-opt-in = {
      subscription_name          = "CREATE_MARKETING_CONTACT"
      topic_name                 = "marketing-opt-in"
      create_service_account     = false
      service_account            = "${module.project.id}@appspot.gserviceaccount.com"
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "marketing-opt-in-dead-letter"
      push_config = {
        push_endpoint = "https://${var.region}-${module.project.id}.cloudfunctions.net/cloudGroup-createSibContact"
        audience      = "https://${var.region}-${module.project.id}.cloudfunctions.net/cloudGroup-createSibContact"
      }
    },
    marketing-opt-out = {
      subscription_name          = "UPDATE_MARKETING_CONTACT"
      topic_name                 = "marketing-opt-out"
      create_service_account     = false
      service_account            = "${module.project.id}@appspot.gserviceaccount.com"
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "marketing-opt-out-dead-letter"
      push_config = {
        push_endpoint = "https://${var.region}-${module.project.id}.cloudfunctions.net/cloudGroup-updateSibContact"
        audience      = "https://${var.region}-${module.project.id}.cloudfunctions.net/cloudGroup-updateSibContact"
      }
    },
    marketing-prefs-updated = {
      subscription_name          = "marketing-prefs-updated"
      topic_name                 = "marketing-prefs-updated"
      create_service_account     = false
      service_account            = "${module.project.id}@appspot.gserviceaccount.com"
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "marketing-prefs-updated-dead-letter"
      push_config = {
        push_endpoint = "https://${var.region}-${module.project.id}.cloudfunctions.net/cloudGroup-updateUserContactPreferences"
        audience      = "https://${var.region}-${module.project.id}.cloudfunctions.net/cloudGroup-updateUserContactPreferences"
      }
    },
    firebase-auth-export = {
      subscription_name          = "FIREBASE_AUTH_EXPORT"
      topic_name                 = "firebase-auth-export"
      create_service_account     = false
      service_account            = "${module.project.id}@appspot.gserviceaccount.com"
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "firebase-auth-export-dead-letter"
      push_config = {
        push_endpoint = "${module.cloud_run["consumer-api"].endpoint}/firebase-auth-export"
        audience      = "${module.cloud_run["consumer-api"].endpoint}"
      }
    },
    verify-review = {
      subscription_name          = "VERIFY_REVIEW"
      topic_name                 = "verify-review"
      create_service_account     = false
      service_account            = "${module.project.id}@appspot.gserviceaccount.com"
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "verify-review-dead-letter"
      push_config = {
        push_endpoint = "${module.cloud_run["consumer-api"].endpoint}/verify-review"
        audience      = "${module.cloud_run["consumer-api"].endpoint}"
      }
    },
    verify-review-reply = {
      subscription_name          = "VERIFY_REVIEW_REPLY"
      topic_name                 = "verify-review-reply"
      create_service_account     = false
      service_account            = "${module.project.id}@appspot.gserviceaccount.com"
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "verify-review-reply-dead-letter"
      push_config = {
        push_endpoint = "${module.cloud_run["consumer-api"].endpoint}/confirm-verification"
        audience      = "${module.cloud_run["consumer-api"].endpoint}"
      }
    },
    review-verify-sms = {
      subscription_name          = "review-verify-sms"
      topic_name                 = "verify-review-reply"
      create_service_account     = false
      service_account            = "${module.project.id}@appspot.gserviceaccount.com"
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "review-verify-sms-dead-letter"
      push_config = {
        push_endpoint = "${var.capi_reviews_public_endpoint}/verify"
        audience      = "${var.capi_reviews_public_endpoint}"
      }
    },
    consumer-area-user-updated = {
      subscription_name          = "CONSUMER_UPDATED"
      topic_name                 = "consumer-updated"
      create_service_account     = false
      service_account            = "consumer-area-to-comms@${module.project.id}.iam.gserviceaccount.com",
      dead_letter_policy_enabled = true
      dead_letter_queue_name     = "consumer-updated-dead-letter"
      push_config = {
        push_endpoint = "${data.google_cloud_run_service.comms_service.status[0].url}/consumer/updated"
        audience      = "${data.google_cloud_run_service.comms_service.status[0].url}"
      },
      filter = "attributes.ce-type = \"ConsumerAreaUserUpdated.v1\""
    }
  }
}


module "pub_sub_topic" {
  for_each = toset(concat(var.pub_sub_topics, local.pub_sub_topics))
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/topic?ref=3.62.1"

  project = module.project
  name    = each.key
}

module "pub_sub_subscription" {
  for_each = merge(local.pub_sub_subscriptions, var.pub_sub_subscriptions)

  source                 = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt?ref=3.62.1"
  env                    = var.environment
  project                = module.project
  subscription_name      = each.value["subscription_name"]
  topic_name             = each.value["topic_name"]
  create_service_account = each.value["create_service_account"]
  service_account        = each.value["service_account"]

  dead_letter_policy_enabled = each.value["dead_letter_policy_enabled"]
  dead_letter_queue_name     = each.value["dead_letter_queue_name"]

  max_delivery_attempts = contains(keys(each.value), "max_delivery_attempts") ? each.value.max_delivery_attempts : local.max_delivery_attempts
  retry_policy          = contains(keys(each.value), "retry_policy") ? each.value.retry_policy : local.retry_policy

  push_config = each.value["push_config"]

  filter = contains(keys(each.value), "filter") ? each.value["filter"] : ""
}


data "google_cloud_run_service" "reviews-backend" {
  name     = "reviews-backend"
  location = "europe-west2"
  project  = var.reviews_project_id
}

resource "google_service_account" "reviews_pubsub_publisher" {
  project = module.project.id

  account_id   = "consumer-area-to-reviews"
  display_name = "Consumer Area Reviews Invoker"
  description  = "Used to publish messages from pub/sub to reviews services"
}

resource "google_service_account" "comms_pubsub_publisher" {
  project = module.project.id

  account_id   = "consumer-area-to-comms"
  display_name = "Consumer Area Comms Invoker"
  description  = "Used to publish messages from pub/sub to comms services"
}

data "google_projects" "comms_service_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:communication*"
}

data "google_cloud_run_service" "comms_service" {
  name     = "communication-api"
  location = "europe-west2"
  project  = data.google_projects.comms_service_projects.projects[0].number
}
