locals {
  default_docker_image = "us-docker.pkg.dev/cloudrun/container/hello"
  default_cloud_run_env_variables = [
    {
      name  = "PROJECT_ID"
      value = module.project.id
    }
  ]

  services = {
    consumer-api = {
      name             = "consumer-api"
      repo_name        = "gcp-consumer-area"
      container_port   = 8080
      secret_variables = []
      env_variables    = []
    }
  }
}

# Start Cloud Run instance(s)
module "cloud_run" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service?ref=3.74.0"

  for_each = local.services

  name                  = each.value["name"]
  gcr_image             = local.default_docker_image
  container_port        = each.value["container_port"]
  project               = module.project
  region                = var.region
  memory                = var.cloud_run_parameters[each.key].memory
  cpu                   = var.cloud_run_parameters[each.key].cpu
  container_concurrency = var.cloud_run_parameters[each.key].container_concurrency

  # env_variables    = concat(each.value["env_variables"], local.default_cloud_run_env_variables, var.cloud_run_env_variables[each.key])
  # secret_variables = each.value["secret_variables"]
  template_metadata_annotations = {
    "autoscaling.knative.dev/initialScale" = var.cloud_run_parameters[each.key].initial_scale
    "autoscaling.knative.dev/maxScale"     = var.cloud_run_parameters[each.key].max_scale
    "autoscaling.knative.dev/minScale"     = var.cloud_run_parameters[each.key].min_scale
  }

  # autodeploy_build_trigger_names = {
  #   development = {
  #     trigger_name = "Feature"
  #   }
  #   staging = {
  #     trigger_name = "PR-to-Main"
  #   }
  #   production = {
  #     trigger_name = "Main"
  #   }
  # }

  custom_labels = {
    "managed-by"   = "gcp-cloud-build-deploy-cloud-run",
    "repo-name"    = each.value["repo_name"],
    "service-name" = each.value["name"]
  }
}

module "cloud_run_mem_alert" {
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service/memory_alerts?ref=3.74.0"
  for_each = module.cloud_run

  alert_duration_memory  = var.alert_duration_memory
  threshold_value_memory = var.threshold_value_memory
  trigger_count_memory   = var.trigger_count_memory
  project                = module.project
  service_name           = each.value["service_name"]
  env                    = var.environment
}

module "cloud_run_cpu_alert" {
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service/cpu_alerts?ref=3.74.0"
  for_each = module.cloud_run

  alert_duration_cpu  = var.alert_duration_cpu
  threshold_value_cpu = var.threshold_value_cpu
  trigger_count_cpu   = var.trigger_count_cpu
  project             = module.project
  service_name        = each.value["service_name"]
  env                 = var.environment
}

module "cloud_run_response_codes_alert" {
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service/response_code_alerts?ref=3.74.0"
  for_each = module.cloud_run

  alert_duration_response_codes  = var.alert_duration_response_codes
  threshold_value_response_codes = var.threshold_value_response_codes
  trigger_count_response_codes   = var.trigger_count_response_codes
  project                        = module.project
  service_name                   = each.value["service_name"]
  env                            = var.environment
}