# All project related modules and configuration
locals {
  project_name = coalesce(var.project_name, "consumer-area")
  project_configuration = {
    name   = local.project_name
    folder = var.project_folder
    apis = concat(var.apis, [
      "storage-component.googleapis.com",
      "cloudbuild.googleapis.com",
      "compute.googleapis.com",
      "run.googleapis.com",
      "secretmanager.googleapis.com",
      "pubsub.googleapis.com",
      "cloudfunctions.googleapis.com",
      "firestore.googleapis.com",
      "firebase.googleapis.com",
      "appengine.googleapis.com",
      "apigateway.googleapis.com",
      "servicemanagement.googleapis.com",
      "servicecontrol.googleapis.com",
      "cloudscheduler.googleapis.com",
      "artifactregistry.googleapis.com",
    ])
  }

  map_environment_to_project_id = tomap({
    development = "consumer-area-dev-47121"
    staging     = "consumer-area-staging-18095"
    production  = "consumer-area-production-40311"
  })
}

module "project" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/project?ref=3.63.3"

  project_config    = local.project_configuration
  legacy_project_id = local.map_environment_to_project_id[var.project_folder]
  team_email        = "<EMAIL>"
  budget_limit      = 350
}

module "firebase" {
  source  = "git::**************:cat-home-experts/terraform-modules.git//gcp/firebase?ref=1.0.7"
  project = module.project.id
}
