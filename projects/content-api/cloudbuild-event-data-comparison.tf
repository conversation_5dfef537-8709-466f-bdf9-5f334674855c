module "cloud_build_triggers_schedule" {
  for_each = var.schedule_cloudbuild_triggers
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_build?ref=3.84.4"

  project = module.project

  name        = each.value["name"]
  description = each.value["description"]
  disabled    = each.value["disabled"]


  push_trigger_enabled         = each.value["push_trigger_enabled"]
  pull_request_trigger_enabled = each.value["pull_request_trigger_enabled"]
  manual_trigger_enabled       = each.value["manual_trigger_enabled"] == null ? false : each.value["manual_trigger_enabled"]


  owner        = each.value["owner"]
  repo_name    = each.value["repo_name"]
  branch_regex = each.value["branch_regex"]
  invert_regex = each.value["invert_regex"]

  filename = each.value["filename"]

  branch_or_tag_ref = "refs/heads/main"

  env_variables = each.value["env_variables"]

  depends_on = [module.project]
}
