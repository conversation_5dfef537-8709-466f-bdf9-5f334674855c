locals {
  # Pubsub inputs
  # Creates topics and subscriptions for comparison testing
  comparison_testing_topics_and_subs = [
    {
      topic = "content-api-trade-res"
    },
    {
      topic = "content-api-trade-response"
    }
  ]
}

resource "google_service_account" "comparison_testing_pubsub_publisher" {
  count = length(local.comparison_testing_topics_and_subs) > 0 ? 1 : 0

  project = module.project.id

  account_id   = "comparison-testing"
  display_name = "Used to publish messages from pub/sub for comparison testing"
}

module "pub_sub_subscriptions_for_comparison_testing" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt?ref=3.79.0"

  for_each = { for i, v in local.comparison_testing_topics_and_subs : i => v }

  project           = module.project
  env               = var.environment
  push_config       = null
  subscription_name = "comparison-testing-${each.value["topic"]}-${contains(keys(each.value), "messageType") ? each.value["messageType"] : ""}${contains(keys(each.value), "messageVersion") ? each.value["messageVersion"] : ""}"
  topic_name        = each.value["topic"]

  service_account = google_service_account.comparison_testing_pubsub_publisher[0].email

  subscription_enabled       = contains(keys(each.value), "subscription_enabled") ? each.value.subscription_enabled : true
  dead_letter_policy_enabled = false
  retry_policy               = contains(keys(each.value), "retry_policy") ? each.value.retry_policy : {}

  filter = "${contains(keys(each.value), "messageType") ? "attributes.messageType = \"${each.value["messageType"]}\" " : ""}${contains(keys(each.value), "messageVersion") ? " attributes.messageVersion = \"${each.value["messageVersion"]}\"" : ""}"
}
