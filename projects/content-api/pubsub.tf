locals {
  content_api_topics_and_subs = [
    "content-api-trade-req",
    "content-api-trade-bulk-req",
    "content-api-categories-request",
    "content-api-user-req",
    "content-api-trade-experience-req",
    "content-api-work-alert-preference-req",
    "content-api-work-alert-preference-bulk-req",
    "content-api-trade-experience-bulk-req",
    "content-api-trade-experience-bulk-request",
    "content-api-trade-experience-album-bulk-req",
    "content-api-trade-experience-album-req",
    "content-api-trade-experience-work-alert-preference-req",
    "content-api-trade-experience-work-alert-preference-bulk-req",
    "content-api-website-tools-update",
  ]
  content_api_shared_topics = [
    "content-api-categories-response",
    "content-api-trade-res",
    "content-api-trade-response",
    "content-api-work-alert-preference-res",
    "content-api-trade-experience-bulk-res",
  ]
}

resource "google_service_account" "pubsub_publisher" {
  count = length(local.content_api_topics_and_subs) > 0 ? 1 : 0

  project = module.project.id

  account_id   = "${var.project_name}-${var.environment}-pubsub"
  display_name = "Used to publish messages from pub/sub"
}

module "pub_sub_topic" {
  for_each = toset(distinct(concat(
    local.content_api_shared_topics,
    local.content_api_topics_and_subs,
    local.guided_experiences_topics_and_subs[*].topic,
    local.trade_experience_topics_and_subs[*].topic,
    local.consumer_area_topics_and_subs[*].topic,
  )))
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/topic?ref=3.62.1"

  project = module.project
  name    = each.key
}

module "pub_sub_subscriptions_for_content_api" {
  for_each = toset(local.content_api_topics_and_subs)

  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt?ref=3.105.1"

  project           = module.project
  subscription_name = "${var.project_name}-${each.key}"
  topic_name        = each.key
  service_account   = google_service_account.pubsub_publisher[0].email

  dead_letter_policy_enabled = true
  dead_letter_queue_name     = "${each.key}-dead-letter"
  max_delivery_attempts      = each.key == "content-api-trade-req" ? 30 : 5
  env                        = var.environment
  push_config = {
    push_endpoint = "${module.cloud_run.endpoint}/pubsub/${each.key}"
    audience      = module.cloud_run.endpoint
  }
}