module "vpc" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/networking/vpc?ref=3.62.1"

  project                    = module.project
  network_name               = "content-api-vpc"
  auto_create_subnetworks    = false
  private_connection_enabled = true
  private_connection_name    = "private-connection"
  private_ip_prefix_length   = 24
}

resource "google_compute_subnetwork" "peered_subnet" {
  project = module.project.id

  region        = var.region
  name          = "peered-subnet"
  ip_cidr_range = "********/24"

  network = module.vpc.network_name
}

module "serverless_connector" {
  # Using early version of access_connector module due to changes in the newer version that cause a recreation of this resource and not allowing us to pass in the existing ip_cidr_range value. There is a peering connection in place with data-services that would be impacted by this.
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/networking/vpc_access_connector?ref=1.0.0"

  project_id = module.project.id

  name          = "dataservicessonnectordev"
  region        = var.region
  ip_cidr_range = "********/28"

  network_name = module.vpc.network_name
}
