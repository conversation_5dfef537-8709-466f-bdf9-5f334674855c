locals {
  name                 = "content-api"
  default_docker_image = "us-docker.pkg.dev/cloudrun/container/hello"
  container_port       = 8080

  default_cloud_run_env_variables = [
    {
      "name"  = "PROJECT_ID"
      "value" = module.project.id
    }
  ]

  secret_variables = [
    {
      secret_id = "SALESFORCE_CLIENT_ID"
      name      = "SALESFORCE_CLIENT_ID"
    },
    {
      secret_id = "SALESFORCE_CLIENT_SECRET"
      name      = "SALESFORCE_CLIENT_SECRET"
    }
  ]
  env_variables = concat([
    {
      name  = "SALESFORCE_API_URI"
      value = var.salesforce_api_uri
    },
    {
      name  = "SALESFORCE_ID_SERVER"
      value = var.salesforce_id_server
    }
  ], local.default_cloud_run_env_variables)

  full_cloud_run_env_variables = concat(local.env_variables, var.cloud_run_env_variables)
}

# Start Cloud Run instance(s)
module "cloud_run" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars?ref=3.79.0"

  project = module.project
  region  = var.region

  name = local.name

  gcr_image      = local.default_docker_image
  container_port = local.container_port

  env_variables    = local.full_cloud_run_env_variables
  secret_variables = local.secret_variables

  container_concurrency = var.container_concurrency

  memory = "1024Mi"
  cpu    = "2000m"

  template_metadata_annotations = {
    "autoscaling.knative.dev/maxScale"        = "30"
    "run.googleapis.com/cloudsql-instances"   = var.cloudsql_instance
    "run.googleapis.com/vpc-access-connector" = module.serverless_connector.connector.id
    "run.googleapis.com/vpc-access-egress"    = "private-ranges-only"
  }

  custom_labels = {
    "managed-by"   = "gcp-cloud-build-deploy-cloud-run",
    "repo-name"    = "gcp-data-services-content-api",
    "service-name" = local.name
  }
}

module "cloud_run_mem_alert" {
  source                 = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/memory_alerts?ref=3.79.0"
  alert_duration_memory  = var.alert_duration_memory
  threshold_value_memory = var.threshold_value_memory
  trigger_count_memory   = var.trigger_count_memory
  project                = module.project
  service_name           = module.cloud_run.service_name
  env                    = var.environment
}


module "cloud_run_cpu_alert" {
  source              = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/cpu_alerts?ref=3.79.0"
  alert_duration_cpu  = var.alert_duration_cpu
  threshold_value_cpu = var.threshold_value_cpu
  trigger_count_cpu   = var.trigger_count_cpu
  project             = module.project
  service_name        = module.cloud_run.service_name
  env                 = var.environment
}


module "cloud_run_response_codes_alert" {
  source                         = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/response_code_alerts?ref=3.79.0"
  alert_duration_response_codes  = var.alert_duration_response_codes
  threshold_value_response_codes = var.threshold_value_response_codes
  trigger_count_response_codes   = var.trigger_count_response_codes
  project                        = module.project
  service_name                   = module.cloud_run.service_name
  env                            = var.environment
}
