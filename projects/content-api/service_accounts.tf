resource "google_service_account" "cloud_scheduler_sa" {
  project = module.project.id

  account_id   = "cloud-scheduler-id"
  display_name = "cloud-scheduler-sa"
}

resource "google_service_account" "data_and_insights" {
  project      = module.project.id
  account_id   = "data-and-insights"
  display_name = "Used by D&I"
}

resource "google_service_account" "hackathon" {
  project      = module.project.id
  account_id   = "temp-hackaton"
  display_name = "Used for the hackathon"
}

resource "google_service_account" "impersonation" {
  project      = module.project.id
  account_id   = "impersonation"
  display_name = "Used to access the API via impersonation for local testing"
}

# Service account for invoking the content-api Cloud Run service
resource "google_service_account" "poc_jamespain_invoker" {
  project      = module.project.id
  account_id   = "poc-jamespain-invoker"
  display_name = "POC James Pain Invoker"
  description  = "Service account for invoking the content-api Cloud Run service via impersonation"
}
