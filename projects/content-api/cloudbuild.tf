module "slackbot_trigger" {
  # From this version, the module create the placeholders for secrets 'slack-dev' & 'slack-main'
  # + the secret 'github-url' and its value
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/slackbot_trigger?ref=3.65.1"

  project = module.project

  env_variables = {
    _GCP_REGION   = "europe-west2"
    _TRIGGER_NAME = "slack-notifier"
  }

  auto_apply_enabled = false

  depends_on = [module.cloud_run]
}

module "deployment_metrics_trigger" {
  source = "git::**************:cat-home-experts/terraform-modules.git//checkatrade/deployment_metrics_trigger?ref=3.63.4"

  project = module.project
}

module "cloud_build_triggers" {
  for_each = var.cloudbuild_triggers
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_build?ref=3.77.0"

  project = module.project

  name        = each.value["name"]
  description = each.value["description"]
  disabled    = each.value["disabled"]

  push_trigger_enabled         = each.value["push_trigger_enabled"]
  pull_request_trigger_enabled = each.value["pull_request_trigger_enabled"]


  owner        = each.value["owner"]
  repo_name    = each.value["repo_name"]
  branch_regex = each.value["branch_regex"]
  invert_regex = each.value["invert_regex"]
  #comment_control = each.value["comment_control"]

  filename = each.value["filename"]

  env_variables = each.value["env_variables"]

  depends_on = [module.project]
}
