locals {
  # Previously list indexes were used as keys, which lead to errors during a list modification.
  # Terraform allows to use only strings as keys in for_each statement 
  # (https://developer.hashicorp.com/terraform/language/meta-arguments/for_each).
  # Indexes are used as topic names, so they are unique.
  trade_experience_topics_and_subs = [
    {
      topic     = "content-api-trade-experience-res"
      endpoint  = "/pubsub/content-api-trade-experience-res"
      cloud_run = "pubsub-receiver"
    },
    {
      topic     = "content-api-trade-experience-album-res"
      endpoint  = "/pubsub/content-api-trade-experience-album-res"
      cloud_run = "pubsub-receiver"
    },
    {
      topic     = "content-api-trade-experience-work-alert-preference-res"
      endpoint  = "/pubsub/content-api-trade-experience-work-alert-preference-res"
      cloud_run = "pubsub-bulk-receiver"
    },
    {
      topic                = "content-api-trade-experience-bulk-res"
      endpoint             = "/pubsub/content-api-trade-experience-bulk-res"
      cloud_run            = "pubsub-bulk-receiver"
      subscription_enabled = false,
    },
    {
      topic     = "content-api-trade-experience-bulk-response"
      endpoint  = "/pubsub/content-api-trade-experience-bulk-response"
      cloud_run = "pubsub-bulk-receiver"
    },
    #     {
    #       topic     = "content-api-trade-res"
    #       endpoint  = "/pubsub/content-api-trade-res"
    #       cloud_run = "pubsub-receiver"
    #     },
    #     Enable new salesforce data subscription when it is ready (INC-780)
    {
      topic             = "content-api-trade-response"
      endpoint          = "/pubsub/content-api-trade-response"
      cloud_run         = "pubsub-receiver"
      subscription_name = "trade-experience-trade-response"
    }
  ]
}

resource "google_service_account" "trade_experience_pubsub_publisher" {
  project = module.project.id

  account_id   = "content-api-to-trade-exp"
  display_name = "Content API Trade Experience Invoker"
  description  = "Used to publish messages from pub/sub to trade experiences Cloud Run"
}

data "google_cloud_run_service" "trade_experience" {
  for_each = { for o in local.trade_experience_topics_and_subs : o.topic => o }
  name     = each.value.cloud_run
  location = "europe-west2"
  project  = var.trade_experience_project_id
}

data "google_project" "trade_experience" {
  project_id = var.trade_experience_project_id
}

module "pub_sub_subscriptions_for_trade_experience" {
  for_each = { for o in local.trade_experience_topics_and_subs : o.topic => o }

  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt?ref=3.79.0"

  project           = module.project
  subscription_name = contains(keys(each.value), "subscription_name") ? each.value.subscription_name : "trade-experience-${element(split("-", each.value["topic"]), 4)}"
  topic_name        = each.value["topic"]
  service_account   = google_service_account.trade_experience_pubsub_publisher.email

  dead_letter_policy_enabled = true
  dead_letter_queue_name     = "${each.value["topic"]}-dead-letter"

  subscription_enabled = contains(keys(each.value), "subscription_enabled") ? each.value.subscription_enabled : true

  push_config = {
    push_endpoint = "${data.google_cloud_run_service.trade_experience[each.key].status[0].url}/${each.value["endpoint"]}"
    audience      = data.google_cloud_run_service.trade_experience[each.key].status[0].url
  }
  env = var.environment
}

moved {
  from = module.pub_sub_subscriptions_for_trade_experience["0"]
  to   = module.pub_sub_subscriptions_for_trade_experience["content-api-trade-experience-res"]
}

moved {
  from = module.pub_sub_subscriptions_for_trade_experience["1"]
  to   = module.pub_sub_subscriptions_for_trade_experience["content-api-trade-experience-album-res"]
}

moved {
  from = module.pub_sub_subscriptions_for_trade_experience["2"]
  to   = module.pub_sub_subscriptions_for_trade_experience["content-api-trade-experience-work-alert-preference-res"]
}

moved {
  from = module.pub_sub_subscriptions_for_trade_experience["3"]
  to   = module.pub_sub_subscriptions_for_trade_experience["content-api-trade-experience-bulk-res"]
}

moved {
  from = module.pub_sub_subscriptions_for_trade_experience["4"]
  to   = module.pub_sub_subscriptions_for_trade_experience["content-api-trade-experience-bulk-response"]
}

moved {
  from = module.pub_sub_subscriptions_for_trade_experience["5"]
  to   = module.pub_sub_subscriptions_for_trade_experience["content-api-trade-res"]
}
