variable "project_name" {
  type    = string
  default = "content-api"
}

variable "region" {
  type        = string
  description = ""
  default     = "europe-west2"
}

variable "project_folder" {
  type = string
}

variable "apis" {
  type    = list(string)
  default = []
}

variable "environment" {
  type    = string
  default = ""
}

variable "cloudbuild_triggers" {
  type = map(object({
    name                         = string
    description                  = string
    disabled                     = bool
    push_trigger_enabled         = bool
    pull_request_trigger_enabled = bool
    owner                        = string
    repo_name                    = string
    branch_regex                 = string
    invert_regex                 = bool
    comment_control              = string
    filename                     = string
    env_variables                = map(string)
    included_files_filter        = list(string)
  }))
}

variable "schedule_cloudbuild_triggers" {
  type = map(object({
    name                         = string
    description                  = string
    disabled                     = bool
    push_trigger_enabled         = bool
    pull_request_trigger_enabled = bool
    manual_trigger_enabled       = bool
    owner                        = string
    repo_name                    = string
    branch_regex                 = string
    invert_regex                 = bool
    comment_control              = string
    filename                     = string
    env_variables                = map(string)
    included_files_filter        = list(string)
  }))
}
variable "cloud_run_env_variables" {
  type = list(any)
}

variable "trade_experience_project_id" {
  type = string
}

variable "jobs_management_project_id" {
  type = string
}

variable "consumer_area_project_id" {
  type = string
}

variable "reviews_project_id" {
  type = string
}


variable "project_static_permissions" {
  type        = map(list(string))
  description = "A map of roles to their list of IAM groups."
  default     = {}
}

variable "salesforce_integ_project_id" {
  type = string
}


# Cloud Run memory alert variables
variable "alert_duration_memory" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"
}

variable "threshold_value_memory" {
  type        = string
  description = "Number of unacked messages before threshold is considered breached"
}

variable "trigger_count_memory" {
  type        = string
  description = "Number of times the threshold must be breached before triggering the alert"
}


# Cloud Run cpu alert variables
variable "alert_duration_cpu" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"
}

variable "threshold_value_cpu" {
  type        = string
  description = "Percent of cpu utilisation before threshold is considered breached"
}

variable "trigger_count_cpu" {
  type        = string
  description = "Number of times the threshold must be breached before triggering the alert"
}


# Cloud Run Response Code alert variables
variable "alert_duration_response_codes" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"
}

variable "threshold_value_response_codes" {
  type        = string
  description = "Rate per second of response codes received in the given range before threshold is considered breached"
}

variable "trigger_count_response_codes" {
  type        = string
  description = "Number of times the threshold must be breached before triggering the alert"
}


# Pubsub alert variables
variable "alert_duration" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"

}

variable "threshold_value" {
  type        = number
  description = "Number of unacked messages before threshold is considered breached"

}

variable "trigger_count" {
  type        = number
  description = "Number of times the threshold must be breached before triggering the alert"
}

variable "container_concurrency" {
  type        = number
  description = "Concurrency per container in cloud run service"
  default     = 20
}

variable "cloudsql_instance" {
  type        = string
  description = "Name of sql instance for sql connector"
}

variable "cloud_scheduler_full_refresh_schedule_v6" {
  type        = string
  description = "Cron schedule for Full Bulk Refresh Run"
}

variable "cloud_scheduler_data_schedule_v6" {
  type        = string
  description = "Cron schedule for Standard Bulk Run"
}

variable "cloud_scheduler_refresh_schedule_v6" {
  type        = string
  description = "Cron schedule for Bulk Refresh Run"
}

variable "cloud_scheduler_categories_schedule" {
  type        = string
  description = "Cron schedule for Categories Refresh"
}

variable "cloud_scheduler_categories_v2_schedule" {
  type        = string
  description = "Cron schedule for Categories Refresh"
}

variable "cloud_scheduler_trade_message_version_v6" {
  type        = string
  description = "Message Version for Trade Bulk Runs"
}

variable "cloud_scheduler_reviews_bulk_schedule" {
  type        = string
  description = "Cron schedule for requesting updated reviews from Content API"
}

variable "cloud_scheduler_reviews_basic_bulk_schedule" {
  type        = string
  description = "Cron schedule for requesting reviews with basic updates from Content API"
}

variable "cloud_scheduler_reviews_all_schedule" {
  type        = string
  description = "Cron schedule for requesting all reviews from Content API"
}

variable "cloud_scheduler_work_alert_preference_data_schedule" {
  type        = string
  description = "Cron schedule for WorkAlertBulkRequest all data run"
}

variable "cloud_scheduler_work_alert_preference_refresh_schedule" {
  type        = string
  description = "Cron schedule for WorkAlertBulkRequest refresh run"
}

variable "cloud_scheduler_work_alert_preference_v2_data_schedule" {
  type        = string
  description = "Cron schedule for WorkAlertBulkRequestV2 all data run"
}

variable "cloud_scheduler_work_alert_preference_v2_refresh_schedule" {
  type        = string
  description = "Cron schedule for WorkAlertBulkRequestV2 refresh run"
}

variable "cloud_scheduler_trade_experience_categories_bulk_refresh_schedule" {
  type        = string
  description = "Cron schedule for TradeExperienceCategoryRequest refresh run"
  default     = "0 4 * * *"
}

variable "cloud_scheduler_data_schedule_v6_all_members" {
  type        = string
  description = "Cron schedule for Trade Profile Bulk Request with all membership types"
}

variable "cloud_scheduler_data_schedule_v6_partial" {
  type        = string
  description = "Cron schedule for Trade Profile Bulk Request with partial membership types"
}

variable "cloud_scheduler_trigger_manual_cloud-build" {
  type        = string
  description = "Cron schedule for trigger manual cloud build"
  default     = "0 4 * * *"
}


variable "salesforce_api_uri" {
  type        = string
  description = "Salesforce API URL"
}
variable "salesforce_id_server" {
  type        = string
  description = "Salesforce ID server URL"
}


variable "trade_res_di_api_gateway_host" {
  type        = string
  description = "The host for the trade res topic D&I subscription"
}

variable "trade_response_di_api_gateway_host" {
  type        = string
  description = "The host for the trade response topic D&I subscription"
}

variable "capi_service_accounts" {
  type        = list(string)
  description = "Service accounts running on the CAPI system that require access"
  default     = []
}
