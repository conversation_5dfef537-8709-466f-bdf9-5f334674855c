module "secrets" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/secret_manager/store?ref=3.62.1"

  project = module.project
  secrets_keys = [
    { name = "content-api-connection-string" },
    { name = "github-packages-username", value = data.google_secret_manager_secret_version.github_packages_username.secret_data },
    { name = "github-packages-token", value = data.google_secret_manager_secret_version.github_packages_token.secret_data },
    { name = "snyk-token" },
    { name = "SALESFORCE_CLIENT_SECRET" },
    { name = "SALESFORCE_CLIENT_ID" },
    { name = "trade-res-di-api-gateway-key" },
    { name = "trade-response-di-api-gateway-key" }
  ]

  provisioner_enabled = true
}

data "google_secret_manager_secret_version" "github_packages_username" {
  project = data.google_projects.operations_projects.projects[0].project_id
  secret  = "github-packages-username-internal"
}

data "google_secret_manager_secret_version" "github_packages_token" {
  project = data.google_projects.operations_projects.projects[0].project_id
  secret  = "github-packages-token-internal"
}

module "retrieve_secrets" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/secret_manager/retrieve?ref=3.63.2"

  for_each = toset(module.secrets.secret_names)

  project   = module.project
  secret_id = each.key

  depends_on = [module.secrets]
}