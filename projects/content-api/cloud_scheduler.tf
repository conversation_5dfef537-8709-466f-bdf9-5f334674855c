locals {
  scheduler_jobs = {
    dataSyncV6 = {
      name                 = "trade-profile-bulk-v6"
      description          = "Job to trigger trade-profile-bulk request on pubsub"
      schedule             = "${var.cloud_scheduler_data_schedule_v6}"
      timezone             = "UTC"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 12,\"chunk\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeProfileBulkRequest", "messageVersion" = "${var.cloud_scheduler_trade_message_version_v6}" }
    },
    refreshSyncV6 = {
      name                 = "trade-profile-bulk-refresh-v6"
      description          = "Job to trigger trade-profile-bulk request on pubsub for last 1080 hours"
      schedule             = "${var.cloud_scheduler_refresh_schedule_v6}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 1080,\"chunk\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeProfileBulkRequest", "messageVersion" = "${var.cloud_scheduler_trade_message_version_v6}" }
    },
    refreshFullSyncV6 = {
      name                 = "full-trade-profile-bulk-refresh-v6"
      description          = "Job to trigger full-trade-profile-bulk request on pubsub for last 1080 hours"
      schedule             = "${var.cloud_scheduler_full_refresh_schedule_v6}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 1080,\"chunk\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeProfileBulkRequest", "messageVersion" = "${var.cloud_scheduler_trade_message_version_v6}" }
    },
    dataSyncV6RequestAllMembers = {
      name                 = "trade-profile-bulk-v6-request-include-all-members"
      description          = "Job to trigger latest trade-profile-bulk request with all membership types on pubsub"
      schedule             = "${var.cloud_scheduler_data_schedule_v6_all_members}"
      timezone             = "UTC"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 12,\"chunk\": 1000, \"usecatlive\":false,\"includeAllMembershipTypes\":true,\"limit\":150000,\"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeProfileBulkRequest", "messageVersion" = "${var.cloud_scheduler_trade_message_version_v6}" }
    },
    dataSyncV6RequestPartial = {
      name                 = "trade-profile-bulk-v6-request-partial"
      description          = "Job to trigger latest trade-profile-bulk request with partial membership types on pubsub"
      schedule             = "${var.cloud_scheduler_data_schedule_v6_partial}"
      timezone             = "UTC"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 12,\"chunk\": 1000, \"usecatlive\":false,\"includeAllMembershipTypes\":false,\"limit\":150000,\"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeProfileBulkRequest", "messageVersion" = "${var.cloud_scheduler_trade_message_version_v6}" }
    },
    refreshSyncV6Request = {
      name                 = "trade-profile-bulk-refresh-v6-request"
      description          = "Job to trigger latest trade-profile-bulk request on pubsub for last 1080 hours"
      schedule             = "${var.cloud_scheduler_refresh_schedule_v6}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 1080,\"chunk\": 1000, \"usecatlive\":false, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeProfileBulkRequest", "messageVersion" = "${var.cloud_scheduler_trade_message_version_v6}" }
    },
    refreshFullSyncV6Request = {
      name                 = "full-trade-profile-bulk-refresh-v6-request"
      description          = "Job to trigger latest full-trade-profile-bulk request on pubsub for last 1080 hours"
      schedule             = "${var.cloud_scheduler_full_refresh_schedule_v6}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 1080,\"chunk\": 1000, \"usecatlive\":false, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeProfileBulkRequest", "messageVersion" = "${var.cloud_scheduler_trade_message_version_v6}" }
    },
    workAlertPreferenceDataSync = {
      name                 = "work-alert-preference-bulk"
      description          = "Job to trigger WorkAlertPreferenceBulkReqeust on pubsub"
      schedule             = "${var.cloud_scheduler_work_alert_preference_data_schedule}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-work-alert-preference-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 13,\"chunk\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "WorkAlertPreferenceBulkRequest", "messageVersion" = "Basic" }
    },
    workAlertPreferenceRefreshSync = {
      name                 = "work-alert-preference-bulk-refresh"
      description          = "Job to trigger WorkAlertPreferenceBulkRequest for last 1080 hours"
      schedule             = "${var.cloud_scheduler_work_alert_preference_refresh_schedule}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-work-alert-preference-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 1080,\"chunk\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "WorkAlertPreferenceBulkRequest", "messageVersion" = "Basic" }
    },
    workAlertPreferenceV2DataSync = {
      name                 = "work-alert-preference-v2-bulk"
      description          = "Job to trigger WorkAlertPreferenceV2BulkReqeust on pubsub"
      schedule             = "${var.cloud_scheduler_work_alert_preference_v2_data_schedule}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-experience-work-alert-preference-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 13,\"chunk\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "WorkAlertPreferenceBulkRequest", "messageVersion" = "v2" }
    },
    workAlertPreferenceV2RefreshSync = {
      name                 = "work-alert-preference-v2-bulk-refresh"
      description          = "Job to trigger WorkAlertPreferenceV2BulkRequest for last 1080 hours"
      schedule             = "${var.cloud_scheduler_work_alert_preference_v2_refresh_schedule}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-experience-work-alert-preference-bulk-req"
      topic_data_in_base64 = base64encode("{\"maxhourssincelastmodified\": 1080,\"chunk\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "WorkAlertPreferenceBulkRequest", "messageVersion" = "v2" }
    },
    tradeExperienceCategoriesRequestRefreshSync = {
      name                 = "trade-experience-categories-request-bulk-refresh"
      description          = "Triggers Content API to get all Categories response to Trade Experience Firestore"
      schedule             = "${var.cloud_scheduler_trade_experience_categories_bulk_refresh_schedule}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-trade-experience-bulk-request"
      topic_data_in_base64 = base64encode("{\"chunkSize\": 1000, \"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "TradeExperienceCategoryRequest", "messageVersion" = "v1" }
    },
    categoriesRequestTask = {
      name                 = "categories-request"
      description          = "Triggers ContentAPI to push Categories"
      schedule             = "${var.cloud_scheduler_categories_schedule}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-categories-request"
      topic_data_in_base64 = base64encode("{\"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "CategoriesRequest", "messageVersion" = "Basic" }
    },
    categoriesRequestV2Task = {
      name                 = "categories-request-v2"
      description          = "Triggers ContentAPI to push Categories V2"
      schedule             = "${var.cloud_scheduler_categories_v2_schedule}"
      topic_name_id        = "projects/${module.project.id}/topics/content-api-categories-request"
      topic_data_in_base64 = base64encode("{\"originator\":\"scheduler\"}")
      topic_attributes     = { "messageType" = "CategoriesRequest", "messageVersion" = "V2" }
    }
  }
}

module "cloud_scheduler_http_job" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_scheduler?ref=3.86.0"

  project            = module.project
  name               = "schedule-cloud-build"
  schedule           = var.cloud_scheduler_trigger_manual_cloud-build
  http_target        = true
  http_target_method = "POST"
  http_target_uri    = "https://cloudbuild.googleapis.com/v1/projects/${module.project.id}/triggers/${module.cloud_build_triggers_schedule["scheduledTrigger"].manual_trigger_triggerid[0]}:run"
  http_target_sa     = google_service_account.cloud_scheduler_sa.email
  oidc_token         = false
  oauth_token        = true
}

module "cloud_scheduler" {
  for_each = local.scheduler_jobs
  source   = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_scheduler?ref=3.62.1"

  project     = module.project
  name        = each.value["name"]
  description = each.value["description"]
  schedule    = each.value["schedule"]

  http_target = false

  topic_name_id        = each.value["topic_name_id"]
  topic_data_in_base64 = each.value["topic_data_in_base64"]
  topic_attributes     = each.value["topic_attributes"]

  timezone = try(each.value["timezone"], "Europe/London")
}
