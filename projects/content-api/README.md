

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.3.9 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_assert_operations_projects_length"></a> [assert\_operations\_projects\_length](#module\_assert\_operations\_projects\_length) | rhythmictech/errorcheck/terraform | 1.3.0 |
| <a name="module_cloud_build_triggers"></a> [cloud\_build\_triggers](#module\_cloud\_build\_triggers) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_build | 3.77.0 |
| <a name="module_cloud_build_triggers_schedule"></a> [cloud\_build\_triggers\_schedule](#module\_cloud\_build\_triggers\_schedule) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_build | 3.84.4 |
| <a name="module_cloud_run"></a> [cloud\_run](#module\_cloud\_run) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars | 3.79.0 |
| <a name="module_cloud_run_cpu_alert"></a> [cloud\_run\_cpu\_alert](#module\_cloud\_run\_cpu\_alert) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/cpu_alerts | 3.79.0 |
| <a name="module_cloud_run_mem_alert"></a> [cloud\_run\_mem\_alert](#module\_cloud\_run\_mem\_alert) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/memory_alerts | 3.79.0 |
| <a name="module_cloud_run_response_codes_alert"></a> [cloud\_run\_response\_codes\_alert](#module\_cloud\_run\_response\_codes\_alert) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars/response_code_alerts | 3.79.0 |
| <a name="module_cloud_scheduler"></a> [cloud\_scheduler](#module\_cloud\_scheduler) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_scheduler | 3.62.1 |
| <a name="module_cloud_scheduler_http_job"></a> [cloud\_scheduler\_http\_job](#module\_cloud\_scheduler\_http\_job) | git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_scheduler | 3.86.0 |
| <a name="module_datadog"></a> [datadog](#module\_datadog) | git::**************:cat-home-experts/terraform-modules.git//checkatrade/datadog/gcp | 3.65.2 |
| <a name="module_deployment_metrics_trigger"></a> [deployment\_metrics\_trigger](#module\_deployment\_metrics\_trigger) | git::**************:cat-home-experts/terraform-modules.git//checkatrade/deployment_metrics_trigger | 3.63.4 |
| <a name="module_firebase"></a> [firebase](#module\_firebase) | git::**************:cat-home-experts/terraform-modules.git//gcp/firebase | 3.63.8 |
| <a name="module_project"></a> [project](#module\_project) | git::**************:cat-home-experts/terraform-modules.git//gcp/project | 3.63.3 |
| <a name="module_project_access_permissions"></a> [project\_access\_permissions](#module\_project\_access\_permissions) | git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative | 2.0.0 |
| <a name="module_pub_sub_subscription"></a> [pub\_sub\_subscription](#module\_pub\_sub\_subscription) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt | 3.79.0 |
| <a name="module_pub_sub_subscriptions_for_comparison_testing"></a> [pub\_sub\_subscriptions\_for\_comparison\_testing](#module\_pub\_sub\_subscriptions\_for\_comparison\_testing) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt | 3.79.0 |
| <a name="module_pub_sub_subscriptions_for_consumer_area"></a> [pub\_sub\_subscriptions\_for\_consumer\_area](#module\_pub\_sub\_subscriptions\_for\_consumer\_area) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt | 3.79.0 |
| <a name="module_pub_sub_subscriptions_for_content_api"></a> [pub\_sub\_subscriptions\_for\_content\_api](#module\_pub\_sub\_subscriptions\_for\_content\_api) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt | 3.79.0 |
| <a name="module_pub_sub_subscriptions_for_reviews"></a> [pub\_sub\_subscriptions\_for\_reviews](#module\_pub\_sub\_subscriptions\_for\_reviews) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt | 3.79.0 |
| <a name="module_pub_sub_subscriptions_for_search_indexer"></a> [pub\_sub\_subscriptions\_for\_search\_indexer](#module\_pub\_sub\_subscriptions\_for\_search\_indexer) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt | 3.79.0 |
| <a name="module_pub_sub_subscriptions_for_trade_experience"></a> [pub\_sub\_subscriptions\_for\_trade\_experience](#module\_pub\_sub\_subscriptions\_for\_trade\_experience) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt | 3.79.0 |
| <a name="module_pub_sub_topic"></a> [pub\_sub\_topic](#module\_pub\_sub\_topic) | git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/topic | 3.62.1 |
| <a name="module_secrets"></a> [secrets](#module\_secrets) | git::**************:cat-home-experts/terraform-modules.git//gcp/secret_manager/store | 3.62.1 |
| <a name="module_serverless_connector"></a> [serverless\_connector](#module\_serverless\_connector) | git::**************:cat-home-experts/terraform-modules.git//gcp/networking/vpc_access_connector | 1.0.0 |
| <a name="module_slackbot_trigger"></a> [slackbot\_trigger](#module\_slackbot\_trigger) | git::**************:cat-home-experts/terraform-modules.git//gcp/slackbot_trigger | 3.65.1 |
| <a name="module_vpc"></a> [vpc](#module\_vpc) | git::**************:cat-home-experts/terraform-modules.git//gcp/networking/vpc | 3.62.1 |

## Resources

| Name | Type |
|------|------|
| [google_compute_subnetwork.peered_subnet](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_subnetwork) | resource |
| [google_service_account.cloud_scheduler_sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_service_account.comparison_testing_pubsub_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_service_account.consumer_area_pubsub_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_service_account.jobs_pubsub_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_service_account.pubsub_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_service_account.reviews_pubsub_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_service_account.search_pubsub_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_service_account.trade_experience_pubsub_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_active_folder.cloudbuild_current](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/active_folder) | data source |
| [google_active_folder.current](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/active_folder) | data source |
| [google_active_folder.for_operations_projects](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/active_folder) | data source |
| [google_cloud_run_service.jobs_cloud_run](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/cloud_run_service) | data source |
| [google_cloud_run_service.reviews_reviews_ingester](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/cloud_run_service) | data source |
| [google_cloud_run_service.search_indexer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/cloud_run_service) | data source |
| [google_cloud_run_service.trade_experience](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/cloud_run_service) | data source |
| [google_project.consumer_area](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |
| [google_project.reviews](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |
| [google_project.trade_experience](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |
| [google_projects.image_service_projects](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/projects) | data source |
| [google_projects.operations_projects](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/projects) | data source |
| [google_projects.reviews_projects](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/projects) | data source |
| [google_projects.search_projects](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/projects) | data source |
| [google_projects.secure_contacts_projects](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/projects) | data source |
| [google_projects.trade_experience_projects](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/projects) | data source |
| [google_secret_manager_secret_version.github_packages_token](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/secret_manager_secret_version) | data source |
| [google_secret_manager_secret_version.github_packages_username](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/secret_manager_secret_version) | data source |
| [google_service_account.run_reviews_backend](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/service_account) | data source |
| [google_service_account.sc_content_api_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/service_account) | data source |
| [google_service_account.trade_experience_content_api_publisher](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/service_account) | data source |
| [google_service_account.trade_experience_run_app_api](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/service_account) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_alert_duration"></a> [alert\_duration](#input\_alert\_duration) | Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values | `string` | n/a | yes |
| <a name="input_alert_duration_cpu"></a> [alert\_duration\_cpu](#input\_alert\_duration\_cpu) | Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values | `string` | n/a | yes |
| <a name="input_alert_duration_memory"></a> [alert\_duration\_memory](#input\_alert\_duration\_memory) | Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values | `string` | n/a | yes |
| <a name="input_alert_duration_response_codes"></a> [alert\_duration\_response\_codes](#input\_alert\_duration\_response\_codes) | Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values | `string` | n/a | yes |
| <a name="input_apis"></a> [apis](#input\_apis) | n/a | `list(string)` | `[]` | no |
| <a name="input_cloud_run_env_variables"></a> [cloud\_run\_env\_variables](#input\_cloud\_run\_env\_variables) | n/a | `list(any)` | n/a | yes |
| <a name="input_cloud_scheduler_categories_schedule"></a> [cloud\_scheduler\_categories\_schedule](#input\_cloud\_scheduler\_categories\_schedule) | Cron schedule for Categories Refresh | `string` | n/a | yes |
| <a name="input_cloud_scheduler_categories_v2_schedule"></a> [cloud\_scheduler\_categories\_v2\_schedule](#input\_cloud\_scheduler\_categories\_v2\_schedule) | Cron schedule for Categories Refresh | `string` | n/a | yes |
| <a name="input_cloud_scheduler_data_schedule_v6"></a> [cloud\_scheduler\_data\_schedule\_v6](#input\_cloud\_scheduler\_data\_schedule\_v6) | Cron schedule for Standard Bulk Run | `string` | n/a | yes |
| <a name="input_cloud_scheduler_full_refresh_schedule_v6"></a> [cloud\_scheduler\_full\_refresh\_schedule\_v6](#input\_cloud\_scheduler\_full\_refresh\_schedule\_v6) | Cron schedule for Full Bulk Refresh Run | `string` | n/a | yes |
| <a name="input_cloud_scheduler_refresh_schedule_v6"></a> [cloud\_scheduler\_refresh\_schedule\_v6](#input\_cloud\_scheduler\_refresh\_schedule\_v6) | Cron schedule for Bulk Refresh Run | `string` | n/a | yes |
| <a name="input_cloud_scheduler_reviews_all_schedule"></a> [cloud\_scheduler\_reviews\_all\_schedule](#input\_cloud\_scheduler\_reviews\_all\_schedule) | Cron schedule for requesting all reviews from Content API | `string` | n/a | yes |
| <a name="input_cloud_scheduler_reviews_basic_bulk_schedule"></a> [cloud\_scheduler\_reviews\_basic\_bulk\_schedule](#input\_cloud\_scheduler\_reviews\_basic\_bulk\_schedule) | Cron schedule for requesting reviews with basic updates from Content API | `string` | n/a | yes |
| <a name="input_cloud_scheduler_reviews_bulk_schedule"></a> [cloud\_scheduler\_reviews\_bulk\_schedule](#input\_cloud\_scheduler\_reviews\_bulk\_schedule) | Cron schedule for requesting updated reviews from Content API | `string` | n/a | yes |
| <a name="input_cloud_scheduler_trade_experience_categories_bulk_refresh_schedule"></a> [cloud\_scheduler\_trade\_experience\_categories\_bulk\_refresh\_schedule](#input\_cloud\_scheduler\_trade\_experience\_categories\_bulk\_refresh\_schedule) | Cron schedule for TradeExperienceCategoryRequest refresh run | `string` | `"0 4 * * *"` | no |
| <a name="input_cloud_scheduler_trade_message_version_v6"></a> [cloud\_scheduler\_trade\_message\_version\_v6](#input\_cloud\_scheduler\_trade\_message\_version\_v6) | Message Version for Trade Bulk Runs | `string` | n/a | yes |
| <a name="input_cloud_scheduler_trigger_manual_cloud-build"></a> [cloud\_scheduler\_trigger\_manual\_cloud-build](#input\_cloud\_scheduler\_trigger\_manual\_cloud-build) | Cron schedule for trigger manual cloud build | `string` | `"0 4 * * *"` | no |
| <a name="input_cloud_scheduler_work_alert_preference_data_schedule"></a> [cloud\_scheduler\_work\_alert\_preference\_data\_schedule](#input\_cloud\_scheduler\_work\_alert\_preference\_data\_schedule) | Cron schedule for WorkAlertBulkRequest all data run | `string` | n/a | yes |
| <a name="input_cloud_scheduler_work_alert_preference_refresh_schedule"></a> [cloud\_scheduler\_work\_alert\_preference\_refresh\_schedule](#input\_cloud\_scheduler\_work\_alert\_preference\_refresh\_schedule) | Cron schedule for WorkAlertBulkRequest refresh run | `string` | n/a | yes |
| <a name="input_cloud_scheduler_work_alert_preference_v2_data_schedule"></a> [cloud\_scheduler\_work\_alert\_preference\_v2\_data\_schedule](#input\_cloud\_scheduler\_work\_alert\_preference\_v2\_data\_schedule) | Cron schedule for WorkAlertBulkRequestV2 all data run | `string` | n/a | yes |
| <a name="input_cloud_scheduler_work_alert_preference_v2_refresh_schedule"></a> [cloud\_scheduler\_work\_alert\_preference\_v2\_refresh\_schedule](#input\_cloud\_scheduler\_work\_alert\_preference\_v2\_refresh\_schedule) | Cron schedule for WorkAlertBulkRequestV2 refresh run | `string` | n/a | yes |
| <a name="input_cloudbuild_triggers"></a> [cloudbuild\_triggers](#input\_cloudbuild\_triggers) | n/a | <pre>map(object({<br>    name                         = string<br>    description                  = string<br>    disabled                     = bool<br>    push_trigger_enabled         = bool<br>    pull_request_trigger_enabled = bool<br>    owner                        = string<br>    repo_name                    = string<br>    branch_regex                 = string<br>    invert_regex                 = bool<br>    comment_control              = string<br>    filename                     = string<br>    env_variables                = map(string)<br>    included_files_filter        = list(string)<br>  }))</pre> | n/a | yes |
| <a name="input_cloudsql_instance"></a> [cloudsql\_instance](#input\_cloudsql\_instance) | Name of sql instance for sql connector | `string` | n/a | yes |
| <a name="input_consumer_area_project_id"></a> [consumer\_area\_project\_id](#input\_consumer\_area\_project\_id) | n/a | `string` | n/a | yes |
| <a name="input_container_concurrency"></a> [container\_concurrency](#input\_container\_concurrency) | Concurrency per container in cloud run service | `number` | `20` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | n/a | `string` | `""` | no |
| <a name="input_jobs_management_project_id"></a> [jobs\_management\_project\_id](#input\_jobs\_management\_project\_id) | n/a | `string` | n/a | yes |
| <a name="input_project_folder"></a> [project\_folder](#input\_project\_folder) | n/a | `string` | n/a | yes |
| <a name="input_project_name"></a> [project\_name](#input\_project\_name) | n/a | `string` | `"content-api"` | no |
| <a name="input_project_static_permissions"></a> [project\_static\_permissions](#input\_project\_static\_permissions) | A map of roles to their list of IAM groups. | `map(list(string))` | `{}` | no |
| <a name="input_region"></a> [region](#input\_region) | n/a | `string` | `"europe-west2"` | no |
| <a name="input_reviews_project_id"></a> [reviews\_project\_id](#input\_reviews\_project\_id) | n/a | `string` | n/a | yes |
| <a name="input_salesforce_api_uri"></a> [salesforce\_api\_uri](#input\_salesforce\_api\_uri) | Salesforce API URL | `string` | n/a | yes |
| <a name="input_salesforce_id_server"></a> [salesforce\_id\_server](#input\_salesforce\_id\_server) | Salesforce ID server URL | `string` | n/a | yes |
| <a name="input_salesforce_integ_project_id"></a> [salesforce\_integ\_project\_id](#input\_salesforce\_integ\_project\_id) | n/a | `string` | n/a | yes |
| <a name="input_schedule_cloudbuild_triggers"></a> [schedule\_cloudbuild\_triggers](#input\_schedule\_cloudbuild\_triggers) | n/a | <pre>map(object({<br>    name                         = string<br>    description                  = string<br>    disabled                     = bool<br>    push_trigger_enabled         = bool<br>    pull_request_trigger_enabled = bool<br>    manual_trigger_enabled       = bool<br>    owner                        = string<br>    repo_name                    = string<br>    branch_regex                 = string<br>    invert_regex                 = bool<br>    comment_control              = string<br>    filename                     = string<br>    env_variables                = map(string)<br>    included_files_filter        = list(string)<br>  }))</pre> | n/a | yes |
| <a name="input_threshold_value"></a> [threshold\_value](#input\_threshold\_value) | Number of unacked messages before threshold is considered breached | `number` | n/a | yes |
| <a name="input_threshold_value_cpu"></a> [threshold\_value\_cpu](#input\_threshold\_value\_cpu) | Percent of cpu utilisation before threshold is considered breached | `string` | n/a | yes |
| <a name="input_threshold_value_memory"></a> [threshold\_value\_memory](#input\_threshold\_value\_memory) | Number of unacked messages before threshold is considered breached | `string` | n/a | yes |
| <a name="input_threshold_value_response_codes"></a> [threshold\_value\_response\_codes](#input\_threshold\_value\_response\_codes) | Rate per second of response codes received in the given range before threshold is considered breached | `string` | n/a | yes |
| <a name="input_trade_experience_project_id"></a> [trade\_experience\_project\_id](#input\_trade\_experience\_project\_id) | n/a | `string` | n/a | yes |
| <a name="input_trigger_count"></a> [trigger\_count](#input\_trigger\_count) | Number of times the threshold must be breached before triggering the alert | `number` | n/a | yes |
| <a name="input_trigger_count_cpu"></a> [trigger\_count\_cpu](#input\_trigger\_count\_cpu) | Number of times the threshold must be breached before triggering the alert | `string` | n/a | yes |
| <a name="input_trigger_count_memory"></a> [trigger\_count\_memory](#input\_trigger\_count\_memory) | Number of times the threshold must be breached before triggering the alert | `string` | n/a | yes |
| <a name="input_trigger_count_response_codes"></a> [trigger\_count\_response\_codes](#input\_trigger\_count\_response\_codes) | Number of times the threshold must be breached before triggering the alert | `string` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->
