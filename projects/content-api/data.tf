# Secure contacts Data

data "google_active_folder" "cloudbuild_current" {
  display_name = var.project_folder
  parent       = "organizations/************"
}

data "google_projects" "secure_contacts_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:secure-contacts*"
}

data "google_service_account" "sc_content_api_publisher" {
  account_id = "sc-content-api-publisher@${data.google_projects.secure_contacts_projects.projects[0].project_id}.iam.gserviceaccount.com"
}

data "google_projects" "image_service_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:image-service*"
}

data "google_projects" "trade_experience_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:trade-experience*"
}

data "google_projects" "directories_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:directories*"
}

data "google_projects" "salesforce_integ_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:salesforce-integ*"
}

data "google_service_account" "trade_experience_content_api_publisher" {
  account_id = "trade-exp-to-content-api@${data.google_projects.trade_experience_projects.projects[0].project_id}.iam.gserviceaccount.com"
}

data "google_service_account" "trade_experience_run_app_api" {
  account_id = "run-app-api@${data.google_projects.trade_experience_projects.projects[0].project_id}.iam.gserviceaccount.com"
}

data "google_projects" "reviews_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:reviews*"
}

data "google_service_account" "run_reviews_backend" {
  account_id = "run-reviews-backend@${data.google_projects.reviews_projects.projects[0].project_id}.iam.gserviceaccount.com"
}

data "google_service_account" "directories_run_app_api" {
  account_id = "run-directories-webapp@${data.google_projects.directories_projects.projects[0].project_id}.iam.gserviceaccount.com"
}

data "google_service_account" "salesforce_integ_run_app" {
  account_id = "run-sf-integ-admin-ui@${data.google_projects.salesforce_integ_projects.projects[0].project_id}.iam.gserviceaccount.com"
}

data "google_projects" "ops_tooling_projects" {
  filter = "parent.id:${replace(data.google_active_folder.cloudbuild_current.id, "folders/", "")} name:ops-tooling-app*"
}

data "google_service_account" "run_ops_tooling_app" {
  account_id = "run-ops-tooling-app@${data.google_projects.ops_tooling_projects.projects[0].project_id}.iam.gserviceaccount.com"
}