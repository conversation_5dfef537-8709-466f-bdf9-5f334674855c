# All project related modules and configuration
locals {
  map_environment_to_project_id = tomap({
    development = "content-api-dev-18783"
    staging     = "content-api-staging-32612"
    production  = "content-api-production-36346"
  })
  project_configuration = {
    name   = var.project_name
    folder = var.project_folder
    apis = concat(var.apis, [
      "storage-component.googleapis.com",
      "cloudbuild.googleapis.com",
      "compute.googleapis.com",
      "run.googleapis.com",
      "secretmanager.googleapis.com",
      "pubsub.googleapis.com",
      "cloudfunctions.googleapis.com",
      "monitoring.googleapis.com",
      "logging.googleapis.com",
      "servicenetworking.googleapis.com",
      "vpcaccess.googleapis.com",
      "cloudscheduler.googleapis.com",
      "sqladmin.googleapis.com",
      "iam.googleapis.com",
    ])
  }
}

data "google_active_folder" "for_operations_projects" {
  display_name = var.project_folder == "int" ? "development" : "core"
  parent       = "organizations/535868630468"
}

data "google_projects" "operations_projects" {
  filter = "parent.id:${replace(data.google_active_folder.for_operations_projects.id, "folders/", "")} name:operations*"
}

module "assert_operations_projects_length" {
  source  = "rhythmictech/errorcheck/terraform"
  version = "1.3.0"

  assert        = length(data.google_projects.operations_projects.projects) == 1
  error_message = "Too many 'operations' projects under '${var.project_folder == "int" ? "development" : "core"}' folder"
}

module "project" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/project?ref=3.63.3"

  project_config    = local.project_configuration
  legacy_project_id = local.map_environment_to_project_id[var.project_folder]
  team_email        = "<EMAIL>"
  budget_limit      = 5000
}

module "firebase" {
  source  = "git::**************:cat-home-experts/terraform-modules.git//gcp/firebase?ref=3.63.8"
  project = module.project
}
