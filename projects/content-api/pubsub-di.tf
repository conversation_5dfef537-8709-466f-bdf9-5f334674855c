locals {
  di_topics_and_subs = [
    {
      topic                       = "content-api-trade-res"
      subscription                = "content-api-trade-res-di"
      subscription_host           = var.trade_res_di_api_gateway_host
      subscription_api_key_secret = module.retrieve_secrets["trade-res-di-api-gateway-key"].secret_data
    },
    {
      topic                       = "content-api-trade-response"
      subscription                = "content-api-trade-response-di"
      subscription_host           = var.trade_response_di_api_gateway_host
      subscription_api_key_secret = module.retrieve_secrets["trade-response-di-api-gateway-key"].secret_data
    }
  ]
}

module "pub_sub_subscriptions_for_di" {
  for_each = { for i, v in local.di_topics_and_subs : i => v }

  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription?ref=3.79.1"

  project           = module.project
  subscription_name = each.value["subscription"]
  topic_name        = each.value["topic"]
  env               = var.environment

  push_config = {
    push_endpoint = format("%s?apiKey=%s", each.value["subscription_host"], each.value["subscription_api_key_secret"])
  }
}