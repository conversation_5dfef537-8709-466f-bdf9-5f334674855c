locals {

  default_cloudbuild_service_account = "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com"

  project_team_admin = ["group:<EMAIL>"]
  project_team       = ["group:<EMAIL>"]

  service_accounts_for_cloudbuild_triggers = [
    for trg in var.cloudbuild_triggers : "serviceAccount:build-${lower(trg.name)}@${module.project.id}.iam.gserviceaccount.com"
  ]

  service_accounts_for_schedule_cloudbuild_triggers = [
    for trg in var.schedule_cloudbuild_triggers : "serviceAccount:build-${lower(trg.name)}@${module.project.id}.iam.gserviceaccount.com"
  ]

  project_computed_permissions = {
    "roles/monitoring.editor" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
    ]),
    "roles/secretmanager.secretAccessor" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      local.service_accounts_for_schedule_cloudbuild_triggers,
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]),
    "roles/secretmanager.secretVersionAdder" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      local.service_accounts_for_schedule_cloudbuild_triggers,
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
    ]),
    "roles/cloudfunctions.developer" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]),
    "roles/iam.serviceAccountTokenCreator" : flatten([
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:${google_service_account.cloud_scheduler_sa.email}",
      "serviceAccount:${data.google_service_account.run_ops_tooling_app.email}",
      "serviceAccount:${google_service_account.impersonation.email}",
      local.project_team,
      local.project_team_admin
    ]),
    "roles/cloudscheduler.jobRunner" : [
      "serviceAccount:${google_service_account.cloud_scheduler_sa.email}"
    ],
    "roles/run.invoker" : flatten([
      "serviceAccount:${google_service_account.pubsub_publisher[0].email}",
      "serviceAccount:${data.google_service_account.sc_content_api_publisher.email}",
      local.service_accounts_for_schedule_cloudbuild_triggers[0],
      "serviceAccount:${data.google_service_account.trade_experience_content_api_publisher.email}",
      "serviceAccount:salesforce-to-content-api@${var.salesforce_integ_project_id}.iam.gserviceaccount.com",
      "serviceAccount:run-app-api@${var.trade_experience_project_id}.iam.gserviceaccount.com",
      "serviceAccount:${google_service_account.data_and_insights.email}",
      "serviceAccount:${data.google_service_account.directories_run_app_api.email}",
      "serviceAccount:${google_service_account.hackathon.email}",
      "serviceAccount:${data.google_service_account.run_ops_tooling_app.email}",
      "serviceAccount:${google_service_account.impersonation.email}",
      "serviceAccount:${data.google_service_account.salesforce_integ_run_app.email}",
      var.capi_service_accounts
    ]),
    "roles/run.admin" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]),
    "roles/iam.serviceAccountUser" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      local.service_accounts_for_schedule_cloudbuild_triggers,
      "serviceAccount:${google_service_account.cloud_scheduler_sa.email}",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${data.google_service_account.run_ops_tooling_app.email}",
      "serviceAccount:${google_service_account.impersonation.email}",
    ]),
    "roles/pubsub.admin" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]),
    "roles/errorreporting.user" : flatten([
      local.default_cloudbuild_service_account,
      local.service_accounts_for_cloudbuild_triggers,
      local.service_accounts_for_schedule_cloudbuild_triggers,
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]),
    "roles/cloudbuild.builds.builder" : flatten(concat([
      local.service_accounts_for_cloudbuild_triggers,
      local.service_accounts_for_schedule_cloudbuild_triggers,
      local.default_cloudbuild_service_account,
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.slackbot_trigger.cloud_build_sa_email}",
      local.project_team_admin
    ])),
    "roles/cloudbuild.builds.editor" : flatten([
      "serviceAccount:${google_service_account.cloud_scheduler_sa.email}"
    ]),
    "roles/pubsub.publisher" : flatten(concat([
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:${data.google_project.trade_experience.number}-<EMAIL>",
      "serviceAccount:${data.google_service_account.trade_experience_run_app_api.email}",
      "serviceAccount:${google_service_account.trade_experience_pubsub_publisher.email}",
      "serviceAccount:${data.google_project.consumer_area.project_id}@appspot.gserviceaccount.com",
      "serviceAccount:run-salesforce-gcp-integ@${var.salesforce_integ_project_id}.iam.gserviceaccount.com"
      ],
      length(local.content_api_topics_and_subs) > 0 ? ["serviceAccount:${google_service_account.pubsub_publisher[0].email}"] : [],
      var.capi_service_accounts
    )),
    "roles/pubsub.subscriber" : flatten(concat([
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${module.cloud_run.service_account_email}",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:${data.google_project.trade_experience.number}-<EMAIL>",
      "serviceAccount:${google_service_account.trade_experience_pubsub_publisher.email}",
      "serviceAccount:${data.google_project.consumer_area.project_id}@appspot.gserviceaccount.com",
      "serviceAccount:run-salesforce-gcp-integ@${var.salesforce_integ_project_id}.iam.gserviceaccount.com",
      "serviceAccount:${data.google_service_account.trade_experience_run_app_api.email}",
      ],
      length(local.content_api_topics_and_subs) > 0 ? ["serviceAccount:${google_service_account.pubsub_publisher[0].email}"] : [],
      var.capi_service_accounts
    )),
  }
}

module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project  = module.project
  humans   = var.project_static_permissions
  machines = local.project_computed_permissions
}

# Grant the poc_jamespain_invoker service account permission to invoke the content-api Cloud Run service
resource "google_cloud_run_service_iam_member" "poc_jamespain_invoker_permission" {
  project  = module.project.id
  location = module.cloud_run.location
  service  = module.cloud_run.service_name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.poc_jamespain_invoker.email}"
}

# Grant the external service account permission to impersonate the poc_jamespain_invoker service account
resource "google_service_account_iam_member" "poc_jamespain_impersonation" {
  service_account_id = google_service_account.poc_jamespain_invoker.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:<EMAIL>"
}

# Grant the cat4b staging service account permission to impersonate the poc_jamespain_invoker service account
resource "google_service_account_iam_member" "catforbusiness_staging_impersonation" {
  service_account_id = google_service_account.poc_jamespain_invoker.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:<EMAIL>"
}

# Grant the cat4b prod service account permission to impersonate the poc_jamespain_invoker service account
resource "google_service_account_iam_member" "catforbusiness_prod_impersonation" {
  service_account_id = google_service_account.poc_jamespain_invoker.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:<EMAIL>"
}
