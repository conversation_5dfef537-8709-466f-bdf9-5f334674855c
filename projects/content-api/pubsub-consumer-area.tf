locals {
  consumer_area_topics_and_subs = [
    {
      topic    = "content-api-user-res"
      endpoint = "cloudGroup-updateUserData"
    }
  ]
}
resource "google_service_account" "consumer_area_pubsub_publisher" {
  project = module.project.id

  account_id   = "content-api-to-consumer-area"
  display_name = "Used to publish messages from pub/sub to consumer area cloud function"
}

data "google_project" "consumer_area" {
  project_id = var.consumer_area_project_id
}

module "pub_sub_subscriptions_for_consumer_area" {
  for_each = { for i, v in local.consumer_area_topics_and_subs : i => v }

  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/pubsub/subscription_jwt?ref=3.79.0"

  project           = module.project
  subscription_name = "consumer-area-${element(split("-", each.value["topic"]), 4)}"
  topic_name        = each.value["topic"]
  service_account   = google_service_account.consumer_area_pubsub_publisher.email

  dead_letter_policy_enabled = true
  dead_letter_queue_name     = "${each.value["topic"]}-dead-letter"

  push_config = {
    push_endpoint = "https://${var.region}-${var.consumer_area_project_id}.cloudfunctions.net/${each.value["endpoint"]}"
    audience      = "https://${var.region}-${var.consumer_area_project_id}.cloudfunctions.net/${each.value["endpoint"]}"
  }
  env = var.environment
}
