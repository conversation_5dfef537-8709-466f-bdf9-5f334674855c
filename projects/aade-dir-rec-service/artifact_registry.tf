module "artifact_registry" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/artifact_registry?ref=3.63.1"

  project = module.project
  name    = var.project_name

  # Registry readers and writers set to null in order to avoid adding the admin role to the service account specified,
  # because admin roles require approve from the platform team.
  # Ideally, the module should be updated to avoid using the admin role
  registry_readers = null
  registry_writers = null
}
