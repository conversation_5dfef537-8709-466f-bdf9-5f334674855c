module "secrets" {
  source  = "git::**************:cat-home-experts/terraform-modules.git//gcp/secret_manager/store?ref=3.99.5"
  project = module.project
  secrets_keys = [
    {
      name = "DATABASE_URL"
    }
  ]
  provisioner_enabled = true
}

module "retrieve_secrets" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/secret_manager/retrieve?ref=3.63.2"

  for_each = toset(module.secrets.secret_names)

  project    = module.project
  secret_id  = each.key
  depends_on = [module.secrets]
}
