locals {
  db_name      = "${var.project_name}-api"
  db_user_name = local.db_name
}

resource "random_password" "db" {
  length  = 16
  special = false

  lifecycle {
    ignore_changes = all
  }
}

module "postgres_database" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_sql?ref=3.93.0"

  instance_name    = var.project_name
  database_name    = local.db_name
  database_version = "POSTGRES_15"
  project          = module.project
  region           = var.region

  tier              = var.sql_db_params.tier
  availability_type = "REGIONAL"

  notification_targets            = module.project.default_slack_channel_alerts.datadog_formatted
  cpu_monitor                     = {}
  database_instance_state_monitor = {}

  backup_enabled                        = true
  backup_bucket_name_override           = var.sql_backup_bucket_name
  backup_point_in_time_recovery_enabled = true

  ipv4_enabled       = true
  private_network_id = module.vpc.network_id

  user_name     = local.db_user_name
  user_password = random_password.db.result
  disk_size     = var.sql_db_params.disk_size

  custom_labels = var.labels

  depends_on = [module.vpc]
}
