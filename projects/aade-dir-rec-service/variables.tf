variable "organization" {
  type    = string
  default = "organizations/535868630468"
}

variable "project_name" {
  type    = string
  default = ""
}

variable "environment" {
  type    = string
  default = ""
}

variable "region" {
  type        = string
  description = "(Optional) The region the project will sit in."
  default     = "europe-west2"
}

variable "apis" {
  type    = list(string)
  default = []
}

variable "project_static_permissions" {
  type        = map(list(string))
  description = "A map of roles to their list of IAM groups."
  default     = {}
}

variable "project_team" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "project_team_admin" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "qa_team" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "qa_team_admin" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "viewers" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "everyone" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "labels" {
  type        = map(string)
  description = "A map of labels to be applied to all resources."
  default     = {}
}

variable "consent_screen_app_title" {
  type = string
}

variable "api_dns_name" {
  type = string
}

variable "sql_db_params" {
  type = object({
    tier      = optional(string, "db-g1-small")
    disk_size = optional(number, 10)
  })
}

variable "sql_backup_bucket_name" {
  type    = string
  default = ""
}