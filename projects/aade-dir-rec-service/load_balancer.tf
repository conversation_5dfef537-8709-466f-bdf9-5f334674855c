resource "google_compute_url_map" "api" {
  project         = module.project.id
  name            = "${var.project_name}-api-load-balancer"
  default_service = google_compute_backend_service.api.self_link

  host_rule {
    hosts        = [var.api_dns_name]
    path_matcher = "api"
  }

  path_matcher {
    name            = "api"
    default_service = google_compute_backend_service.api.self_link
  }

  timeouts {}
}

resource "google_compute_global_address" "api" {
  project      = module.project.id
  name         = "${var.project_name}-api-address"
  ip_version   = "IPV4"
  address_type = "EXTERNAL"
}

resource "google_compute_global_forwarding_rule" "https" {
  project    = module.project.id
  name       = "${var.project_name}-api-https-rule"
  target     = google_compute_target_https_proxy.api.self_link
  ip_address = google_compute_global_address.api.address
  port_range = "443"

  labels = var.labels
}

resource "google_compute_target_https_proxy" "api" {
  project          = module.project.id
  name             = "${var.project_name}-api-https-proxy"
  url_map          = google_compute_url_map.api.self_link
  ssl_certificates = [google_compute_managed_ssl_certificate.api.id]
}

resource "google_compute_managed_ssl_certificate" "api" {
  project = module.project.id
  name    = "${var.project_name}-api"

  managed {
    domains = [var.api_dns_name]
  }
}

resource "google_compute_region_network_endpoint_group" "api" {
  project               = module.project.id
  name                  = "${var.project_name}-api"
  network_endpoint_type = "SERVERLESS"
  region                = var.region

  cloud_run {
    service = module.cloud_run_api_service.service_name
  }
}

resource "google_compute_backend_service" "api" {
  project  = module.project.id
  name     = "${var.project_name}-api"
  protocol = "HTTPS"

  backend {
    group = google_compute_region_network_endpoint_group.api.id
  }

  iap {
    oauth2_client_id     = module.iap.iap_client_id
    oauth2_client_secret = module.iap.iap_client_secret
  }
}
