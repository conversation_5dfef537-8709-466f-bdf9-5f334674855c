module "cloud_run_api_service" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service_with_env_vars?ref=3.101.1"

  project = module.project
  region  = var.region

  name = var.project_name

  cpu    = "1000m"
  memory = "2Gi"

  secret_variables = [
    { secret_id = "DATABASE_URL" },
  ]

  env_variables = []

  gcr_image                = "us-docker.pkg.dev/cloudrun/container/hello"
  container_port           = 8080
  container_concurrency    = 2
  requests_timeout_seconds = 30

  template_metadata_annotations = {
    "autoscaling.knative.dev/minScale"        = var.environment == "production" ? "1" : "1"
    "autoscaling.knative.dev/maxScale"        = var.environment == "production" ? "2" : "1"
    "run.googleapis.com/cloudsql-instances"   = module.postgres_database.instance.connection_name
    "run.googleapis.com/vpc-access-connector" = module.serverless_connector.connector_id
    "run.googleapis.com/vpc-access-egress"    = "private-ranges-only"
  }

  custom_labels = var.labels
}
