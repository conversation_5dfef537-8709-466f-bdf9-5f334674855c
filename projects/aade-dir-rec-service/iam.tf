locals {
  project_computed_permissions = {
    "roles/run.invoker" : flatten([
      "serviceAccount:${google_project_service_identity.iap.email}",
    ])
    "roles/logging.logWriter" : [
      "serviceAccount:${module.project.number}-<EMAIL>",
      "serviceAccount:${google_service_account.github_actions.email}"
    ],
    "roles/artifactregistry.writer" : [
      "serviceAccount:${google_service_account.github_actions.email}"
    ]
    "roles/run.developer" : [
      "serviceAccount:${google_service_account.github_actions.email}"
    ]
    "roles/iam.serviceAccountUser" : [
      "serviceAccount:${google_service_account.github_actions.email}"
    ]
    "roles/iap.httpsResourceAccessor" : [
      "serviceAccount:${google_project_service_identity.iap.email}"
    ]
    "roles/cloudfunctions.developer" : [
      "serviceAccount:${google_service_account.github_actions.email}",
    ]
    "roles/pubsub.publisher" : [
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
      "serviceAccount:${module.cloud_run_api_service.service_account_email}",
    ]
    "roles/pubsub.subscriber" : [
      "serviceAccount:service-${module.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com",
    ]
    "roles/secretmanager.secretAccessor" : [
      "serviceAccount:${module.cloud_run_api_service.service_account_email}",
    ]
    "roles/cloudsql.client" : [
      "serviceAccount:${module.cloud_run_api_service.service_account_email}",
    ]
  }
}

module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project  = module.project
  humans   = var.project_static_permissions
  machines = local.project_computed_permissions
}

# A service account for the IAP to be able to access the Cloud Run services from the Load Balancer
resource "google_project_service_identity" "iap" {
  provider = google-beta

  project = module.project.id
  service = "iap.googleapis.com"
}

resource "google_service_account" "airflow" {
  project      = module.project.id
  account_id   = "airflow"
  display_name = "Used by Airflow pipelines"
}
