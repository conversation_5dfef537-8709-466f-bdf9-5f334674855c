locals {
  main_subnet_cidr_range      = "10.10.4.0/24"
  connector_subnet_cidr_range = "10.10.5.0/28"
}

module "vpc" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/networking/vpc?ref=3.93.0"

  project                    = module.project
  network_name               = var.project_name
  auto_create_subnetworks    = false
  private_connection_enabled = true
  private_connection_name    = "sql-connection"
}

resource "google_compute_subnetwork" "main_subnet" {
  project = module.project.id
  network = module.vpc.network_name

  region        = var.region
  name          = "main-subnet"
  ip_cidr_range = local.main_subnet_cidr_range

}

resource "google_compute_subnetwork" "connector_subnet" {
  project = module.project.id
  network = module.vpc.network_name

  region        = var.region
  name          = "connector-subnet"
  ip_cidr_range = local.connector_subnet_cidr_range
}

module "serverless_connector" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/networking/vpc_access_connector?ref=3.93.0"

  project               = module.project
  name                  = "cloud-run-connector"
  region                = var.region
  connector_subnet_name = google_compute_subnetwork.connector_subnet.name
  max_throughput        = 500
}
