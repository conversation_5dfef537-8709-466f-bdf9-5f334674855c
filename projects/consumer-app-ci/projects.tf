locals {
  project_configuration = {
    name   = var.project_name
    folder = var.environment
    apis   = [] # Visit https://developers.google.com/apis-explorer
  }
}

module "project" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/project?ref=3.90.3"

  project_config = local.project_configuration
  team_email     = "<EMAIL>" # Change this

  groups = {
    create        = true
    group_name    = "gcp-${var.project_name}"
    group_manager = "<EMAIL>"
  }
}
