# Below is an example of how we use data sources to get the details of an existing project (in this case 'consumer-area')
# The outputs from google_projects.consumer_area_projects are used in an example environment variable within the
# cloud_build_triggers module call in cloud_build.tf. Remove these data sources and the assertion module call if
# you don't need to reference consumer-area

data "google_active_folder" "current" {
  display_name = var.environment
  parent       = "organizations/535868630468"
}

data "google_projects" "consumer_area_projects" {
  filter = "parent.id:${replace(data.google_active_folder.current.id, "folders/", "")} name:consumer-area*"
}

module "assert_consumer_area_projects_length" {
  source  = "rhythmictech/errorcheck/terraform"
  version = "1.3.0"

  assert        = length(data.google_projects.consumer_area_projects.projects) <= 1
  error_message = "Too many 'consumer-area' projects under '${var.environment}' folder."
}
