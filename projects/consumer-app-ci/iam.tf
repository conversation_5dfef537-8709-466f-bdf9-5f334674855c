locals {
  project_computed_permissions = {
    "roles/storage.objectViewer" : [
      "serviceAccount:${google_service_account.android_ci.email}", # When your pipeline needs read access to a Storage Bucket
    ]
    "roles/storage.admin" : [
      "serviceAccount:${google_service_account.android_ci.email}", # When your pipeline needs read access to a Storage Bucket
    ]
    "roles/firebaseappdistro.admin" : [
      "serviceAccount:${google_service_account.android_ci.email}", # When your pipeline needs read access to a Storage Bucket
    ]
    "roles/firebase.developAdmin" : [
      "serviceAccount:${google_service_account.android_ci.email}", # When your pipeline needs read access to a Storage Bucket
    ]
    "roles/cloudtestservice.testAdmin" : [
      "serviceAccount:${google_service_account.android_ci.email}", # When your pipeline needs read access to a Storage Bucket
    ]
  }
}

module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project  = module.project
  humans   = var.project_static_permissions
  machines = local.project_computed_permissions
}

resource "google_service_account" "android_ci" {
  account_id   = "android-ci"
  display_name = "Android CI"
  project      = module.project.id
}

resource "google_service_account" "stream_io" {
  account_id   = "streamio"
  display_name = "Stream IO Push Notifications"
  project      = module.project.id
}

resource "google_service_account" "twilio_notifications" {
  account_id   = "twilio-notifications"
  display_name = "Twilio Push Notifications"
  project      = module.project.id
}

resource "google_project_iam_custom_role" "push_notifier" {
  role_id     = "pushNotifier"
  title       = "Push Notifier"
  description = "Allows sending of push notifications via Firebase Cloud Messaging."
  permissions = [
    "cloudmessaging.messages.create"
  ]
  project = module.project.id
}

resource "google_project_iam_custom_role" "firebase_app_distributor" {
  role_id     = "firebaseAppDistributor"
  title       = "Firebase App Distribution"
  description = "Allows adding, removing and managing testers in Firebase App Distribution"
  permissions = [
    "firebaseappdistro.testers.list",
    "firebaseappdistro.testers.update"
  ]
  project = module.project.id
}

resource "google_project_iam_member" "twilio_notifications_push_notifier" {
  project = module.project.id
  role    = google_project_iam_custom_role.push_notifier.id
  member  = "serviceAccount:${google_service_account.twilio_notifications.email}"
}

resource "google_project_iam_member" "viewers_firebase_app_distributor" {
  for_each = toset(var.viewers)

  project = module.project.id
  role    = google_project_iam_custom_role.firebase_app_distributor.id
  member  = each.value
}