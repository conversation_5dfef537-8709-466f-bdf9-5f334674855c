module "cloud_run_service" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service?ref=3.84.0"

  project        = module.project
  container_port = "8080"
  gcr_image      = "us-docker.pkg.dev/cloudrun/container/hello"
  name           = "test-cloudrun-service"

  cpu_monitor               = { priority = 2 }
  memory_monitor            = { priority = 2 }
  instance_count_monitor    = { priority = 2 }
  request_count_monitor_4xx = { priority = 2 }
  request_count_monitor_5xx = { priority = 2 }
  request_latency_monitor   = { priority = 2 }
}
