locals {
  project_computed_permissions = {
    "roles/cloudfunctions.developer" : [
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.slackbot_trigger.cloud_build_sa_email}",
    ]
    "roles/secretmanager.secretAccessor" : [
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.slackbot_trigger.cloud_build_sa_email}",
    ]
    "roles/iam.serviceAccountUser" : [
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.slackbot_trigger.cloud_build_sa_email}",
    ]

  }
}

module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project  = module.project
  humans   = var.project_static_permissions
  machines = local.project_computed_permissions
}
