locals {
  default_docker_image = "us-docker.pkg.dev/cloudrun/container/hello"

  services = {
    consumer-api = {
      name           = "consumer-api"
      repo_name      = "consumer-api"
      container_port = 8080
    }
  }
}

# Start Cloud Run instance(s)
module "cloud_run" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_run/service?ref=3.74.0"

  for_each = local.services

  name                  = each.value["name"]
  gcr_image             = local.default_docker_image
  container_port        = each.value["container_port"]
  project               = module.project
  region                = var.region
  memory                = var.cloud_run_parameters[each.key].memory
  cpu                   = var.cloud_run_parameters[each.key].cpu
  container_concurrency = var.cloud_run_parameters[each.key].container_concurrency

  template_metadata_annotations = {
    "autoscaling.knative.dev/initialScale" = var.cloud_run_parameters[each.key].initial_scale
    "autoscaling.knative.dev/maxScale"     = var.cloud_run_parameters[each.key].max_scale
    "autoscaling.knative.dev/minScale"     = var.cloud_run_parameters[each.key].min_scale
  }

  # autodeploy_build_trigger_names = {
  #   development = {
  #     trigger_name = "Feature"
  #   }
  #   staging = {
  #     trigger_name = "PR-to-Main"
  #   }
  #   production = {
  #     trigger_name = "Main"
  #   }
  # }

  custom_labels = {
    "managed-by"   = "gcp-cloud-build-deploy-cloud-run",
    "repo-name"    = each.value["repo_name"],
    "service-name" = each.value["name"]
  }
}
