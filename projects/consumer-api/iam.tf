locals {

  service_accounts_for_services = [
    for service in module.cloud_run : "serviceAccount:run-${lower(service.service_name)}@${module.project.id}.iam.gserviceaccount.com"
  ]

  project_computed_permissions = {
    "roles/appengine.appAdmin" : [
      "serviceAccount:${module.project.number}-<EMAIL>", # Default SA for Cloud Run
    ]
    "roles/secretmanager.secretAccessor" : flatten([
      local.service_accounts_for_services,
      "serviceAccount:${google_service_account.cloud_build.email}",
      "serviceAccount:${module.project.number}-<EMAIL>", # Default SA for Cloud Run and VN Instances
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ])
    "roles/iam.serviceAccountUser" : [
      "serviceAccount:${google_service_account.cloud_build.email}",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]
    "roles/cloudfunctions.developer" : [
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]
    "roles/firebase.admin" : [
      "serviceAccount:${module.project.number}@cloudbuild.gserviceaccount.com",
    ]
    "roles/run.developer" : [
      "serviceAccount:${google_service_account.cloud_build.email}",
    ]
  }
}

module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project  = module.project
  humans   = var.project_static_permissions
  machines = local.project_computed_permissions
}
