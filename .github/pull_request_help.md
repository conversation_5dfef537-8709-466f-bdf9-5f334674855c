## PR Help  <!-- omit in toc -->

You want to unlock a blocking PR, go to that PR, and run `tf unlock` there.

<table>
<tr>
<th>⚠️ Notifications</th>
</tr>
<tr>
<td>
PRs will now auto merge and close once all required checks are complete.  This is to save the manual step of merging and closing and to help prevent situations whereby a PR has been applied but not merged and closed and a subsequent PR on the same project overwrites it.
<!-- If you see the error below, blablabli blablabla...<BR><BR>
<ins>Error</ins>:
<pre>
│ Error: Failed to do some s... as usual Terraform...
│
│ blablabli blablabla...
</pre>
<ins>How to fix the s...?</ins><BR>
-->
</td>
</tr>
</table>

<details>
<summary>More details</summary>

- [You project is locked](#you-project-is-locked)
  - [It's an Atlantis lock](#its-an-atlantis-lock)
  - [It's a Terraform lock](#its-a-terraform-lock)
- [Apply <PERSON><PERSON><PERSON>](#apply-errors)
  - [Stale Plan](#stale-plan)
  - [Cloud Run Service Update Error](#cloud-run-service-update-error)

### You project is locked

#### It's an Atlantis lock

Identifiable because there is no Terraform plan rendered. Instead, you will notice a comment looking like:

> **Plan Failed**: This project is currently locked by an unapplied plan from pull #XXXX. To continue, delete the lock from #XXXX or apply that plan and merge the pull request.

💡 How to fix it:

- click on the hyperlink `#XXXX`
- ask to the `#XXXX` PR owner if you can unlock his PR, this will stale his plan
- come back to your PR and run `tf plan`

#### It's a Terraform lock

Those locks are identifiable with a Terraform plan output being rendered and mentioning a comment like:

> Plan Error
>
> <details open="open">
> <summary>Show Output</summary>
>
> ```
> Error: Error acquiring the state lock
>
> Error message: writing
> "gs://cathex-tf-states/<env>/<project>/default.tflock" failed:
> googleapi: Error 412: At least one of the pre-conditions you specified did
> not hold., conditionNotMet
> Lock Info:
>   ID:        xxxxxxxxxxxxxxxx
>   Path:      gs://cathex-tf-states/<env>/<project>/default.tflock
>   Operation: OperationTypeApply
>   Who:       atlantis@atlantis
>   Version:   x.y.z
>   Created:   xxxx-yy-zz xx:yy:zz.ttttttttt +0000 UTC
>   Info:
>
> Terraform acquires a state lock to protect the state from being written
> by multiple users at the same time...
> ```
>
> </details>

💡 How to fix it: trigger a support escalation writing in the Slack `#platform-team` channel that you need a terraform lock to be removed.
Don't forget to include the link to your PR.

### Apply Errors

#### Stale Plan

It looks like:

> Apply Error
>
> <details open="open">
> <summary>Show Output</summary>
>
> ```
>
> Error: Saved plan is stale
>
> The given plan file can no longer be applied because the state was changed by
> another operation after the plan was created.
> ```
>
> </details>

💡 How to fix it: run a new `tf plan`

#### Cloud Run Service Update Error

It always mentions a version conflict similar to:

> Apply Error
>
> <details open="open">
> <summary>Show Output</summary>
>
> ```
>
> Error: Error updating Service "locations/<my region>/namespaces/<my project id>/services/<my service>": googleapi: Error 409: Conflict for resource '<my service>': version 'xxxxxxxxxxxxxxxx' was specified but current version is 'yyyyyyyyyyyyyyyy'.
>
>   with module.cloud_run["<my service>"].google_cloud_run_service.this,
>   on .terraform/modules/cloud_run/gcp/cloud_run/main.tf line xy, in resource "google_cloud_run_service" "this":
>   xy: resource "google_cloud_run_service" "this" {
> ```

💡 How to fix it: re-run a plan (`tf plan -d folders/{your env}/{your project}`) to force a refresh which will sync your Cloud Run service ID, then apply.

</details>
