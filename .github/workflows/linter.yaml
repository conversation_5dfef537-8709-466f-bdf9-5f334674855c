name: "<PERSON><PERSON>"

on:
  pull_request:
  push:

jobs:
  terraform_linter:
    name: "Terraform"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: "Marking related changes"
        uses: dorny/paths-filter@v2.10.2
        id: changes
        with:
          initial-fetch-depth: '1'
          filters: |
            terraform:
              - '**.tf'
              - '**.tfvars'

      - name: "Setup Terraform"
        if: ${{ github.event_name == 'pull_request' && steps.changes.outputs.terraform == 'true' }}
        uses: hashicorp/setup-terraform@v1

      - name: "Lint Terraform"
        if: ${{ github.event_name == 'pull_request' && steps.changes.outputs.terraform == 'true' }}
        run: terraform fmt -write=false -list=true -check -diff -recursive ./projects

      - run: echo ""
        if: ${{ steps.changes.outputs.terraform == 'false' }}

  terragrunt_linter:
    name: "Terragrunt"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: "Marking related changes"
        uses: dorny/paths-filter@v2.10.2
        id: changes
        with:
          initial-fetch-depth: '1'
          filters: |
            terragrunt:
              - '**.hcl'

      - name: "Setup Terragrunt"
        if: ${{ github.event_name == 'pull_request' && steps.changes.outputs.terragrunt == 'true' }}
        uses: autero1/action-terragrunt@v1.1.0
        with:
          terragrunt_version: 0.29.0

      - name: "Lint Terragrunt"
        if: ${{ github.event_name == 'pull_request' && steps.changes.outputs.terragrunt == 'true' }}
        run: terragrunt hclfmt --terragrunt-check

      - run: echo ""
        if: ${{ steps.changes.outputs.terragrunt == 'false' }}
