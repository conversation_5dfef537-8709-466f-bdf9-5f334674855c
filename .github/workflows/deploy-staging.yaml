name: deploy-staging

permissions:
  contents: read
  id-token: write
  packages: read

on:
  push:
    branches:
      - main
  # allows you to run this workflow manually from the "Actions" tab
  workflow_dispatch:

concurrency: deploy-staging-${{ github.ref }}

jobs:
  deploy:
    uses: checkatrade/action-core/.github/workflows/deploy-staging.yaml@v1
    with:
      helm_namespace: core-trade-bff
      helm_release: core-trade-bff
