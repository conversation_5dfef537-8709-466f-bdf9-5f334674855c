name: rollback

permissions:
  id-token: write

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Rollback environment. Either staging or production
        required: true
        options:
          - staging
          - production
      helm_release:
        type: string
        description: Name of the Helm release to rollback
        default: core-trade-bff
      helm_namespace:
        type: string
        description: Name of the Helm namespace (if not specified, defaults to `helm_release`)
        default: core-trade-bff
      revision:
        type: number
        description: Revision to rollback to (0 for previous version)
        required: false
        default: 0

concurrency: deploy-${{ inputs.environment }}-${{ github.ref }}

jobs:
  rollback:
    uses: checkatrade/action-core/.github/workflows/helm-rollback.yaml@v1
    with:
      helm_release: ${{ inputs.helm_release }}
      helm_namespace: ${{ inputs.helm_namespace || inputs.helm_release }}
      revision: ${{ inputs.revision != '' && fromJSON(inputs.revision) || 0 }}
      environment: ${{ inputs.environment }}
