name: 'Notifier'

on:
  repository_dispatch:
    types: [slack-notification]

jobs:
  slack_notification:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 1
      - name: 'Send Slack Security Notification'
        if: github.event.client_payload.type == 'alert'
        uses: cat-home-experts/action-slack-notify@master
        env:
          SLACK_ICON: 'https://octodex.github.com/images/Sentrytocat_octodex.jpg'
          SLACK_WEBHOOK: ${{ secrets.SLACK_ALERTS_WEBHOOK }}
          SLACK_USERNAME: ${{ secrets.SLACK_USERNAME || 'Platform Team' }}
          SLACK_COLOR: ${{ github.event.client_payload.color }}
          SLACK_TITLE: ${{ github.event.client_payload.title }}
          SLACK_MESSAGE: ${{ github.event.client_payload.message }}
          MSG_MINIMAL: Commit
          SLACK_FOOTER: ''
      - name: 'Send Slack Notification to Raise Awareness'
        if: github.event.client_payload.type == 'awareness'
        uses: cat-home-experts/action-slack-notify@master
        env:
          SLACK_ICON: 'https://octodex.github.com/images/collabocats.jpg'
          SLACK_WEBHOOK: ${{ secrets.SLACK_AWARENESS_WEBHOOK || secrets.SLACK_BUILD_WEBHOOK }}
          SLACK_USERNAME: ${{ secrets.SLACK_USERNAME || 'Platform Team' }}
          SLACK_COLOR: ${{ github.event.client_payload.color }}
          SLACK_TITLE: ${{ github.event.client_payload.title }}
          SLACK_MESSAGE: ${{ github.event.client_payload.message }}
          MSG_MINIMAL: Commit
          SLACK_FOOTER: ''
      - name: 'Send Slack Build Completion Notification'
        if: github.event.client_payload.type == 'build'
        uses: cat-home-experts/action-slack-notify@master
        env:
          SLACK_ICON: 'https://octodex.github.com/images/constructocat2.jpg'
          SLACK_WEBHOOK: ${{ secrets.SLACK_BUILD_WEBHOOK }}
          SLACK_USERNAME: ${{ secrets.SLACK_USERNAME || 'Platform Team' }}
          SLACK_COLOR: ${{ github.event.client_payload.color }}
          SLACK_TITLE: ${{ github.event.client_payload.title }}
          SLACK_MESSAGE: ${{ github.event.client_payload.message }}
          MSG_MINIMAL: Commit
          SLACK_FOOTER: ''
      - name: 'Send Slack Housekeeping Notification'
        if: github.event.client_payload.type == 'housekeeping'
        uses: cat-home-experts/action-slack-notify@master
        env:
          SLACK_ICON: 'https://octodex.github.com/images/mona-the-rivetertocat.png'
          SLACK_WEBHOOK: ${{ secrets.SLACK_HOUSEKEEPING_WEBHOOK || secrets.SLACK_BUILD_WEBHOOK }}
          SLACK_USERNAME: ${{ secrets.SLACK_USERNAME || 'Platform Team' }}
          SLACK_COLOR: ${{ github.event.client_payload.color }}
          SLACK_TITLE: ${{ github.event.client_payload.title }}
          SLACK_MESSAGE: ${{ github.event.client_payload.message }}
          MSG_MINIMAL: Commit
          SLACK_FOOTER: ''
