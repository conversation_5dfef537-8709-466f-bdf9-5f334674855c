name: test

permissions:
  contents: read
  id-token: write
  issues: write
  packages: read
  pull-requests: write

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

concurrency:
  group: test-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    uses: checkatrade/action-core/.github/workflows/test.yaml@v1.4
    with:
      firestore_emulator: true
      env_file: .env.sample
      scan_with_sonar: true
    secrets: inherit
