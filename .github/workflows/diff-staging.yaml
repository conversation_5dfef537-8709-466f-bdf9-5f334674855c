name: diff-staging

permissions:
  contents: read
  id-token: write
  packages: read
  pull-requests: write

on:
  pull_request:
    branches:
      - main

concurrency:
  group: diff-staging-${{ github.ref }}
  cancel-in-progress: true

jobs:
  diff:
    uses: checkatrade/action-core/.github/workflows/diff-staging.yaml@v1
    with:
      helm_namespace: core-trade-bff
      helm_release: core-trade-bff
