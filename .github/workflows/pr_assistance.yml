name: 'CI'

on:
  pull_request:
    types: [opened, reopened, synchronize]
  pull_request_review:
    types: [submitted]

jobs:
  pr_helper:
    name: PR Helper
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    env:
      # fixes the github remote error 'fatal: ambiguous argument 'origin/HEAD': unknown revision or path not in the working tree.'
      HEAD: remotes/origin/main
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Comment the guidelines
        if: github.event.action == 'opened' || github.event.action == 'reopened'
        uses: thollander/actions-comment-pull-request@cc0985985869aaf7396522b3b576e27b425fe4bf # v2.2.0
        with:
          filePath: .github/pull_request_help.md
      - name: Generate outdated modules table
        id: generate_outdated_modules_table
        if: github.event.action == 'opened' || github.event.action == 'reopened' || github.event.action == 'ready_for_review'
        env:
          BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
        run: |
          if [[ -f scripts/terraform_helpers/flag_outdated_modules.sh ]]; then
            scripts/terraform_helpers/flag_outdated_modules.sh
          else
            echo "Script 'scripts/terraform_helpers/flag_outdated_modules.sh' missing"
          fi
          if [[ -f "${{ github.workspace }}/outdated-modules.md" ]]; then
            echo "has_outdated_modules=true" >> $GITHUB_OUTPUT
          fi
      - name: Comment the outdated modules
        if: steps.generate_outdated_modules_table.outputs.has_outdated_modules == 'true'
        uses: thollander/actions-comment-pull-request@cc0985985869aaf7396522b3b576e27b425fe4bf # v2.2.0
        with:
          filePath: /outdated-modules.md

  pr_reviewer:
    name: PR Reviewer
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request_review' && github.event.review.state == 'approved'
    steps:
      #- uses: actions/checkout@v2
      - name: Get platform membership status
        uses: tspascoal/get-user-teams-membership@37c08f7b52a72ca95d12af2e7ab2553ca9adf13b # v2.0.0
        id: get_platform_membership
        with:
          username: ${{ github.actor }}
          team: 'gcp-devops'
          GITHUB_TOKEN: ${{ secrets.CI_GH_ORG_READ }}
      - name: Label when platform approves
        if: github.event.review.state == 'approved' && steps.get_platform_membership.outputs.isTeamMember == 'true'
        uses: TobKed/label-when-approved-action@0058d0094da27e116fad6e0da516ebe1107f26de # v1.4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          label: 'platform-approved'
          require_committers_approval: 'true'
          remove_label_when_approval_missing: 'true'
