name: restart

permissions:
  id-token: write

on:
  workflow_dispatch:
    inputs:
      deployment:
        type: string
        description: Name of the Kubernetes deployment (application) to restart
        required: true
      environment:
        type: choice
        description: Environment to restart the deployment in
        required: true
        default: staging
        options:
          - staging
          - production
      namespace:
        type: string
        description: Kubernetes namespace
        required: true
        default: core-trade-bff

concurrency: restart-${{ inputs.environment }}-${{ inputs.namespace }}-${{ inputs.deployment }}

jobs:
  rollback:
    uses: checkatrade/action-core/.github/workflows/kubernetes-restart.yaml@v1
    with:
      deployment: ${{ inputs.deployment }}
      environment: ${{ inputs.environment }}
      namespace: ${{ inputs.namespace }}
