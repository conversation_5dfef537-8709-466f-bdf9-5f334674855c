name: 'Housekeeper'

on:
  schedule:
    - cron: '0 10 * * 5'  # every friday at 10 am
  workflow_dispatch: # Allows you to run this workflow manually from the Actions tab

defaults:
  run:
    shell: bash

jobs:
  send_prs_stats:
    name: Send PR stats
    runs-on: ubuntu-latest
    if: (github.event_name == 'schedule' && github.event.schedule == '0 10 * * 5') || github.event_name == 'workflow_dispatch'
    steps:
      - name: Run pull request stats
        uses: cat-home-experts/pull-request-stats@2.3.0-checkatrade
        with:
          slack-webhook: ${{ secrets.SLACK_HOUSEKEEPING_WEBHOOK }}
          slack-channel: '#platform-private'
