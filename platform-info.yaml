---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: core-trade-bff
  title: Trade BFF
  description: API for the trade app
  annotations:
    backstage.io/techdocs-ref: dir:.
    sonarqube.org/project-key: checkatrade_core-trade-bff_3eaba8bb-d07a-4f0c-a1c8-e3bab1bf7a55
spec:
  type: service
  owner: trade-experience
  domain: trade
---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: core-trade-bff-service
  title: Trade BFF
  description: API to serve the consumer trade profile page
spec:
  system: core-trade-bff
  owner: trade-experience
  providesApis:
    - trade-bff
  lifecycle: production
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: trade-bff
  title: trade-bff API
spec:
  type: openapi
  lifecycle: production
  owner: trade-experience
  system: core-trade-bff
  definition:
    $openapi: ./src/api/trade-app-bff/spec/openapi-v3.yaml
