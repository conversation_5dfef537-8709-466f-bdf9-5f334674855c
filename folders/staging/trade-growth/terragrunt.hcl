terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name             = basename(get_terragrunt_dir())
  environment              = basename(dirname(get_terragrunt_dir()))
  env                      = local.map_environment_to_env[local.environment]
  consent_screen_app_title = local.project_name
  repo_name                = "gcp-trade-growth"
  organisation             = "cat-home-experts"
  project_id               = "trade-growth-stg-49610"

  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  app_engine_friendly_domain_name = "join-staging.checkatrade.com"

  dsu_api_base_url = "https://digital-sign-up-api-e4hmp2v66q-nw.a.run.app"
  sf_base_url      = "https://checkatrade--uatpartial.sandbox.my.salesforce.com"
  cat_api_base_url = "https://api.staging.checkatrade.com"

  dsu_api_cloud_run_env_vars = {
    _GAR_HOSTNAME      = "europe-west2-docker.pkg.dev"
    _PLATFORM          = "managed",
    _PROJECT_ENV       = local.env,
    _PROJECT_ENV_FULL  = local.environment
    _SERVICE_NAME      = "digital-sign-up-api",
    _CAMPAIGNS_API_URL = "https://campaigns-api-y2vk3llcca-nw.a.run.app"
  }

  sf_cloud_run_env_vars = {
    _GAR_HOSTNAME     = "europe-west2-docker.pkg.dev"
    _PLATFORM         = "managed",
    _PROJECT_ENV      = local.env,
    _PROJECT_ENV_FULL = local.environment
    _SERVICE_NAME     = "digital-sign-up-sf-api",
    _SF_BASE_URL      = local.sf_base_url,
    _SF_LEAD_FLOW     = "/services/data/v58.0/actions/custom/flow/GCP_Trade_Growth_DSU_Lead",
    _DSU_API_BASE_URL = local.dsu_api_base_url
  }

  functions_env_vars = {
    _PROJECT_ID       = local.project_id,
    _PROJECT_ENV      = local.env,
    _DSU_API_URL      = local.dsu_api_base_url,
    _CAT_API_URL      = local.cat_api_base_url,
    _REFERRAL_API_URL = "https://referral-factory.com"
  }

  app_engine_env_vars = {
    _GAR_HOSTNAME                               = "europe-west2-docker.pkg.dev"
    _PROJECT_ENV                                = local.env,
    _NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN           = "${local.project_id}.firebaseapp.com",
    _NEXT_PUBLIC_FIREBASE_PROJECT_ID            = local.project_id,
    _NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET        = "${local.project_id}.appspot.com",
    _NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID   = "787062040152",
    _NEXT_PUBLIC_FIREBASE_APP_ID                = "1:787062040152:web:bf253b3a8c472fc5374c2d",
    _NEXT_PUBLIC_GTM_ID                         = "GTM-MP3QRF",
    _NEXT_PUBLIC_GTM_AUTH_ID                    = "BgrLr68SjsE8wAZDfBPP5g",
    _NEXT_PUBLIC_GTM_PREVIEW_ID                 = "env-819"
    _NEXT_PUBLIC_COOKIE_CONSENT_KEY             = "86d6ba8d-83fb-4938-91e1-90629e3d0c3f-test"
    _NEXT_PUBLIC_VAT_RATE                       = "20"
    _NEXT_PUBLIC_API_ENDPOINT                   = "https://digital-sign-up-api-e4hmp2v66q-nw.a.run.app"
    _NEXT_PUBLIC_DD_ENDPOINT                    = "https://checkatrade--uatpartial.sandbox.my.salesforce-sites.com/DirectDebitSignup/DDForm"
    _NEXT_PUBLIC_DATADOG_ENABLED                = true
    _NEXT_PUBLIC_WEB_BASE_URL                   = "/dsu"
    _NEXT_PUBLIC_KINESIS_POOL_ID                = "eu-west-2:b33be2dd-64b5-4cdb-a54c-92799dd93a5e"
    _NEXT_PUBLIC_KINESIS_REGION                 = "eu-west-2"
    _NEXT_PUBLIC_KINESIS_STREAM                 = "cathex-data-datalake-web"
    _NEXT_PUBLIC_KINESIS_TRACKING_ENV           = "dsu-staging"
    _NEXT_PUBLIC_WEB_DOMAIN                     = "join-staging.checkatrade.com"
    _NEXT_PUBLIC_IDENTITY_CLIENT_ID             = "0672e392-7a01-7f7d-b300-91ae1fd3963f"
    _NEXTAUTH_URL                               = "https://join-staging.checkatrade.com/dsu/api/auth"
    _NEXT_PUBLIC_CHECKATRADE_BASE_URL           = local.cat_api_base_url
    _NEXT_PUBLIC_DATADOG_API_SEND_LOGS_ENDPOINT = "https://http-intake.logs.datadoghq.eu/api/v2/logs"
  }

  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  iap_web_users      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment

  consent_screen_app_title         = local.project_name
  app_engine_friendly_domain_name  = local.app_engine_friendly_domain_name
  app_engine_custom_certificate_id = "23360184"

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/appengine.appAdmin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/cloudscheduler.jobRunner" : concat(
      local.project_team,
      local.project_team_admin,
    ),
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/run.invoker" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iap.httpsResourceAccessor" : concat(
      local.project_team,
      local.project_team_admin,
      local.iap_web_users
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
      local.project_team,
    ),
    "roles/errorreporting.user" : concat(
      local.project_team_admin,
      local.project_team,
    ),
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
      local.project_team,
    ),
    "roles/secretmanager.secretAccessor" : concat(
      local.project_team_admin,
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    emulatorTrigger = {
      name                         = "Emulator"
      description                  = "Builds and releases emulator so that other builds can work."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = {}
      included_files_filter        = ["emulator/**"] # Can be commented out
    }
    devTrigger = {
      name                         = "Feature-Api"
      description                  = "Builds and releases DSU Api to dev on any non main commit."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      manual_trigger_enabled       = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "service/cloudbuild.dsuapi.yaml"
      env_variables                = local.dsu_api_cloud_run_env_vars
      included_files_filter        = ["service/TradeGrowth.DigitalSignUpApi/**", "contracts/TradeGrowth.Contracts/**"]
    }
    stagingTrigger = {
      name                         = "PR-Api"
      description                  = "Builds and releases DSU Api to staging on an opened PR to Main"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "service/cloudbuild.dsuapi.yaml"
      env_variables                = local.dsu_api_cloud_run_env_vars
      included_files_filter        = ["service/TradeGrowth.DigitalSignUpApi/**", "contracts/TradeGrowth.Contracts/**"]
    }
    productionTrigger = {
      name                         = "Main-Api"
      description                  = "Builds and releases DSU Api to staging on a commit to main, for staging to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "service/cloudbuild.dsuapi.yaml"
      env_variables                = local.dsu_api_cloud_run_env_vars
      included_files_filter        = ["service/TradeGrowth.DigitalSignUpApi/**", "contracts/TradeGrowth.Contracts/**"]
    }
    devTriggerWebApp = {
      name                         = "Feature-Web"
      description                  = "Builds and releases DSU App to dev on any non main commit."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      manual_trigger_enabled       = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "app/cloudbuild.webapp.yaml"
      env_variables                = local.app_engine_env_vars
      included_files_filter        = ["app/**"]
    }
    stagingTriggerWebApp = {
      name                         = "PR-Web"
      description                  = "Builds and releases DSU App to staging on an opened PR to Main"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "app/cloudbuild.webapp.yaml"
      env_variables                = local.app_engine_env_vars
      included_files_filter        = ["app/**"]
    }
    productionTriggerWebApp = {
      name                         = "Main-Web"
      description                  = "Builds and releases DSU App to staging on a commit to main, for staging to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "app/cloudbuild.webapp.yaml"
      env_variables                = local.app_engine_env_vars
      included_files_filter        = ["app/**"]
    }
    functionsTrigger = {
      name                         = "Feature-Lead-Function"
      description                  = "Builds and releases Functions to dev on any non-main commit."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      manual_trigger_enabled       = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables                = local.functions_env_vars
      filename                     = "functions/lead-listener/cloudbuild.lead-listener.yaml"
      included_files_filter        = ["functions/lead-listener/**", "contracts/TradeGrowth.Contracts/**"]
    }
    functionsTriggerPrToMain = {
      name                         = "Pr-Lead-Function"
      description                  = "Builds and releases Functions to staging on an opened PR to Main"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = local.functions_env_vars
      filename                     = "functions/lead-listener/cloudbuild.lead-listener.yaml"
      included_files_filter        = ["functions/lead-listener/**", "contracts/TradeGrowth.Contracts/**"]
    }
    functionsTriggerPushToMain = {
      name                         = "Main-Lead-Function"
      description                  = "Builds and releases LeadListener Functions to stg on a commit to main, for stg to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = local.functions_env_vars
      filename                     = "functions/lead-listener/cloudbuild.lead-listener.yaml"
      included_files_filter        = ["functions/lead-listener/**", "contracts/TradeGrowth.Contracts/**"]
    }
    sfApiDevTrigger = {
      name                         = "Feature-Sf-Api"
      description                  = "Builds and releases Sf Api to dev on any non main commit."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      manual_trigger_enabled       = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "service/SalesForceApi/cloudbuild.salesforceapi.yaml"
      env_variables                = local.sf_cloud_run_env_vars
      included_files_filter        = ["service/SalesForceApi/TradeGrowth.DigitalSignUpSalesForceApi/**", "contracts/TradeGrowth.Contracts/**"]
    }
    sfStagingTrigger = {
      name                         = "PR-Sf-Api"
      description                  = "Builds and releases Sf Api to staging on an opened PR to Main"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "service/SalesForceApi/cloudbuild.salesforceapi.yaml"
      env_variables                = local.sf_cloud_run_env_vars
      included_files_filter        = ["service/SalesForceApi/TradeGrowth.DigitalSignUpSalesForceApi/**", "contracts/TradeGrowth.Contracts/**"]
    }
    sfProductionTrigger = {
      name                         = "Main-Sf-Api"
      description                  = "Builds and releases Sf Api to staging on a commit to main, for staging to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "service/SalesForceApi/cloudbuild.salesforceapi.yaml"
      env_variables                = local.sf_cloud_run_env_vars
      included_files_filter        = ["service/SalesForceApi/TradeGrowth.DigitalSignUpSalesForceApi/**", "contracts/TradeGrowth.Contracts/**"]
    }
  }

  cloud_run_parameters = {
    digital-sign-up-api = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 10
    },
    digital-sign-up-sf-api = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 10
      max_latency_threshold = 4000
    }
  }

  trigger_alert_duration = "last_15m"
  incident_io            = "@<EMAIL>"
  trade_experience_slack = "@slack-trade-experience-alerts-non-prod"
}
