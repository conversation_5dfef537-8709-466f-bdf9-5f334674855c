content-api-connection-string: ENC[AES256_GCM,data:3H3pWTqblLVe0TkRUuzsZ467miCPEWpfJKPihtZXTSM0x5vxb9bVHPlwk/VTEb938gUsNGwnAYr/js0BbYwvRTOj66Npmx5snMR2XVX/7whhXQ==,iv:JZO3FeG71H59RzvKUvJNv8TEzPRUBnMy/OqMn99JXQo=,tag:rOx4NzB15J6n8hBMNPbwzA==,type:str]
github-packages-token: ENC[AES256_GCM,data:30oSAh8tF9TzYSkP/3/WntMUVo646gjBIFrx4Ja6RGd8E+xPXH0j0g==,iv:V2fVoD5M6CtuqGkJPiGybdlPeFxGcSF5/3+SLFb34ag=,tag:/q4ChMZibuXWP048OoAOvg==,type:str]
github-packages-username: E<PERSON>[AES256_GCM,data:iaw8TZnDR6x+hicvr8SFakjVbCTSIA==,iv:H8RbbbRmsPG2a34KOEK044PGAyKPmO36+ubbt3V9o+E=,tag:pDjsE2+iFYF63YJTlWZ1fw==,type:str]
sops:
    kms: []
    gcp_kms:
        - resource_id: projects/operations-14020/locations/europe-west2/keyRings/secret-provisioner-33274/cryptoKeys/staging-sops
          created_at: "2023-08-31T09:40:51Z"
          enc: CiYAos6+P78xyZmsRIKRKxahfFKAutB65A4nNPbXu1zpaJSWmnljkxJJADYS3dliC0hm21HfYT50ToOErFxCRi3ECFRGCTpuTL9/HTgMV7Iy3YV5dJkWystJX9ArOfaz2H9G70zDC/WZX8i6f2VqaP8tBg==
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2023-08-31T09:42:19Z"
    mac: ENC[AES256_GCM,data:y5GpO0EOwWPsFbERGLypuhzzZCit/h+VXG5uJ6gLKbg8r1GuD+ChwzxG0Qy2zMgSNpnq3UFZKqNOPj6YysY5435OWOQNAuvSExcgbtLKkAehQOxGlmUTy/POF3Mb9z6kRNfHt6OcdYwWIiu8L7ELVhaKbV37clCbQDYk7gyNCT4=,iv:cQHb8QzJYD6S+2VaUFTByBbc4rax3j6ep/vLLByNaA8=,tag:4OXII8wQdGH7FExp8w+QvA==,type:str]
    pgp: []
    unencrypted_suffix: _unencrypted
    version: 3.7.3
