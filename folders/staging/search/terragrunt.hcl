terraform {
  source = "./../../..//projects/search"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  env = local.map_environment_to_env[local.environment]

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_github_env_vars = {
    _PROJECT_ENV  = basename(dirname(get_terragrunt_dir()))
    _VERSION      = "staging"
    _TRIGGER_NAME = "Github CI Build"
  }

  search_service_emulator_env_variables = {}

  excluded_files_filter_for_jobs_and_emulator = [
    "emulator/**",
    "services/search-onspd/SearchIndexer.ONSPDLoader/**",
    "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader.FunctionalTests/**"
  ]

  excluded_files_filter_for_jobs = [
    "services/search-onspd/SearchIndexer.ONSPDLoader/**",
    "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader.FunctionalTests/**"
  ]

  viewer_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  scheduler_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  developer_access = [
    "group:<EMAIL>"
  ]

  support_access = [
    "group:<EMAIL>"
  ]

  team_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  publisher_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  consumer_app_developer_access = [
    "group:<EMAIL>"
  ]

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
}

inputs = {
  # Standard inputs
  region         = "europe-west2"
  environment    = "staging"
  project_folder = "staging"

  # Accounts for team defined IAM permissions
  project_static_permissions = {
    "roles/run.admin" : local.team_access,
    "roles/iam.serviceAccountUser" : local.team_access,
    "roles/pubsub.admin" : local.team_access,
    "roles/cloudbuild.builds.editor" : local.team_access,
    "roles/compute.admin" : local.team_access,
    "roles/cloudbuild.builds.builder" : local.team_access,
    "roles/pubsub.publisher" : local.publisher_access,
    "roles/pubsub.subscriber" : local.publisher_access,
    "roles/run.invoker" : concat(
      local.support_access,
      local.developer_access,
    ),
    "roles/cloudbuild.builds.viewer" : local.viewer_access,
    "roles/viewer" : local.viewer_access,
    "roles/cloudtasks.queueAdmin" : local.project_team,
    "roles/cloudscheduler.admin" : local.scheduler_access,
    "roles/run.developer" : local.consumer_app_developer_access,
  }

  # Cloud build trigger configuration
  cloudbuild_triggers = {
    stagingTrigger = {
      name                         = "PR-to-Main"
      description                  = "Builds and releases to staging on a pull request."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/combined_cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = local.excluded_files_filter_for_jobs_and_emulator
    },
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases to staging on a commit to main, for staging to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/combined_cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = local.excluded_files_filter_for_jobs_and_emulator
    }
    stagingEmulatorTrigger = {
      name                         = "PR-to-Main-for-Emulator"
      description                  = "Builds and releases to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.search_service_emulator_env_variables
      included_files_filter        = ["emulator/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    productionEmulatorTrigger = {
      name                         = "Main-for-Emulator"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.search_service_emulator_env_variables
      included_files_filter        = ["emulator/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    stagingTriggerSearchNodeService = {
      name                         = "PR-to-Main-Node-Service"
      description                  = "Builds and releases to staging on a pull request."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-node-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = []
    }
    productionTriggerSearchNodeService = {
      name                         = "Main-Search-Node-Service"
      description                  = "Builds and releases to production on a commit to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-node-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = []
    }
    # Triggers for search service api gateway config file change
    stagingApiGatewayTrigger = {
      name                         = "PR-Api-Gateway"
      description                  = "Builds and releases to staging on a pull request"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["api-gateway/spec.yaml"]
    }
    productionApiGatewayTrigger = {
      name                         = "Main-Api-Gateway"
      description                  = "Builds and releases to staging on a commit to main"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["api-gateway/spec.yaml"]
    }
    stagingTriggerWorker = {
      name                         = "PR-to-Main-Search-Worker"
      description                  = "Builds and releases to staging on a pull request."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-worker/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-worker/**", "services/search-service/SearchService.Contracts/**"]
      excluded_files_filter        = ["emulator/**"]
    },
    productionTriggerWorker = {
      name                         = "Main-Search-Worker"
      description                  = "Builds and releases to staging on a commit to main, for staging to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-worker/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-worker/**", "services/search-service/SearchService.Contracts/**"]
      excluded_files_filter        = ["emulator/**"]
    },
    stagingTriggerONSPDLoader = {
      name                         = "PR-Main-ONSPD-Loader"
      description                  = "Builds and releases to staging on a pull request."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-onspd/SearchIndexer.ONSPDLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-onspd/SearchIndexer.ONSPDLoader/**", "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**"]
      excluded_files_filter        = ["services/search-place-name/SearchIndexer.PlaceNameLoader/**", "services/search-place-name/SearchIndexer.PlaceName.FunctionalTests/**"]
    },
    productionTriggerONSPDLoader = {
      name                         = "Main-Search-ONSPD-Loader"
      description                  = "Builds and releases to staging on a commit to main, for staging to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-onspd/SearchIndexer.ONSPDLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-onspd/SearchIndexer.ONSPDLoader/**", "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**"]
      excluded_files_filter        = ["services/search-place-name/SearchIndexer.PlaceNameLoader/**", "services/search-place-name/SearchIndexer.PlaceName.FunctionalTests/**"]
    }
    stagingTriggerPlaceNameLoader = {
      name                         = "PR-Main-PlaceName-Loader"
      description                  = "Builds and releases to staging on a pull request."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-place-name/SearchIndexer.PlaceNameLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-place-name/SearchIndexer.PlaceNameLoader/**", "services/search-place-name/SearchIndexer.PlaceName.FunctionalTests/**"]
      excluded_files_filter        = ["services/search-onspd/SearchIndexer.ONSPDLoader/**", "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**"]
    },
    productionTriggerPlaceNameLoader = {
      name                         = "Main-PlaceName-Loader"
      description                  = "Builds and releases to staging on a commit to main, for staging to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-place-name/SearchIndexer.PlaceNameLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-place-name/SearchIndexer.PlaceNameLoader/**", "services/search-place-name/SearchIndexer.PlaceName.FunctionalTests/**"]
      excluded_files_filter        = ["services/search-onspd/SearchIndexer.ONSPDLoader/**", "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**"]
    }
  }
  cloudbuild_plain_triggers = {
    githubCITrigger = {
      name                         = "GitHub-CI-Trigger"
      description                  = "Builds CLI image for Github to enable comments."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = ".*"
      invert_regex                 = false
      filename                     = "gcp-scripts/cloudbuild-github-image.yaml"
      env_variables                = local.cloud_build_github_env_vars
      included_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    }
  }

  # Creates topics
  search_service_topics = [
    "search-query",
    "search-indexer-trade-profile",
    "ab-search-request",
    "search-indexer-searchable",
    "search-indexer-trade-card"
  ]

  # Cloud Tasks Push URL
  cloud_tasks_trade_push_url = "https://search-indexer-47oy5m7gpq-nw.a.run.app/api/v1/trade-profile"
  cloud_tasks_delay          = 90

  # Pubsub inputs

  # Elastic Search Url
  elastic_search_url = "https://gcp-search-team-elastic-cloud-staging-cpu-optimize.es.europe-west2.gcp.elastic-cloud.com:9243"

  #CORS origin domains
  cors_origin = "https://*.cathex.team, https://*.checkatrade.com, https://*.appspot.com, http://localhost:3000"

  #Kinesis stream names
  kinesis_stream_name         = "cathex-stg-datalake"
  kinesis_stream_name_offline = ""

  #APM env variables
  dd_trace_enabled = "true"

  dd_env = "staging"

  cloud_scheduler_pull_fairshare_leads = "*/5 * * * *"

  cloud_scheduler_pull_fairshares = "*/5 * * * *"

  cloud_scheduler_pull_fairshares_daily = "*/5 * * * *"

  cloud_scheduler_pull_fairshares_hourly = "*/5 * * * *"

  cloud_scheduler_pull_fairshares_delete = "*/5 * * * *"

  cloud_scheduler_pull_lead_surplus = "*/5 * * * *"

  cloud_scheduler_pull_campaign_event = "*/1 * * * *"

  cloud_scheduler_pull_campaign_event_dlq = "0 22 * * *"

  cloud_scheduler_pull_trade_profile = "*/5 * * * *"

  cloud_scheduler_pull_secure_contacts = "*/5 * * * *"

  cloud_scheduler_cleanup_lead_surplus = "10 03 * * *"

  cloud_scheduler_pull_categories = "45 23 * * *"

  cloud_scheduler_pull_review_metrics = "*/5 * * * *"

  cloud_scheduler_pull_company_account_type_update = "*/5 * * * *"

  cloud_scheduler_pull_company_beta_groups_update = "*/5 * * * *"

  cloud_scheduler_cleanup_searchable = "5 21 * * *"

  cloud_scheduler_pull_trade_experience_album = "*/5 * * * *"

  cloud_scheduler_pull_trade_experience_company = "*/5 * * * *"

  cloud_scheduler_pull_payment_onboarding = "*/5 * * * *"

  cloud_scheduler_pull_generic = "*/5 * * * *"

  # Add Cloud Run env variables
  container_concurrency        = 80
  ranking_optimised_weight     = 1.2
  ranking_not_optimised_weight = 0.8
  offline_tests_enabled        = false
  offline_tests                = ""
  use_campaign_data            = true
  use_services_data            = true

  authorized_email_addresses = "checkatrade.com"
  authorized_issuers         = "securetoken.google.com/reviews-stg-15037"
  authorized_role            = "reviewsAdminUser,reviewsAdminManager"

  core_subscriber_service_account_email = "<EMAIL>"

  core_matching_api_service_account_email = "<EMAIL>"

  core_matching_public_service_account_emails = [
    "<EMAIL>",
    "<EMAIL>"
  ]

  core_review_arp_service_account_email = "<EMAIL>"

  core_review_reviews_admin_bff_service_account_email = "<EMAIL>"

  retool_service_account_email = "<EMAIL>"

  capi_shared_service_account_emails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ]

  trade_profile_auto_reply = 200

  # Maximum number of Cloud Run instances to scale to
  max_scale = {
    search-indexer      = 2
    search-service      = 30
    search-worker       = 5
    search-node-service = 2
    search-admin        = 2
  }

  # Pubsub Alert variables
  alert_duration  = "last_1m"
  threshold_value = "10"

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "last_1m"
  threshold_value_memory = "0.9"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "last_1m"
  threshold_value_cpu = "0.9"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "last_1m"
  threshold_value_response_codes = "0.5"

  # D+I's project only exists in dev and prod so we can't use wildcarding for staging
  # so instead we'll just hardcode
  data_stats_project_id = "data-stats-service-dev-24217"

}
