terraform {
  source = "./../../..//projects/trade-experience"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  # Custom groups
  iam_build_triggers_for_member_data = ["member-data--pr", "member-data--main"]

  viewer_access = [
    "domain:cathex.io",
  ]
  team_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  owner_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  storage_access = [
    "group:<EMAIL>",
  ]
  trade_admins = [
    "group:<EMAIL>",
  ]
  firebase_editor_access = [
    "group:<EMAIL>",
  ]

  advisor_ui_cors_block = [{
    "origin" : ["https://trade-experience-admin-stg.checkatrade.com"],
    "method" : ["GET", "PUT"],
    "response_header" : ["*"],
    "max_age_seconds" : 3600
  }]
}

inputs = {
  # Default inputs
  environment    = "staging"
  project_folder = "staging"

  content_api_project_id        = "content-api-staging-32612"
  data_stats_service_project_id = "data-stats-service-dev-24217"
  salesforce_integ_project_id   = "salesforce-integ-stg-22832"

  firebase_default_bucket = "cat-trades-preview.appspot.com"

  consent_screen_app_title = "Members App Staging" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project

  project_static_permissions = {
    "roles/run.admin" : local.team_access,
    "roles/firebase.analyticsViewer" : local.team_access,
    "roles/iam.serviceAccountUser" : local.team_access
    "roles/firebase.admin" : local.owner_access,
    "roles/appengine.appAdmin" : local.owner_access,
    "roles/secretmanager.admin" : local.owner_access,
    "roles/serviceusage.apiKeysAdmin" : local.owner_access,
    "roles/cloudscheduler.admin" : local.owner_access,
    "roles/apigateway.admin" : local.owner_access,
    "roles/apigateway.viewer" : local.owner_access,
    "roles/appengine.deployer" : local.owner_access,
    "roles/cloudbuild.builds.editor" : local.owner_access,
    "roles/firebase.developAdmin" : local.owner_access,
    "roles/firebase.growthAdmin" : local.firebase_editor_access
    "roles/datastore.owner" : local.owner_access,
    "roles/logging.admin" : local.owner_access,
    "roles/pubsub.admin" : local.owner_access,
    "roles/secretmanager.viewer" : local.owner_access,
    "roles/cloudscheduler.jobRunner" : local.owner_access,
    "roles/storage.objectAdmin" : flatten([
      local.storage_access,
      local.team_access,
    ])
    "roles/iam.serviceAccountTokenCreator" : local.team_access,
    "roles/iap.admin" : local.trade_admins,
  }

  # Accounts for team defined IAM permissions
  viewer_access  = local.viewer_access
  owner_access   = local.owner_access
  team_access    = local.team_access
  storage_access = local.storage_access

  # Custom build trigger permissions
  iam_build_triggers_for_member_data = local.iam_build_triggers_for_member_data

  # App Engine custom domain inputs
  app_engine_friendly_domain_name  = "membersapp-staging.checkatrade.com"
  app_engine_custom_certificate_id = "********"
  app_engine_iap_enabled           = true

  # Cloudbuild inputs
  cloud_run_suffix = "dyziymcfta-nw.a.run.app"


  cloudbuild_triggers_for_requester = {
    pr_trigger = {
      name                         = "requester--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION             = "europe-west2"
        _GCR_HOSTNAME           = "europe-west2-docker.pkg.dev"
        _PLATFORM               = "managed"
        _REQUESTER_SERVICE_NAME = "app-api"
        _TRIGGER_NAME           = "pr"
        _VERSION                = "staging"
        _ENABLE_TRAFFIC         = "false"
      }
    }
    main_trigger = {
      name                         = "requester--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION             = "europe-west2"
        _GCR_HOSTNAME           = "europe-west2-docker.pkg.dev"
        _PLATFORM               = "managed"
        _REQUESTER_SERVICE_NAME = "app-api"
        _TRIGGER_NAME           = "main"
        _VERSION                = "staging"
        _ENABLE_TRAFFIC         = "false"
      }
    }
  }

  cloudbuild_triggers_for_receiver = {
    pr_trigger = {
      name                         = "receiver--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION            = "europe-west2"
        _GCR_HOSTNAME          = "europe-west2-docker.pkg.dev"
        _PLATFORM              = "managed"
        _RECEIVER_SERVICE_NAME = "pubsub-receiver"
        _TRIGGER_NAME          = "pr"
        _VERSION               = "staging"
        _ENABLE_TRAFFIC        = "false"
      }
    }
    main_trigger = {
      name                         = "receiver--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION            = "europe-west2"
        _GCR_HOSTNAME          = "europe-west2-docker.pkg.dev"
        _PLATFORM              = "managed"
        _RECEIVER_SERVICE_NAME = "pubsub-receiver"
        _TRIGGER_NAME          = "main"
        _VERSION               = "staging"
        _ENABLE_TRAFFIC        = "false"
      }
    }
  }

  cloudbuild_triggers_for_firestore_cloud_functions = {
    pr_trigger = {
      name                         = "firestore-func--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID                                     = "email-service-stg-32127"
        _ENABLE_SALESFORCE_FOR_PROFILE_DESCRIPTION_AND_SEARCH_PREVIEW = "true"
        _DOCUMENT_STORE                                               = "document-store-92579811864"
        _FIREBASE_DEFAULT_BUCKET                                      = "cat-trades-preview.appspot.com"
        _SALESFORCE_INTEG_PROJECT_ID                                  = "salesforce-integ-stg-22832"
        _TRIGGER_NAME                                                 = "pr"
        _SQL_DB_NAME                                                  = "quoting"
        _SQL_DB_USERNAME                                              = "cat-trades-preview@appspot"
        _SQL_DB_HOST                                                  = "232dcbb69040.1luc2e6krrixs.europe-west2.sql.goog"
        _SQL_ADMIN_API                                                = "https://www.googleapis.com/auth/sqlservice.admin"
      }
    }
    main_trigger = {
      name                         = "firestore-func--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID                                     = "email-service-stg-32127"
        _ENABLE_SALESFORCE_FOR_PROFILE_DESCRIPTION_AND_SEARCH_PREVIEW = "true"
        _DOCUMENT_STORE                                               = "document-store-92579811864"
        _FIREBASE_DEFAULT_BUCKET                                      = "cat-trades-preview.appspot.com"
        _SALESFORCE_INTEG_PROJECT_ID                                  = "salesforce-integ-stg-22832"
        _TRIGGER_NAME                                                 = "main"
        _SQL_DB_NAME                                                  = "quoting"
        _SQL_DB_USERNAME                                              = "cat-trades-preview@appspot"
        _SQL_DB_HOST                                                  = "232dcbb69040.1luc2e6krrixs.europe-west2.sql.goog"
        _SQL_ADMIN_API                                                = "https://www.googleapis.com/auth/sqlservice.admin"
      }
    }
  }

  cloudbuild_triggers_for_api_gateway = {
    pr_trigger = {
      name                         = "api-gateway--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "trade-gateway-gw"
        _API_GATEWAY_API_NAME = "trade-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "app-api"
        _TRIGGER_NAME         = "pr"
        _VERSION              = "staging"
      }

    }
    main_trigger = {
      name                         = "api-gateway--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "trade-gateway-gw"
        _API_GATEWAY_API_NAME = "trade-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "app-api"
        _TRIGGER_NAME         = "main"
        _VERSION              = "staging"
      }
    }
  }

  cloudbuild_triggers_for_member_api_gateway = {
    pr_trigger = {
      name                         = "member-api-gateway--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "member-gateway-gw"
        _API_GATEWAY_API_NAME = "member-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "member-data"
        _TRIGGER_NAME         = "pr"
        _VERSION              = "staging"
      }
    }
    main_trigger = {
      name                         = "member-api-gateway--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "member-gateway-gw"
        _API_GATEWAY_API_NAME = "member-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "member-data"
        _TRIGGER_NAME         = "main"
        _VERSION              = "staging"
      }
    }
  }

  cloudbuild_triggers_for_bulk_receiver = {
    pr_trigger = {
      name                         = "bulk-receiver--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _GCP_REGION     = "europe-west2"
        _GCR_HOSTNAME   = "europe-west2-docker.pkg.dev"
        _PLATFORM       = "managed"
        _TRIGGER_NAME   = "pr"
        _VERSION        = "staging"
      }

    }
    main_trigger = {
      name                         = "bulk-receiver--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _GCP_REGION     = "europe-west2"
        _GCR_HOSTNAME   = "europe-west2-docker.pkg.dev"
        _PLATFORM       = "managed"
        _TRIGGER_NAME   = "main"
        _VERSION        = "staging"
      }
    }
  }

  cloudbuild_triggers_for_document_store = {
    pr_trigger = {
      name                         = "doc-store--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENVIRONMENT = "stg"
      _TRIGGER_NAME = "pr" }
    }
    main_trigger = {
      name                         = "doc-store--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENVIRONMENT = "stg"
      _TRIGGER_NAME = "main" }
    }
  }

  cloudbuild_triggers_for_image_events = {
    pr_trigger = {
      name                         = "img-events--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME = "image-events"
        _ENABLE_TRAFFIC   = "false"
        _GCP_REGION       = "europe-west2"
        _GCR_HOSTNAME     = "europe-west2-docker.pkg.dev"
        _PLATFORM         = "managed"
        _TRIGGER_NAME     = "pr"
        _VERSION          = "staging"
      }
    }
    main_trigger = {
      name                         = "img-events--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME = "image-events"
        _ENABLE_TRAFFIC   = "false"
        _GCP_REGION       = "europe-west2"
        _GCR_HOSTNAME     = "europe-west2-docker.pkg.dev"
        _PLATFORM         = "managed"
        _TRIGGER_NAME     = "main"
        _VERSION          = "staging"
      }
    }
  }

  cloudbuild_triggers_for_member_data = {
    pr_trigger = {
      name                         = "member-data--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME               = "member-data"
        _CORS_ALLOWED_ORIGINS           = "https://membersapp-staging.checkatrade.com,http://localhost:19006"
        _ENABLE_TRAFFIC                 = "false"
        _GCP_REGION                     = "europe-west2"
        _GCR_HOSTNAME                   = "europe-west2-docker.pkg.dev"
        _GOOGLE_IDENTITY_TOKEN_PROJECT  = "cat-trades-preview"
        _PLATFORM                       = "managed"
        _TRIGGER_NAME                   = "pr"
        _SALESFORCE_API_VERSION         = "57.0"
        _SALESFORCE_HOST                = "checkatrade--uatpartial.sandbox.my.salesforce.com"
        _SALESFORCE_TOKEN_URL           = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"
        _TEST_CHECKATRADE_USER_ID       = "9638096e-ced1-404e-a9ef-507cfbfe585f"
        _TEST_COMPANY_ID                = "918283"
        _MEMBER_PROMISE_TEST_COMPANY_ID = "337953"
        _TRIGGER_NAME                   = "pr"
        _VERSION                        = "staging"
        _SECURE_CONTACTS_API_BASE_URL   = "https://secure-contacts-api-jyyaakql3a-nw.a.run.app/api/"
      }
    }
    main_trigger = {
      name                         = "member-data--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME               = "member-data"
        _CORS_ALLOWED_ORIGINS           = "https://membersapp-staging.checkatrade.com,http://localhost:19006"
        _ENABLE_TRAFFIC                 = "false"
        _GCP_REGION                     = "europe-west2"
        _GCR_HOSTNAME                   = "europe-west2-docker.pkg.dev"
        _GOOGLE_IDENTITY_TOKEN_PROJECT  = "cat-trades-preview"
        _PLATFORM                       = "managed"
        _SALESFORCE_API_VERSION         = "57.0"
        _SALESFORCE_HOST                = "checkatrade--uatpartial.sandbox.my.salesforce.com"
        _SALESFORCE_TOKEN_URL           = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"
        _TEST_CHECKATRADE_USER_ID       = "9638096e-ced1-404e-a9ef-507cfbfe585f"
        _TEST_COMPANY_ID                = "918283"
        _MEMBER_PROMISE_TEST_COMPANY_ID = "337953"
        _TRIGGER_NAME                   = "main"
        _VERSION                        = "staging"
        _SECURE_CONTACTS_API_BASE_URL   = "https://secure-contacts-api-jyyaakql3a-nw.a.run.app/api/"
      }
    }
  }

  cloudbuild_triggers_for_quotes_and_invoices = {
    pr_trigger = {
      name                         = "quotes-invoices--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID = "email-service-stg-32127"
        _ENABLE_TRAFFIC           = "false"
        _GCP_REGION               = "europe-west2"
        _GCR_HOSTNAME             = "europe-west2-docker.pkg.dev"
        _PLATFORM                 = "managed"
        _SERVICE_NAME             = "quotes-and-invoices"
        _TRIGGER_NAME             = "pr"
        _VERSION                  = "staging"
      }
    }
    main_trigger = {
      name                         = "quotes-invoices--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID = "email-service-stg-32127"
        _ENABLE_TRAFFIC           = "false"
        _GCP_REGION               = "europe-west2"
        _GCR_HOSTNAME             = "europe-west2-docker.pkg.dev"
        _PLATFORM                 = "managed"
        _SERVICE_NAME             = "quotes-and-invoices"
        _TRIGGER_NAME             = "main"
        _VERSION                  = "staging"
      }
    }
  }

  cloudbuild_triggers_for_web_app = {
    staging_trigger = {
      name                         = "web-app--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _TRIGGER_NAME   = "pr"
        _VERSION        = "staging"
      }
    }
    main_trigger = {
      name                         = "web-app--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _TRIGGER_NAME   = "main"
        _VERSION        = "staging"
      }
    }
  }

  cloudbuild_triggers_for_firebase = {
    pr_trigger = {
      name                         = "firebase--pr"
      description                  = "Builds firebase config to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _TRIGGER_NAME = "pr"
        _VERSION      = "staging"
      }
    }
    main_trigger = {
      name                         = "firebase--main"
      description                  = "Builds firebase config to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _TRIGGER_NAME = "main"
        _VERSION      = "staging"
      }
    }
  }

  # Cloudrun inputs
  container_concurrency = 80
  cloud_run_app_api_env_variables = [
    {
      "name"  = "CONTENT_API_PROJECT_ID"
      "value" = "content-api-staging-32612"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "SERVICE_ACCOUNT_ID"
      "value" = "<EMAIL>"
    },
    {
      "name"  = "TRADE_APP_WEB_BASE_URL"
      "value" = "https://membersapp-staging.checkatrade.com"
    },
    {
      "name"  = "ZUORA_ENDPOINT"
      "value" = "https://rest.test.eu.zuora.com"
    },
    {
      "name"  = "ZUORA_HOSTED_PAGE_URL"
      "value" = "https://test.eu.zuora.com/apps/PublicHostedPageLite.do"
    },
    {
      "name"  = "ZUORA_DD_HOSTED_PAGE_ID"
      "value" = "8acce27194b0ea310194b1ed076302e0"
    },
    {
      "name"  = "CORS_OPTIONS_ALLOWED_ORIGIN"
      "value" = "https://membersapp-staging.checkatrade.com,http://localhost:19006"
    },
    {
      "name"  = "OIDC_DISCOVERY_URL"
      "value" = "https://api.staging.checkatrade.com/v1/identity/auth/realms/trade/.well-known/openid-configuration"
    },
    {
      "name"  = "CONTENT_API_URL"
      "value" = "https://content-api-inhqerqcdq-nw.a.run.app"
    }
  ]

  cloud_run_receiver_env_variables = [
    {
      "name"  = "CONTENT_API_PROJECT_ID"
      "value" = "content-api-staging-32612"
    },
    {
      "name"  = "RECEIVER_API_HOST"
      "value" = "https://pubsub-receiver-dyziymcfta-nw.a.run.app"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "DOCUMENT_STORE"
      "value" = "document-store-92579811864"
    },
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "cat-trades-preview.appspot.com"
    }
  ]

  cloud_run_bulk_receiver_env_variables = [
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "cat-trades-preview.appspot.com"
    }
  ]

  cloud_run_image_events_env_variables = [
    {
      "name"  = "WORKFLOW_PROJECT"
      "value" = "image-service-stg-10517"
    },
    {
      "name"  = "WORKFLOW_LOCATION"
      "value" = "europe-west1"
    },
    {
      "name"  = "WORKFLOW_NAME"
      "value" = "image-service"
    },
    {
      "name"  = "FirestoreConfig__ProjectId"
      "value" = "cat-trades-preview"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "DD_TRACE_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "DD_APM_ENABLED"
      "value" = "true"
    }
  ]

  cloud_run_member_data_env_variables = [
    {
      "name"  = "GOOGLE_IDENTITY_TOKEN_PROJECT"
      "value" = "cat-trades-preview"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "CORS_ALLOWED_ORIGINS"
      "value" = "https://membersapp-staging.checkatrade.com, http://localhost:19006"
    },
    {
      "name"  = "SALESFORCE_HOST"
      "value" = "checkatrade--uatpartial.sandbox.my.salesforce.com"
    },
    {
      "name"  = "SALESFORCE_TOKEN_URL"
      "value" = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"
    },
    {
      "name"  = "SALESFORCE_API_VERSION"
      "value" = "57.0"
    },
    {
      "name"  = "FIREBASE_PROJECT",
      "value" = "cat-trades-preview"
    },
    {
      "name"  = "MEMBER_DATA_API_HOST"
      "value" = "https://member-data-dyziymcfta-nw.a.run.app"
    },
    {
      "name"  = "SECURE_CONTACTS_API_BASE_URL"
      "value" = "https://secure-contacts-api-jyyaakql3a-nw.a.run.app/api/"
    },
    {
      "name"  = "DD_SERVICE"
      "value" = "member-data"
    }
  ]

  cloud_run_quotes_and_invoices_env_variables = [
    {
      "name"  = "FirestoreConfig__ProjectId"
      "value" = "cat-trades-preview"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "EMAIL_SERVICE_PROJECT_ID"
      "value" = "email-service-stg-32127"
    },
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "cat-trades-preview.appspot.com"
    },
    {
      "name"  = "ENABLE_PDF_GENERATOR_API"
      "value" = "true"
    },
    {
      "name"  = "PDF_GENERATOR_API_ENDPOINT"
      "value" = "https://pdf-generator-kx6bt5vp2a-nw.a.run.app"
    }
  ]

  cloud_run_receiver_parameters = {
    max_scale = 100
  }

  cloud_run_bulk_receiver_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_image_events_api_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_member_data_api_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_quotes_and_invoices_parameters = {
    cpu                   = "4000m"
    memory                = "2048Mi"
    max_scale             = 30
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 40
  }

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  general_notification_targets              = "@slack-trade-experience-alerts-non-prod"
  mobile_platform_team_notification_targets = "@slack-trade-experience-alerts-non-prod"
  firestore_notification_targets            = "@slack-trade-experience-alerts-non-prod"

  # Cloud Storage Lifecycle
  cloud_storage_trade_experience_lifecycle_age = "760"
  cloud_storage_cors_allowed_origins           = ["https://membersapp-staging.checkatrade.com", "http://localhost:19006", "http://localhost:3000", "https://*.nw.r.appspot.com", "https://frontend-staging.checkatrade.com"]

  # Cloud Storage CORS
  document_storage_cors   = local.advisor_ui_cors_block
  temp_image_storage_cors = local.advisor_ui_cors_block

  capi_service_account_domain = "capi-staging-27323.iam.gserviceaccount.com"

  capi_service_accounts = [
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
  ]

  core_firebase_service_accounts = ["serviceAccount:capi-staging-27323.svc.id.goog[core-quoting/quoting-firebase-storage]"]
}
