terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_env_vars = {
    _GCR_HOSTNAME                                  = "eu.gcr.io",
    _PLATFORM                                      = "managed",
    _PROJECT_ENV                                   = local.environment,
    _VERSION                                       = local.env,
    _CONFLUENCE_DOCUMENT_ID                        = "3100147754",
    _CONFLUENCE_BRAZE_TRADE_SCHEMAS_DOCUMENT_ID    = "4321476644",
    _CONFLUENCE_BRAZE_CONSUMER_SCHEMAS_DOCUMENT_ID = "4320854035",
    _ATLASSIAN_USERNAME                            = "<EMAIL>"
  }

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  data_stats_project_id = "data-stats-service-dev-24217"
  retool_project_id     = "retool-stg-72390"
}

inputs = {
  folder       = local.environment
  project_name = local.project_name
  environment  = local.environment

  data_stats_project_id = local.data_stats_project_id
  retool_project_id     = local.retool_project_id

  project_static_permissions = {
    "roles/secretmanager.secretVersionAdder" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/run.developer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/pubsub.publisher" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/storage.objectCreator" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/storage.objectViewer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iam.serviceAccountKeyAdmin" : concat(
      local.project_team_admin,
    )
    "roles/cloudtasks.admin" : concat(
      local.project_team_admin,
    )
    "roles/cloudscheduler.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudsql.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudsql.instanceUser" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/logging.logWriter" : concat(
      local.project_team_admin,
      local.project_team,
    )
  }

  cloud_build_triggers = {
    stagingTrigger = {
      name                         = "PR-to-Main"
      description                  = "Builds and releases to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-communication"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
    }
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-communication"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
    }
    confluencePublisherTrigger = {
      name                         = "confluence-publisher"
      description                  = "Builds and deploys confluence publisher container"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "confluence-publisher"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
    }
  }

  cloud_run_env_variables = {
    communication-api = [
      {
        name  = "PROJECT_ENV"
        value = local.environment
      },
      {
        name  = "KALEYRA_USER"
        value = "CheckatradeAPI"
      },
      {
        name  = "KALEYRA_SHORT_CODE"
        value = "87474"
      },
      {
        name  = "KALEYRA_LONG_CODE"
        value = "07537416151"
      },
      {
        name  = "TEST_MOBILE_NUMBER"
        value = "447378464098"
      },
      {
        name  = "TEST_EMAIL_DOMAIN"
        value = "qier2gw9.mailosaur.net"
      },
      {
        name  = "HOMEOWNER_SITE_BASE_URL"
        value = "http://frontend-staging.checkatrade.com"
      },
      {
        name  = "TRADE_APP_BASE_URL"
        value = "https://membersapp-staging.checkatrade.com"
      },
      {
        name  = "CC_LAW_FIRM_EMAIL_IDS"
        value = "<EMAIL>;<EMAIL>"
      },
      {
        name  = "SALESFORCE_CLIENT_ID"
        value = "salesforce-client-id"
      },
      {
        name  = "SALESFORCE_USERNAME"
        value = "salesforce-username"
      },
      {
        name  = "DATA_STATS_SERVICE_PROJECT_ID"
        value = local.data_stats_project_id
      },
      {
        name  = "BRAZE_CONNECTED_CONTENT_USER_NAME"
        value = "braze"
      },
      {
        name  = "BRAZE_TRADE_SUBSCRIPTION_GROUP_IDS"
        value = "d2434077-d9a0-4a52-81ec-2e85fc07b90c"
      },
      {
        name  = "SENDINBLUE_CONSUMER_MARKETING_LIST_ID"
        value = "2673"
      },
      {
        name  = "CONSUMER_AREA_PROJECT_ID"
        value = "consumer-area-staging-18095"
      },
      {
        name  = "SEARCH_API_BASE_URL"
        value = "https://search-staging.checkatrade.com"
      },
      {
        name  = "ONSI_API_BASE_URL"
        value = "https://api.onsi.com"
      },
      {
        name  = "BRAZE_TRADE_HARD_BOUNCE_SEGMENT_ID"
        value = "f03ca1f6-58fb-4fe2-ba7a-32885f001d2a"
      },
      {
        name  = "BRAZE_EMAIL_BOUNCE_RECIPIENT_EXTERNAL_ID"
        value = "202ea7be-abc5-4c3a-bfb1-685268f6e582"
      },
      {
        name  = "DIRECTORIES_EMAIL"
        value = "<EMAIL>"
      },
      {
        name  = "BOB_API_BASE_URL"
        value = "https://api.sandbox.hibob.com/v1"
      }
    ]
    braze-processor = [
      {
        name  = "PROJECT_ENV"
        value = local.environment
      },
      {
        name  = "KALEYRA_USER"
        value = "CheckatradeAPI"
      },
      {
        name  = "KALEYRA_SHORT_CODE"
        value = "87474"
      },
      {
        name  = "KALEYRA_LONG_CODE"
        value = "07537416151"
      },
      {
        name  = "TEST_MOBILE_NUMBER"
        value = "447378464098"
      },
      {
        name  = "TEST_EMAIL_DOMAIN"
        value = "qier2gw9.mailosaur.net"
      },
      {
        name  = "HOMEOWNER_SITE_BASE_URL"
        value = "http://frontend-staging.checkatrade.com"
      },
      {
        name  = "TRADE_APP_BASE_URL"
        value = "https://membersapp-staging.checkatrade.com"
      },
      {
        name  = "CC_LAW_FIRM_EMAIL_IDS"
        value = "<EMAIL>;<EMAIL>"
      },
      {
        name  = "SALESFORCE_CLIENT_ID"
        value = "salesforce-client-id"
      },
      {
        name  = "SALESFORCE_USERNAME"
        value = "salesforce-username"
      },
      {
        name  = "DATA_STATS_SERVICE_PROJECT_ID"
        value = local.data_stats_project_id
      },
      {
        name  = "BRAZE_CONNECTED_CONTENT_USER_NAME"
        value = "braze"
      },
      {
        name  = "BRAZE_TRADE_SUBSCRIPTION_GROUP_IDS"
        value = "d2434077-d9a0-4a52-81ec-2e85fc07b90c"
      },
      {
        name  = "SENDINBLUE_CONSUMER_MARKETING_LIST_ID"
        value = "2673"
      },
      {
        name  = "CONSUMER_AREA_PROJECT_ID"
        value = "consumer-area-staging-18095"
      },
      {
        name  = "SEARCH_API_BASE_URL"
        value = "https://search-staging.checkatrade.com"
      },
      {
        name  = "ONSI_API_BASE_URL"
        value = "https://api.onsi.com"
      },
      {
        name  = "BRAZE_TRADE_HARD_BOUNCE_SEGMENT_ID"
        value = "f03ca1f6-58fb-4fe2-ba7a-32885f001d2a"
      },
      {
        name  = "BRAZE_EMAIL_BOUNCE_RECIPIENT_EXTERNAL_ID"
        value = "202ea7be-abc5-4c3a-bfb1-685268f6e582"
      },
      {
        name  = "DIRECTORIES_EMAIL"
        value = "<EMAIL>"
      },
      {
        name  = "BOB_API_BASE_URL"
        value = "https://api.sandbox.hibob.com/v1"
      }
    ]
  }

  cloud_run_parameters = {
    communication-api = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
    }
    braze-processor = {
      cpu                   = "2000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 1000
    }
  }

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  sql_db_params = {
    tier      = "db-perf-optimized-N-2"
    disk_size = 20
  }
}
