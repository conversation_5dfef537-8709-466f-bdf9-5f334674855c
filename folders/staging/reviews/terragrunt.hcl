terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    "../../../projects/${local.project_name}/template/*.tpl*",
  ]

  pasabi_export_bucket_name            = "pasabi-checkatrade-production-csv-inputs"
  firestore_export_bucket_name         = "reviews-stg-15037-cat-live-csv-restore"
  slack_webhook_url                    = "*******************************************************************************" #pragma: allowlist secret
  ingest_force_publish_verified_update = "true"
  content_api_project_id               = "content-api-staging-32612"
  consumer_area_api_url                = "https://consumer-api-3yp6skhkzq-nw.a.run.app/"

  # Teams definitions
  project_team                       = ["group:<EMAIL>"]
  project_team_admin                 = ["group:<EMAIL>"]
  qa_team                            = ["group:<EMAIL>"]
  qa_team_admin                      = ["group:<EMAIL>"]
  secure_contacts_project_team       = ["group:<EMAIL>"]
  secure_contacts_project_team_admin = ["group:<EMAIL>"]
  search_project_team                = ["group:<EMAIL>"]
  search_project_team_admin          = ["group:<EMAIL>"]
  jobs_management_team_admin         = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
    local.secure_contacts_project_team,
    local.secure_contacts_project_team_admin,
    local.search_project_team,
    local.search_project_team_admin
  )
  # Admin Auth
  salesforce_fqdn                 = "checkatrade--uatpartial.sandbox.my.salesforce.com"
  firebase_app_id                 = "1:89300884025:web:f369aa852f524386a90be8"
  firebase_auth_domain            = "reviews-stg-15037.firebaseapp.com"
  allowed_frame_ancestor          = "https://checkatrade--uatpartial.sandbox.lightning.force.com"
  client_id                       = "3MVG9xqN3LZmHU7nO2Q47RbMFtZ1X9Qt_ujg5daJRJ2sKLQVhVeTYSW.mN1cAFOkVQz3q9a5687fgWAJRab_l"
  capi_client_id                  = "3MVG9xqN3LZmHU7nO2Q47RbMFtckSXXQ_JV85.hRrTNGhBbNJxvhKnARCHyMOxu.8LobYQlOhISlRqVoL5K0f"
  external_url                    = "https://reviews-admin-stg.checkatrade.com"
  admin_app_service_name          = "reviews-admin-ui"      # Backend service for Salesforce Admin embedded app
  short_api_id                    = "reviews-admin-ui"      # _API_ID that comes from the module includes the full path
  capi_short_api_id               = "capi-reviews-admin-ui" # _API_ID that comes from the module includes the full path
  capi_admin_app_service_name     = "capi-reviews-admin-ui" # Backend service for Salesforce Admin embedded app
  capi_external_url               = "https://reviews-admin-capi-stg.checkatrade.com"
  additional_domains_to_authorize = "https://api.staging.checkatrade.com,https://reviews-admin-stg.checkatrade.com"
}

inputs = {
  # Default inputs
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment

  content_api_project_id         = local.content_api_project_id
  trade_experience_project_id    = "cat-trades-preview"
  search_project_id              = "search-staging-49836"
  consumer_area_project_id       = "consumer-area-staging-18095"
  salesforce_integ_project_id    = "salesforce-integ-stg-22832"
  trade_exp_admin_ui_project_id  = "trade-exp-admin-ui-stg-22772"
  trigger_review_published_comms = false
  trigger_review_created_comms   = true

  data_readers = [
    "serviceAccount:<EMAIL>",
  ]

  # ---- Admin Auth ----
  salesforce_fqdn                 = local.salesforce_fqdn
  firebase_app_id                 = local.firebase_app_id
  firebase_auth_domain            = local.firebase_auth_domain
  allowed_frame_ancestor          = local.allowed_frame_ancestor
  client_id                       = local.client_id
  capi_client_id                  = local.capi_client_id
  external_url                    = local.external_url
  admin_app_service_name          = local.admin_app_service_name
  short_api_id                    = local.short_api_id
  capi_external_url               = local.capi_external_url
  capi_admin_app_service_name     = local.capi_admin_app_service_name
  capi_short_api_id               = local.capi_short_api_id
  additional_domains_to_authorize = local.additional_domains_to_authorize

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  app_env = local.environment

  project_static_permissions = {
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
      local.search_project_team,
      local.search_project_team_admin,
      local.jobs_management_team_admin
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
      local.search_project_team,
      local.search_project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
      local.search_project_team,
      local.search_project_team_admin
    )
    "roles/iam.serviceAccountTokenCreator" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
      local.search_project_team,
      local.search_project_team_admin
    ),
    "roles/firebase.admin" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
      local.search_project_team,
      local.search_project_team_admin,
      local.jobs_management_team_admin
    )
    "roles/firebase.viewer" : concat(
      local.everyone,
    )
    "roles/run.developer" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
      local.search_project_team,
      local.search_project_team_admin
    )
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.codeViewer" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
      local.search_project_team,
      local.search_project_team_admin
    )
    "roles/iap.admin" : concat(
      local.project_team_admin,
    )
    "roles/logging.admin" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/secretmanager.secretVersionAdder" : concat(
      local.project_team_admin,
      local.project_team,
      local.secure_contacts_project_team,
      local.secure_contacts_project_team_admin,
    )
    "roles/cloudscheduler.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/datastore.viewer" : [
      "serviceAccount:<EMAIL>",
      "serviceAccount:<EMAIL>",
    ]
  }

  firebase_notification_targets = "@reviews-admin-ui-alerts-stg"

  cloud_build_triggers_for_admin_auth = {
    staging_trigger = {
      name                         = "PR-to-Main-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR, for ${basename(dirname(get_terragrunt_dir()))} to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }

  cloud_build_triggers_for_capi_admin_auth = {
    staging_trigger = {
      name                         = "PR-to-Main-Admin-Auth-CAPI"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR, for ${basename(dirname(get_terragrunt_dir()))} to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth-CAPI"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }

  cloudbuild_triggers_for_to_postgre_sql = {
    pr_trigger = {
      name                         = "to-postgre-sql--pr"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on an opened PR to main."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _DOTNET_ENVIRONMENT = "Staging"
        _ENABLE_TRAFFIC     = "false"
        _GCP_REGION         = "europe-west2"
        _GCR_HOSTNAME       = "europe-west2-docker.pkg.dev"
        _PLATFORM           = "managed"
        _TRIGGER_NAME       = "pr"
        _VERSION            = "staging"
        _TARGET_PROJECT_ID  = "capi-staging-27323"
        _INSTANCE_NAME      = "consumer-app-sql"
      }

    }
    main_trigger = {
      name                         = "to-postgre-sql--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _DOTNET_ENVIRONMENT = "Staging"
        _ENABLE_TRAFFIC     = "false"
        _GCP_REGION         = "europe-west2"
        _GCR_HOSTNAME       = "europe-west2-docker.pkg.dev"
        _PLATFORM           = "managed"
        _TRIGGER_NAME       = "main"
        _VERSION            = "staging"
        _TARGET_PROJECT_ID  = "capi-staging-27323"
        _INSTANCE_NAME      = "consumer-app-sql"
      }
    }
  }

  # If no environment variables need to be passed here, please set an empty map within a list like "[{}]"
  # Setting an empty list only like "[]" will cause a type mismatch when running a plan
  cloud_run_env_variables = {
    submit-review = [
      {
        name  = "CORS_ALLOWED_ORIGINS"
        value = "^https:\\/\\/.*checkatrade.com$, ^https:\\/\\/.*appspot.com$, ^http://localhost:3000$"
      }
    ]
    reviews-backend = [
      {
        name  = "PASABI_EXPORT_BUCKET_NAME"
        value = local.pasabi_export_bucket_name
      },
      {
        name  = "FIRESTORE_EXPORT_BUCKET_NAME"
        value = local.firestore_export_bucket_name
      },
      {
        name  = "CONTENT_API_PROJECT_ID"
        value = local.content_api_project_id
      },
      {
        name  = "SLACK_WEBHOOK_URL"
        value = local.slack_webhook_url
      },
      {
        name  = "CONSUMER_AREA_API_URL"
        value = local.consumer_area_api_url
      }
    ]
    reviews-ingester = [{
      name  = "INGEST_FORCE_PUBLISH_VERIFIED_UPDATE"
      value = local.ingest_force_publish_verified_update
    }]
  }

  cloud_run_secret_variables = {
    submit-review = [
      {
        name      = "TURNSTILE_DEVELOPMENT_SECRET"
        secret_id = "TURNSTILE_DEVELOPMENT_SECRET"
      },
    ]
  }

  # AWS API Gateway hosts for DI Subscriptions
  tasks_events_di_api_gateway_host = "https://c2blqu3efc.execute-api.eu-west-2.amazonaws.com/staging/events"

  cloud_function_endpoint = "europe-west2-reviews-stg-15037.cloudfunctions.net"
  workflow_endpoint       = "https://workflowexecutions.googleapis.com"
}
