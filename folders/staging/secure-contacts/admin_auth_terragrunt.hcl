inputs = {
  cloud_run_admin_auth_settings = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    container_concurrency = 500
    max_scale             = 2
    min_scale             = 0
    initial_scale         = 0
  }

  cloud_build_env_vars_for_admin_auth = {
    # Firebase API keys are not considered sensitive so are not to be scanned by Gitleaks
    # https://firebase.google.com/support/guides/security-checklist#api-keys-not-secret
    # pragma: allowlist nextline secret
    _FIREBASE_API_KEY       = "AIzaSyA2ZOTcNTZVcz44ecpmejZv-YHZCqvIe5I" #  # gitleaks:allow
    _FIREBASE_APP_ID        = "1:141436075991:web:0b4f5c70fcac46c7385592"
    _FIREBASE_AUTH_DOMAIN   = "contacts-staging-47704.firebaseapp.com"
    _AUTHORIZATION_ENDPOINT = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/authorize"
    _CLIENT_ID              = "3MVG9xqN3LZmHU7nO2Q47RbMFtUNB3HgThuaDkq9nNrHkQzZTe.IWAYCcR7tO3.rG.SuHpAPksI64skzAIw._"
    _ISSUER                 = "https://checkatrade--uatpartial.sandbox.my.salesforce.com"
    _JWKS_URI               = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/id/keys"
    _TOKEN_ENDPOINT         = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"
    _ALLOWED_FRAME_ANCESTOR = "https://checkatrade--uatpartial.sandbox.lightning.force.com"
    _EXTERNAL_URL           = "https://secure-contacts-admin-stg.checkatrade.net"
  }

  cloud_build_triggers_for_admin_auth = {
    staging_trigger = {
      name                         = "PR-to-Main-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }
}
