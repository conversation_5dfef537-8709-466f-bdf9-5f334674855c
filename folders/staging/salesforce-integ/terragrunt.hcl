terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  repo_name    = "salesforce-gcp-integration"
  organisation = "cat-home-experts"
  region       = "europe-west2"

  cloudbuild_env_variables = {
    emulator = {}
    salesforce-gcp-integ = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "salesforce-gcp-integ"
      _VERSION        = "stg"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    gateway = {
      _GCP_REGION           = "europe-west2"
      _SERVICE_NAME         = "salesforce-gcp-integ"
      _VERSION              = "stg"
      _PLATFORM             = "managed"
      _API_GATEWAY_NAME     = "salesforce-gcp-integ-gateway-gw"
      _API_GATEWAY_API_NAME = "salesforce-gcp-integ-gateway"
    }
    sf_integ_admin_ui = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "sf-integ-admin-ui"
      _VERSION        = "stg"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
  }

  env = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
  })

  # ------------ For Atlantis ------------
  # If you want to disable its creation, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    #"../../../projects/${local.project_name}/template/*.tpl*"  # example for the projects API gateways
  ]



  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]

  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  # Admin Auth
  salesforce_fqdn        = "checkatrade--uatpartial.sandbox.my.salesforce.com"
  firebase_app_id        = "1:693340619352:web:ac18e195ac875ac037c02d"
  firebase_auth_domain   = "salesforce-integ-stg-22832.firebaseapp.com"
  client_id              = "3MVG9xqN3LZmHU7nO2Q47RbMFtTDBELklB6QRJnCKa2yWPKY81sUxl4hGY0l6B.3Qw8ZmErbvM6j.7jKG9tZg"
  allowed_frame_ancestor = "https://checkatrade--uatpartial.sandbox.lightning.force.com"
  external_url           = "https://salesforce-integ-admin-stg.checkatrade.com"
  admin_app_service_name = "sf-integ-admin-ui"
  short_api_id           = "sf-integ-admin-ui"

  salesforce_api_uri   = "https://checkatrade--uatpartial.sandbox.my.salesforce.com"
  salesforce_id_server = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"

  content_api_uri           = "https://content-api-inhqerqcdq-nw.a.run.app"
  content_api_iap_client_id = "288239833219-e2998utn64eqomtpu8hmctkk7k98k01u.apps.googleusercontent.com"

  consumer_api_uri = "https://api.staging.checkatrade.com/v1/consumer/"
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # --- Salesforce settings ---
  salesforce_api_uri              = local.salesforce_api_uri
  salesforce_api_uri_enterprise   = local.salesforce_api_uri
  salesforce_id_server            = local.salesforce_id_server
  salesforce_id_server_enterprise = local.salesforce_id_server
  trade_experience_project_id     = "cat-trades-preview"
  content_api_project_id          = "content-api-staging-32612"
  jobs_management_project_id      = "jobs-management-stg-36500"
  capi_project_id                 = "capi-staging-27323"

  # ---- env Vars -----
  content_api_uri           = local.content_api_uri
  content_api_iap_client_id = local.content_api_iap_client_id
  consumer_api_uri          = local.consumer_api_uri

  # ---- Should be passed to any project ----
  project_name   = local.project_name
  environment    = local.environment
  project_folder = local.environment

  # ---- Admin Auth ----
  salesforce_fqdn        = local.salesforce_fqdn
  firebase_app_id        = local.firebase_app_id
  firebase_auth_domain   = local.firebase_auth_domain
  client_id              = local.client_id
  allowed_frame_ancestor = local.allowed_frame_ancestor
  external_url           = local.external_url
  admin_app_service_name = local.admin_app_service_name
  short_api_id           = local.short_api_id

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/apigateway.admin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iam.serviceAccountAdmin" : concat(
      local.project_team_admin,
    )
    "roles/resourcemanager.projectIamAdmin" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountKeyAdmin" : concat(
      local.project_team_admin,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.editor" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/secretmanager.secretVersionManager" : concat(
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/logging.viewAccessor" : concat(
      local.viewers,
    )
    "roles/run.admin" : concat(
      local.project_team,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    ),
    "roles/logging.privateLogViewer" : concat(
      local.project_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
      local.project_team
    ),
    "roles/secretmanager.secretVersionAdder" : concat(
      local.project_team_admin,
      local.project_team
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    prGatewayTriggerApi = {
      name                         = "api-gateway-pr"
      description                  = "Builds and releases to staging on pr to main."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.gateway
      included_files_filter        = ["api-gateway/**"]
    }

    pushGatewayTriggerApi = {
      name                         = "api-gateway-push"
      description                  = "Builds and releases to staging on push to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.gateway
      included_files_filter        = ["api-gateway/**"]
    }

    prMain = {
      name                         = "main-pr"
      description                  = "Builds and releases to staging on pr to main."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["salesforce-gcp-integ"],
        {
          _TRIGGER_NAME   = "main-pr"
          _ENABLE_TRAFFIC = false
        }
      )
    }

    # api push to main
    pushMain = {
      name                         = "main-push"
      description                  = "Builds and releases to staging on push to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["salesforce-gcp-integ"],
        {
          _TRIGGER_NAME   = "main-push"
          _ENABLE_TRAFFIC = true
        }
      )
      excluded_files_filter = ["README.md"]
    }

    # standalone emulator
    build_firestore_emulator = {
      name                         = "firestore-emulator"
      description                  = "Builds Docker image on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-emulator"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      excluded_files_filter        = ["README.md"]
    }
    admin_ui_pr-to-main_trigger = {
      name                         = "Admin-UI-pr-to-Main"
      description                  = "Builds and releases Jobs Mgmt Admin UI to staging on any pr to main."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "src/JobsManagement/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.sf_integ_admin_ui
      included_files_filter        = ["src/JobsManagement/**"] # Can be commented out
    }
    admin_ui_commit_to_main_trigger = {
      name                         = "Admin-UI-commit-to-Main"
      description                  = "Builds and releases Jobs Mgmt Api to staging on any main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "src/JobsManagement/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.sf_integ_admin_ui
      included_files_filter        = ["src/JobsManagement/**"] # Can be commented out
    }
  }

  cloud_run_env_variables = {
    salesforce-gcp-integ = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      },
      {
        name  = "LawFirmStorageBucketConfig__BucketName"
        value = "credit_control_storage_bucket-27"
      }
    ]
    sf_integ_admin_ui = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      }
    ]
  }

  cloud_run_parameters = {
    salesforce-gcp-integ = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    }
    sf_integ_admin_ui = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    }
  }

  firebase_notification_targets = "@salesforce-integ-stg"

  cloud_build_triggers_for_admin_auth = {
    development_trigger = {
      name                         = "Feature-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
    }
    staging_trigger = {
      name                         = "PR-to-Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR, for ${basename(dirname(get_terragrunt_dir()))} to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }
  capi_service_accounts = [
    "serviceAccount:<EMAIL>",
  ]

  capi_pubsub_publish_accounts = [
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
  ]

  cloud_run_invokers = [
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>"
  ]
}

