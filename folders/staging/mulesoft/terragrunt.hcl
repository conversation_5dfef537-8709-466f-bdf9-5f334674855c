terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
}

inputs = {
  # Default inputs
  project_name   = local.project_name
  environment    = local.environment
  project_folder = local.environment

  cloudbuild_triggers = {
    # subscription sql sapi triggers
    subscriptionSqlSapiStagingTrigger = {
      name                         = "subs-sql-sapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "subscription-sql-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID           = "17562396"
        _ANYPOINT_ENV     = "Staging"
        _DEPLOYMENT_NAME  = "cat-s-stg-subscription-sql-api"
        _MULE_API_LAYER   = "system"
        _MULE_APP_TAGS    = "subscription"
        _MULE_ENV         = "stg"
        _MULE_NUM_WORKERS = 1
        _MULE_WORKER_TYPE = "MICRO"
        _DB_NAME          = "CatPreview"
        _DB_MAX_POOL_SIZE = "10"
        _DB_MIN_POOL_SIZE = "3"
      }
      excluded_files_filter = []
    },
    subscriptionSqlSapiProdTrigger = {
      name                         = "subs-sql-sapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "subscription-sql-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID           = "17562396"
        _ANYPOINT_ENV     = "Staging"
        _DEPLOYMENT_NAME  = "cat-s-stg-subscription-sql-api"
        _MULE_API_LAYER   = "system"
        _MULE_APP_TAGS    = "subscription"
        _MULE_ENV         = "stg"
        _MULE_NUM_WORKERS = 1
        _MULE_WORKER_TYPE = "MICRO"
        _DB_NAME          = "CatPreview"
        _DB_MAX_POOL_SIZE = "10"
        _DB_MIN_POOL_SIZE = "3"
      }
      excluded_files_filter = []
    },
    # subscription xapi triggers
    subscriptionXapiStagingTrigger = {
      name                         = "subs-xapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "subscription-xapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                            = "17429702"
        _ANYPOINT_ENV                                      = "Staging"
        _DEPLOYMENT_NAME                                   = "cat-e-stg-subscription-api"
        _MULE_API_LAYER                                    = "experience"
        _MULE_APP_TAGS                                     = "subscription"
        _MULE_ENV                                          = "stg"
        _MULE_NUM_WORKERS                                  = 1
        _MULE_WORKER_TYPE                                  = "MICRO"
        _SALESFORCE_AUTHORISATION_URL                      = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _SALESFORCE_FLOW_MAX_CONCURRENCY                   = "200"
        _MQ_ENDPOINT                                       = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_EXCHANGE_SUBSCRIPTION_NAME                     = "x-cat-e-subscription-api-stg"
        _MQ_EXCHANGE_TRADE_CATEGORIES_FETCH                = "x-cat-e-subscription-api-categories-lib-fetch-stg"
        _MQ_EXCHANGE_TRADE_CATEGORIES_UPDATE               = "x-cat-e-subscription-api-categories-lib-update-stg"
        _GCP_TOPIC_SUBSCRIBER_TRADE_CATEGORIES_NAME_FETCH  = "trade-experience-salesforce-subscription-req"
        _GCP_TOPIC_SUBSCRIBER_TRADE_CATEGORIES_NAME_UPDATE = "trade-experience-salesforce-subscription-write-req"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_PROJECT_ID      = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_CLIENT_EMAIL    = "<EMAIL>"
      }
      excluded_files_filter = []
    },
    subscriptionXapiProdTrigger = {
      name                         = "subscription-xapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "subscription-xapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                            = "17429702"
        _ANYPOINT_ENV                                      = "Staging"
        _DEPLOYMENT_NAME                                   = "cat-e-stg-subscription-api"
        _MULE_API_LAYER                                    = "experience"
        _MULE_APP_TAGS                                     = "subscription"
        _MULE_ENV                                          = "stg"
        _MULE_NUM_WORKERS                                  = 1
        _MULE_WORKER_TYPE                                  = "MICRO"
        _SALESFORCE_AUTHORISATION_URL                      = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _SALESFORCE_FLOW_MAX_CONCURRENCY                   = "200"
        _MQ_ENDPOINT                                       = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_EXCHANGE_SUBSCRIPTION_NAME                     = "x-cat-e-subscription-api-stg"
        _MQ_EXCHANGE_TRADE_CATEGORIES_FETCH                = "x-cat-e-subscription-api-categories-lib-fetch-stg"
        _MQ_EXCHANGE_TRADE_CATEGORIES_UPDATE               = "x-cat-e-subscription-api-categories-lib-update-stg"
        _GCP_TOPIC_SUBSCRIBER_TRADE_CATEGORIES_NAME_FETCH  = "trade-experience-salesforce-subscription-req"
        _GCP_TOPIC_SUBSCRIBER_TRADE_CATEGORIES_NAME_UPDATE = "trade-experience-salesforce-subscription-write-req"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_PROJECT_ID      = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_CLIENT_EMAIL    = "<EMAIL>"
      }
      excluded_files_filter = []
    },
    # contact xapi triggers
    contactXapiStagingTrigger = {
      name                         = "contact-xapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "contact-xapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                    = "17567025"
        _ANYPOINT_ENV                              = "Staging"
        _DEPLOYMENT_NAME                           = "cat-e-stg-contact-api"
        _MULE_API_LAYER                            = "experience"
        _MULE_APP_TAGS                             = "contact"
        _MULE_ENV                                  = "stg"
        _MULE_NUM_WORKERS                          = 1
        _MULE_WORKER_TYPE                          = "MICRO"
        _SALESFORCE_AUTHORISATION_URL              = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _SALESFORCE_FLOW_MAX_CONCURRENCY           = "200"
        _MQ_ENDPOINT                               = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_EXCHANGE_CONTACT_NAME                  = "x-cat-e-contact-api-stg"
        _MQ_EXCHANGE_MARKETING_PREF_NAME           = "x-cat-e-contact-api-marketing-preferences-stg"
        _GCP_TOPIC_SUBSCRIBER_CONTACT_NAME         = "contact-preferences-req-mulesoft"
        _GCP_TOPIC_SUBSCRIBER_CONTACT_PROJECT_ID   = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_CONTACT_CLIENT_EMAIL = "<EMAIL>"
      }
      excluded_files_filter = []
    },
    contactXapiProdTrigger = {
      name                         = "contact-xapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "contact-xapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                    = "17567025"
        _ANYPOINT_ENV                              = "Staging"
        _DEPLOYMENT_NAME                           = "cat-e-stg-contact-api"
        _MULE_API_LAYER                            = "experience"
        _MULE_APP_TAGS                             = "contact"
        _MULE_ENV                                  = "stg"
        _MULE_NUM_WORKERS                          = 1
        _MULE_WORKER_TYPE                          = "MICRO"
        _SALESFORCE_AUTHORISATION_URL              = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _SALESFORCE_FLOW_MAX_CONCURRENCY           = "200"
        _MQ_ENDPOINT                               = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_EXCHANGE_CONTACT_NAME                  = "x-cat-e-contact-api-stg"
        _MQ_EXCHANGE_MARKETING_PREF_NAME           = "x-cat-e-contact-api-marketing-preferences-stg"
        _GCP_TOPIC_SUBSCRIBER_CONTACT_NAME         = "contact-preferences-req-mulesoft"
        _GCP_TOPIC_SUBSCRIBER_CONTACT_PROJECT_ID   = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_CONTACT_CLIENT_EMAIL = "<EMAIL>"
      }
      excluded_files_filter = []
    },
    # account xapi triggers
    accountXapiStagingTrigger = {
      name                         = "account-xapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "account-xapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                           = "********"
        _ANYPOINT_ENV                                     = "Staging"
        _DEPLOYMENT_NAME                                  = "cat-e-stg-account-api"
        _MULE_API_LAYER                                   = "experience"
        _MULE_APP_TAGS                                    = "account"
        _MULE_ENV                                         = "stg"
        _MULE_NUM_WORKERS                                 = 1
        _MULE_WORKER_TYPE                                 = "MICRO"
        _SALESFORCE_AUTHORISATION_URL                     = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _SALESFORCE_FLOW_MAX_CONCURRENCY                  = "200"
        _MQ_ENDPOINT                                      = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_EXCHANGE_ACCOUNT_NAME                         = "x-cat-e-account-api-stg"
        _MQ_EXCHANGE_PLI_NAME                             = "x-cat-e-account-api-pli-stg"
        _MQ_EXCHANGE_AVAILABILITY_NAME                    = "x-cat-e-account-api-availability-stg"
        _MQ_EXCHANGE_PROFILE_NAME                         = "x-cat-e-account-api-profile-stg"
        _MQ_EXCHANGE_SALE_NAME                            = "x-cat-e-account-api-sale-stg"
        _MQ_EXCHANGE_LIBRARY_UPSERT_NAME                  = "x-cat-e-account-api-categories-library-stg"
        _MQ_EXCHANGE_LIBRARY_FETCH_NAME                   = "x-cat-e-account-api-categories-library-fetch-stg"
        _GCP_TOPIC_SUBSCRIBER_LIBRARY_NAME                = "trade-experience-salesforce-account-req"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_NAME           = "trade-profile-req-mulesoft"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_PROJECT_ID     = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_CLIENT_EMAIL   = "<EMAIL>"
        _GCP_TOPIC_SUBSCRIBER_DOCUMENT_STORE_NAME         = "salesforce-document-store"
        _GCP_TOPIC_SUBSCRIBER_DOCUMENT_STORE_PROJECT_ID   = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_DOCUMENT_STORE_CLIENT_EMAIL = "<EMAIL>"
      }
      excluded_files_filter = []
    },
    accountXapiProdTrigger = {
      name                         = "account-xapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "account-xapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                           = "********"
        _ANYPOINT_ENV                                     = "Staging"
        _DEPLOYMENT_NAME                                  = "cat-e-stg-account-api"
        _MULE_API_LAYER                                   = "experience"
        _MULE_APP_TAGS                                    = "account"
        _MULE_ENV                                         = "stg"
        _MULE_NUM_WORKERS                                 = 1
        _MULE_WORKER_TYPE                                 = "MICRO"
        _SALESFORCE_AUTHORISATION_URL                     = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _SALESFORCE_FLOW_MAX_CONCURRENCY                  = "200"
        _MQ_ENDPOINT                                      = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_EXCHANGE_ACCOUNT_NAME                         = "x-cat-e-account-api-stg"
        _MQ_EXCHANGE_PLI_NAME                             = "x-cat-e-account-api-pli-stg"
        _MQ_EXCHANGE_AVAILABILITY_NAME                    = "x-cat-e-account-api-availability-stg"
        _MQ_EXCHANGE_PROFILE_NAME                         = "x-cat-e-account-api-profile-stg"
        _MQ_EXCHANGE_SALE_NAME                            = "x-cat-e-account-api-sale-stg"
        _MQ_EXCHANGE_LIBRARY_UPSERT_NAME                  = "x-cat-e-account-api-categories-library-stg"
        _MQ_EXCHANGE_LIBRARY_FETCH_NAME                   = "x-cat-e-account-api-categories-library-fetch-stg"
        _GCP_TOPIC_SUBSCRIBER_LIBRARY_NAME                = "trade-experience-salesforce-account-req"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_NAME           = "trade-profile-req-mulesoft"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_PROJECT_ID     = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_AVAILABILITY_CLIENT_EMAIL   = "<EMAIL>"
        _GCP_TOPIC_SUBSCRIBER_DOCUMENT_STORE_NAME         = "salesforce-document-store"
        _GCP_TOPIC_SUBSCRIBER_DOCUMENT_STORE_PROJECT_ID   = "cat-trades-preview"
        _GCP_TOPIC_SUBSCRIBER_DOCUMENT_STORE_CLIENT_EMAIL = "<EMAIL>"
      }
      excluded_files_filter = []
    },
    # account sql sapi triggers
    accountSqlSapiStagingTrigger = {
      name                         = "acc-sql-sapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "account-sql-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID           = "********"
        _ANYPOINT_ENV     = "Staging"
        _DEPLOYMENT_NAME  = "cat-s-stg-account-sql-api"
        _MULE_API_LAYER   = "system"
        _MULE_APP_TAGS    = "account"
        _MULE_ENV         = "stg"
        _MULE_NUM_WORKERS = 1
        _MULE_WORKER_TYPE = "MICRO"
        _DB_NAME          = "CatPreview"
        _DB_MAX_POOL_SIZE = "10"
        _DB_MIN_POOL_SIZE = "3"
      }
      excluded_files_filter = []
    },
    accountSqlSapiProdTrigger = {
      name                         = "account-sql-sapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "account-sql-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID           = "********"
        _ANYPOINT_ENV     = "Staging"
        _DEPLOYMENT_NAME  = "cat-s-stg-account-sql-api"
        _MULE_API_LAYER   = "system"
        _MULE_APP_TAGS    = "account"
        _MULE_ENV         = "stg"
        _MULE_NUM_WORKERS = 1
        _MULE_WORKER_TYPE = "MICRO"
        _DB_NAME          = "CatPreview"
        _DB_MAX_POOL_SIZE = "10"
        _DB_MIN_POOL_SIZE = "3"
      }
      excluded_files_filter = []
    },
    # subscription papi triggers
    subscriptionPapiStagingTrigger = {
      name                         = "subs-papi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "subscription-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                              = "17484548"
        _ANYPOINT_ENV                                        = "Staging"
        _DEPLOYMENT_NAME                                     = "cat-p-stg-subscription-api"
        _MULE_API_LAYER                                      = "process"
        _MULE_APP_TAGS                                       = "subscription"
        _MULE_ENV                                            = "stg"
        _MULE_NUM_WORKERS                                    = 1
        _MULE_WORKER_TYPE                                    = "MICRO"
        _MQ_ENDPOINT                                         = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_QUEUE_SUBSCRIBER_SUBSCRIPTION_NAME               = "q-cat-p-subscription-api-stg"
        _MQ_QUEUE_SUBSCRIBER_SUBSCRIPTION_ENABLED            = "true"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_FETCH_NAME     = "q-cat-p-subscription-api-categories-lib-fetch-stg"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_FETCH_ENABLED  = "true"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_UPDATE_NAME    = "q-cat-p-subscription-api-categories-lib-update-stg"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_UPDATE_ENABLED = "true"
        _SUBSCRIPTION_SQL_SAPI_HOST                          = "cat-s-stg-subscription-sql-api.mulesoft-intl.dev.checkatrade.net"
        _SUBSCRIPTION_SQL_SAPI_PORT                          = "80"
        _SUBSCRIPTION_SQL_SAPI_CONNECTION_TIMEOUT            = "60000"
        _SUBSCRIPTION_SQL_SAPI_RESPONSE_TIMEOUT              = "45000"
        _DEFERRED_ACTION_SERVICE_SAPI_HOST                   = "cat-s-stg-deferred-action-service-api.mulesoft-intl.dev.checkatrade.net"
        _DEFERRED_ACTION_SERVICE_SAPI_PORT                   = "80"
        _DEFERRED_ACTION_SERVICE_SAPI_CONNECTION_TIMEOUT     = "60000"
        _DEFERRED_ACTION_SERVICE_SAPI_RESPONSE_TIMEOUT       = "45000"
        _ACCOUNT_SF_SAPI_HOST                                = "cat-s-stg-account-sf-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SF_SAPI_PORT                                = "80"
        _ACCOUNT_SF_SAPI_CONNECTION_TIMEOUT                  = "60000"
        _ACCOUNT_SF_SAPI_RESPONSE_TIMEOUT                    = "45000"
        _ACCOUNT_GCP_SAPI_HOST                               = "cat-s-stg-account-gcp-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_GCP_SAPI_PORT                               = "80"
        _ACCOUNT_GCP_SAPI_CONNECTION_TIMEOUT                 = "60000"
        _ACCOUNT_GCP_SAPI_RESPONSE_TIMEOUT                   = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES                        = "1"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD                    = "1000"
        _SCHEDULER_SUBSCRIPTION_ENABLED                      = "true"
        _SCHEDULER_SUBSCRIPTION_FREQUENCY                    = "30"
      }
      excluded_files_filter = []
    },
    subscriptionPapiProdTrigger = {
      name                         = "subscription-papi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "subscription-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                              = "17484548"
        _ANYPOINT_ENV                                        = "Staging"
        _DEPLOYMENT_NAME                                     = "cat-p-stg-subscription-api"
        _MULE_API_LAYER                                      = "process"
        _MULE_APP_TAGS                                       = "subscription"
        _MULE_ENV                                            = "stg"
        _MULE_NUM_WORKERS                                    = 1
        _MULE_WORKER_TYPE                                    = "MICRO"
        _MQ_ENDPOINT                                         = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_QUEUE_SUBSCRIBER_SUBSCRIPTION_NAME               = "q-cat-p-subscription-api-stg"
        _MQ_QUEUE_SUBSCRIBER_SUBSCRIPTION_ENABLED            = "true"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_FETCH_NAME     = "q-cat-p-subscription-api-categories-lib-fetch-stg"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_FETCH_ENABLED  = "true"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_UPDATE_NAME    = "q-cat-p-subscription-api-categories-lib-update-stg"
        _MQ_QUEUE_SUBSCRIBER_TRADE_CATEGORIES_UPDATE_ENABLED = "true"
        _SUBSCRIPTION_SQL_SAPI_HOST                          = "cat-s-stg-subscription-sql-api.mulesoft-intl.dev.checkatrade.net"
        _SUBSCRIPTION_SQL_SAPI_PORT                          = "80"
        _SUBSCRIPTION_SQL_SAPI_CONNECTION_TIMEOUT            = "60000"
        _SUBSCRIPTION_SQL_SAPI_RESPONSE_TIMEOUT              = "45000"
        _DEFERRED_ACTION_SERVICE_SAPI_HOST                   = "cat-s-stg-deferred-action-service-api.mulesoft-intl.dev.checkatrade.net"
        _DEFERRED_ACTION_SERVICE_SAPI_PORT                   = "80"
        _DEFERRED_ACTION_SERVICE_SAPI_CONNECTION_TIMEOUT     = "60000"
        _DEFERRED_ACTION_SERVICE_SAPI_RESPONSE_TIMEOUT       = "45000"
        _ACCOUNT_SF_SAPI_HOST                                = "cat-s-stg-account-sf-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SF_SAPI_PORT                                = "80"
        _ACCOUNT_SF_SAPI_CONNECTION_TIMEOUT                  = "60000"
        _ACCOUNT_SF_SAPI_RESPONSE_TIMEOUT                    = "45000"
        _ACCOUNT_GCP_SAPI_HOST                               = "cat-s-stg-account-gcp-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_GCP_SAPI_PORT                               = "80"
        _ACCOUNT_GCP_SAPI_CONNECTION_TIMEOUT                 = "60000"
        _ACCOUNT_GCP_SAPI_RESPONSE_TIMEOUT                   = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES                        = "1"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD                    = "1000"
        _SCHEDULER_SUBSCRIPTION_ENABLED                      = "true"
        _SCHEDULER_SUBSCRIPTION_FREQUENCY                    = "30"
      }
      excluded_files_filter = []
    },
    # deferred action service sapi triggers
    deferredActionServiceSapiStagingTrigger = {
      name                         = "def-sapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "deferred-action-service-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID           = "********"
        _ANYPOINT_ENV     = "Staging"
        _DEPLOYMENT_NAME  = "cat-s-stg-deferred-action-service-api"
        _MULE_API_LAYER   = "system"
        _MULE_APP_TAGS    = "deferred-action-service"
        _MULE_ENV         = "stg"
        _MULE_NUM_WORKERS = 1
        _MULE_WORKER_TYPE = "MICRO"
        _DB_NAME          = "DeferredMessagesPreview"
        _DB_MAX_POOL_SIZE = "10"
        _DB_MIN_POOL_SIZE = "3"
      }
      excluded_files_filter = []
    },
    deferredActionServiceSapiProdTrigger = {
      name                         = "def-action-sapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "deferred-action-service-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID           = "********"
        _ANYPOINT_ENV     = "Staging"
        _DEPLOYMENT_NAME  = "cat-s-stg-deferred-action-service-api"
        _MULE_API_LAYER   = "system"
        _MULE_APP_TAGS    = "deferred-action-service"
        _MULE_ENV         = "stg"
        _MULE_NUM_WORKERS = 1
        _MULE_WORKER_TYPE = "MICRO"
        _DB_NAME          = "DeferredMessagesPreview"
        _DB_MAX_POOL_SIZE = "10"
        _DB_MIN_POOL_SIZE = "3"
      }
      excluded_files_filter = []
    },
    # account papi triggers
    accountPapiStagingTrigger = {
      name                         = "account-papi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "account-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                     = "********"
        _ANYPOINT_ENV                               = "Staging"
        _DEPLOYMENT_NAME                            = "cat-p-stg-account-api"
        _MULE_API_LAYER                             = "process"
        _MULE_APP_TAGS                              = "account"
        _MULE_ENV                                   = "stg"
        _MULE_NUM_WORKERS                           = 1
        _MULE_WORKER_TYPE                           = "MICRO"
        _MQ_ENDPOINT                                = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_QUEUE_SUBSCRIBER_ACCOUNT_NAME           = "q-cat-p-account-api-stg"
        _MQ_QUEUE_SUBSCRIBER_ACCOUNT_ENABLED        = "true"
        _MQ_QUEUE_SUBSCRIBER_PLI_NAME               = "q-cat-p-account-api-pli-stg"
        _MQ_QUEUE_SUBSCRIBER_PLI_ENABLED            = "true"
        _MQ_QUEUE_SUBSCRIBER_AVAILABILITY_NAME      = "q-cat-p-account-api-availability-stg"
        _MQ_QUEUE_SUBSCRIBER_AVAILABILITY_ENABLED   = "true"
        _MQ_QUEUE_SUBSCRIBER_PROFILE_NAME           = "q-cat-p-account-api-profile-stg"
        _MQ_QUEUE_SUBSCRIBER_PROFILE_ENABLED        = "true"
        _MQ_QUEUE_SUBSCRIBER_SALE_NAME              = "q-cat-p-account-api-sale-stg"
        _MQ_QUEUE_SUBSCRIBER_SALE_ENABLED           = "true"
        _MQ_QUEUE_SUBSCRIBER_ASSIGNMENT_NAME        = "q-cat-p-account-api-assignment-stg"
        _MQ_QUEUE_SUBSCRIBER_ASSIGNMENT_ENABLED     = "true"
        _MQ_QUEUE_DESTINATION_ASSIGNMENT            = "x-cat-p-account-api-assignment-stg"
        _MQ_QUEUE_SUBSCRIBER_LIBRARY_UPSERT_NAME    = "q-cat-p-account-api-categories-library-stg"
        _MQ_QUEUE_SUBSCRIBER_LIBRARY_UPSERT_ENABLED = "true"
        _ACCOUNT_SQL_SAPI_HOST                      = "cat-s-stg-account-sql-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SQL_SAPI_PORT                      = "80"
        _ACCOUNT_SQL_SAPI_CONNECTION_TIMEOUT        = "60000"
        _ACCOUNT_SQL_SAPI_RESPONSE_TIMEOUT          = "45000"
        _ACCOUNT_SF_SAPI_HOST                       = "cat-s-stg-account-sf-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SF_SAPI_PORT                       = "80"
        _ACCOUNT_SF_SAPI_CONNECTION_TIMEOUT         = "60000"
        _ACCOUNT_SF_SAPI_RESPONSE_TIMEOUT           = "45000"
        _ACCOUNT_GCP_SAPI_HOST                      = "cat-s-stg-account-gcp-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_GCP_SAPI_PORT                      = "80"
        _ACCOUNT_GCP_SAPI_CONNECTION_TIMEOUT        = "60000"
        _ACCOUNT_GCP_SAPI_RESPONSE_TIMEOUT          = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES               = "1"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD           = "1000"
      }
      excluded_files_filter = []
    },
    accountPapiProdTrigger = {
      name                         = "account-papi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "account-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                     = "********"
        _ANYPOINT_ENV                               = "Staging"
        _DEPLOYMENT_NAME                            = "cat-p-stg-account-api"
        _MULE_API_LAYER                             = "process"
        _MULE_APP_TAGS                              = "account"
        _MULE_ENV                                   = "stg"
        _MULE_NUM_WORKERS                           = 1
        _MULE_WORKER_TYPE                           = "MICRO"
        _MQ_ENDPOINT                                = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_QUEUE_SUBSCRIBER_ACCOUNT_NAME           = "q-cat-p-account-api-stg"
        _MQ_QUEUE_SUBSCRIBER_ACCOUNT_ENABLED        = "true"
        _MQ_QUEUE_SUBSCRIBER_PLI_NAME               = "q-cat-p-account-api-pli-stg"
        _MQ_QUEUE_SUBSCRIBER_PLI_ENABLED            = "true"
        _MQ_QUEUE_SUBSCRIBER_AVAILABILITY_NAME      = "q-cat-p-account-api-availability-stg"
        _MQ_QUEUE_SUBSCRIBER_AVAILABILITY_ENABLED   = "true"
        _MQ_QUEUE_SUBSCRIBER_PROFILE_NAME           = "q-cat-p-account-api-profile-stg"
        _MQ_QUEUE_SUBSCRIBER_PROFILE_ENABLED        = "true"
        _MQ_QUEUE_SUBSCRIBER_SALE_NAME              = "q-cat-p-account-api-sale-stg"
        _MQ_QUEUE_SUBSCRIBER_SALE_ENABLED           = "true"
        _MQ_QUEUE_SUBSCRIBER_ASSIGNMENT_NAME        = "q-cat-p-account-api-assignment-stg"
        _MQ_QUEUE_SUBSCRIBER_ASSIGNMENT_ENABLED     = "true"
        _MQ_QUEUE_DESTINATION_ASSIGNMENT            = "x-cat-p-account-api-assignment-stg"
        _MQ_QUEUE_SUBSCRIBER_LIBRARY_UPSERT_NAME    = "q-cat-p-account-api-categories-library-stg"
        _MQ_QUEUE_SUBSCRIBER_LIBRARY_UPSERT_ENABLED = "true"
        _ACCOUNT_SQL_SAPI_HOST                      = "cat-s-stg-account-sql-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SQL_SAPI_PORT                      = "80"
        _ACCOUNT_SQL_SAPI_CONNECTION_TIMEOUT        = "60000"
        _ACCOUNT_SQL_SAPI_RESPONSE_TIMEOUT          = "45000"
        _ACCOUNT_SF_SAPI_HOST                       = "cat-s-stg-account-sf-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SF_SAPI_PORT                       = "80"
        _ACCOUNT_SF_SAPI_CONNECTION_TIMEOUT         = "60000"
        _ACCOUNT_SF_SAPI_RESPONSE_TIMEOUT           = "45000"
        _ACCOUNT_GCP_SAPI_HOST                      = "cat-s-stg-account-gcp-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_GCP_SAPI_PORT                      = "80"
        _ACCOUNT_GCP_SAPI_CONNECTION_TIMEOUT        = "60000"
        _ACCOUNT_GCP_SAPI_RESPONSE_TIMEOUT          = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES               = "1"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD           = "1000"
      }
      excluded_files_filter = []
    },
    # account sf sapi triggers
    accountSfSapiStagingTrigger = {
      name                         = "acc-sf-sapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "account-sf-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                           = "********"
        _ANYPOINT_ENV                     = "Staging"
        _DEPLOYMENT_NAME                  = "cat-s-stg-account-sf-api"
        _MULE_API_LAYER                   = "system"
        _MULE_APP_TAGS                    = "account"
        _MULE_ENV                         = "stg"
        _MULE_NUM_WORKERS                 = 1
        _MULE_WORKER_TYPE                 = "MICRO"
        _SALESFORCE_AUTHORISATION_URL     = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _ID_SERVER_ENDPOINT_TOKEN         = "https://preview-idsvr.checkatrade.com/connect/token"
        _ID_SERVER_ENDPOINT_UPDATE_EMAIL  = "https://preview-idsvr.checkatrade.com/account/UpdateUserEmail"
        _ID_SERVER_ENDPOINT_LOCKOUT_USER  = "https://preview-idsvr.checkatrade.com/account/LockoutUsers"
        _ID_SERVER_CONNECTION_TIMEOUT     = "60000"
        _ID_SERVER_RESPONSE_TIMEOUT       = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES     = "2"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD = "1000"
      }
      excluded_files_filter = []
    },
    accountSfSapiProdTrigger = {
      name                         = "account-sf-sapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "account-sf-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                           = "********"
        _ANYPOINT_ENV                     = "Staging"
        _DEPLOYMENT_NAME                  = "cat-s-stg-account-sf-api"
        _MULE_API_LAYER                   = "system"
        _MULE_APP_TAGS                    = "account"
        _MULE_ENV                         = "stg"
        _MULE_NUM_WORKERS                 = 1
        _MULE_WORKER_TYPE                 = "MICRO"
        _SALESFORCE_AUTHORISATION_URL     = "https://checkatrade--uatpartial.my.salesforce.com/services/Soap/u/51"
        _ID_SERVER_ENDPOINT_TOKEN         = "https://preview-idsvr.checkatrade.com/connect/token"
        _ID_SERVER_ENDPOINT_UPDATE_EMAIL  = "https://preview-idsvr.checkatrade.com/account/UpdateUserEmail"
        _ID_SERVER_ENDPOINT_LOCKOUT_USER  = "https://preview-idsvr.checkatrade.com/account/LockoutUsers"
        _ID_SERVER_CONNECTION_TIMEOUT     = "60000"
        _ID_SERVER_RESPONSE_TIMEOUT       = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES     = "2"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD = "1000"
      }
      excluded_files_filter = []
    },
    # contact papi triggers
    contactPapiStagingTrigger = {
      name                         = "contact-papi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "contact-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                            = "********"
        _ANYPOINT_ENV                                      = "Staging"
        _DEPLOYMENT_NAME                                   = "cat-p-stg-contact-api"
        _MULE_API_LAYER                                    = "process"
        _MULE_APP_TAGS                                     = "contact"
        _MULE_ENV                                          = "stg"
        _MULE_NUM_WORKERS                                  = 1
        _MULE_WORKER_TYPE                                  = "MICRO"
        _MQ_ENDPOINT                                       = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_QUEUE_SUBSCRIBER_CONTACT_NAME                  = "q-cat-p-contact-api-stg"
        _MQ_QUEUE_SUBSCRIBER_CONTACT_ENABLED               = "true"
        _MQ_QUEUE_SUBSCRIBER_MARKETING_PREFERENCES_NAME    = "q-cat-p-contact-api-marketing-preferences-stg"
        _MQ_QUEUE_SUBSCRIBER_MARKETING_PREFERENCES_ENABLED = "true"
        _SUBSCRIPTION_SQL_SAPI_HOST                        = "cat-s-stg-subscription-sql-api.mulesoft-intl.dev.checkatrade.net"
        _SUBSCRIPTION_SQL_SAPI_PORT                        = "80"
        _SUBSCRIPTION_SQL_SAPI_CONNECTION_TIMEOUT          = "60000"
        _SUBSCRIPTION_SQL_SAPI_RESPONSE_TIMEOUT            = "45000"
        _ACCOUNT_SF_SAPI_HOST                              = "cat-s-stg-account-sf-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SF_SAPI_PORT                              = "80"
        _ACCOUNT_SF_SAPI_CONNECTION_TIMEOUT                = "60000"
        _ACCOUNT_SF_SAPI_RESPONSE_TIMEOUT                  = "45000"
        _ACCOUNT_GCP_SAPI_HOST                             = "cat-s-stg-account-gcp-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_GCP_SAPI_PORT                             = "80"
        _ACCOUNT_GCP_SAPI_CONNECTION_TIMEOUT               = "60000"
        _ACCOUNT_GCP_SAPI_RESPONSE_TIMEOUT                 = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES                      = "1"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD                  = "1000"
      }
      excluded_files_filter = []
    },
    contactPapiProdTrigger = {
      name                         = "contact-papi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "contact-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                            = "********"
        _ANYPOINT_ENV                                      = "Staging"
        _DEPLOYMENT_NAME                                   = "cat-p-stg-contact-api"
        _MULE_API_LAYER                                    = "process"
        _MULE_APP_TAGS                                     = "contact"
        _MULE_ENV                                          = "stg"
        _MULE_NUM_WORKERS                                  = 1
        _MULE_WORKER_TYPE                                  = "MICRO"
        _MQ_ENDPOINT                                       = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_QUEUE_SUBSCRIBER_CONTACT_NAME                  = "q-cat-p-contact-api-stg"
        _MQ_QUEUE_SUBSCRIBER_CONTACT_ENABLED               = "true"
        _MQ_QUEUE_SUBSCRIBER_MARKETING_PREFERENCES_NAME    = "q-cat-p-contact-api-marketing-preferences-stg"
        _MQ_QUEUE_SUBSCRIBER_MARKETING_PREFERENCES_ENABLED = "true"
        _SUBSCRIPTION_SQL_SAPI_HOST                        = "cat-s-stg-subscription-sql-api.mulesoft-intl.dev.checkatrade.net"
        _SUBSCRIPTION_SQL_SAPI_PORT                        = "80"
        _SUBSCRIPTION_SQL_SAPI_CONNECTION_TIMEOUT          = "60000"
        _SUBSCRIPTION_SQL_SAPI_RESPONSE_TIMEOUT            = "45000"
        _ACCOUNT_SF_SAPI_HOST                              = "cat-s-stg-account-sf-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_SF_SAPI_PORT                              = "80"
        _ACCOUNT_SF_SAPI_CONNECTION_TIMEOUT                = "60000"
        _ACCOUNT_SF_SAPI_RESPONSE_TIMEOUT                  = "45000"
        _ACCOUNT_GCP_SAPI_HOST                             = "cat-s-stg-account-gcp-api.mulesoft-intl.dev.checkatrade.net"
        _ACCOUNT_GCP_SAPI_PORT                             = "80"
        _ACCOUNT_GCP_SAPI_CONNECTION_TIMEOUT               = "60000"
        _ACCOUNT_GCP_SAPI_RESPONSE_TIMEOUT                 = "45000"
        _UNTIL_SUCCESSFUL_MAX_RETRIES                      = "1"
        _UNTIL_SUCCESSFUL_INTERVAL_PERIOD                  = "1000"
      }
      excluded_files_filter = []
    },
    # account gcp sapi triggers
    accountGcpSapiStagingTrigger = {
      name                         = "account-sapi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "account-gcp-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                     = "********"
        _ANYPOINT_ENV                               = "Staging"
        _DEPLOYMENT_NAME                            = "cat-s-stg-account-gcp-api"
        _MULE_API_LAYER                             = "system"
        _MULE_APP_TAGS                              = "account"
        _MULE_ENV                                   = "stg"
        _MULE_NUM_WORKERS                           = 1
        _MULE_WORKER_TYPE                           = "MICRO"
        _GCP_TOPIC_AVAILABILITY_NAME                = "trade-profile-res-mulesoft"
        _GCP_TOPIC_AVAILABILITY_PROJECT_ID          = "cat-trades-preview"
        _GCP_TOPIC_AVAILABILITY_CLIENT_EMAIL        = "<EMAIL>"
        _GCP_TOPIC_CONTACT_PREFERENCES_NAME         = "contact-preferences-res-mulesoft"
        _GCP_TOPIC_CONTACT_PREFERENCES_PROJECT_ID   = "cat-trades-preview"
        _GCP_TOPIC_CONTACT_PREFERENCES_CLIENT_EMAIL = "<EMAIL>"
        _GCP_TOPIC_SECURE_CONTACT_PROJECT_ID        = "secure-contacts-staging-47704"
        _GCP_TOPIC_SECURE_CONTACT_CLIENT_EMAIL      = "<EMAIL>"
        _GCP_TOPIC_SECURE_CONTACT_NAME_ACCOUNT      = "sc-account-event"
        _GCP_TOPIC_SECURE_CONTACT_NAME_SUBSCRIPTION = "sc-subscription-event"
        _GCP_TOPIC_SECURE_CONTACT_NAME_CONTACT      = "sc-contact-event"
        _GCP_TOPIC_SECURE_CONTACT_NAME_ASSIGNMENT   = "sc-assignment-event"
        _GCP_TOPIC_TRADE_CATEGORIES_NAME            = "salesforce-subscription-write"
        _GCP_TOPIC_CATEGORIES_LIBRARY_NAME          = "salesforce-account-write"
      }
      excluded_files_filter = []
    },
    accountGcpSapiProdTrigger = {
      name                         = "account-gcp-sapi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "account-gcp-sapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                                     = "********"
        _ANYPOINT_ENV                               = "Staging"
        _DEPLOYMENT_NAME                            = "cat-s-stg-account-gcp-api"
        _MULE_API_LAYER                             = "system"
        _MULE_APP_TAGS                              = "account"
        _MULE_ENV                                   = "stg"
        _MULE_NUM_WORKERS                           = 1
        _MULE_WORKER_TYPE                           = "MICRO"
        _GCP_TOPIC_AVAILABILITY_NAME                = "trade-profile-res-mulesoft"
        _GCP_TOPIC_AVAILABILITY_PROJECT_ID          = "cat-trades-preview"
        _GCP_TOPIC_AVAILABILITY_CLIENT_EMAIL        = "<EMAIL>"
        _GCP_TOPIC_CONTACT_PREFERENCES_NAME         = "contact-preferences-res-mulesoft"
        _GCP_TOPIC_CONTACT_PREFERENCES_PROJECT_ID   = "cat-trades-preview"
        _GCP_TOPIC_CONTACT_PREFERENCES_CLIENT_EMAIL = "<EMAIL>"
        _GCP_TOPIC_SECURE_CONTACT_PROJECT_ID        = "secure-contacts-staging-47704"
        _GCP_TOPIC_SECURE_CONTACT_CLIENT_EMAIL      = "<EMAIL>"
        _GCP_TOPIC_SECURE_CONTACT_NAME_ACCOUNT      = "sc-account-event"
        _GCP_TOPIC_SECURE_CONTACT_NAME_SUBSCRIPTION = "sc-subscription-event"
        _GCP_TOPIC_SECURE_CONTACT_NAME_CONTACT      = "sc-contact-event"
        _GCP_TOPIC_SECURE_CONTACT_NAME_ASSIGNMENT   = "sc-assignment-event"
        _GCP_TOPIC_TRADE_CATEGORIES_NAME            = "salesforce-subscription-write"
        _GCP_TOPIC_CATEGORIES_LIBRARY_NAME          = "salesforce-account-write"
      }
      excluded_files_filter = []
    },
    # reprocess papi triggers
    reprocessPapiStagingTrigger = {
      name                         = "repro-papi-PR-to-main"
      description                  = "Builds and releases to staging on any PR to main."
      disabled                     = true
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "checkatrade-mule"
      repo_name                    = "reprocess-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                              = "********"
        _ANYPOINT_ENV                        = "Staging"
        _DEPLOYMENT_NAME                     = "cat-p-stg-reprocess-api"
        _MULE_API_LAYER                      = "process"
        _MULE_APP_TAGS                       = "reprocess"
        _MULE_ENV                            = "stg"
        _MULE_NUM_WORKERS                    = 1
        _MULE_WORKER_TYPE                    = "MICRO"
        _MQ_ENDPOINT                         = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_SUBSCRIPTION_ACCOUNT_NAME        = "q-cat-p-account-api-dlq-stg"
        _MQ_SUBSCRIPTION_PLI_NAME            = "q-cat-p-account-api-pli-dlq-stg"
        _MQ_SUBSCRIPTION_CONTACT_NAME        = "q-cat-p-contact-api-dlq-stg"
        _MQ_SUBSCRIPTION_ACCOUNT_DLQ_ENABLED = "true"
        _MQ_SUBSCRIPTION_PLI_DLQ_ENABLED     = "true"
        _MQ_SUBSCRIPTION_CONTACT_DLQ_ENABLED = "true"
        _MQ_DESTINATION_ACCOUNT              = "x-cat-e-account-api-stg"
        _MQ_DESTINATION_PLI                  = "x-cat-e-account-api-pli-stg"
        _MQ_DESTINATION_CONTACT              = "x-cat-e-contact-api-stg"
        _MQ_DESTINATION_ERROR                = "x-cat-e-account-api-error-stg"
      }
      excluded_files_filter = []
    },
    reprocessPapiProdTrigger = {
      name                         = "reprocess-papi-main"
      description                  = "Builds and releases to staging on any commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "checkatrade-mule"
      repo_name                    = "reprocess-papi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _API_ID                              = "********"
        _ANYPOINT_ENV                        = "Staging"
        _DEPLOYMENT_NAME                     = "cat-p-stg-reprocess-api"
        _MULE_API_LAYER                      = "process"
        _MULE_APP_TAGS                       = "reprocess"
        _MULE_ENV                            = "stg"
        _MULE_NUM_WORKERS                    = 1
        _MULE_WORKER_TYPE                    = "MICRO"
        _MQ_ENDPOINT                         = "https://mq-eu-west-2.anypoint.mulesoft.com/api/v1"
        _MQ_SUBSCRIPTION_ACCOUNT_NAME        = "q-cat-p-account-api-dlq-stg"
        _MQ_SUBSCRIPTION_PLI_NAME            = "q-cat-p-account-api-pli-dlq-stg"
        _MQ_SUBSCRIPTION_CONTACT_NAME        = "q-cat-p-contact-api-dlq-stg"
        _MQ_SUBSCRIPTION_ACCOUNT_DLQ_ENABLED = "true"
        _MQ_SUBSCRIPTION_PLI_DLQ_ENABLED     = "true"
        _MQ_SUBSCRIPTION_CONTACT_DLQ_ENABLED = "true"
        _MQ_DESTINATION_ACCOUNT              = "x-cat-e-account-api-stg"
        _MQ_DESTINATION_PLI                  = "x-cat-e-account-api-pli-stg"
        _MQ_DESTINATION_CONTACT              = "x-cat-e-contact-api-stg"
        _MQ_DESTINATION_ERROR                = "x-cat-e-account-api-error-stg"
      }
      excluded_files_filter = []
    },
  }



  # Permissions for humans only
  # project_team       = local.project_team
  # project_team_admin = local.project_team_admin
  # viewers            = local.viewers


  project_static_permissions = {
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin,
    )
  }

  app_secrets = [
    "svat-mq-client-secret",
    "svat-app-client-secret",
    "catdb-host",
    "catdb-username",
    "catdb-user-password",
    "dasdb-host",
    "dasdb-username",
    "dasdb-user-password",
    "salesforce-user",
    "salesforce-password",
    "salesforce-security-token",
    "subscription-mq-client-id",
    "subscription-mq-client-secret",
    "subscription-services-app-client-id",
    "subscription-services-app-client-secret",
    "contact-mq-client-id",
    "contact-mq-client-secret",
    "contact-services-app-client-id",
    "contact-services-app-client-secret",
    "account-mq-client-id",
    "account-mq-client-secret",
    "account-services-app-client-id",
    "account-services-app-client-secret",
    "contact-services-app-client-id",
    "contact-services-app-client-secret",
    "gcp-topic-availability-client-id",
    "gcp-topic-availability-private-key-id",
    "gcp-topic-availability-private-key",
    "gcp-topic-subscriber-availability-client-id",
    "gcp-topic-subscriber-availability-private-key-id",
    "gcp-topic-subscriber-availability-private-key",
    "gcp-topic-subscriber-document-store-client-id",
    "gcp-topic-subscriber-document-store-private-key-id",
    "gcp-topic-subscriber-document-store-private-key",
    "gcp-topic-subscriber-contact-client-id",
    "gcp-topic-subscriber-contact-private-key-id",
    "gcp-topic-subscriber-contact-private-key",
    "gcp-topic-contact-client-id",
    "gcp-topic-contact-private-key-id",
    "gcp-topic-contact-private-key",
    "gcp-topic-secure-contact-client-id",
    "gcp-topic-secure-contact-private-key-id",
    "gcp-topic-secure-contact-private-key",
    "id-server-client-id",
    "id-server-client-secret",
  ]
}
