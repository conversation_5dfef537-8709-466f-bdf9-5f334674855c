terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_env_vars = {
    _ENABLE_TRAFFIC = "true",
    _GCR_HOSTNAME   = "eu.gcr.io",
    _PLATFORM       = "managed",
    _PROJECT_ENV    = local.environment,
    _SERVICE_NAME   = "trade-exp-admin-ui",
    _VERSION        = local.env,
    _GCP_REGION     = "europe-west2"
  }

  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be decalred in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name   = local.project_name
  environment    = local.environment
  project_folder = local.environment

  trade_experience_project_id = "cat-trades-preview"

  # ---- Admin Auth ----
  salesforce_fqdn        = "checkatrade--uatpartial.sandbox.my.salesforce.com"
  firebase_app_id        = "1:895661281679:web:9f9b0179f43f39f5f0fbfa"
  firebase_auth_domain   = "trade-exp-admin-ui-stg-22772.firebaseapp.com"
  client_id              = "3MVG9xqN3LZmHU7nO2Q47RbMFtZPnIc0Q7tkNIILjq3nBA.fiDaRCd4.Gx5EdT1kT8CKccmRhRIFUCeuzCK18"
  allowed_frame_ancestor = "https://checkatrade--uatpartial.sandbox.lightning.force.com"
  external_url           = "https://trade-experience-admin-stg.checkatrade.com"
  admin_app_service_name = "trade-exp-admin-ui" # Backend service for Salesforce Admin embedded app
  short_api_id           = "trade-exp-admin-ui" # _API_ID that comes from the module includes the full path

  # ---- IAM config ----

  # Permissions for humans only

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/firebase.viewer" : concat(
      local.everyone,
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    pushEmulatorTrigger = {
      name                         = "emulator-push"
      description                  = "Builds and pushes the emulator image on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-trade-experience-admin-ui"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = {}
      included_files_filter        = ["emulator/**"]
    }
    stagingTrigger = {
      name                         = "PR-to-Main-for-TE-Admin"
      description                  = "Builds and releases to staging on an opened PR."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-trade-experience-admin-ui"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
      excluded_files_filter        = ["emulator/**"]
    }
    productionTrigger = {
      name                         = "Main-for-TE-Admin-UI"
      description                  = "Builds and releases to staging on a commit to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-trade-experience-admin-ui"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
      excluded_files_filter        = ["emulator/**"]
    }
  }


  cloud_run_env_variables = {
    trade-exp-admin-ui = [
      {
        name  = "DOCUMENT_STORE"
        value = "document-store-92579811864"
      },
      {
        name  = "FIREBASE_DEFAULT_BUCKET"
        value = "cat-trades-preview.appspot.com"
      },
      {
        name  = "REVIEWS_BACKEND_API"
        value = "https://reviews-backend-gpkkhm4nsa-nw.a.run.app"
      },
      {
        name  = "CORE_TRADE_DATA_API"
        value = "https://api.staging.checkatrade.com"
      }
    ]
  }

  cloud_run_parameters = {
    trade-exp-admin-ui = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
    }
  }

  firebase_notification_targets = "@trade-exp-admin-ui-alerts-dev"

  cloud_build_triggers_for_admin_auth = {
    staging_trigger = {
      name                         = "PR-to-Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR, for ${basename(dirname(get_terragrunt_dir()))} to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }
}
