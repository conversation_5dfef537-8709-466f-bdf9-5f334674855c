terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  builders_artifact_repo_name = "platform-builders"

  builder_triggers_included_files = [
    "${local.builders_artifact_repo_name}/**/Dockerfile",
    "${local.builders_artifact_repo_name}/**/.dockerignore",
  ]
}

inputs = {
  project_id     = read_terragrunt_config(find_in_parent_folders()).inputs.operations_project_id
  project_name   = local.project_name
  environment    = local.environment
  project_folder = local.environment


  # Cloud Build Triggers

  cloudbuild_triggers = {
    goWithDockerTrigger = {
      name                         = "Go-With-Docker-Main"
      description                  = "Builds Go-With-Docker image."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "cat-central-docker-base-images"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "builders/go-with-docker/cloudbuild.yaml"
      env_variables = {
        _REGION       = "europe-west2"
        _TRIGGER_NAME = "main"
        _DOCKER_PATH  = "builders"
        _DOCKER_IMAGE = "goWithDocker"
        _GCP_REGION   = "europe-west2"
        _TRIGGER_NAME = "main"
      }
      included_files_filter = ["builders/go-with-docker/Dockerfile.*"]
    }

    taskTrigger = {
      name                         = "Task-Main"
      description                  = "Builds Task image."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "cat-central-docker-base-images"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "builders/task/cloudbuild.yaml"
      env_variables = {
        _REGION       = "europe-west2"
        _TRIGGER_NAME = "main"
        _DOCKER_PATH  = "builders"
        _DOCKER_IMAGE = "task"
        _GCP_REGION   = "europe-west2"
        _TRIGGER_NAME = "main"
      }
      included_files_filter = ["builders/task/Dockerfile.*"]
    },
    gcpScriptsTrigger = {
      name                         = "Gcp-Scripts-Main"
      description                  = "Builds Gcp Scripts image."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "cat-central-docker-base-images"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "gcp-scripts/cloudbuild.yaml"
      env_variables = {
        _REGION       = "europe-west2"
        _TRIGGER_NAME = "main"
        _DOCKER_PATH  = "gcp-scripts"
        _DOCKER_IMAGE = "gcp-scripts"
        _GCP_REGION   = "europe-west2"
        _TRIGGER_NAME = "main"
      }
      included_files_filter = ["gcp-scripts/Dockerfile.*"]
    },

  }

  builders_artifact_repo_name = local.builders_artifact_repo_name

  builders_triggers = {
    # TODO: add those 2 triggers below to the future operations-dev project
    # dev_trigger = {
    #   name                         = "PR-to-Main-for-Builders"
    #   description                  = "Builds and push to ${basename(dirname(get_terragrunt_dir()))} on an opened PR."
    #   disabled                     = false
    #   push_trigger_enabled         = false
    #   pull_request_trigger_enabled = true
    #   branch_regex                 = "^main$"
    #   invert_regex                 = false
    #   included_files               = local.builder_triggers_included_files
    # }

    # core_trigger = {
    #   name                         = "Main-for-Builders"
    #   description                  = "Builds and push to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with core."
    #   disabled                     = false
    #   push_trigger_enabled         = true
    #   pull_request_trigger_enabled = false
    #   branch_regex                 = "^main$"
    #   invert_regex                 = false
    #   included_files               = local.builder_triggers_included_files
    # }

    core_trigger = {
      name                         = "Main-for-Builders"
      description                  = "Builds and push to ${basename(dirname(get_terragrunt_dir()))} on a commit to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      included_files               = local.builder_triggers_included_files
    }
  }

  cloudflare_triggers = {
    mainTrigger = {
      name                         = "Main-for-Cloudflare"
      description                  = "Builds Cloudflare Go app image"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "cloudflare"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION = "europe-west2"
      }
      included_files_filter = []
      excluded_files_filter = []
    },
  }
}
