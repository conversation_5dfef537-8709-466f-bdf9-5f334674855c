terraform {
  source = "./../../..//projects/data-services"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]
}

inputs = {
  # Default inputs
  project_folder = "core"
  environment    = "core"

  sink_destination_bucket = "sre-logs-prod"

  # Accounts for team defined IAM permissions
  viewer_access = [
    "group:<EMAIL>"
  ]


  project_static_permissions = {
    "roles/cloudsql.viewer" : concat(
      ["group:<EMAIL>"],
    ),
    "roles/logging.viewer" : concat(
      ["group:<EMAIL>"],
    ),
  }
}
