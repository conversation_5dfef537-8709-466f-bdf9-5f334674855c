terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))

  # ---- only if env needed in this local block ----
  env = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_env_vars = {
    _GCR_HOSTNAME = "eu.gcr.io",
    _PLATFORM     = "managed",
    _PROJECT_ENV  = local.environment,
    _VERSION      = local.env,
    _TRIGGER_NAME = "${local.project_name}-${local.env}"
  }

  cloud_build_env_vars_main = merge(
    local.cloud_build_env_vars, {
      _TRIGGER_NAME = "Main"
    }
  )

  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  dni_team_admin     = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be decalred in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
      [ # Had to hard code these due to the cloud build module creating a custom service account and we needed different permissions assigning to across the envs (not all envs have same triggers)
        "serviceAccount:<EMAIL>",
      ]
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
      local.dni_team_admin,
      [ # Had to hard code these due to the cloud build module creating a custom service account and we needed different permissions assigning to across the envs (not all envs have same triggers)
        "serviceAccount:<EMAIL>",
      ]
    )
    "roles/iam.serviceAccountKeyAdmin" : concat(
      local.project_team_admin,
      local.dni_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/secretmanager.secretVersionManager" : concat(
      local.project_team_admin
    )
    "roles/secretmanager.secretAccessor" : [
      "serviceAccount:<EMAIL>"
    ]
    "roles/cloudfunctions.developer" : [
      "serviceAccount:<EMAIL>"
    ]
  }


  # ---- Cloudbuild triggers config ----
  # Please make sure to add any deploy triggers to IAM in this format
  #
  # "serviceAccount:build-main@${module.project.id}.iam.gserviceaccount.com",       # Cloud build Main
  # "serviceAccount:build-pr-to-main@${module.project.id}.iam.gserviceaccount.com", # Cloud build PR
  # "serviceAccount:build-feature@${module.project.id}.iam.gserviceaccount.com",    # Cloud build feature
  #

  cloud_build_triggers = {
    platformStatsMain = {
      name                         = "main"
      description                  = "Builds and releases on commit to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "streamlit-apps"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars_main
      excluded_files_filter        = ["README.md"] # Can be commented out
    }

  }
}
