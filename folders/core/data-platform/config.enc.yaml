Jira: ENC[AES256_GCM,data:UnuBfkMax4FV/W3brxYlTU8GQBtqdI0AWItcjoOd+JLJeOhq4YxQ47H9G/1b+me9JzXVI4lKGHAYmhtWvPOVt+AMnp7Y8gI22K87Tr1LKgDmfgl9Y6EeCGy8Rdrq5ocQCDtfBY2du31GCc/AjRioQDWXvpNa+C6DN7YaSiyIdX2pRSbPUja9OBDzQlD1jcC8TBGjbOBpTjl2dukMstbuOZvAgGWOuHL9jsh7yUfHUncb3f34EL2hQDSlZbrj/nmK,iv:NwMnvfLqI5Bnr+pjbDfUJMEVGpwarvIcB8pTkg8ahp8=,tag:xm7Nidxm52prVr8q7RwuFQ==,type:str]
dali-designer-slack-bot-token: ENC[AES256_GCM,data:AmioSHh51CWkhLXSAS41yn7wj6FFcOHt/HwUl387wwKo/RHYmBY1Hj2CeQmnscWPZHyk0B1EQT0=,iv:KBfmEMNznyZc9XIliHe8uctVZsFp0QRWZ9qfglwAUGM=,tag:I/SeG7BRso6vHRQ6jOjngw==,type:str]
dali-designer-slack-signing-secret: ENC[AES256_GCM,data:QNarHasyLqYRG35e5aA602vmNPlLPvuNniTltY95ObQ=,iv:1G2ZMX6HpEamU56MNoe2WEdBPBCpkpBLRb8VQZk8a0U=,tag:J+b9xsQzyCD4/JpYYhg4jw==,type:str]
dali-designer-stable-diffusion-key: ENC[AES256_GCM,data:68h43ejfw3RHIwv9o9uw3sleuhUnp1rlmk414p8iBpQQ56Yij9maznelSyjkiWtuM6Hoa9rhijVObrZm,iv:5cM+0oNVyNF698YQQIiOyCrB4hGsfAq9YP3XIemQqSA=,tag:EXwndU49Jba/jc98dP9JaA==,type:str]
freshdesk-api-key: ENC[AES256_GCM,data:ok0jP6WKZb+dBOJgTKPlPOd7Eqk=,iv:87d6v/cLos7XBDVTEOSEZWRH5TNaiDQc2aNe79eT8NE=,tag:33y6D0bXpR0W4aqXecQgeQ==,type:str]
freshservice-token: ENC[AES256_GCM,data:Osvov8SkFYMw/V0AJhLsxP3Mre8=,iv:G/JmvA0KfjYvuQa4LA43NKKZEkwZzpaPym6I/LSRY+g=,tag:odB0GpSoDFGS0wWb6QkrTA==,type:str]
github-token-engineering-data: ENC[AES256_GCM,data:GAT3Yr9iH6kTNh5O3vs2Jk1CL3MZt+M2haj2YanqLLJcrSN0QMewvg==,iv:1h6xmspu/S8utpK7le2ppuBuHEHuBS75Z5Dffr5efTY=,tag:qqk8Pogx06CNbXt9/fv9NQ==,type:str]
incident-io-token: ENC[AES256_GCM,data:/fb2qe6ZMGWl/w1nsWeKbCiYaGf6QuoMMtaLn5ixux+PnnjA/k5uVkEdqEe4zrX5U/vRE+lBDCNqNipwvb3QpUUXnzE=,iv:e8zSsG1lTdbhaZrk4J3U9BeM3HJ6u3VxxL8gsIVi2k0=,tag:qWLun6iNu2HsPTpkN4sXsw==,type:str]
jeeves-slack-bot-token: ENC[AES256_GCM,data:E4cbv4jraBNYLYdF7WapnvjBEzaNsy9HJYGphZTzkhB+OjVyHjYG0SIfYmvNd6oWZqe/nvLnhdY=,iv:Smb7kfg7O7IMGSYIJ5JV9rOPaUEwWUsZe0y1InePPzk=,tag:Dtm4OdKYrf3SUQSSVoI1qg==,type:str]
jira-api-key: ENC[AES256_GCM,data:e5lGY/WVcYTrbyQg0v9vlR2D+V3dKR4P,iv:rcwhzAt9CRpClZ+/rS6svch3sWL3U8kGalxdyvF04qI=,tag:TxodfeJgYh4zEXCkqhUuOg==,type:str]
open-ai-api-key: ENC[AES256_GCM,data:a3rgwNdS2BJkRGbkM1CdhO1wtL6ODixXRt23DueNeV3Up8jlRy5qTvHMlqEtzKwnx6EC,iv:zGY16aePjp5/700jES3B2FXj74ColrHW2qXXtPunXGo=,tag:aAjPKIK087PnrbI1LgT0Gg==,type:str]
sops:
    kms: []
    gcp_kms:
        - resource_id: projects/operations-14020/locations/europe-west2/keyRings/secret-provisioner-33274/cryptoKeys/core-sops
          created_at: "2024-01-15T10:59:33Z"
          enc: CiYAfaBUiwLRwGfG8D8E1uBsv+6gQbZ+CtsAYDp51bHVc+Ne6dOv7hJJAEoglxXR26IHoYJlk9qOOyra/WBnSrNS+hA91zBE/t0JbI9NQDNi2JpORTXkhog6GECB7rXkGIiyjsdYRJeC/jag1sk7whrwYg==
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2024-01-15T13:02:34Z"
    mac: ENC[AES256_GCM,data:j/W/pJdNLlcIaRaH14OfAzOmFBSHLfVJNE8fn5Edp6MWF93dsDRVj0ZA5TtAR0pj2JHOnUJ4MI7mC9ttfgwAHisQjm7eSfibCARkJzqv8PiW3tW9BgZmaQ84l9jhov/TA1U8aYF94XONzbV1dDhv88/sgCE8W1c8/+r0qOOJ5LM=,iv:DGDscjkHMpXuU9uTrjX7Jd8log4xSJhVbMF/i63M4e0=,tag:ZSlUoUQ/Vm+GURdb2ylN1w==,type:str]
    pgp: []
    unencrypted_suffix: _unencrypted
    version: 3.7.3
