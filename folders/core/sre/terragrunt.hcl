terraform {
  source = "./../../..//projects/sre"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]
}

inputs = {
  project_name   = basename(get_terragrunt_dir())
  environment    = local.environment
  project_folder = local.environment

  # Pubsub Alert variables
  alert_duration  = "60s"
  threshold_value = "10"
  trigger_count   = "1"

  # Datadog monitor inputs
  notifications          = "@opsgenie-Data-Services"
  heritage_notifications = "@opsgenie-Data-Services @slack-platform-alerts @<EMAIL>"

}
