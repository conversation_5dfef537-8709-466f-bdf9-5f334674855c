terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to the Terraform templates
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terragunt.hcl' file
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]
}

inputs = {
  project_name = local.project_name
  environment  = local.environment

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {}
}
