# Atlantis set a static version of Terragrunt which is downloaded at build time.
terragrunt_version_constraint = ">= 0.38.12"


remote_state {
  backend = "gcs"
  config = {
    bucket                      = "cathex-tf-states"
    prefix                      = path_relative_to_include()
    project                     = local.operations_project_id
    location                    = "europe-west2"
    impersonate_service_account = local.operations_super_sa_email
  }
}


locals {
  operations_project_id     = "operations-14020"
  operations_super_sa_email = "atlantis@${local.operations_project_id}.iam.gserviceaccount.com"
  project_raw_name          = basename(get_original_terragrunt_dir())
  environment               = basename(dirname(get_original_terragrunt_dir()))
  project_path              = "${get_original_terragrunt_dir()}/../../../projects/${local.project_raw_name}"
  main_tf                   = file("${local.project_path}/main.tf")

  # 'All or nothing':
  # We try to guess if some impersonation has been specificly defined on any google provider within the
  # project the Terraform command is run from. If there is a single impersonate_service_account on any of
  # them, then the developer must define impersonate_service_account on all google providers of main.tf
  contains_impersonate_service_account = length(regexall("(?m)^provider (\"google\"|\"google-beta\")[\\s]*{[\\n\\r\\s]*impersonate_service_account =.*[\\n\\r\\s]*}", local.main_tf)) > 0 ? true : false

  # <<-------------   Environment configuration   --------------
  contains_impersonate_service_account_to_impersonation_env_var_map = tomap({
    false = {
      GOOGLE_IMPERSONATE_SERVICE_ACCOUNT = local.operations_super_sa_email
    }
    true = {}
  })

  impersonation_env_var = local.contains_impersonate_service_account_to_impersonation_env_var_map[local.contains_impersonate_service_account]
  # -------------   Environment configuration   -------------->>
}


inputs = merge(
  {
    # Passing these to operations-core to have this bootstrap project id
    # hardcoded in a single location here in locals block
    operations_project_id     = local.operations_project_id
    operations_super_sa_email = local.operations_super_sa_email
  },
  # Loading the Salesforce dedicated app inputs.
  # They're isolated to identify clearly all templates related to the Salesforce integration.
  # It looks for an admin_auth_terragrunt file and uses its inputs. If the file doesn't exist, it sets the inputs attribute to an empty map.
  read_terragrunt_config(find_in_parent_folders("admin_auth_terragrunt.hcl", "admin_auth_terragrunt.hcl"), { inputs = {} }).inputs,
)


generate "providers" {
  path      = "providers.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<-EOF
    terraform {
      required_providers {
        datadog = {
          source  = "DataDog/datadog"
          version = ">= 3.10.0"
        }
        lacework = {
          source  = "lacework/lacework"
          version = "~> 1.4.0"
        }
        slack = {
          source  = "pablovarela/slack"
          version = "~> 1.0"
        }
        external = {
          source = "hashicorp/external"
          version = ">= 2.1.0"
        }
        google = {
          source = "hashicorp/google"
          version = "4.84.0"
        }
        google-beta = {
          source = "hashicorp/google-beta"
          version = "4.84.0"
        }
        opsgenie = {
          source  = "opsgenie/opsgenie"
          version = ">= 0.6.20"
        }
        cloudflare = {
          source  = "cloudflare/cloudflare"
          version = "4.8.0"
        }
        ec = {
          source  = "elastic/ec"
          version = "0.5.1"
      }
        elasticstack = {
          source  = "elastic/elasticstack"
          version = "0.5.0"
        }
        googleworkspace = {
          source = "hashicorp/googleworkspace"
          version = "0.7.0"
        }
      }
    }

    provider "googleworkspace" {
      customer_id = "C03lzib5x"
      access_token = data.google_service_account_access_token.default.access_token
      oauth_scopes = [
      "https://www.googleapis.com/auth/admin.directory.group",
      "https://www.googleapis.com/auth/admin.directory.group.member",
      "https://www.googleapis.com/auth/apps.groups.settings",
      ]
    }

    provider "elasticstack" {
      elasticsearch {
        username  = data.google_secret_manager_secret_version.elasticstack_super_username.secret_data
        password  = data.google_secret_manager_secret_version.elasticstack_super_user_password.secret_data
        endpoints = [data.google_secret_manager_secret_version.elasticstack_endpoint.secret_data]

      }
    }

    provider "ec" {
      apikey = data.google_secret_manager_secret_version.elastic_search_api_key.secret_data
    }

    provider "cloudflare" {
      api_token = data.google_secret_manager_secret_version.cloudflare_token.secret_data
    }

    provider "slack" {
      token = data.google_secret_manager_secret_version.retrieve_slack_secret.secret_data
    }

    provider "datadog" {
      api_key  = data.google_secret_manager_secret_version.providers_datadog_api_key.secret_data
      app_key  = data.google_secret_manager_secret_version.providers_datadog_app_key.secret_data
      api_url  = "https://api.datadoghq.eu/"
      validate = false # Permit the destroy flow
    }

    provider "lacework" {
      account    = "checkatrade.fra.lacework.net"
      api_key    = data.google_secret_manager_secret_version.providers_lacework_api_key.secret_data
      api_secret = data.google_secret_manager_secret_version.providers_lacework_api_secret.secret_data
    }

    provider "opsgenie" {
      api_key = data.google_secret_manager_secret_version.providers_opsgenie_api_key.secret_data
      api_url = "api.eu.opsgenie.com"
    }

    locals {
      map_environment_to_operations_folder = tomap({
        poc         = "core"
        development = "core"
        dev         = "core"
        staging     = "core"
        stg         = "core"
        production  = "core"
        prod        = "core"
        core        = "core"
        integration = "dev"
        int         = "dev"
        playground  = "core" #"dev" temporary
        plg         = "core" #"dev" temporary
      })
      operations_project_id = data.google_projects.providers_operations_projects.projects[0].project_id
    }

    data "google_secret_manager_secret_version" "cloudflare_token" {
      project = local.operations_project_id
      secret  = "cloudflare-token"
    }

    data "google_active_folder" "providers_operations_folder" {
      display_name = try(local.map_environment_to_operations_folder[var.environment], "core")
      parent       = "organizations/535868630468"
    }

    data "google_projects" "providers_operations_projects" {
      filter = "parent.id:$${replace(data.google_active_folder.providers_operations_folder.id, "folders/", "")} name:operations*"
    }

    data "google_secret_manager_secret_version" "providers_datadog_api_key" {
      project = local.operations_project_id
      secret  = "datadog-api-key"
    }

    data "google_secret_manager_secret_version" "providers_datadog_app_key" {
      project = local.operations_project_id
      secret  = "datadog-app-key"
    }

    data "google_secret_manager_secret_version" "providers_lacework_api_key" {
      project = local.operations_project_id
      secret  = "lacework-api-key"
    }

    data "google_secret_manager_secret_version" "providers_lacework_api_secret" {
      project = local.operations_project_id
      secret  = "lacework-api-secret"
    }

    data "google_secret_manager_secret_version" "retrieve_slack_secret" {
      project = local.operations_project_id
      secret  = "slack-api-token"
    }

    data "google_secret_manager_secret_version" "providers_opsgenie_api_key" {
      project = local.operations_project_id
      secret  = "opsgenie-api-key"
    }
    data "google_secret_manager_secret_version" "elastic_search_api_key" {
      project = local.operations_project_id
      secret  = "elasticsearch-api-key"
    }

    data "google_active_folder" "providers_search_elasticstack_folder" {
      // display_name = var.environment
      // Hardcoding the environment to poc as elasticsearch project is in poc only for now
      display_name = "poc"
      parent       = "organizations/535868630468"
    }

    data "google_projects" "providers_search_elasticstack_projects" {
      filter = "parent.id:$${replace(data.google_active_folder.providers_search_elasticstack_folder.id, "folders/", "")} name:search-elasticsearch*"
    }

    data "google_secret_manager_secret_version" "elasticstack_super_username" {
      project = data.google_projects.providers_search_elasticstack_projects.projects[0].project_id
      secret  = "elasticstack-super-username"
    }

    data "google_secret_manager_secret_version" "elasticstack_super_user_password" {
      project = data.google_projects.providers_search_elasticstack_projects.projects[0].project_id
      secret  = "elasticstack-super-user-password"
    }

    data "google_secret_manager_secret_version" "elasticstack_endpoint" {
      project = data.google_projects.providers_search_elasticstack_projects.projects[0].project_id
      secret  = "elasticstack-endpoint"
    }

    data "google_service_account_access_token" "default" {
    provider               = google
    scopes = ["https://www.googleapis.com/auth/admin.directory.group",
      "https://www.googleapis.com/auth/admin.directory.group.member",
      "https://www.googleapis.com/auth/apps.groups.settings",]
    target_service_account = "atlantis@${local.operations_project_id}.iam.gserviceaccount.com"
  
    lifetime               = "300s"
}

  EOF
}


terraform {
  extra_arguments "impersonation" {
    commands = get_terraform_commands_that_need_locking()
    env_vars = local.impersonation_env_var
  }

  # The impersonation bit is made for gcloud to use the right service account when terraform uses null_resource provisioner
  # execs or external datasources with gcloud.
  before_hook "show_context" {
    commands = get_terraform_commands_that_need_locking()
    execute = ["/bin/bash", "-c", <<-EOF
        gcloud config set auth/impersonate_service_account "${local.operations_super_sa_email}"
        [[ -n "$GOOGLE_IMPERSONATE_SERVICE_ACCOUNT" ]] && echo "GOOGLE_IMPERSONATE_SERVICE_ACCOUNT: $(printenv GOOGLE_IMPERSONATE_SERVICE_ACCOUNT)"
      EOF
    ]
  }

  # This after hook comes as a way to tackle the env vars overrides that we get when a
  # cloud build trigger provided a configuration different from the terraform template ones.
  # It also help us in traffic switching with new Cloud Run services settings.
  after_hook "run_cloud_run_services_autodeploy" {
    commands = ["apply"]
    execute = ["/bin/bash", "-c", <<-EOF
        [[ -f deploy_cloud_run_services.sh ]] && /bin/bash deploy_cloud_run_services.sh
        rm -f deploy_cloud_run_services.sh &>/dev/null
      EOF
    ]
  }

  after_hook "run_project_controls" {
    commands = ["apply"]
    execute = ["/bin/bash", "-c", <<-EOF
        [[ -f deploy_project_controls.sh ]] && /bin/bash deploy_project_controls.sh
        rm -f deploy_project_controls.sh &>/dev/null
      EOF
    ]
  }

  after_hook "post_apply_cleanup" {
    commands = ["apply"]
    execute = ["/bin/bash", "-c", <<-EOF
        rm -f deploy_cloud_run_services.sh &>/dev/null
        rm -f deploy_project_controls.sh &>/dev/null
      EOF
    ]
    run_on_error = true
  }

  error_hook "lacework_iam_denied" {
    commands = ["plan"]
    execute = ["/bin/bash", "-c", <<-EOF
        if [[ "${local.project_raw_name}" == "lacework" ]]; then
          echo -e "\n\n---\nThe permisison denied issue you get might come from the need to extend 4 Organization IAM binding condition before running your plan.\nYou can do it via clickops if you're a security admin. For more detail, read this repo projects/lacework/README.md.\n---\n\n"
        fi
      EOF
    ]
    on_errors = [
      ".*IAM_PERMISSION_DENIED",
    ]
  }
}
