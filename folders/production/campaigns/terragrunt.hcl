terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name             = basename(get_terragrunt_dir())
  environment              = basename(dirname(get_terragrunt_dir()))
  env                      = local.map_environment_to_env[local.environment]
  consent_screen_app_title = local.project_name
  repo_name                = "campaigns"
  organisation             = "cat-home-experts"

  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    # "../../../projects/${local.project_name}/template/*.tpl*", # example of API gateway template trigger
  ]

  cloud_build_env_vars = {
    gateway = {
      _GCP_REGION                  = "europe-west2"
      _SERVICE_NAME                = "campaigns-api"
      _VERSION                     = local.environment
      _PLATFORM                    = "managed"
      _API_GATEWAY_NAME            = "campaigns-api-gw"
      _API_GATEWAY_API_NAME        = "campaigns-api"
      _TRADE_EXPERIENCE_PROJECT_ID = "cat-trades"
    }
    campaigns-api = {
      _GAR_HOSTNAME                = "europe-west2-docker.pkg.dev"
      _PLATFORM                    = "managed"
      _PROJECT_ENV                 = local.env
      _ENABLE_TRAFFIC              = true
      _SERVICE_NAME                = "campaigns-api"
      _CORS_OPTIONS_ALLOWED_ORIGIN = "https://membersapp.checkatrade.com, https://cat-trades.nw.r.appspot.com"
    }
    campaigns-core = {
      _PROJECT_ENV = local.environment
    }
  }

  functions_env_vars = {
    _PROJECT_ENV = local.env,
  }

  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  # Admin Auth Vars - client_id will be updated later
  salesforce_fqdn        = "checkatrade.my.salesforce.com"
  firebase_app_id        = "1:894554237221:web:a376f4bdded1e6e2e392cc"
  firebase_auth_domain   = "campaigns-prod-38742.firebaseapp.com"
  client_id              = "3MVG9tzQRhEbH_K2nO6Y9U.mkX.tU1YyPQVgvbXrAO1z1ZTYcRr_xgG2MW.e4dDhynw0uqmQJHNppMLijNhMy" # Salesforce ConsumerKey
  allowed_frame_ancestor = "https://checkatrade.lightning.force.com"
  external_url           = "https://campaigns-admin.checkatrade.com"
  admin_app_service_name = "campaigns-admin"    # Backend service for Salesforce Admin embedded app
  short_api_id           = "campaigns-admin-ui" # _API_ID that comes from the module includes the full path
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment

  # D+I's project only exists in dev and prod so we can't use wildcarding for staging
  # so instead we'll just hardcode
  data_stats_project_id = "data-stats-service-prod-42093"

  # renewal period is 35 days plus a few extra days to realise something went wrong...
  logging_retention_days = 45

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone


  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/run.invoker" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin
    ),
    "roles/errorreporting.user" : concat(
      local.project_team_admin
    ),
    "roles/pubsub.admin" : concat(
      local.project_team_admin
    )
    "roles/run.admin" : concat(
      local.project_team_admin,
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin
    )
    "roles/cloudscheduler.viewer" : concat(
      local.project_team,
    )
    "roles/cloudscheduler.admin" : concat(
      local.project_team_admin,
    )
    "roles/bigquery.dataOwner" : concat(
      local.project_team,
    )
    "roles/bigquery.admin" : concat(
      local.project_team_admin,
    )
    "roles/apigateway.admin" : concat(
      local.project_team_admin,
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    #####################################
    # API cloud build triggers
    #####################################
    apiProductionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases Campaigns Api to production on a commit to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "service/cloudbuild.campaigns.api.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-api
      included_files_filter        = ["service/**", "contracts/**", "common/**"]
    }
    #####################################
    # Core & Contracts cloud build triggers
    #####################################
    coreProductionTrigger = {
      name                         = "Core-Main"
      description                  = "Builds and releases Campaigns Core to production on a commit to main.."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "common/cloudbuild.campaigns.core.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-core
      included_files_filter        = ["common/**", "contracts/**"]
    }
    #####################################
    # Functions cloud build triggers
    #####################################
    functionsProductionTrigger = {
      name                         = "Main-Functions"
      description                  = "Builds and releases Functions to production on a commit to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = local.functions_env_vars
      filename                     = "functions/cloudbuild.functions.yaml"
      included_files_filter        = ["functions/**", "contracts/**", "common/**"]
    }
    #####################################
    # DataStrem cloud build triggers
    #####################################
    dataStreamProductionTrigger = {
      name                         = "DataStream-Main"
      description                  = "Builds and releases data-stream to production on a commit to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = {}
      filename                     = "data-stream/cloudbuild.data-stream.yaml"
      included_files_filter        = ["data-stream/**"]
    }
    #####################################
    # Emulator cloud build trigger
    #####################################
    emulatorTrigger = {
      name                         = "Emulator"
      description                  = "Builds and releases emulator so that other builds can work."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-api
      included_files_filter        = ["emulator/**"]
    }
    #####################################
    # Api Gateway cloud build trigger
    #####################################
    pushGatewayTriggerApi = {
      name                         = "Gateway-api-push"
      description                  = "Builds and releases API gateway to production on push to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.gateway
      included_files_filter        = ["api-gateway/**", "spec/**"]
    }
    #####################################
    # Firebase cloud build triggers
    #####################################
    firebaseProductionTrigger = {
      name                         = "Firebase-Main"
      description                  = "Tests and deploys firebase config to dev on any push to main, for dev to be on par with production."
      filename                     = "firebase/cloudbuild.yaml"
      included_files_filter        = ["firebase/**"]
      disabled                     = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _TRIGGER_NAME = "feature"
        _VERSION      = "dev"
      }
    }
  }

  cloud_run_parameters = {
    campaigns-api = {
      cpu                   = "1000m"
      memory                = "1Gi"
      max_scale             = 80
      min_scale             = 10
      initial_scale         = 1
      container_concurrency = 80
    },
    campaigns-admin = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 10
      min_scale             = 1
      initial_scale         = 0
      container_concurrency = 80
    }
  }

  # Cloud Run inputs
  cloud_run_env_variables = {
    campaigns-api = [
      {
        "name"  = "CORS_OPTIONS_ALLOWED_ORIGIN"
        "value" = local.cloud_build_env_vars["campaigns-api"]._CORS_OPTIONS_ALLOWED_ORIGIN
      },
      {
        "name"  = "CloudRunConfig__DeployedEnvironment"
        "value" = local.environment
      }
    ],
    campaigns-admin = []
  }

  #####################################
  # Admin auth inputs
  #####################################
  salesforce_fqdn        = local.salesforce_fqdn
  firebase_app_id        = local.firebase_app_id
  firebase_auth_domain   = local.firebase_auth_domain
  client_id              = local.client_id
  allowed_frame_ancestor = local.allowed_frame_ancestor
  external_url           = local.external_url
  admin_app_service_name = local.admin_app_service_name
  short_api_id           = local.short_api_id

  cloud_build_triggers_for_admin_auth = {
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }

  jobs_board_notification_targets = "@slack-jobs-board-alerts-prod"
  product_catalog_api_base_url    = "https://api.checkatrade.com/v1/product-catalog"
  finance_api_base_url            = "https://api.checkatrade.com/v1/finance/product-catalog"

  core_subscriber_service_account_email = "<EMAIL>"

  capi_service_accounts = [
    # this is the poorly named finance service, not the Product Catalog. this grants access to the adjustments endpoint.
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    # Shared service accounts for the same services
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
  ]
}
