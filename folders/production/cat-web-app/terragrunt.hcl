terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name             = basename(get_terragrunt_dir())
  environment              = basename(dirname(get_terragrunt_dir()))
  consent_screen_app_title = local.project_name
  repo_name                = "cat-web-app"
  organisation             = "cat-home-experts"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_env_vars = {
    _GAR_HOSTNAME = "eu-docker.pkg.dev"
    _PLATFORM     = "managed",
    _PROJECT_ENV  = local.environment,
  }

  # app_engine_friendly_domain_name = "${local.project_name}-${local.environment}.checkatrade.com"

  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment

  consent_screen_app_title = "Checkatrade" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project
  # app_engine_friendly_domain_name = local.app_engine_friendly_domain_name
  # app_engine_custom_certificate_id = "????"

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    # App Engine Permissions
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
    )
    #"roles/appengine.serviceAdmin" : concat(
    #  local.project_team,
    #)

    # Other Permissions
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
  }


  # ---- Cloudbuild triggers config ----
  # Commented out for now, until we have a working Cloud Build config in the cat-web-app repo

  # cloud_build_triggers = {
  #   productionTrigger = {
  #     name                         = "Main"
  #     description                  = "Builds and releases to prod on a commit to main."
  #     disabled                     = false
  #     push_trigger_enabled         = true
  #     pull_request_trigger_enabled = false
  #     owner                        = local.organisation
  #     repo_name                    = local.repo_name
  #     branch_regex                 = "^main$"
  #     invert_regex                 = false
  #     filename                     = "cloudbuild.yaml"
  #     env_variables                = local.cloud_build_env_vars
  #     excluded_files_filter        = []
  #   }
  # }
}

