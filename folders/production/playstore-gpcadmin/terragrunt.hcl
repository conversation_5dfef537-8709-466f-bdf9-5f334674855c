terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]

  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  owners = [
    "user:<EMAIL>",
  ]

  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}

inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment


  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  viewers            = local.viewers
  everyone           = local.everyone


  project_static_permissions = {
    "roles/viewer" : concat(
      local.everyone,
    )

    "roles/secretmanager.secretAccessor" : concat(
      local.project_team_admin,
    )

    "roles/owner" : concat(
      local.owners,
    )
  }
}