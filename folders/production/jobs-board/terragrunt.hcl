terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_env_vars = {
    _GCR_HOSTNAME    = "eu.gcr.io",
    _PLATFORM        = "managed",
    _PROJECT_ENV     = local.environment,
    _VERSION         = local.env,
    _PPL_ENABLED     = "false",
    _PRICING_API_URL = "https://pricing-api-gw-9tew9wu0.nw.gateway.dev",
  }

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}

inputs = {
  folder       = local.environment
  project_name = local.project_name
  environment  = local.environment

  project_static_permissions = {
    "roles/secretmanager.secretVersionAdder" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team_admin,
    )
    "roles/run.developer" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.publisher" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin,
    )
    "roles/bigquery.dataOwner" : concat(
      local.project_team,
    )
    "roles/bigquery.admin" : concat(
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.privateLogViewer" : concat(
      local.project_team,
    )
  }

  cloud_build_triggers = {
    productionTrigger = {
      name                         = "jobs-board-api-main"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-jobs-board"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
    }
  }

  cloud_run_env_variables = {
    jobs-board-api = []
  }

  cloud_run_parameters = {
    jobs-board-api = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 4
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
    }
  }

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  communication_api_notification_targets = "@slack-communication-alerts-prod"
  aws_incoming_role_params = {
    aws_account_id = "************"
    aws_role_name  = "datahub-production-aws-to-gcp-assume-role"
  }
}
