tradex-firestore-key: ENC[AES256_GCM,data:cY3yGPY6EZZyKlCxMnEhS/lHGIoKPqxV85F6I5BdJDcjCycikwlf,iv:/eAI9s6F7B1hQkp66jL5ZGCV4jKwonD1Hbsop18W6/w=,tag:iw16KXVf4wMmcI/mfaZGsA==,type:str]
sops:
    kms: []
    gcp_kms:
        - resource_id: projects/operations-14020/locations/europe-west2/keyRings/secret-provisioner-33274/cryptoKeys/production-sops
          created_at: "2023-09-26T12:49:48Z"
          enc: CiYAS6e987WMmNt9zDnFHUqHIl/zpuFU1OZyPhlwIH6oEnejGq/UbRJJAP88PSoPjhAp91mtIxgZUFJIwQEORj/Is2g66ZVxHqimp4oKu0uygZ0thl1GO9o1/CZMe05m8j8sZ6eHgkLnSfvbDuk6kcfQJQ==
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2023-09-26T12:50:35Z"
    mac: ENC[AES256_GCM,data:EOgwRJVhYvoWweCxayiK+F16POdBpFo7KjeJR02ad/ybGtL2NDDuldiTHiEyz50wg1CdvbPpW5MJa6a+EzsZ3AElkdeoSrgifcfaZVZz3v2g6kAVZCG0UFpagKHJb6TnhcCJ8hlZtRmjUkWs2vLWfppiCf9l7392fRqczUYZgqs=,iv:D64dSUKqn2tecrWqfV7m5Js2LJcNmRS++eD3Mk5L0EE=,tag:cJZU12IKGfHv4nnCZiGhQw==,type:str]
    pgp: []
    unencrypted_suffix: _unencrypted
    version: 3.7.3
