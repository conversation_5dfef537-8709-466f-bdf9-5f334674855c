terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  env_vars = {
  }

  cloud_build_env_vars = {
    _GCP_REGION     = "europe-west2"
    _TRIGGER_NAME   = "main"
    _SERVICE_NAME   = local.project_name
    _VERSION        = local.env
    _GCR_HOSTNAME   = "eu.gcr.io"
    _PLATFORM       = "managed"
    _ENABLE_TRAFFIC = true
  }

  cloud_run_env_vars = [
    # {
    #   "name"  = "CHAMBER_KMS_KEY_ALIAS"
    #   "value" = "alias/cathex-staging-chamber"
    # },
  ]
}

inputs = {
  # Default inputs
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment

  # Accounts for team defined IAM permissions
  viewer_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  owner_access = []

  developer_access = [
    "group:<EMAIL>"
  ]

  team_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  publisher_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  scheduler_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  topics_and_subs = [
    "sales-data-sector-sales-bulk-req"
  ]

  cloudbuild_triggers = {
    prodTrigger = {
      name                         = "main"
      description                  = "Builds and releases to production from main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-sales-data-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
      excluded_files_filter        = []
    }
  }

  # Cloud Run
  container_concurrency   = 3
  max_scale               = 1
  cloud_run_env_variables = local.cloud_run_env_vars

  # Pubsub Alert variables
  alert_duration  = "60s"
  threshold_value = "10"
  trigger_count   = "1"
}
