terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  region       = "europe-west2"
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })


  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]


  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating
  # If you don't follow the standard of using groups named gcp-<my-project>@cathex.io
  # and gcp-<my-project>-<EMAIL> you will need to use the project module differently,
  # see https://github.com/cat-home-experts/terraform-modules/blob/main/gcp/project/README.md

  # Teams definitions
  project_team       = ["group:gcp-${local.project_name}@cathex.io"]
  project_team_admin = ["group:gcp-${local.project_name}-<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
  vertex_ai_users_service_accounts = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ]
  vertex_ai_users_emails = [
    "<EMAIL>",
    "<EMAIL>",
  ]
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment
  region       = local.region


  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/aiplatform.user" : concat(
      local.project_team,
      local.project_team_admin,
      [for sa in local.vertex_ai_users_service_accounts : "serviceAccount:${sa}"],
      [for email in local.vertex_ai_users_emails : "user:${email}"],
    )
    "roles/editor" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/iap.httpsResourceAccessor" : concat(
      local.everyone,
    )
    "roles/run.developer" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/secretmanager.secretAccessor" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/storage.objectUser" : concat(
      local.project_team_admin,
      local.project_team,
    )
  }

  labels = {
    owner       = "data-science"
    environment = local.environment
    project     = local.project_name
  }
}
