terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  region       = "europe-west2"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    "../../../projects/${local.project_name}/template/*.tpl*",
  ]

  cloudbuild_env_variables = {
    emulator = {}
    secure-contacts-ui = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "secure-contacts-ui"
      _VERSION        = "prod"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    secure-contacts-services = {
      _GCP_REGION             = "europe-west2"
      _API_SERVICE_NAME       = "secure-contacts-api"
      _PROCESSOR_SERVICE_NAME = "secure-contacts-processor"
      _VERSION                = "prod"
      _GCR_HOSTNAME           = "eu.gcr.io"
      _PLATFORM               = "managed"
      _ENABLE_TRAFFIC         = false
    }
    gateway = {
      _GCP_REGION           = "europe-west2"
      _SERVICE_NAME         = "secure-contacts-api"
      _VERSION              = "prod"
      _PLATFORM             = "managed"
      _API_GATEWAY_NAME     = "secure-contacts-gateway-net-gw"
      _API_GATEWAY_API_NAME = "secure-contacts-gateway-net"
    }
  }

  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}

inputs = {
  project_name   = local.project_name
  environment    = "production"
  project_folder = local.environment
  region         = local.region

  bigtable_nodes              = 1
  bigtable_storage_type       = "SSD"
  enable_bigtable_instance    = true
  bigtable_backup_enabled     = true
  bigtable_backup_schedule    = "0 1 * * *"
  transit_encryption_mode     = "DISABLED"
  redis_version               = "REDIS_5_0"
  salesforce_integ_project_id = "salesforce-integ-prod-38869"

  scheduler_jobs = {}

  # ---- IAM config ----

  # Permissions for humans only

  project_static_permissions = {
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin,
    )
    "roles/datastore.indexAdmin" : concat(
      local.project_team_admin,
    )
    "roles/datastore.user" : concat(
      local.project_team_admin,
    )
    "roles/cloudscheduler.admin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.editor" : concat(
      local.project_team_admin,
    )
    "roles/secretmanager.secretVersionManager" : concat(
      local.project_team_admin,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageConsumer" : concat(
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.viewer" : concat(
      local.viewers,
    )
    "roles/viewer" : concat(
      local.viewers,
    )
    "roles/bigquery.admin" : concat(
      local.project_team_admin,
    )
    "roles/storage.admin" : concat(
      local.project_team_admin
    )
    "roles/datastore.importExportAdmin" : concat(
      local.project_team_admin
    )
    "roles/run.invoker" : concat(flatten([
      "group:<EMAIL>",
      "group:<EMAIL>",
      local.project_team_admin,
    ]))
    "roles/iam.serviceAccountUser" : concat(flatten([
      "group:<EMAIL>",
      "group:<EMAIL>",
      local.project_team_admin,
    ]))
    "roles/iam.serviceAccountTokenCreator" : concat(flatten([
      "group:<EMAIL>",
      "group:<EMAIL>",
      local.project_team_admin,
    ]))
  }



  cloudbuild_triggers = {
    # Triggers to build legacy app
    prodTrigger = {
      name                         = "main"
      description                  = "Builds and releases to production from main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "secure-contacts-service"
      branch_regex                 = "^master$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "main"
        _SERVICE_NAME   = "secure-contacts"
        _VERSION        = "production"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = false
      }
      excluded_files_filter = []
    }

    # Triggers to build .NET Core app and related components

    # UI
    pushTriggerUi = {
      name                         = "ui-push-main"
      description                  = "Builds and releases to production on PR merge to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-secure-contacts-ui"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["secure-contacts-ui"],
        {
          _TRIGGER_NAME   = "feature"
          _ENABLE_TRAFFIC = true
      })
    }

    # API Gateway
    pushTriggerApiGateway = {
      name                         = "apigateway-push-main"
      description                  = "Builds and releases to production on PR merge to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-secure-contacts"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "gcp-secure-contacts-api/api-gateway/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.gateway
      included_files_filter        = ["gcp-secure-contacts-api/api-gateway/**"]
    }

    # Shared emulator
    pushTriggerEmulator = {
      name                         = "emulator-push-main"
      description                  = "Builds and releases to production on PR merge to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-secure-contacts"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "shared/emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["shared/emulator/**"]
    }

    # API and Processor
    pushTriggerServices = {
      name                         = "services-push-main"
      description                  = "Builds and releases to production on PR merge to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-secure-contacts"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "shared/cloudbuild.yml"
      env_variables = merge(local.cloudbuild_env_variables["secure-contacts-services"],
        {
          _TRIGGER_NAME   = "feature"
          _ENABLE_TRAFFIC = true
      })
      excluded_files_filter = [
        "shared/emulator/**",
        "gcp-secure-contacts-api/cloudfunctions/**",
        "gcp-secure-contacts-api/api-gateway/**",
        "gcp-secure-contacts-automation-tests/**"
      ]
    }

    # Firestore Write Listener
    pushTriggerFirestoreWriteListenerServices = {
      name                         = "fs-write-listener-push"
      description                  = "Builds and releases to production on PR merge to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-secure-contacts"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "gcp-secure-contacts-api/cloudfunctions/cloudbuild.yaml"
      env_variables = {
        _GCP_REGION = "europe-west2"
      }
      included_files_filter = ["gcp-secure-contacts-api/cloudfunctions/**"]
    }

    forwarderApiTrigger = {
      name                         = "forwarder-api"
      description                  = "Builds and releases secure contacts call forwarder."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      manual_trigger_enabled       = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-secure-contacts-typescript"
      branch_regex                 = "^main"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild-forwarder.yaml"
      env_variables = {
      }
      excluded_files_filter = []
    }
  }

  # Cloudrun inputs
  cloud_run_env_variables_dotnet = {
    secure-contacts-api = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      },
      {
        name  = "SC_LOG_LEVEL"
        value = "Information"
      },
      {
        name  = "CORS_ALLOWED_HOSTS"
        value = "https://secure-contacts-admin.checkatrade.com"
      },
      {
        name  = "SF_URL"
        value = "https://checkatrade.lightning.force.com"
      }
    ]
    secure-contacts-processor = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      },
      {
        name  = "SC_LOG_LEVEL"
        value = "Information"
      },
      {
        name  = "SMS_API_URL"
        value = "http://**************"
      },
      {
        name  = "CALL_API_URL"
        value = "https://cat-api.safenumbers.com"
      },
      {
        name  = "CALL_API_OVERRIDE_CERTIFICATE_VALIDATION" #ssl override
        value = "false"
      },
      {
        name  = "CALL_API_OVERRIDE_ALL_DIVERT_TO"
        value = ""
      },
      {
        name  = "CALL_API_OVERRIDE_TEST_MODE"
        value = "false"
      },
      {
        name  = "MOCK_SMS_CLIENT_ENABLED"
        value = "false"
      },
      {
        name  = "CHECK_SECURENUMBER_AUDIT_COLLECTION_FOR_CALL_LOGS"
        value = "true"
      }
    ]
    secure-contacts-ui = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      },
    ]
  }

  cloud_run_parameters = {
    secure-contacts-api = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    },
    secure-contacts-processor = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    },
    secure-contacts-ui = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 1
      initial_scale         = 0
      container_concurrency = 30
    }
  }

  # Pubsub Alert variables
  # These are currently unused, it's worth considering use of the pubsub/alerts
  # module in the project's pubsub.tf file
  # alert_duration  = "60s"
  # threshold_value = "10"
  # trigger_count   = "1"

  # Cloud Run vars
  container_concurrency = 20
  initial_scale         = 0
  min_scale             = 0
  max_scale             = 20


  cloud_run_env_variables = [
    {
      "name"  = "CACHE_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "PARTNER_API_URL"
      "value" = "https://partnerapi.checkatrade.com/api/"
    },
    {
      "name"  = "MC_API_URL"
      "value" = "https://mcapi.checkatrade.com/api/ManagedContacts"
    },
  ]

  # Datadog monitor inputs
  notifications              = "@slack-secure-contacts-alerts"
  silverlining_notifications = "@slack-silverlining-healthcheck-alerts"

  jobs_management_project_id = "jobs-management-prod-47638"

  core_subscriber_service_account_email = "<EMAIL>"

  capi_service_accounts = [
    # Shared service accounts for core-calls
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>"
  ]
}
