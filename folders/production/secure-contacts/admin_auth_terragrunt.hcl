inputs = {
  cloud_run_admin_auth_settings = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    container_concurrency = 500
    max_scale             = 2
    min_scale             = 0
    initial_scale         = 0
  }

  cloud_build_env_vars_for_admin_auth = {
    # Firebase API keys are not considered sensitive so are not to be scanned by Gitleaks
    # https://firebase.google.com/support/guides/security-checklist#api-keys-not-secret
    # pragma: allowlist nextline secret
    _FIREBASE_API_KEY       = "AIzaSyBc1gEosaTg8cFzZhcJygwyOJ_2IjQHZhg" # gitleaks:allow
    _FIREBASE_APP_ID        = "1:104067567273:web:d8b007584fcded620423b8"
    _FIREBASE_AUTH_DOMAIN   = "contacts-prod-36231.firebaseapp.com"
    _AUTHORIZATION_ENDPOINT = "https://checkatrade.my.salesforce.com/services/oauth2/authorize"
    _CLIENT_ID              = "3MVG9tzQRhEbH_K2nO6Y9U.mkX3ux_Sk0lLG1SzVA5i0zQk2sfIAHb_8CuuXPBB.5ocqVEh0abJTGKKZOENc5"
    _ISSUER                 = "https://checkatrade.my.salesforce.com"
    _JWKS_URI               = "https://checkatrade.my.salesforce.com/id/keys"
    _TOKEN_ENDPOINT         = "https://checkatrade.my.salesforce.com/services/oauth2/token"
    _ALLOWED_FRAME_ANCESTOR = "https://checkatrade.lightning.force.com"
    _EXTERNAL_URL           = "https://secure-contacts-admin.checkatrade.com"
  }

  cloud_build_triggers_for_admin_auth = {
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }
}
