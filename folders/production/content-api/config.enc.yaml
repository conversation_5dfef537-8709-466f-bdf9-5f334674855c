content-api-connection-string: ENC[AES256_GCM,data:pmHHqJIecr53Qso9WJDEBOXDInHU+ckoMgVZhhYLnnZcB3kSarMJTqhG7GXrBDW3f6vugJCC3vj1d7e+rOlaDkC16d3/Sm53hMu2zNYbeYUcFAyyV3/TO+1sorLX1zywnvA=,iv:gkASJ789+C6oI4X4dqVxQj+SsLZO4g/Isy0oOBRBOXo=,tag:X7tpkGm69oyatG1+f41u3g==,type:str]
github-packages-token: ENC[AES256_GCM,data:AG7hyedQoIQ5WKd13VXx9En5ff/IAC/M/djaR36NyFtVxdt5uAtqhA==,iv:XdefT4D+2DZ2t9GnEeapHjURoaARpmkbYT3VnSioFy4=,tag:IQfGZlE57hWHLDLW5IV0WA==,type:str]
github-packages-username: ENC[AES256_GCM,data:wqdHwOfS37r5UTZ1XHlpINVib/OBPA==,iv:IsqOd78LQYjliEiaPKeh05IqpQ71SFe/+eM8SrKG++I=,tag:VreOKZnE+/dCrktX4HWbfA==,type:str]
sops:
    kms: []
    gcp_kms:
        - resource_id: projects/operations-14020/locations/europe-west2/keyRings/secret-provisioner-33274/cryptoKeys/production-sops
          created_at: "2023-08-31T10:23:14Z"
          enc: CiYAS6e98zouyrcKdDIayqPvUAg8WXHWvYYpBS35/zwqD8DgoQ4D6hJJADYxiUUhh/sgxg9iiwAsrHCKxRRM8le5CBQzZpmspW+Yz/nck1l7OQP+W5peAoHyMXYw38wnlazHGuMksNeRHuM+OFVT2NmSNw==
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2023-08-31T10:24:15Z"
    mac: ENC[AES256_GCM,data:ulmaDME71fx2B4/4ZyXsTbbUf+Pzh6iXsY+y3DBpmZJ3PNn5H33FfS/4jiQit79VLdfadbvdTMw9WEaD530xWKNvhjnM9LaPQcmWOLN2TaNqdeFKPq9DuUl75HUCxImwGJPtSKofhbzIsg7iyvFj9/fna6O0l91VYo0kx3Wrbk0=,iv:GCsWRl/lOWEyi2yXvBqY5xne9R0efH6Gl2l58vNG/0A=,tag:1sf/slWVavUDGIjFrNDSUg==,type:str]
    pgp: []
    unencrypted_suffix: _unencrypted
    version: 3.7.3
