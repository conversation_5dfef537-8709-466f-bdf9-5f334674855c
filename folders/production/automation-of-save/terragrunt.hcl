terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  region       = "europe-west2"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloudbuild_env_variables = {
    emulator = {}
    gateway = {
      _GCP_REGION           = "europe-west2"
      _SERVICE_NAME         = "campaign-management-api"
      _VERSION              = local.env
      _PLATFORM             = "managed"
      _API_GATEWAY_NAME     = "campaign-management-gateway-gw"
      _API_GATEWAY_API_NAME = "campaign-management-gateway"
    }
    campaign-management-api = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "campaign-management-api"
      _VERSION        = local.env
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed",
      _ENABLE_TRAFFIC = false
    }
    campaign-management-processor = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "campaign-management-processor"
      _VERSION        = local.env
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    campaign-management-rejection-app = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "campaign-management-rejection-app"
      _VERSION        = local.env
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
  }
}


inputs = {
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment
  region         = local.region

  viewer_access = [
    "group:<EMAIL>"
  ]

  developer_access = [
    "group:<EMAIL>"
  ]

  team_access = [
    "group:<EMAIL>"
  ]

  publisher_access = [
    "group:<EMAIL>"
  ]

  cloudbuild_triggers = {

    # api emulator push to main
    mainEmulatorTriggerApi = {
      name                         = "emulator-api-production"
      description                  = "Builds and releases to production on push to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-api"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }

    # api gateway push to main
    mainGatewayTriggerApi = {
      name                         = "gateway-api-production"
      description                  = "Builds and releases to production on push to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-api"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.gateway
      included_files_filter        = ["api-gateway/**"]
    }

    # api push to main
    mainTriggerApi = {
      name                         = "api-production"
      description                  = "Builds and releases to production on push to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-api"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["campaign-management-api"],
        {
          _TRIGGER_NAME = "main"
      })
      excluded_files_filter = ["emulator/**"]
    }

    # processor emulator push to main
    mainEmulatorTriggerProcessor = {
      name                         = "emulator-processor-production"
      description                  = "Builds and releases to production on any main commit."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-processor"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }

    # processor push to main
    mainTriggerProcessor = {
      name                         = "processor-production"
      description                  = "Builds and releases to production on any main commit."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-processor"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["campaign-management-processor"],
        {
          _TRIGGER_NAME = "main"
      })
      excluded_files_filter = ["emulator/**"]
    }


    # Rejection App push to main
    mainTriggerRejectionApp = {
      name                         = "rejection-app-production"
      description                  = "Builds and releases to production on any main commit."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-rejection-app"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["campaign-management-rejection-app"],
        {
          _TRIGGER_NAME = "main"
      })
      excluded_files_filter = ["emulator/**"]
    }

    # Rejection App Emulator push to main
    mainEmulatorTriggerRejectionApp = {
      name                         = "emulator-rejection-app-production"
      description                  = "Builds and releases to production on push to main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-rejection-app"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }
  }

  # Cloudrun inputs
  cloud_run_env_variables = {
    campaign-management-api = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      },
    ]
    campaign-management-processor = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      },
    ]
    campaign-management-rejection-app = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = local.environment
      },
    ]
  }

  cloud_run_parameters = {
    campaign-management-api = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 3
    }
    campaign-management-processor = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 3
    }
    campaign-management-rejection-app = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 3
    }
  }

  # Pubsub Alert variables
  alert_duration  = "60s"
  threshold_value = "10"
  trigger_count   = "1"

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"
}
