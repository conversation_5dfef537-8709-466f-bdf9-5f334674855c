terraform {
  source = "./../../..//projects/image-service"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  github_owner  = "cat-home-experts"
  github_repo   = "gcp-image-service-v2"
  branch_regex  = "^main$"
  builder_regex = ".*"

  collection = "image-data"
}


inputs = {
  # Default inputs
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment



  consent_screen_app_title = "Image Service prod" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project
  image_service_collection = local.collection
  bucket_name              = "cathex-image-service"
  transition_bucket_name   = "cathex-image-service-aws-transition-prod"
  # sink_destination_bucket = "sre-logs-prod"

  # Cloudbuild inputs
  cloudbuild_triggers = {
    imageServiceProduction = {
      name                         = "gcp-image-service-prod"
      description                  = "Production deployment of the image service"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-image-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _APP_URL     = "https://image-service-production-14602.nw.r.appspot.com/"
        _BUCKET_NAME = "cathex-image-service"
        _PROJECT     = "image-service-production-14602"
        _VERSION     = "p"
      }
      included_files_filter = []
    }
    imageResizer = {
      name                         = "image-resizer"
      description                  = "image resizer cloud function"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-image-resizer"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = {}
      included_files_filter        = []
    }
  }

  # Cloudbuild v2 triggers
  cloud_build_push = {
    main_backend = {
      name         = "main-backend"
      description  = "Builds and releases to prod on any main commit."
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/backend/cloudbuild.yaml"
      substitutions = {
        _SERVICE_NAME = "image-backend"
      }
      included_files = [
        "apps/backend/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
    },
    main_api = {
      name         = "main-api"
      description  = "Builds and releases to prod on any main commit."
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/api/cloudbuild.yaml"
      included_files = [
        "apps/api/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-api"
      }
    },
    main_processor = {
      name         = "main-processor"
      description  = "Builds and releases to prod on any main commit."
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/processor/cloudbuild.yaml"
      included_files = [
        "apps/processor/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-processor"
      }
    },
    main_vetting = {
      name         = "main-vetting"
      description  = "Builds and releases to prod on any main commit."
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/vetting/cloudbuild.yaml"
      included_files = [
        "apps/vetting/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-vetting"
      }
    },
    main_workflow = {
      name         = "main-workflow"
      description  = "Builds and releases to prod on any main commit."
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "workflow/cloudbuild.yaml"
      included_files = [
        "workflow/workflow.yaml.j2",
        "workflow/tests/**"
      ]
      substitutions = {
      },
    },
    #main_image_service_v1 = {
    #  name         = "main-image-service-v1"
    #  description  = "Builds and releases to prod on any main commit."
    #  owner        = local.github_owner
    #  repository   = local.github_repo
    #  invert_regex = false
    #  branch       = local.branch_regex
    #  filename     = "apps/image-service-v1/cloudbuild.yaml"
    #  substitutions = {
    #    _SERVICE_NAME = "image-service-v1"
    #  }
    #  included_files = [
    #    "apps/image-service-v1/*"
    #  ]
    #  ignored_files = [
    #    "**/*Dockerfile*",
    #    "**/*.dockerignore",
    #  ]
    #},
    main_image_service_migration = {
      name         = "main-image-service-migration"
      description  = "Builds and releases to default on any non main commit."
      owner        = local.github_owner
      repository   = "image-migration"
      invert_regex = false
      branch       = local.branch_regex
      filename     = "cloudbuild.yaml"
      substitutions = {
        _SERVICE_NAME = "image-migration"
      }
      included_files = [

      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
    },
    # main_frontend = { # disabeling front end for the time being
    #   name         = "main-frontend"
    #   description  = "Builds and releases to prod on any main commit."
    #   owner        = local.github_owner
    #   repository   = local.github_repo
    #   invert_regex = false
    #   branch       = local.branch_regex
    #   filename     = "apps/frontend/cloud_build.yaml"
    #   substitutions = {

    #   }
    #   included_files = [
    #     "apps/frontend/*"
    #   ]
    #   ignored_files = [
    #     "**/*Dockerfile*",
    #     "**/*.dockerignore",
    #   ]
    # },
  }
  cloud_build_pull = {}
  #alerts slack channel
  notifications = "@slack-image-service-alerts"

}
