terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })


  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    # "../../../projects/${local.project_name}/template/*.tpl*", # example of API gateway template trigger
  ]


  # ------------ Cloudbuild ------------

  # NOTE: Replace <my-repo> with the actual name of your project's repository

  repo_name    = "ops-tooling-app"
  organisation = "cat-home-experts"
  cloud_build_env_vars = {
    _GAR_HOSTNAME = "europe-west2-docker.pkg.dev",
    _PLATFORM     = "managed",
    _PROJECT_ENV  = local.environment,
    # CUSTOM ENV VARIABLES
    _SERVICE_NAME = "ops-tooling-app",
    _GCP_REGION   = "europe-west2",
    _VERSION      = "prod",
  }


  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating
  # If you don't follow the standard of using groups named gcp-<my-project>@cathex.io
  # and gcp-<my-project>-<EMAIL> you will need to use the project module differently,
  # see https://github.com/cat-home-experts/terraform-modules/blob/main/gcp/project/README.md

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment


  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      # local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountKeyAdmin" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountUser" : concat(
      # local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.builder" : concat(
      local.project_team_admin,
    )
    # "roles/cloudbuild.builds.editor" : concat(
    #   # local.project_team,
    #   local.project_team_admin,
    #   # local.qa_team,
    #   # local.qa_team_admin,
    # )
    "roles/firebase.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/logging.viewAccessor" : concat(
      # local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      # local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/run.developer" : concat(
      local.project_team_admin,
    )
    "roles/secretmanager.secretAccessor" : concat(
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases to prod env on push to main branch."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
      excluded_files_filter        = ["emulator/**"] # Can be commented out
      comment_control              = "COMMENTS_DISABLED"
    }
  }

  cloud_run_parameters = {
    ops-tooling-app = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 10
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 50
    }
  }
}
