terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    "../../../projects/${local.project_name}/template/*.tpl*",
  ]
}

inputs = {
  project_name   = local.project_name
  environment    = "production"
  project_folder = local.environment

  cloudbuild_triggers = {
    # Triggers for frontend-strapi
    prodTrigger = {
      name                         = "main"
      description                  = "Builds and releases to production from main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "trade-profile-service"
      branch_regex                 = "^master$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "main"
        _SERVICE_NAME   = "trade-profile"
        _VERSION        = "production"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = false
      }
      excluded_files_filter = []
    }
  }

  # Cloud Run vars
  container_concurrency = 20
  initial_scale         = 0
  min_scale             = 0
  max_scale             = 20


  cloud_run_env_variables = [
    {
      "name"  = "CACHE_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "RACK_ENV"
      "value" = "production"
    },
    {
      "name"  = "CORS_ALLOWED_ORIGINS"
      "value" = "*"
    },
    {
      "name"  = "SECURE_CONTACTS_URL"
      "value" = "https://secure-contacts-knfbjqcnfa-nw.a.run.app"
    },
    {
      "name"  = "PARTNER_API_URL"
      "value" = "https://partnerapi.checkatrade.com/api/"
    },
    {
      "name"  = "SENTRY_DSN"
      "value" = "https://e35848a739ab4454abfc7b15bdf37943:<EMAIL>/34"
    },
    {
      "name"  = "REDIS_EXPIRY"
      "value" = "43200"
    }
  ]
}
