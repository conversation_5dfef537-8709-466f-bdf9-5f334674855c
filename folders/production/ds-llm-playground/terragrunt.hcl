terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  region       = "europe-west2"
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })


  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]


  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating
  # If you don't follow the standard of using groups named gcp-<my-project>@cathex.io
  # and gcp-<my-project>-<EMAIL> you will need to use the project module differently,
  # see https://github.com/cat-home-experts/terraform-modules/blob/main/gcp/project/README.md

  # Teams definitions
  project_team       = ["group:gcp-${local.project_name}@cathex.io"]
  project_team_admin = ["group:gcp-${local.project_name}-<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
  llm_users_service_accounts = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ]
  log_syncs_service_accounts = [
    "serviceAccount:<EMAIL>",  # ds-llm-playground-dev-40784
    "serviceAccount:<EMAIL>",   # ds-llm-playground-stg-48944
    "serviceAccount:<EMAIL>",  # ds-dynamic-pricing-dev-10449
    "serviceAccount:<EMAIL>",  # ds-dynamic-pricing-prod-19088
    "serviceAccount:<EMAIL>",  # ds-dynamic-pricing-stg-33214 
    "serviceAccount:<EMAIL>",  # ds-fake-reviews-dev-11948
    "serviceAccount:<EMAIL>",  # ds-fake-reviews-prod-40832
    "serviceAccount:<EMAIL>", # ds-fake-reviews-stg-40423
    "serviceAccount:<EMAIL>",  # ds-job-value-dev-49498
    "serviceAccount:<EMAIL>",  # ds-job-value-prod-38589
    "serviceAccount:<EMAIL>",  # ds-job-value-stg-41723
    "serviceAccount:<EMAIL>",  # ds-lead-forecasting-dev-38970
    "serviceAccount:<EMAIL>",  # ds-lead-forecasting-prod-38816
    "serviceAccount:<EMAIL>",  # ds-lead-forecasting-stg-47518
    "serviceAccount:<EMAIL>",  # ds-match-algo-dev-34980
    "serviceAccount:<EMAIL>",  # ds-match-algo-prod-26741
    "serviceAccount:<EMAIL>",  # ds-match-algo-stg-15206
    "serviceAccount:<EMAIL>",  # ds-nlp-service-dev-35765
    "serviceAccount:<EMAIL>",  # ds-nlp-service-prod-48433
    "serviceAccount:<EMAIL>",   # ds-nlp-service-stg-36176
    "serviceAccount:<EMAIL>",  # ds-semantic-search-dev-35454
    "serviceAccount:<EMAIL>",  # ds-semantic-search-prod-13408
    "serviceAccount:<EMAIL>", # ds-semantic-search-stg-15929
    "serviceAccount:<EMAIL>",  # ds-trade-risk-dev-33721
    "serviceAccount:<EMAIL>", # ds-trade-risk-prod-18224
    "serviceAccount:<EMAIL>",  # ds-trade-risk-stg-42823
  ]
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment
  region       = local.region


  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/editor" : concat(
      local.project_team_admin,
    )
    "roles/aiplatform.user" : concat(
      local.project_team_admin,
      [for sa in local.llm_users_service_accounts : "serviceAccount:${sa}"]
    )
    "roles/bigquery.dataEditor" : local.log_syncs_service_accounts,
    "roles/pubsub.publisher" : local.log_syncs_service_accounts,
  }

  labels = {
    owner       = "data-science"
    environment = local.environment
    project     = local.project_name
  }
}
