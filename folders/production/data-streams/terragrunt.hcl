terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]

  organisation = "cat-home-experts"
  repo_name    = "data-streams"

  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name     = local.project_name
  environment      = local.environment
  provider_enabled = true

  env = local.env

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.admin" : concat(
      local.project_team_admin,
    )
    "roles/errorreporting.user" : concat(
      local.project_team,
    )
    "roles/errorreporting.admin" : concat(
      local.project_team_admin,
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iam.serviceAccountAdmin" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountKeyAdmin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
  }

  review_metrics_di_api_gateway_host          = "https://750xzjxyqf.execute-api.eu-west-2.amazonaws.com/prod/events"
  heatable_journey_events_di_api_gateway_host = "https://r64c4ygbjb.execute-api.eu-west-2.amazonaws.com/prod/events"
  dynamic_pricing_io_logs_di_api_gateway_host = "https://ax42gtbc23.execute-api.eu-west-2.amazonaws.com/prod/events"

  reviews_project_id = "reviews-prod-22507"

  core_subscriber_service_account_email = "<EMAIL>"

  cloud_run_parameters = {
    gcp-to-aws-data-streamer = {
      min_scale             = 0
      max_scale             = 10
      container_concurrency = 250
    }
  }

  capi_project_id = "capi-production-21756"

  apis = [
    "iap.googleapis.com",
    "googleads.googleapis.com",
  ]
}
