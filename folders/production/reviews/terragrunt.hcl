terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    "../../../projects/${local.project_name}/template/*.tpl*",
  ]

  pasabi_export_bucket_name            = "pasabi-checkatrade-production-csv-inputs"
  firestore_export_bucket_name         = "reviews-prod-22507-cat-live-csv-restore"
  slack_webhook_url                    = "*******************************************************************************" #pragma: allowlist secret
  ingest_force_publish_verified_update = "true"
  content_api_project_id               = "content-api-production-36346"
  consumer_area_api_url                = "https://consumer-api-inv4w6yq3q-nw.a.run.app/"

  # Teams definitions
  project_team                       = ["group:<EMAIL>"]
  project_team_admin                 = ["group:<EMAIL>"]
  qa_team                            = ["group:<EMAIL>"]
  qa_team_admin                      = ["group:<EMAIL>"]
  secure_contacts_project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
  # Admin Auth
  salesforce_fqdn                 = "checkatrade.my.salesforce.com"
  firebase_app_id                 = "1:327631004518:web:83db14b6b828c99d095ea7"
  firebase_auth_domain            = "reviews-prod-22507.firebaseapp.com"
  allowed_frame_ancestor          = "https://checkatrade.lightning.force.com"
  client_id                       = "3MVG9tzQRhEbH_K2nO6Y9U.mkX2dNNwIF9gpaSjfqkrA5syONz6C6_1IJxis6KRqpP46pkak0uQw73xueVGwq"
  capi_client_id                  = "3MVG9tzQRhEbH_K2nO6Y9U.mkX3ptHm9pwp_UmL0_1gDJHgrxb.DC_p0mWa_qI1BqOBXgXcBCeugUxHMBEi6f"
  external_url                    = "https://reviews-admin.checkatrade.com"
  admin_app_service_name          = "reviews-admin-ui"      # Backend service for Salesforce Admin embedded app
  short_api_id                    = "reviews-admin-ui"      # _API_ID that comes from the module includes the full path
  capi_admin_app_service_name     = "capi-reviews-admin-ui" # Backend service for Salesforce Admin embedded app
  capi_short_api_id               = "capi-reviews-admin-ui" # _API_ID that comes from the module includes the full path
  capi_external_url               = "https://reviews-admin-capi.checkatrade.com"
  additional_domains_to_authorize = "https://api.checkatrade.com"
}

inputs = {
  # Default inputs
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment

  content_api_project_id         = local.content_api_project_id
  trade_experience_project_id    = "cat-trades"
  search_project_id              = "search-production-41695"
  consumer_area_project_id       = "consumer-area-production-40311"
  salesforce_integ_project_id    = "salesforce-integ-prod-38869"
  trade_exp_admin_ui_project_id  = "trade-exp-admin-ui-prod-40691"
  data_comparison_post_to_slack  = true
  trigger_review_published_comms = false
  trigger_review_created_comms   = true

  data_readers = [
    "serviceAccount:<EMAIL>",
  ]

  # ---- Admin Auth ----
  salesforce_fqdn                 = local.salesforce_fqdn
  firebase_app_id                 = local.firebase_app_id
  firebase_auth_domain            = local.firebase_auth_domain
  allowed_frame_ancestor          = local.allowed_frame_ancestor
  client_id                       = local.client_id
  capi_client_id                  = local.capi_client_id
  external_url                    = local.external_url
  admin_app_service_name          = local.admin_app_service_name
  short_api_id                    = local.short_api_id
  capi_external_url               = local.capi_external_url
  capi_admin_app_service_name     = local.capi_admin_app_service_name
  capi_short_api_id               = local.capi_short_api_id
  additional_domains_to_authorize = local.additional_domains_to_authorize

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone


  app_env = local.environment

  project_static_permissions = {
    "roles/pubsub.viewer" : concat(
      local.project_team
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
    )
    "roles/firebase.viewer" : concat(
      local.everyone,
    )
    "roles/run.invoker" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/run.developer" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.codeViewer" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/iap.admin" : concat(
      local.project_team_admin,
    )
    "roles/logging.admin" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/secretmanager.secretVersionAdder" : concat(
      local.project_team_admin,
      local.secure_contacts_project_team_admin,
    )
    "roles/cloudscheduler.admin" : concat(
      local.project_team_admin,
    )
    "roles/datastore.viewer" : [
      "serviceAccount:<EMAIL>",
    ]
  }

  firebase_notification_targets = "@reviews-admin-ui-alerts-prod"

  cloud_build_triggers_for_admin_auth = {
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }

  cloud_build_triggers_for_capi_admin_auth = {
    production_trigger = {
      name                         = "Main-for-Admin-Auth-CAPI"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }

  cloudbuild_triggers_for_to_postgre_sql = {
    main_trigger = {
      name                         = "to-postgre-sql--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _DOTNET_ENVIRONMENT = "Production"
        _ENABLE_TRAFFIC     = "true"
        _GCP_REGION         = "europe-west2"
        _GCR_HOSTNAME       = "europe-west2-docker.pkg.dev"
        _PLATFORM           = "managed"
        _TRIGGER_NAME       = "main"
        _VERSION            = "production"
        _TARGET_PROJECT_ID  = "capi-production-21756"
        _INSTANCE_NAME      = "consumer-app-sql"
      }
    }
  }


  # If no environment variables need to be passed here, please set an empty map within a list like "[{}]"
  # Setting an empty list only like "[]" will cause a type mismatch when running a plan
  cloud_run_env_variables = {
    submit-review = [
      {
        name  = "CORS_ALLOWED_ORIGINS"
        value = "^https:\\/\\/.*checkatrade.com$"
      }
    ]
    reviews-backend = [
      {
        name  = "PASABI_EXPORT_BUCKET_NAME"
        value = local.pasabi_export_bucket_name
      },
      {
        name  = "FIRESTORE_EXPORT_BUCKET_NAME"
        value = local.firestore_export_bucket_name
      },
      {
        name  = "CONTENT_API_PROJECT_ID"
        value = local.content_api_project_id
      },
      {
        name  = "SLACK_WEBHOOK_URL"
        value = local.slack_webhook_url
      },
      {
        name  = "CONSUMER_AREA_API_URL"
        value = local.consumer_area_api_url
      }
    ]
    reviews-ingester = [{
      name  = "INGEST_FORCE_PUBLISH_VERIFIED_UPDATE"
      value = local.ingest_force_publish_verified_update
    }]
  }

  cloud_run_secret_variables = {
    submit-review = [
      {
        name      = "TURNSTILE_DEVELOPMENT_SECRET"
        secret_id = "TURNSTILE_DEVELOPMENT_SECRET"
      },
    ]
  }

  # AWS API Gateway hosts for DI Subscriptions
  tasks_events_di_api_gateway_host = "https://c2blqu3efc.execute-api.eu-west-2.amazonaws.com/prod/events"

  cloud_function_endpoint = "europe-west2-reviews-prod-22507.cloudfunctions.net"
  workflow_endpoint       = "https://workflowexecutions.googleapis.com"
}
