terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  region       = "europe-west2"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloudbuild_env_variables = {
    emulator = {}
    email-service = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "email-service"
      _VERSION        = "prod"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = true
    }
  }

  # ---- IAM Config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}

inputs = {
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment
  region         = local.region


  # ---- IAM Config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  viewers            = local.viewers

  project_static_permissions = {
    "roles/logging.viewAccessor" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.viewer" : concat(
      local.viewers,
    )
    "roles/cloudscheduler.viewer" : concat(
      local.viewers,
    )
    "roles/viewer" : concat(
      local.viewers,
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
    )
    "roles/cloudscheduler.jobRunner" : concat(
      local.project_team,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin,
    )
  }

  cloudbuild_triggers = {
    # email-service push to main
    mainTriggerEmailService = {
      name                         = "production"
      description                  = "Builds and releases to production on any main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-email-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables.email-service,
        {
          _TRIGGER_NAME = "main"
      })
      excluded_files_filter = ["emulator/**"]
    }

    # email-service emulator push to main
    mainEmulatorTriggerEmailService = {
      name                         = "emulator-production"
      description                  = "Builds and releases to production on push to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-email-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }
  }

  # Cloudrun inputs
  cloud_run_env_variables = {
    email-service = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "Production"
      }
    ]
  }

  cloud_run_parameters = {
    email-service = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
    }
  }

  # Pubsub Alert variables
  alert_duration  = "60s"
  threshold_value = "10"
  trigger_count   = "1"

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"
}
