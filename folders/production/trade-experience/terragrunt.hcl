terraform {
  source = "./../../..//projects/trade-experience"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  # Custom groups
  iam_build_triggers_for_member_data = ["member-data--main"]

  viewer_access = [
    "allUsers",
  ]
  owner_access = [
    "group:<EMAIL>",
  ]
  team_access = [
    "group:<EMAIL>",
  ]
  storage_access = [
    "group:<EMAIL>",
  ]
  trade_admins = [
    "group:<EMAIL>",
  ]
  firebase_editor_access = [
    "group:<EMAIL>",
  ]

  advisor_ui_cors_block = [{
    "origin" : ["https://trade-experience-admin.checkatrade.com"],
    "method" : ["GET", "PUT"],
    "response_header" : ["*"],
    "max_age_seconds" : 3600
  }]
}

inputs = {
  # Default inputs
  environment    = "production"
  project_folder = "production"

  content_api_project_id        = "content-api-production-36346"
  data_stats_service_project_id = "data-stats-service-prod-42093"
  salesforce_integ_project_id   = "salesforce-integ-prod-38869"


  firebase_default_bucket = "cat-trades.appspot.com"

  consent_screen_app_title = "Members App" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project

  project_static_permissions = {
    "roles/run.admin" : local.owner_access,
    "roles/firebase.analyticsViewer" : local.team_access,
    "roles/iam.serviceAccountUser" : local.owner_access
    "roles/firebase.admin" : local.owner_access,
    "roles/appengine.appAdmin" : local.owner_access,
    "roles/secretmanager.admin" : local.owner_access,
    "roles/serviceusage.apiKeysAdmin" : local.owner_access,
    "roles/cloudscheduler.admin" : local.owner_access,
    "roles/apigateway.admin" : local.owner_access,
    "roles/apigateway.viewer" : local.owner_access,
    "roles/appengine.deployer" : local.owner_access,
    "roles/cloudbuild.builds.editor" : local.owner_access,
    "roles/firebase.developAdmin" : local.owner_access,
    "roles/firebase.growthAdmin" : local.firebase_editor_access
    "roles/datastore.owner" : local.owner_access,
    "roles/logging.admin" : local.owner_access,
    "roles/pubsub.admin" : local.owner_access,
    "roles/secretmanager.viewer" : local.owner_access,
    "roles/cloudscheduler.jobRunner" : local.owner_access,
    "roles/storage.objectAdmin" : flatten([
      local.storage_access,
      local.owner_access,
    ])
    "roles/iam.serviceAccountTokenCreator" : local.owner_access,
    "roles/iap.admin" : local.trade_admins,
  }

  # Accounts for team defined IAM permissions
  viewer_access  = local.viewer_access
  owner_access   = local.owner_access
  team_access    = local.team_access
  storage_access = local.storage_access



  # Custom build trigger permissions
  iam_build_triggers_for_member_data = local.iam_build_triggers_for_member_data

  # App Engine custom domain inputs
  app_engine_friendly_domain_name  = "membersapp.checkatrade.com"
  app_engine_custom_certificate_id = "********"
  app_engine_iap_enabled           = false

  cloud_run_suffix = "3rn2eua6jq-nw.a.run.app"

  cloudbuild_triggers_for_requester = {
    main_trigger = {
      name                         = "requester--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION             = "europe-west2"
        _GCR_HOSTNAME           = "europe-west2-docker.pkg.dev"
        _PLATFORM               = "managed"
        _REQUESTER_SERVICE_NAME = "app-api"
        _TRIGGER_NAME           = "main"
        _VERSION                = "production"
        _ENABLE_TRAFFIC         = "true"
      }
    }
  }

  cloudbuild_triggers_for_receiver = {
    main_trigger = {
      name                         = "receiver--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION            = "europe-west2"
        _GCR_HOSTNAME          = "europe-west2-docker.pkg.dev"
        _PLATFORM              = "managed"
        _RECEIVER_SERVICE_NAME = "pubsub-receiver"
        _TRIGGER_NAME          = "main"
        _VERSION               = "production"
        _ENABLE_TRAFFIC        = "true"
      }
    }
  }

  cloudbuild_triggers_for_firestore_cloud_functions = {
    main_trigger = {
      name                         = "firestore-func--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _DOCUMENT_STORE                                               = "document-store-263011460521"
        _EMAIL_SERVICE_PROJECT_ID                                     = "email-service-prod-17114"
        _ENABLE_SALESFORCE_FOR_PROFILE_DESCRIPTION_AND_SEARCH_PREVIEW = "true"
        _FIREBASE_DEFAULT_BUCKET                                      = "cat-trades.appspot.com"
        _SALESFORCE_INTEG_PROJECT_ID                                  = "salesforce-integ-prod-38869"
        _TRIGGER_NAME                                                 = "main"
        _SQL_DB_NAME                                                  = "quoting"
        _SQL_DB_USERNAME                                              = "cat-trades@appspot"
        _SQL_DB_HOST                                                  = "053753479da0.ygl1fg3b8haf.europe-west2.sql.goog"
        _SQL_ADMIN_API                                                = "https://www.googleapis.com/auth/sqlservice.admin"
      }
    }
  }

  cloudbuild_triggers_for_api_gateway = {
    main_trigger = {
      name                         = "api-gateway--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "trade-gateway-gw"
        _API_GATEWAY_API_NAME = "trade-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "app-api"
        _TRIGGER_NAME         = "main"
        _VERSION              = "production"
      }
    }
  }

  cloudbuild_triggers_for_member_api_gateway = {
    main_trigger = {
      name                         = "member-api-gateway--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "member-gateway-gw"
        _API_GATEWAY_API_NAME = "member-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "member-data"
        _TRIGGER_NAME         = "main"
        _VERSION              = "production"
      }
    }
  }

  cloudbuild_triggers_for_bulk_receiver = {
    main_trigger = {
      name                         = "bulk-receiver--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "true"
        _GCP_REGION     = "europe-west2"
        _GCR_HOSTNAME   = "europe-west2-docker.pkg.dev"
        _PLATFORM       = "managed"
        _TRIGGER_NAME   = "main"
        _VERSION        = "production"
      }
    }
  }

  cloudbuild_triggers_for_document_store = {
    main_trigger = {
      name                         = "doc-store--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENVIRONMENT  = "production"
        _TRIGGER_NAME = "main"
      }
    }
  }

  cloudbuild_triggers_for_image_events = {
    main_trigger = {
      name                         = "image-events--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME = "image-events"
        _ENABLE_TRAFFIC   = "true"
        _GCP_REGION       = "europe-west2"
        _GCR_HOSTNAME     = "europe-west2-docker.pkg.dev"
        _PLATFORM         = "managed"
        _TRIGGER_NAME     = "main"
        _VERSION          = "production"
      }
    }
  }

  cloudbuild_triggers_for_member_data = {
    main_trigger = {
      name                         = "member-data--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME               = "member-data"
        _CORS_ALLOWED_ORIGINS           = "https://membersapp.checkatrade.com"
        _ENABLE_TRAFFIC                 = "true"
        _GCP_REGION                     = "europe-west2"
        _GCR_HOSTNAME                   = "europe-west2-docker.pkg.dev"
        _GOOGLE_IDENTITY_TOKEN_PROJECT  = "cat-trades"
        _PLATFORM                       = "managed"
        _SALESFORCE_API_VERSION         = "57.0"
        _SALESFORCE_HOST                = "checkatrade.my.salesforce.com"
        _SALESFORCE_TOKEN_URL           = "https://checkatrade.my.salesforce.com/services/oauth2/token"
        _TEST_CHECKATRADE_USER_ID       = "23eaab68-e926-4bee-ad43-f242c988d411"
        _TEST_COMPANY_ID                = "918283"
        _MEMBER_PROMISE_TEST_COMPANY_ID = "337953"
        _TRIGGER_NAME                   = "main"
        _VERSION                        = "production"
        _SECURE_CONTACTS_API_BASE_URL   = "https://secure-contacts-api-knfbjqcnfa-nw.a.run.app/api/"
      }
    }
  }

  cloudbuild_triggers_for_quotes_and_invoices = {
    main_trigger = {
      name                         = "quotes-invoices--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID = "email-service-prod-17114"
        _ENABLE_TRAFFIC           = "true"
        _GCP_REGION               = "europe-west2"
        _GCR_HOSTNAME             = "europe-west2-docker.pkg.dev"
        _PLATFORM                 = "managed"
        _VERSION                  = "production"
        _SERVICE_NAME             = "quotes-and-invoices"
        _TRIGGER_NAME             = "main"
      }
    }
  }

  cloudbuild_triggers_for_web_app = {
    main_trigger = {
      name                         = "web-app--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "true"
        _TRIGGER_NAME   = "main"
        _VERSION        = "production"
      }
    }
  }

  cloudbuild_triggers_for_firebase = {
    main_trigger = {
      name                         = "firebase--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _TRIGGER_NAME = "main"
        _VERSION      = "production"
      }
    }
  }

  # Cloudrun inputs
  container_concurrency = 80
  cloud_run_app_api_env_variables = [
    {
      "name"  = "CONTENT_API_PROJECT_ID"
      "value" = "content-api-production-36346"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Warning"
    },
    {
      "name"  = "SERVICE_ACCOUNT_ID"
      "value" = "<EMAIL>"
    },
    {
      "name"  = "TRADE_APP_WEB_BASE_URL"
      "value" = "https://membersapp.checkatrade.com"
    },
    {
      "name"  = "ZUORA_ENDPOINT"
      "value" = "https://rest.eu.zuora.com"
    },
    {
      "name"  = "ZUORA_HOSTED_PAGE_URL"
      "value" = "https://eu.zuora.com/apps/PublicHostedPageLite.do"
    },
    {
      "name"  = "ZUORA_DD_HOSTED_PAGE_ID"
      "value" = "8a28fd5694dac8a40194db09ce6d1c2c"
    },
    {
      "name"  = "CORS_OPTIONS_ALLOWED_ORIGIN"
      "value" = "https://membersapp.checkatrade.com"
    },
    {
      "name"  = "OIDC_DISCOVERY_URL"
      "value" = "https://api.checkatrade.com/v1/identity/auth/realms/trade/.well-known/openid-configuration"
    },
    {
      "name"  = "CONTENT_API_URL"
      "value" = "https://content-api-2o3rbep6jq-nw.a.run.app"
    }
  ]

  cloud_run_receiver_env_variables = [
    {
      "name"  = "CONTENT_API_PROJECT_ID"
      "value" = "content-api-production-36346"
    },
    {
      "name"  = "RECEIVER_API_HOST"
      "value" = "https://pubsub-receiver-3rn2eua6jq-nw.a.run.app"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Warning"
    },
    {
      "name"  = "DOCUMENT_STORE"
      "value" = "document-store-263011460521"
    },
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "cat-trades.appspot.com"
    }
  ]

  cloud_run_bulk_receiver_env_variables = [
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "cat-trades.appspot.com"
    }
  ]

  cloud_run_image_events_env_variables = [
    {
      "name"  = "WORKFLOW_PROJECT"
      "value" = "image-service-production-14602"
    },
    {
      "name"  = "WORKFLOW_LOCATION"
      "value" = "europe-west1"
    },
    {
      "name"  = "WORKFLOW_NAME"
      "value" = "image-service"
    },
    {
      "name"  = "FirestoreConfig__ProjectId"
      "value" = "cat-trades"
    }
  ]

  cloud_run_member_data_env_variables = [
    {
      "name"  = "GOOGLE_IDENTITY_TOKEN_PROJECT"
      "value" = "cat-trades"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Warning"
    },
    {
      "name"  = "CORS_ALLOWED_ORIGINS"
      "value" = "https://membersapp.checkatrade.com"
    },
    {
      "name"  = "SALESFORCE_HOST"
      "value" = "checkatrade.my.salesforce.com"
    },
    {
      "name"  = "SALESFORCE_TOKEN_URL"
      "value" = "https://checkatrade.my.salesforce.com/services/oauth2/token"
    },
    {
      "name"  = "SALESFORCE_API_VERSION"
      "value" = "57.0"
    },
    {
      "name"  = "FIREBASE_PROJECT",
      "value" = "cat-trades"
    },
    {
      "name"  = "MEMBER_DATA_API_HOST"
      "value" = "https://member-data-3rn2eua6jq-nw.a.run.app"
    },
    {
      "name"  = "SECURE_CONTACTS_API_BASE_URL"
      "value" = "https://secure-contacts-api-knfbjqcnfa-nw.a.run.app/api/"
    },
    {
      "name"  = "DD_SERVICE"
      "value" = "member-data"
    }
  ]

  cloud_run_quotes_and_invoices_env_variables = [
    {
      "name"  = "FirestoreConfig__ProjectId"
      "value" = "cat-trades"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Error"
    },
    {
      "name"  = "EMAIL_SERVICE_PROJECT_ID"
      "value" = "email-service-prod-17114"
    },
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "cat-trades.appspot.com"
    },
    {
      "name"  = "DD_TRACE_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "DD_APM_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "ENABLE_PDF_GENERATOR_API"
      "value" = "true"
    },
    {
      "name"  = "PDF_GENERATOR_API_ENDPOINT"
      "value" = "https://pdf-generator-uvzg6cqk2a-nw.a.run.app"
    }
  ]

  cloud_run_receiver_parameters = {
    max_scale     = 100
    min_scale     = 1
    initial_scale = 1
  }

  cloud_run_bulk_receiver_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_image_events_api_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_member_data_api_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_quotes_and_invoices_parameters = {
    cpu                   = "4000m"
    memory                = "2048Mi"
    max_scale             = 30
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 40
  }

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  general_notification_targets              = "@slack-trade-experience-alerts-prod"
  mobile_platform_team_notification_targets = "@slack-trade-experience-mobile-platform-alerts"
  firestore_notification_targets            = "@slack-trade-experience-alerts-prod"

  # Cloud Storage Lifecycle
  cloud_storage_trade_experience_lifecycle_age = "760"
  cloud_storage_cors_allowed_origins           = ["https://membersapp.checkatrade.com", "https://www.checkatrade.com"]

  # Cloud Storage CORS
  document_storage_cors   = local.advisor_ui_cors_block
  temp_image_storage_cors = local.advisor_ui_cors_block

  capi_service_account_domain = "capi-production-21756.iam.gserviceaccount.com"

  capi_service_accounts = [
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    #Shared service accounts
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
  ]

  core_firebase_service_accounts = ["serviceAccount:capi-production-21756.svc.id.goog[core-quoting/quoting-firebase-storage]"]
}
