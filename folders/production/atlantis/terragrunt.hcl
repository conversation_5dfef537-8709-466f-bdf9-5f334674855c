terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    development = "dev"
    production  = "prod"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = false
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]
}

inputs = {
  project_name = local.project_name
  environment  = local.environment

  atlantis_vm_machine_type = "c2-standard-8"

  # See https://www.runatlantis.io/docs/server-configuration.html#flags for documentation
  atlantis_settings = {
    # Checkatrade comma-separated list read from the server-side pre-workflow hook
    RUNNER_SUPPORTED_FOLDERS = "development,dev,staging,core,poc,production,business"

    ATLANTIS_ALLOW_COMMANDS  = "version,plan,apply,unlock,approve_policies"
    ATLANTIS_ALLOW_DRAFT_PRS = "false"

    # Do not automerge if a repo uses more than 1 runner as it would automerge as soon as one runner finishes
    ATLANTIS_AUTOMERGE = "true"

    # See https://www.runatlantis.io/docs/checkout-strategy.html
    ATLANTIS_CHECKOUT_STRATEGY = "merge"
    ATLANTIS_DISABLE_APPLY     = "false"

    # Requires the rego policy checks to be deployed
    # see https://www.runatlantis.io/docs/policy-checking.html#conftest-policy-checking
    # and https://www.runatlantis.io/docs/server-side-repo-config.html#reference
    ATLANTIS_ENABLE_POLICY_CHECKS = "true"

    ATLANTIS_EXECUTABLE_NAME   = "tf"
    ATLANTIS_GH_TEAM_ALLOWLIST = "*:version,*:plan,*:apply,*:unlock,gcp-devops:approve_policies"
    ATLANTIS_LOG_LEVEL         = "warn"

    # How much plan and apply you can run in parallel, Atlantis default is 15
    ATLANTIS_PARALLEL_POOL_SIZE = "30"

    # Comma-separated list
    ATLANTIS_REPO_ALLOWLIST = "github.com/cat-home-experts/gcp-terraform,github.com/cat-home-experts/aws-terraform,github.com/cat-home-experts/azure-terraform"

    ATLANTIS_VCS_STATUS_NAME = "atlantis-${local.env}"

    SNYK_SEVERITY_THRESHOLD = "medium"
  }

  atlantis_image_triggers = {
    atlantis_trigger = {
      name                 = "Push-to-Main-for-Atlantis-Image"
      description          = "Builds and releases to ${local.environment} registry on a commit to main."
      push_trigger_enabled = true
      branch_regex         = "^main$"
    }
  }
}
