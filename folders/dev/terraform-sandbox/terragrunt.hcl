terraform {
  source = "./../../..//projects/terraform-sandbox"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]
}

inputs = {
  # Default inputs
  environment    = "dev"
  project_folder = "development"
  project_name   = basename(get_terragrunt_dir())



  cloudbuild_triggers = {
    # Triggers for frontend-strapi
    devTrigger = {
      name                         = "feature"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "java-api-template"
      branch_regex                 = "^main"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "feature"
        _SERVICE_NAME   = "hello-world-api"
        _VERSION        = "dev"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = true
      }
      excluded_files_filter = ["HELP.md", "**/target", ".idea", ".mvn", ".*ignore"]
    }
  }
}
