terraform {
  source = "./../../..//projects/trade-experience"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  # Custom groups
  iam_build_triggers_for_member_data = ["member-data--push"]

  viewer_access = [
    "domain:cathex.io",
  ]
  team_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  owner_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  storage_access = [
    "group:<EMAIL>",
  ]
  trade_admins = [
    "group:<EMAIL>",
  ]
  firebase_editor_access = [
    "group:<EMAIL>",
  ]

  advisor_ui_cors_block = [{
    "origin" : ["https://trade-experience-admin-dev.checkatrade.com", "http://localhost:5555"],
    "method" : ["GET", "PUT"],
    "response_header" : ["*"],
    "max_age_seconds" : 3600
  }]
}

inputs = {
  # Default inputs
  environment    = "dev"
  project_folder = "development"

  content_api_project_id        = "content-api-dev-18783"
  data_stats_service_project_id = "data-stats-service-dev-24217"
  salesforce_integ_project_id   = "salesforce-integ-dev-39443"

  firebase_default_bucket = "trade-experience-dev-35966.appspot.com"

  consent_screen_app_title = "Members App Development" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project

  project_static_permissions = {
    "roles/run.admin" : local.team_access,
    "roles/firebase.analyticsViewer" : local.team_access,
    "roles/iam.serviceAccountUser" : local.team_access
    "roles/firebase.admin" : local.owner_access,
    "roles/appengine.appAdmin" : local.owner_access,
    "roles/secretmanager.admin" : local.owner_access,
    "roles/serviceusage.apiKeysAdmin" : local.owner_access,
    "roles/cloudscheduler.admin" : local.owner_access,
    "roles/apigateway.admin" : local.owner_access,
    "roles/apigateway.viewer" : local.owner_access,
    "roles/appengine.deployer" : local.owner_access,
    "roles/cloudbuild.builds.editor" : local.owner_access,
    "roles/firebase.developAdmin" : local.owner_access,
    "roles/firebase.growthAdmin" : local.firebase_editor_access
    "roles/datastore.owner" : local.owner_access,
    "roles/logging.admin" : local.owner_access,
    "roles/pubsub.admin" : local.owner_access,
    "roles/secretmanager.viewer" : local.owner_access,
    "roles/cloudscheduler.jobRunner" : local.owner_access,
    "roles/storage.objectAdmin" : flatten([
      local.storage_access,
      local.team_access,
    ])
    "roles/iam.serviceAccountTokenCreator" : local.team_access,
    "roles/iap.admin" : local.trade_admins,
  }

  # Accounts for team defined IAM permissions
  viewer_access  = local.viewer_access
  owner_access   = local.owner_access
  storage_access = local.storage_access
  team_access    = local.team_access

  # Custom build trigger permissions
  iam_build_triggers_for_member_data = local.iam_build_triggers_for_member_data

  # App Engine custom domain inputs
  app_engine_friendly_domain_name  = "membersapp-development.checkatrade.com"
  app_engine_custom_certificate_id = "********"
  app_engine_iap_enabled           = true

  # Cloudbuild inputs
  cloud_run_suffix = "fzulqcpgba-nw.a.run.app"


  cloudbuild_triggers_for_requester = {
    dev_trigger = {
      name                         = "requester--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _GCP_REGION             = "europe-west2"
        _GCR_HOSTNAME           = "europe-west2-docker.pkg.dev"
        _PLATFORM               = "managed"
        _REQUESTER_SERVICE_NAME = "app-api"
        _TRIGGER_NAME           = "push"
        _VERSION                = "dev"
        _ENABLE_TRAFFIC         = "false"
      }
    }
    main_trigger = {
      name                         = "requester--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION             = "europe-west2"
        _GCR_HOSTNAME           = "europe-west2-docker.pkg.dev"
        _PLATFORM               = "managed"
        _REQUESTER_SERVICE_NAME = "app-api"
        _TRIGGER_NAME           = "main"
        _VERSION                = "dev"
        _ENABLE_TRAFFIC         = "false"
      }
    }
  }

  cloudbuild_triggers_for_receiver = {
    dev_trigger = {
      name                         = "receiver--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _GCP_REGION            = "europe-west2"
        _GCR_HOSTNAME          = "europe-west2-docker.pkg.dev"
        _PLATFORM              = "managed"
        _RECEIVER_SERVICE_NAME = "pubsub-receiver"
        _TRIGGER_NAME          = "push"
        _VERSION               = "dev"
        _ENABLE_TRAFFIC        = "false"
      }
    }
    main_trigger = {
      name                         = "receiver--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION            = "europe-west2"
        _GCR_HOSTNAME          = "europe-west2-docker.pkg.dev"
        _PLATFORM              = "managed"
        _RECEIVER_SERVICE_NAME = "pubsub-receiver"
        _TRIGGER_NAME          = "main"
        _VERSION               = "dev"
        _ENABLE_TRAFFIC        = "false"
      }
    }
  }

  cloudbuild_triggers_for_firestore_cloud_functions = {
    dev_trigger = {
      name                         = "firestore-func--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _DOCUMENT_STORE                                               = "document-store-550001349983"
        _EMAIL_SERVICE_PROJECT_ID                                     = "email-service-dev-25125"
        _ENABLE_SALESFORCE_FOR_PROFILE_DESCRIPTION_AND_SEARCH_PREVIEW = "true"
        _FIREBASE_DEFAULT_BUCKET                                      = "trade-experience-dev-35966.appspot.com"
        _SALESFORCE_INTEG_PROJECT_ID                                  = "salesforce-integ-dev-39443"
        _TRIGGER_NAME                                                 = "push"
      }
    }
    main_trigger = {
      name                         = "firestore-func--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _DOCUMENT_STORE                                               = "document-store-550001349983"
        _EMAIL_SERVICE_PROJECT_ID                                     = "email-service-dev-25125"
        _ENABLE_SALESFORCE_FOR_PROFILE_DESCRIPTION_AND_SEARCH_PREVIEW = "true"
        _FIREBASE_DEFAULT_BUCKET                                      = "trade-experience-dev-35966.appspot.com"
        _SALESFORCE_INTEG_PROJECT_ID                                  = "salesforce-integ-dev-39443"
        _TRIGGER_NAME                                                 = "main"
      }
    }
  }

  cloudbuild_triggers_for_api_gateway = {
    dev_trigger = {
      name                         = "api-gateway--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _API_GATEWAY_NAME     = "trade-gateway-gw"
        _API_GATEWAY_API_NAME = "trade-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "app-api"
        _TRIGGER_NAME         = "push"
        _VERSION              = "development"
      }

    }
    main_trigger = {
      name                         = "api-gateway--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "trade-gateway-gw"
        _API_GATEWAY_API_NAME = "trade-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "app-api"
        _TRIGGER_NAME         = "main"
        _VERSION              = "development"
      }
    }
  }

  cloudbuild_triggers_for_member_api_gateway = {
    dev_trigger = {
      name                         = "member-api-gateway--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _API_GATEWAY_NAME     = "member-gateway-gw"
        _API_GATEWAY_API_NAME = "member-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "member-data"
        _TRIGGER_NAME         = "push"
        _VERSION              = "development"
      }
    }
    main_trigger = {
      name                         = "member-api-gateway--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_GATEWAY_NAME     = "member-gateway-gw"
        _API_GATEWAY_API_NAME = "member-gateway"
        _GCP_REGION           = "europe-west2"
        _PLATFORM             = "managed"
        _SERVICE_NAME         = "member-data"
        _TRIGGER_NAME         = "main"
        _VERSION              = "development"
      }
    }
  }

  cloudbuild_triggers_for_bulk_receiver = {
    dev_trigger = {
      name                         = "bulk-receiver--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _GCP_REGION     = "europe-west2"
        _GCR_HOSTNAME   = "europe-west2-docker.pkg.dev"
        _PLATFORM       = "managed"
        _TRIGGER_NAME   = "push"
        _VERSION        = "dev"
      }

    }
    main_trigger = {
      name                         = "bulk-receiver--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _GCP_REGION     = "europe-west2"
        _GCR_HOSTNAME   = "europe-west2-docker.pkg.dev"
        _PLATFORM       = "managed"
        _TRIGGER_NAME   = "main"
        _VERSION        = "dev"
      }
    }
  }

  cloudbuild_triggers_for_document_store = {
    dev_trigger = {
      name                         = "doc-store--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _ENVIRONMENT  = "dev"
        _TRIGGER_NAME = "push"
      }
    }
    main_trigger = {
      name                         = "doc-store--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENVIRONMENT  = "dev"
        _TRIGGER_NAME = "main"
      }
    }
  }

  cloudbuild_triggers_for_image_events = {
    dev_trigger = {
      name                         = "img-events--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _API_SERVICE_NAME = "image-events"
        _ENABLE_TRAFFIC   = "false"
        _GCP_REGION       = "europe-west2"
        _GCR_HOSTNAME     = "europe-west2-docker.pkg.dev"
        _PLATFORM         = "managed"
        _TRIGGER_NAME     = "push"
        _VERSION          = "dev"
      }
    }
    main_trigger = {
      name                         = "img-events--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME = "image-events"
        _ENABLE_TRAFFIC   = "false"
        _GCP_REGION       = "europe-west2"
        _GCR_HOSTNAME     = "europe-west2-docker.pkg.dev"
        _PLATFORM         = "managed"
        _TRIGGER_NAME     = "main"
        _VERSION          = "dev"
      }
    }
  }

  cloudbuild_triggers_for_member_data = {
    dev_trigger = {
      name                         = "member-data--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _API_SERVICE_NAME               = "member-data"
        _CORS_ALLOWED_ORIGINS           = "https://membersapp-development.checkatrade.com,http://localhost:19006"
        _ENABLE_TRAFFIC                 = "false"
        _GCP_REGION                     = "europe-west2"
        _GCR_HOSTNAME                   = "europe-west2-docker.pkg.dev"
        _GOOGLE_IDENTITY_TOKEN_PROJECT  = "trade-experience-dev-35966"
        _PLATFORM                       = "managed"
        _SALESFORCE_API_VERSION         = "57.0"
        _SALESFORCE_HOST                = "checkatrade--uatpartial.sandbox.my.salesforce.com"
        _SALESFORCE_TOKEN_URL           = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"
        _TEST_CHECKATRADE_USER_ID       = "9638096e-ced1-404e-a9ef-507cfbfe585f"
        _TEST_COMPANY_ID                = "918283"
        _MEMBER_PROMISE_TEST_COMPANY_ID = "337953"
        _TRIGGER_NAME                   = "push"
        _VERSION                        = "dev"
        _SECURE_CONTACTS_API_BASE_URL   = "https://secure-contacts-api-5krtc7h7da-nw.a.run.app/api/"
      }
    }
    main_trigger = {
      name                         = "member-data--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _API_SERVICE_NAME               = "member-data"
        _ENABLE_TRAFFIC                 = "false"
        _GCP_REGION                     = "europe-west2"
        _GCR_HOSTNAME                   = "europe-west2-docker.pkg.dev"
        _GOOGLE_IDENTITY_TOKEN_PROJECT  = "trade-experience-dev-35966"
        _PLATFORM                       = "managed"
        _SALESFORCE_API_VERSION         = "57.0"
        _SALESFORCE_HOST                = "checkatrade--uatpartial.sandbox.my.salesforce.com"
        _SALESFORCE_TOKEN_URL           = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"
        _CORS_ALLOWED_ORIGINS           = "https://membersapp-development.checkatrade.com,http://localhost:19006"
        _TEST_CHECKATRADE_USER_ID       = "9638096e-ced1-404e-a9ef-507cfbfe585f"
        _TEST_COMPANY_ID                = "918283"
        _MEMBER_PROMISE_TEST_COMPANY_ID = "337953"
        _TRIGGER_NAME                   = "main"
        _VERSION                        = "dev"
        _SECURE_CONTACTS_API_BASE_URL   = "https://secure-contacts-api-5krtc7h7da-nw.a.run.app/api/"
      }
    }
  }

  cloudbuild_triggers_for_quotes_and_invoices = {
    dev_trigger = {
      name                         = "quotes-and-inv--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID = "email-service-dev-25125"
        _ENABLE_TRAFFIC           = "false"
        _GCP_REGION               = "europe-west2"
        _GCR_HOSTNAME             = "europe-west2-docker.pkg.dev"
        _PLATFORM                 = "managed"
        _SERVICE_NAME             = "quotes-and-invoices"
        _TRIGGER_NAME             = "push"
        _VERSION                  = "dev"
      }
    }
    main_trigger = {
      name                         = "quotes-and-inv--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID = "email-service-dev-25125"
        _ENABLE_TRAFFIC           = "false"
        _GCP_REGION               = "europe-west2"
        _GCR_HOSTNAME             = "europe-west2-docker.pkg.dev"
        _PLATFORM                 = "managed"
        _SERVICE_NAME             = "quotes-and-invoices"
        _TRIGGER_NAME             = "main"
        _VERSION                  = "dev"
      }
    }
  }

  cloudbuild_triggers_for_web_app = {
    dev_trigger = {
      name                         = "web-app--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _TRIGGER_NAME   = "push"
        _VERSION        = "dev"
      }
    }
    main_trigger = {
      name                         = "web-app--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _ENABLE_TRAFFIC = "false"
        _TRIGGER_NAME   = "main"
        _VERSION        = "dev"
      }
    }
  }

  cloudbuild_triggers_for_firebase = {
    dev_trigger = {
      name                         = "firebase--push"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any non-main commit."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _TRIGGER_NAME = "push"
        _VERSION      = "dev"
      }
    }
    main_trigger = {
      name                         = "firebase--main"
      description                  = "Builds to ${basename(dirname(get_terragrunt_dir()))} on any push to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _TRIGGER_NAME = "main"
        _VERSION      = "dev"
      }
    }
  }

  # Cloudrun inputs
  container_concurrency = 80
  cloud_run_app_api_env_variables = [
    {
      "name"  = "CONTENT_API_PROJECT_ID"
      "value" = "content-api-dev-18783"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "SERVICE_ACCOUNT_ID"
      "value" = "<EMAIL>"
    },
    {
      "name"  = "TRADE_APP_WEB_BASE_URL"
      "value" = "https://membersapp-development.checkatrade.com"
    },
    {
      "name"  = "ZUORA_ENDPOINT"
      "value" = "https://rest.test.eu.zuora.com"
    },
    {
      "name"  = "ZUORA_HOSTED_PAGE_URL"
      "value" = "https://test.eu.zuora.com/apps/PublicHostedPageLite.do"
    },
    {
      "name"  = "CORS_OPTIONS_ALLOWED_ORIGIN"
      "value" = "https://membersapp-development.checkatrade.com,http://localhost:19006"
    },
    {
      "name"  = "CONTENT_API_URL"
      "value" = "https://content-api-x2k2mja74q-nw.a.run.app"
    }
  ]

  cloud_run_receiver_env_variables = [
    {
      "name"  = "CONTENT_API_PROJECT_ID"
      "value" = "content-api-dev-18783"
    },
    {
      "name"  = "RECEIVER_API_HOST"
      "value" = "https://pubsub-receiver-fzulqcpgba-nw.a.run.app"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "DOCUMENT_STORE"
      "value" = "document-store-550001349983"
    },
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "trade-experience-dev-35966.appspot.com"
    }
  ]

  cloud_run_bulk_receiver_env_variables = [
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "trade-experience-dev-35966.appspot.com"
    }
  ]

  cloud_run_image_events_env_variables = [
    {
      "name"  = "WORKFLOW_PROJECT"
      "value" = "image-service-dev-36389"
    },
    {
      "name"  = "WORKFLOW_LOCATION"
      "value" = "europe-west1"
    },
    {
      "name"  = "WORKFLOW_NAME"
      "value" = "image-service"
    },
    {
      "name"  = "FirestoreConfig__ProjectId"
      "value" = "trade-experience-dev-35966"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
  ]

  cloud_run_member_data_env_variables = [
    {
      "name"  = "GOOGLE_IDENTITY_TOKEN_PROJECT"
      "value" = "trade-experience-dev-35966"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "CORS_ALLOWED_ORIGINS"
      "value" = "https://membersapp-development.checkatrade.com, http://localhost:19006"
    },
    {
      "name"  = "SALESFORCE_HOST"
      "value" = "checkatrade--uatpartial.sandbox.my.salesforce.com"
    },
    {
      "name"  = "SALESFORCE_TOKEN_URL"
      "value" = "https://checkatrade--uatpartial.sandbox.my.salesforce.com/services/oauth2/token"
    },
    {
      "name"  = "SALESFORCE_API_VERSION"
      "value" = "57.0"
    },
    {
      "name"  = "FIREBASE_PROJECT",
      "value" = "trade-experience-dev-35966"
    },
    {
      "name"  = "MEMBER_DATA_API_HOST"
      "value" = "https://member-data-fzulqcpgba-nw.a.run.app"
    },
    {
      "name"  = "SECURE_CONTACTS_API_BASE_URL"
      "value" = "https://secure-contacts-api-5krtc7h7da-nw.a.run.app/api/"
    },
    {
      "name"  = "DD_SERVICE"
      "value" = "member-data"
    }
  ]

  cloud_run_quotes_and_invoices_env_variables = [
    {
      "name"  = "FirestoreConfig__ProjectId"
      "value" = "trade-experience-dev-35966"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
    {
      "name"  = "EMAIL_SERVICE_PROJECT_ID"
      "value" = "email-service-dev-25125"
    },
    {
      "name"  = "FIREBASE_DEFAULT_BUCKET"
      "value" = "trade-experience-dev-35966.appspot.com"
    },
    {
      "name"  = "DD_TRACE_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "DD_APM_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "ENABLE_PDF_GENERATOR_API"
      "value" = "true"
    },
    {
      "name"  = "PDF_GENERATOR_API_ENDPOINT"
      "value" = "https://pdf-generator-653rsxe7mq-nw.a.run.app"
    }
  ]

  cloud_run_receiver_parameters = {
    max_scale = 30
  }

  cloud_run_bulk_receiver_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_image_events_api_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_member_data_api_parameters = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    max_scale             = 20
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 80
  }

  cloud_run_quotes_and_invoices_parameters = {
    cpu                   = "4000m"
    memory                = "2048Mi"
    max_scale             = 30
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 40
  }

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  general_notification_targets              = "@slack-trade-experience-alerts-non-prod"
  mobile_platform_team_notification_targets = "@slack-trade-experience-alerts-non-prod"
  firestore_notification_targets            = "@slack-trade-experience-alerts-non-prod"

  # Cloud Storage Lifecycle
  cloud_storage_trade_experience_lifecycle_age = "30"
  cloud_storage_cors_allowed_origins           = ["https://membersapp-development.checkatrade.com", "http://localhost:19006", "http://localhost:3000"]

  # Cloud Storage CORS
  document_storage_cors   = local.advisor_ui_cors_block
  temp_image_storage_cors = local.advisor_ui_cors_block

  capi_service_account_domain = "capi-staging-27323.iam.gserviceaccount.com"
}
