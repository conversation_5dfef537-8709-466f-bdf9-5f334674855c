terraform {
  source = "./../../..//projects/stats-service"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    "../../../projects/${local.project_name}/template/*.tpl*"
  ]
}

inputs = {
  # Default inputs
  project_name   = "stats-service"
  environment    = "dev"
  project_folder = "development"

  sink_destination_bucket = "sre-logs"

  team_access = ["group:<EMAIL>"]

  cloudbuild_triggers = {
    # Triggers for frontend-strapi
    devTrigger = {
      name                         = "feature"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "stats-service"
      branch_regex                 = "^master$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "feature"
        _SERVICE_NAME   = "stats-service"
        _VERSION        = "dev"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = true
      }
      excluded_files_filter = []
    }
    stageTrigger = {
      name                         = "pr-to-main"
      description                  = "Builds and releases to staging on pr to main."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "stats-service"
      branch_regex                 = "^master$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "pr-to-main"
        _SERVICE_NAME   = "stats-service"
        _VERSION        = "dev"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = false
      }
      excluded_files_filter = []
    }
    prodTrigger = {
      name                         = "main"
      description                  = "Builds and releases to production from main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "stats-service"
      branch_regex                 = "^master$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "main"
        _SERVICE_NAME   = "stats-service"
        _VERSION        = "dev"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = false
      }
      excluded_files_filter = []
    }
  }

  # Cloud Run vars
  container_concurrency = 3
  initial_scale         = 0
  min_scale             = 0
  max_scale             = 1

  cloud_run_env_variables = [
    {
      "name"  = "CACHE_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "RACK_ENV"
      "value" = "development"
    },
  ]
}
