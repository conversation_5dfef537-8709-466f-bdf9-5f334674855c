terraform {
  source = "./../../..//projects/content-api"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  reviews_team       = ["group:<EMAIL>"]
  reviews_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.reviews_team,
    local.reviews_team_admin,
    local.viewers,
  )

  salesforce_api_uri = "https://checkatrade--systest.sandbox.my.salesforce.com"
}

inputs = {

  # --- Salesforce settings ---
  salesforce_api_uri   = local.salesforce_api_uri
  salesforce_id_server = "${local.salesforce_api_uri}/services/oauth2/token"

  # Standard inputs
  region         = "europe-west2"
  environment    = "dev"
  project_folder = "development"
  project_name   = basename(get_terragrunt_dir())

  project_static_permissions = {
    "roles/run.invoker" : concat([
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>"
    ])
    "roles/pubsub.publisher" : concat(
      local.project_team,
      local.project_team_admin
    )
    "roles/pubsub.subscriber" : concat(
      local.project_team,
      local.project_team_admin
    )
    "roles/run.admin" : concat(
      local.project_team_admin,
      local.project_team
    ),
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin
    ),
    "roles/pubsub.admin" : concat(
    local.project_team_admin),
    "roles/errorreporting.user" : concat(
      local.project_team,
      local.project_team_admin
    ),
    "roles/cloudscheduler.admin" : concat([
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>",
      "group:<EMAIL>"
    ]),
    "roles/viewer" : concat(
      local.everyone,
    ),
    "roles/cloudbuild.builds.viewer" : concat(
      local.project_team,
      local.project_team_admin
    ),
    "roles/secretmanager.secretVersionAdder" : concat(
      local.project_team_admin,
    ),
    "roles/secretmanager.secretVersionManager" : concat(
      local.project_team_admin,
    )
  }


  trade_experience_project_id = "trade-experience-dev-35966"
  consumer_area_project_id    = "consumer-area-dev-47121"
  reviews_project_id          = "reviews-dev-44869"
  jobs_management_project_id  = "jobs-management-dev-33113"
  salesforce_integ_project_id = "salesforce-integ-dev-39443"

  cloudsql_instance = "data-services-52314123:europe-west2:data-services-dev-db"



  # Cloudbuild inputs
  cloudbuild_triggers = {
    devTrigger = {
      name                         = "Feature"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-data-services-content-api"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION    = "europe-west2"
        _TRIGGER_NAME  = "feature"
        _SERVICE_NAME  = "content-api"
        _VERSION       = "dev"
        _PLATFORM      = "managed"
        _GCR_HOSTNAME  = "eu.gcr.io"
        _CLOUD_RUN_URL = "https://content-api-x2k2mja74q-nw.a.run.app"
      }
      included_files_filter = []
    },
    stagingTrigger = {
      name                         = "PR-to-Main"
      description                  = "Builds and releases to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-data-services-content-api"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION    = "europe-west2"
        _TRIGGER_NAME  = "pr-to-main"
        _SERVICE_NAME  = "content-api"
        _VERSION       = "dev"
        _PLATFORM      = "managed"
        _GCR_HOSTNAME  = "eu.gcr.io"
        _CLOUD_RUN_URL = "https://content-api-x2k2mja74q-nw.a.run.app"
      }
      included_files_filter = []
    },
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-data-services-content-api"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION    = "europe-west2"
        _TRIGGER_NAME  = "main"
        _SERVICE_NAME  = "content-api"
        _VERSION       = "dev"
        _PLATFORM      = "managed"
        _GCR_HOSTNAME  = "eu.gcr.io"
        _CLOUD_RUN_URL = "https://content-api-x2k2mja74q-nw.a.run.app"
      }
      included_files_filter = []
    }
  }
  schedule_cloudbuild_triggers = {
    scheduledTrigger = {
      name                         = "Scheduled"
      description                  = "Builds and releases to dev on a schedule."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      manual_trigger_enabled       = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-data-services-content-api"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "schedule_cloud_build.yaml"
      env_variables = {
        _GCP_REGION    = "europe-west2"
        _TRIGGER_NAME  = "feature"
        _SERVICE_NAME  = "content-api"
        _VERSION       = "dev"
        _PLATFORM      = "managed"
        _GCR_HOSTNAME  = "eu.gcr.io"
        _CLOUD_RUN_URL = "https://content-api-x2k2mja74q-nw.a.run.app"
      }
      included_files_filter = []
    }
  }
  # Cloudrun inputs
  cloud_run_env_variables = [
    {
      "name"  = "CONTENT_API_HOST"
      "value" = ""
    },
    {
      "name"  = "ENABLE_DATALOGGING"
      "value" = "false"
    },
    {
      "name"  = "LOG_LEVEL_DATABASE"
      "value" = "Error"
    },
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Information"
    },
  ]

  cloud_scheduler_data_schedule_v6                          = "30 6 1 * *"
  cloud_scheduler_data_schedule_v6_all_members              = "30 23 1 * *"
  cloud_scheduler_data_schedule_v6_partial                  = "30 13 1 * *"
  cloud_scheduler_refresh_schedule_v6                       = "30 6 1 1 *"
  cloud_scheduler_full_refresh_schedule_v6                  = "30 6 1 1 *"
  cloud_scheduler_categories_schedule                       = "0 23 * * *"
  cloud_scheduler_categories_v2_schedule                    = "30 23 * * *"
  cloud_scheduler_reviews_bulk_schedule                     = "0 0 * * 1-5"
  cloud_scheduler_reviews_basic_bulk_schedule               = "1-59/5 * * * 1-5"
  cloud_scheduler_reviews_all_schedule                      = "0 0 31 12 *"
  cloud_scheduler_trade_message_version_v6                  = "ProfileV6"
  cloud_scheduler_work_alert_preference_data_schedule       = "0 6 1 * *"
  cloud_scheduler_work_alert_preference_refresh_schedule    = "0 6 1 1 *"
  cloud_scheduler_work_alert_preference_v2_data_schedule    = "0 4 1 * *"
  cloud_scheduler_work_alert_preference_v2_refresh_schedule = "0 4 1 1 *"

  # Pubsub Alert variables
  alert_duration  = "300s"
  threshold_value = "10"
  trigger_count   = "1"

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  # AWS API Gateway hosts for DI Subscriptions
  trade_res_di_api_gateway_host      = "https://yvvmlaxeo7.execute-api.eu-west-2.amazonaws.com/dev/events"
  trade_response_di_api_gateway_host = "https://fy0geew7le.execute-api.eu-west-2.amazonaws.com/dev/events"

}
