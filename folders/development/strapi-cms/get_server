#!/bin/bash
# This script should be placed in /opt/cathex and be executable
project=$(curl --silent         "http://metadata.google.internal/computeMetadata/v1/project/project-id"         -H "Metadata-Flavor: Google")

sql_address=$(gcloud sql instances list --project="${project}" --filter="NAME=${project}-db" --format="value(PRIVATE_ADDRESS)" 2>/dev/null) || sql_address="error"
 
echo "${sql_address}"
