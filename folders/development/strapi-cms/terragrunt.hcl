terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  app_engine_friendly_domain_name           = "cms${local.env == "prod" ? "" : "-${local.environment}"}.checkatrade.com"
  strapi_images_bucket_friendly_domain_name = "cms-images${local.env == "prod" ? "" : "-${local.environment}"}.checkatrade.net"

  cloud_build_env_vars = {
    _VERSION            = local.env
    _TRIGGER_NAME       = "${local.project_name}-${local.env}"
    _FRIENDLY_URL       = "${local.app_engine_friendly_domain_name}"
    _STRAPI_ASSETS_URL  = "${local.strapi_images_bucket_friendly_domain_name}"
    _ENABLE_TRAFFIC     = "true"
    _MACHINE_TYPE       = "F2"
    _ADMIN_MACHINE_TYPE = "F2"
  }

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  iap_web_users = ["group:<EMAIL>"]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  cloud_build_github_env_vars = {
    _PROJECT_ENV  = local.environment
    _VERSION      = local.env
    _TRIGGER_NAME = "Github CI Build"
  }
}

inputs = {
  project_name             = local.project_name
  environment              = local.env
  project_folder           = local.environment
  notifications            = "@opsgenie-Strapi-CMS"
  consent_screen_app_title = "Strapi CMS" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project

  app_engine_friendly_domain_name           = local.app_engine_friendly_domain_name
  strapi_images_bucket_friendly_domain_name = local.strapi_images_bucket_friendly_domain_name
  app_engine_custom_certificate_id          = "********"

  # cloud-sql related inputs
  region                       = "europe-west2"
  tier                         = "db-custom-2-8192"
  backup_configuration_enabled = false

  project_static_permissions = {
    "roles/appengine.appAdmin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iap.httpsResourceAccessor" : concat(
      local.everyone,
      local.iap_web_users,
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iam.serviceAccountTokenCreator" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.viewers,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
      local.viewers,
    )
    "roles/compute.osAdminLogin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudsql.admin" : concat(
      local.project_team_admin,
      local.project_team,
    )
  }

  # Cloudbuild inputs
  cloudbuild_triggers = {
    # Triggers for frontend-strapi
    devTrigger = {
      name                         = "Feature-for-Strapi"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "strapi"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
      excluded_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    }
    stagingTrigger = {
      name                         = "PR-to-Main-for-Strapi"
      description                  = "Builds and releases to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "strapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
      excluded_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    }
    productionTrigger = {
      name                         = "Main-for-Strapi"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "strapi"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars
      excluded_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    }
  }
  cloudbuild_plain_triggers = {
    githubCITrigger = {
      name                         = "GitHub-CI-Trigger"
      description                  = "Builds CLI image for Github to enable comments."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "strapi"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "gcp-scripts/cloudbuild-github-image.yaml"
      env_variables                = local.cloud_build_github_env_vars
      included_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    }
  }

  # Redis

  redis_version           = "REDIS_6_X"
  memory_size_gb          = 1
  transit_encryption_mode = "DISABLED"
}
