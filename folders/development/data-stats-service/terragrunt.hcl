terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]

  organisation = "cat-home-experts"
  repo_name    = "trader-market-insights"

  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_env_vars = {
    data-stats-service-api = {
      _GCP_REGION   = "europe-west2"
      _SERVICE_NAME = "data-stats-service-api"
      _VERSION      = local.env
      _GCR_HOSTNAME = "eu.gcr.io"
      _PLATFORM     = "managed"
    }
    main-trigger = {
      _TRIGGER_NAME = "main"
    }
  }


  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name     = local.project_name
  environment      = local.environment
  provider_enabled = true

  env = local.env

  trade_experience_project_id = "trade-experience-dev-35966"
  comms_service_project_id    = "communication-dev-26905"

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/iap.httpsResourceAccessor" : concat(
      local.everyone,
    )
    "roles/logging.admin" : concat(
      local.project_team_admin,
    )
    "roles/errorreporting.user" : concat(
      local.project_team,
    )
    "roles/errorreporting.admin" : concat(
      local.project_team_admin,
    )
    "roles/cloudsql.admin" : concat(
      local.project_team_admin,
    )
    "roles/run.developer" : concat(
      local.project_team
    )
    "roles/run.admin" : concat(
      local.project_team_admin
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iam.serviceAccountAdmin" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountKeyAdmin" : concat(
      local.project_team_admin,
    )
    "roles/iap.admin" : concat(
      local.project_team_admin,
    )
    "roles/identityplatform.admin" : concat(
      local.project_team_admin,
    )
    "roles/artifactregistry.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/storage.admin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.editor" : concat(
      local.project_team,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
    )
    "roles/cloudscheduler.jobRunner" : concat(
      local.project_team_admin
    )
  }


  consent_screen_app_title = "Data Stats Service Dev"


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds latest PR merge to main"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = merge(local.cloud_build_env_vars["data-stats-service-api"], local.cloud_build_env_vars["main-trigger"])
      excluded_files_filter        = [] # Can be commented out
    }
  }

  cloud_run_env_variables = {
    data-stats-service-api = [
      {
        name  = "PRESTO_PORT"
        value = "8081"
      },
      {
        name  = "PRESTO_CATALOG"
        value = "hive"
      },
      {
        name  = "PRESTO_SCHEMA"
        value = "data"
      },
      {
        name  = "POSTGRES_CONNECTION_TYPE"
        value = "unix"
      },
      {
        name  = "POSTGRES_SCHEMA"
        value = "analytics"
      },
      {
        name  = "POSTGRES_PORT"
        value = "5432"
      },
      {
        name  = "POSTGRES_DATABASE"
        value = "analytics"
      },
      {
        name  = "ENABLE_MAPBOX_SCHEDULED_TASK"
        value = "false"
      }
    ]
  }


  cloud_run_parameters = {
    data-stats-service-api = {
      cpu                   = "1000m"
      memory                = "2048Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
      vpc_access_egress     = "all-traffic" # Can be 'all-traffic' or 'private-ranges-only'
    }
  }

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"


  cloudsql_instance = "data-stats-service-dev-24217:europe-west2:analytics-db"

  campaign_stream_di_api_gateway_host = "https://2q5e1ba429.execute-api.eu-west-2.amazonaws.com/dev/events"

  dynamic_pricing_logs_di_api_gateway_host = "https://psmsozi3f2.execute-api.eu-west-2.amazonaws.com/dev/events"

  reviews_trade_events_di_api_gateway_host = "https://c2blqu3efc.execute-api.eu-west-2.amazonaws.com/dev/events"

  capi_project_id = "capi-staging-27323"

  # Cloud Scheduler

  url_suffix = "upload_mapbox_tiles"
  schedule   = "45 5 * * *"

  http_resource_accessors = [
    "serviceAccount:<EMAIL>"
  ]

  # Core Services (CAPI)
  core_subscriber_service_account_email = "<EMAIL>"
}
