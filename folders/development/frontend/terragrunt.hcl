terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}


locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  app_engine_friendly_domain_name = "${local.env == "prod" ? "www" : "${local.project_name}-${local.environment}"}.checkatrade.com"

  map_env_to_cloudflare_cname = tomap({
    dev  = "-dev"
    stg  = "-staging"
    prod = ""
    int  = "-dev"
  })
  map_env_to_cloudflare_root_domain = tomap({
    dev  = ".net"
    stg  = ".net"
    prod = ".com"
    int  = ".net"
  })
  reviews_url         = "https://reviews${local.map_env_to_cloudflare_cname[local.env]}.checkatrade${local.map_env_to_cloudflare_root_domain[local.env]}/"
  trade_profile_url   = "https://trade-profile${local.map_env_to_cloudflare_cname[local.env]}.checkatrade${local.map_env_to_cloudflare_root_domain[local.env]}/"
  secure_contacts_url = "https://secure-contacts${local.map_env_to_cloudflare_cname[local.env]}.checkatrade${local.map_env_to_cloudflare_root_domain[local.env]}/"
  consumer_area_url   = "https://consumer-area${local.map_env_to_cloudflare_cname[local.env]}.checkatrade${local.map_env_to_cloudflare_root_domain[local.env]}/"
  search_url          = "https://search${local.map_env_to_cloudflare_cname[local.env]}.checkatrade.com/"
  jobs_management_url = "https://jobs${local.map_env_to_cloudflare_cname[local.env]}.checkatrade${local.map_env_to_cloudflare_root_domain[local.env]}/"

  map_env_to_wapi_cloudflare_url = tomap({
    dev  = "https://wapi.preview.checkatrade.com/"
    stg  = "https://wapi.preview.checkatrade.com/"
    prod = "https://wapi.checkatrade.com/"
    int  = "https://wapi.preview.checkatrade.com/"
  })

  map_env_to_heritage_cloudflare_url = tomap({
    dev  = "frontend-heritage-upstream-development.checkatrade.com/"
    stg  = "frontend-heritage-upstream-staging.checkatrade.com/"
    prod = "frontend-heritage-upstream-production.checkatrade.com/"
  })
  firebase_app_id  = "1:781662827407:web:47cdb691c28ddf970311fa"
  slash_id_auth_id = "06617ad3-6abd-76f0-9a00-a631bfffaa2c"

  cloud_build_env_vars = {
    _CHECKATRADE_API_URL                       = local.map_env_to_wapi_cloudflare_url[local.env]
    _CHECKATRADE_API_URL_SERVER                = local.map_env_to_wapi_cloudflare_url[local.env]
    _COMMUNITY_OAUTH_CLIENT_ID                 = "85dde57fdbfb43d5906230c8fc744197"
    _COMMUNITY_OAUTH_REDIRECT_URI              = "https://checkatrade.test.standingongiants.com/sso_callback/Homeowner"
    _CONSUMER_AREA_API_URL                     = local.consumer_area_url
    _ENABLE_TRAFFIC                            = "false"
    _FIREBASE_APP_ID                           = local.firebase_app_id
    _FIREBASE_MSECS_BETWEEN_USER_DATA_REQUESTS = "600000"
    _FRIENDLY_URL                              = local.app_engine_friendly_domain_name
    _MACHINE_TYPE                              = "F4"
    _REVIEWS_API_URL                           = local.reviews_url
    _SEARCH_API_URL                            = local.search_url
    _JOBS_MANAGEMENT_API_URL                   = local.jobs_management_url
    _SECURE_CONTACTS_API_URL                   = local.secure_contacts_url
    _SENTRY_CURRENT_ENV                        = "gcp-${local.environment}"
    _SERVE_STYLEGUIDE                          = "true"
    _SHOW_ACCOUNT                              = "true"
    _TRADE_PROFILE_API_URL                     = local.trade_profile_url
    _TRIGGER_NAME                              = "${local.project_name}-${local.env}"
    _URL_HOSTNAME                              = local.app_engine_friendly_domain_name
    _URL_JOIN_HOSTNAME                         = "join.checkatrade.com"
    _URL_MEMBERS_HOSTNAME                      = "membersapp-development.checkatrade.com"
    _USE_NEW_FLOW                              = "true"
    _VERSION                                   = local.env
    _AWS_TRACKING_ENV                          = "development"
    _USE_CROSS_DOMAIN_ANALYTICS_COOKIES        = "true"
    _GCR_HOSTNAME                              = "eu.gcr.io"
    _NEXTAUTH_URL                              = "https://${local.app_engine_friendly_domain_name}"
    _SLASH_ID_AUTH_ID                          = local.slash_id_auth_id
    _IDENTITY_SERVICE_URL                      = "https://api.staging.checkatrade.com/v1/identity/auth/realms/consumer"
    _NEXT_PUBLIC_CONSUMER_SERVICE_URL          = "https://api.staging.checkatrade.com/v1/consumer-app"
    _NEXT_PUBLIC_CONSUMER_PUBLIC_URL           = "https://api.staging.checkatrade.com/v1/consumer-public"
    _NEXT_PUBLIC_MATCHING_PUBLIC_URL           = "https://api.staging.checkatrade.com/v1/matching-public"
    _NEXT_PUBLIC_AD_SERVER_PUBLIC_URL          = "https://api.staging.checkatrade.com/v1/ad-server"
    _NEXT_PUBLIC_HEATABLE_SALES_FUNNEL_URL     = "https://dev-fe.heatable.co.uk/new-boilers/quote"
    _NEXT_PUBLIC_HEATABLE_PARTNER_ID           = "fdcf0ce4-a92b-442f-b93b-5caf3b4affb0"
    _NEXT_PUBLIC_ACCREDITATION_URL             = "https://storage.googleapis.com/frontend-dev-23775-accred-images"
    _NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT        = "staging"
    _NEXT_PUBLIC_CONTENTFUL_SPACE_ID           = "5kq8dse7hipf"
  }

  cloud_build_env_vars_main = merge(
    local.cloud_build_env_vars, {
      _ENABLE_TRAFFIC = "true"
    }
  )

  cloud_build_github_env_vars = {
    _PROJECT_ENV  = local.environment
    _VERSION      = local.env
    _TRIGGER_NAME = "Github CI Build"
  }

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}


inputs = {
  project_name             = local.project_name
  environment              = local.env
  project_folder           = local.environment
  notifications            = "@pagerduty-Frontend" ## !! We need to make this dynamic based on the folder name
  consent_screen_app_title = "Frontend Dev"        ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project

  app_engine_friendly_domain_name  = local.app_engine_friendly_domain_name
  app_engine_custom_certificate_id = "23360184"

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  project_static_permissions = {
    "roles/appengine.appAdmin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/aiplatform.admin" : concat(
      local.project_team_admin,
    )
    "roles/cloudfunctions.developer" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.viewers,
    )
    "roles/logging.viewer" : concat(
      local.project_team,
      local.project_team_admin,
      local.viewers,
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin,
    )
  }

  # Cloudbuild inputs
  cloudbuild_triggers = {
    # Triggers for frontend
    # devTrigger = {
    #   name                         = "Feature-for-Frontend"
    #   description                  = "Builds and releases to dev on any non main commit."
    #   disabled                     = false
    #   push_trigger_enabled         = true
    #   pull_request_trigger_enabled = false
    #   owner                        = "cat-home-experts"
    #   repo_name                    = "frontend"
    #   branch_regex                 = "^main$"
    #   invert_regex                 = true
    #   comment_control              = "COMMENTS_DISABLED"
    #   filename                     = "cloudbuild.yaml"
    #   env_variables                = local.cloud_build_env_vars
    #   excluded_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    # }
    # stagingTrigger = {
    #   name                         = "PR-to-Main-for-Frontend"
    #   description                  = "Builds and releases to dev on an opened PR, for dev to be on par with staging."
    #   disabled                     = false
    #   push_trigger_enabled         = false
    #   pull_request_trigger_enabled = true
    #   owner                        = "cat-home-experts"
    #   repo_name                    = "frontend"
    #   branch_regex                 = "^main$"
    #   invert_regex                 = false
    #   comment_control              = "COMMENTS_DISABLED"
    #   filename                     = "cloudbuild.yaml"
    #   env_variables                = local.cloud_build_env_vars
    #   excluded_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    # }
    # productionTrigger = {
    #   name                         = "Main-for-Frontend"
    #   description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
    #   disabled                     = false
    #   push_trigger_enabled         = true
    #   pull_request_trigger_enabled = false
    #   owner                        = "cat-home-experts"
    #   repo_name                    = "frontend"
    #   branch_regex                 = "^main$"
    #   invert_regex                 = false
    #   comment_control              = "COMMENTS_DISABLED"
    #   filename                     = "cloudbuild.yaml"
    #   env_variables                = local.cloud_build_env_vars_main
    #   excluded_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    # }
  }
  cloudbuild_plain_triggers = {
    githubCITrigger = {
      name                         = "GitHub-CI-Trigger"
      description                  = "Builds CLI image for Github to enable comments."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "frontend"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "gcp-scripts/cloudbuild-github-image.yaml"
      env_variables                = local.cloud_build_github_env_vars
      included_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    }
  }
}
