terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  region       = "europe-west2"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloudbuild_env_variables = {
    emulator = {}
    gateway = {
      _GCP_REGION           = "europe-west2"
      _SERVICE_NAME         = "jobs-api"
      _DATA_SERVICE         = "jobs-dependency-data-service"
      _VERSION              = "dev"
      _PLATFORM             = "managed"
      _API_GATEWAY_NAME     = "jobs-management-gateway-gw"
      _API_GATEWAY_API_NAME = "jobs-management-gateway"
    }
    jobs-api = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "jobs-api"
      _VERSION        = "dev"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    jobs-processor = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "jobs-processor"
      _VERSION        = "dev"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    jobs-lead-generator = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "jobs-lead-generator"
      _VERSION        = "dev"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    jobs-dependency-data-service = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "jobs-dependency-data-service"
      _VERSION        = "dev"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    cloud-functions = {
      _GCP_REGION             = "europe-west2"
      _GCR_HOSTNAME           = "eu.gcr.io"
      _ASPNETCORE_ENVIRONMENT = "Development"
    }
    e2etests = {
      _BRANCH_TAG = "main"
    }
  }

  # Teams definition for new v2 IAM module
  team_access = [
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]

  team_admin_access = [
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]

  data_admins = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]

  publisher_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]
}

inputs = {
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment
  region         = local.region

  # viewer_access = [
  #   "group:<EMAIL>",
  #   "group:<EMAIL>",
  #   "group:<EMAIL>"
  # ]

  developer_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]

  #team_access = local.team_access

  # team_admin_access = [
  #   "group:<EMAIL>",
  #   "group:<EMAIL>"
  # ]

  # data_admins = [
  #   "group:<EMAIL>",
  #   "group:<EMAIL>",
  #   "group:<EMAIL>"
  # ]

  project_static_permissions = {
    "roles/iam.serviceAccountUser" : concat(
      local.team_access,
    )
    "roles/run.developer" : [
      "group:<EMAIL>",
      "group:<EMAIL>"
    ]
    "roles/pubsub.publisher" : local.publisher_access
    "roles/pubsub.subscriber" : local.publisher_access
    "roles/storage.objectViewer" : flatten(concat(
      [
        local.team_access,
        "group:<EMAIL>",
      ]
    ))
    "roles/cloudbuild.builds.viewer" : flatten(concat([
      local.team_access,
      "group:<EMAIL>",
    ]))
    "roles/cloudscheduler.viewer" : flatten(concat([
      local.team_access,
      "group:<EMAIL>",
    ]))
    "roles/viewer" : flatten(concat([
      local.team_access,
      "group:<EMAIL>",
    ]))
    "roles/cloudtasks.viewer" : flatten(concat([
      local.team_access,
      "group:<EMAIL>",
    ]))
    "roles/cloudscheduler.jobRunner" : flatten(concat([
      local.team_access,
    ]))
    "roles/cloudbuild.builds.editor" : flatten(concat([
      local.team_access,
    ]))
    "roles/serviceusage.apiKeysAdmin" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/cloudscheduler.admin" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/pubsub.admin" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/secretmanager.admin" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/servicemanagement.admin" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/serviceusage.serviceUsageAdmin" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/serviceusage.serviceUsageConsumer" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/iam.serviceAccountKeyAdmin" : flatten(concat([
      local.team_admin_access,
    ]))
    "roles/cloudtasks.admin" : flatten(concat([
      local.team_admin_access,
      local.team_access,
    ]))
    "roles/firebase.admin" : flatten(concat([
      local.data_admins
    ]))
    "roles/datastore.viewer" : flatten(concat([
      "group:<EMAIL>",
    ]))
  }

  cloud_scheduler_redirect_jobs_schedule           = "*/2 * * * *"
  cloud_scheduler_trade_lead_reminder              = "30 8,12,17 * * *"
  cloud_scheduler_trade_lead_daily_reminder        = "30 18 * * 1-6"
  cloud_scheduler_activate_guarantee_email_weekday = "00 12 * * 1-5"
  cloud_scheduler_activate_guarantee_email_weekend = "30 10 * * 6,0"

  cloudbuild_triggers = {

    apiPush = {
      name                         = "api-push"
      description                  = "Builds and releases api on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-jobs-management-processor"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "services/api/cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["jobs-api"],
        {
          _TRIGGER_NAME = "feature"
        }
      )
      included_files_filter = ["services/api/**"]
    }

    processorPush = {
      name                         = "processor-push"
      description                  = "Builds and releases processor on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-jobs-management-processor"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "services/processor/cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["jobs-processor"],
        {
          _TRIGGER_NAME = "feature"
        }
      )
      included_files_filter = ["services/processor/**"]
    }

    # cloud functions
    cloudFunctionsPush = {
      name                         = "cloud-functions-push"
      description                  = "Builds and releases to dev on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-jobs-management-processor"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudfunctions/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.cloud-functions
      included_files_filter        = ["cloudfunctions/**"]
    }

    leadGeneratorPush = {
      name                         = "lead-generator-push"
      description                  = "Builds and releases on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-jobs-management-processor"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "services/lead-generator/cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["jobs-lead-generator"],
        {
          _TRIGGER_NAME = "feature"
        }
      )
      included_files_filter = ["services/lead-generator/**"]
    }

    dataServicePush = {
      name                         = "data-service-push"
      description                  = "Builds and releases on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-jobs-management-processor"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "services/data-service/cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables.jobs-dependency-data-service,
        {
          _TRIGGER_NAME = "feature"
        }
      )
      included_files_filter = ["services/data-service/**"]
    }

    # standalone emulator
    emulatorPush = {
      name                         = "firestore-emulator"
      description                  = "Builds Docker image on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-emulator"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      excluded_files_filter        = ["README.md"]
    }

    # Please don't change this trigger name as it is referenced by name in the cloud build scripts
    endToEndTestsPush = {
      name                         = "end-to-end-tests-push"
      description                  = "Runs end-to-end tests"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-jobs-management-processor"
      branch_regex                 = ".*"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "tests/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.e2etests
      included_files_filter        = ["tests/**"]
    }
  }

  # Cloudrun inputs
  cloud_run_env_variables = {
    jobs-api = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "Development"
      },
    ]
    jobs-processor = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "Development"
      },
    ]
    jobs-lead-generator = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "Development"
      },
    ]
    jobs-dependency-data-service = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "Development"
      },
    ]
  }

  cloud_run_parameters = {
    jobs-api = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 40
    }
    jobs-processor = {
      cpu                   = "1000m"
      memory                = "768Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 60
    }
    jobs-lead-generator = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 20
    }
    jobs-dependency-data-service = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 20
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
    }
  }

  # Pubsub Alert variables
  alert_duration  = "60s"
  threshold_value = "10"
  trigger_count   = "1"

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  test_automation_service_account_enabled = true

  # Project Ids
  content_api_project_id      = "content-api-dev-18783"
  trade_experience_project_id = "trade-experience-dev-35966"
  secure_contacts_project_id  = "secure-contacts-dev-42829"
  salesforce_integ_project_id = "salesforce-integ-dev-39443"
}
