inputs = {
  cloud_run_admin_auth_settings = {
    cpu                   = "1000m"
    memory                = "1024Mi"
    container_concurrency = 500
    max_scale             = 2
    min_scale             = 0
    initial_scale         = 0
  }

  cloud_build_env_vars_for_admin_auth = {
    # Firebase API keys are not considered sensitive so are not to be scanned by Gitleaks
    # https://firebase.google.com/support/guides/security-checklist#api-keys-not-secret
    # pragma: allowlist nextline secret
    _FIREBASE_API_KEY       = "AIzaSyArPju6_R2J5HhJBOoIvqTLYtg17NvLCvY" # gitleaks:allow
    _FIREBASE_APP_ID        = "1:************:web:4f53d4c89b6e76f4583b44"
    _FIREBASE_AUTH_DOMAIN   = "contacts-dev-42829.firebaseapp.com"
    _AUTHORIZATION_ENDPOINT = "https://checkatrade--nawazdev.sandbox.my.salesforce.com/services/oauth2/authorize"
    _CLIENT_ID              = "3MVG9LlLrkcRhGHbLlDHkcvfpb5AT18w4qfhKbNn.lalxE9_nux5RAC1eXoSfz8RGKEwyx00U9cchptZtZrab"
    _ISSUER                 = "https://checkatrade--nawazdev.sandbox.my.salesforce.com"
    _JWKS_URI               = "https://checkatrade--nawazdev.sandbox.my.salesforce.com/id/keys"
    _TOKEN_ENDPOINT         = "https://checkatrade--nawazdev.sandbox.my.salesforce.com/services/oauth2/token"
    _ALLOWED_FRAME_ANCESTOR = "https://checkatrade--nawazdev.sandbox.lightning.force.com"
    _EXTERNAL_URL           = "https://secure-contacts-admin-dev.checkatrade.net"
  }

  cloud_build_triggers_for_admin_auth = {
    development_trigger = {
      name                         = "Feature-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
    }
    staging_trigger = {
      name                         = "PR-to-Main-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR, for ${basename(dirname(get_terragrunt_dir()))} to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }
}
