terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name             = basename(get_terragrunt_dir())
  environment              = basename(dirname(get_terragrunt_dir()))
  consent_screen_app_title = local.project_name
  repo_name                = "salesforce-gcp-integration"
  organisation             = "cat-home-experts"

  cloudbuild_env_variables = {
    emulator = {}
    salesforce-gcp-integ = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "salesforce-gcp-integ"
      _VERSION        = "dev"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    gateway = {
      _GCP_REGION           = "europe-west2"
      _SERVICE_NAME         = "salesforce-gcp-integ"
      _VERSION              = "dev"
      _PLATFORM             = "managed"
      _API_GATEWAY_NAME     = "salesforce-gcp-integ-gateway-gw"
      _API_GATEWAY_API_NAME = "salesforce-gcp-integ-gateway"
    }
    sf_integ_admin_ui = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "sf-integ-admin-ui"
      _VERSION        = "dev"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
  }

  # ---- only if env needed in this local block ----
  env = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
  })

  # ------------ For Atlantis ------------
  # If you want to disable its creation, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    #"../../../projects/${local.project_name}/template/*.tpl*"  # example for the projects API gateways
  ]



  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]

  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  salesforce_api_uri = "https://checkatrade--systest.sandbox.my.salesforce.com"
  # use the enterprise sandbox instead of systest for enterprise/ppl feature testing
  salesforce_api_uri_enterprise = "https://checkatrade--enterprise.sandbox.my.salesforce.com"

  content_api_uri           = "https://content-api-x2k2mja74q-nw.a.run.app"
  content_api_iap_client_id = "161849807894-cgr6d2ed9tg479jm6l3aus9643c5vmp1.apps.googleusercontent.com"

  consumer_api_uri = "https://api.staging.checkatrade.com/v1/consumer/"

  # Admin Auth
  salesforce_fqdn        = "checkatrade--systest.sandbox.my.salesforce.com"
  firebase_app_id        = "1:243262651850:web:0cdb880d7977abc58e6333"
  firebase_auth_domain   = "salesforce-integ-dev-39443.firebaseapp.com"
  client_id              = "3MVG954MqIw6FnnN7jVUNnH8hChuJX5HzYk6iYN3yZpOKU.riKZvZn7Q2aVoJQBj82CDoPkZhEOqVaAqiIKzr" # scan:ignore
  allowed_frame_ancestor = "https://checkatrade--systest.sandbox.lightning.force.com"
  external_url           = "https://salesforce-integ-admin-dev.checkatrade.com"
  admin_app_service_name = "sf-integ-admin-ui" # Backend service for Salesforce Admin embedded app  [Is this where there's just a single GC app behind the API, so you have yaml where we need two?]
  short_api_id           = "sf-integ-admin-ui" # _API_ID that comes from the module includes the full path     [Is this where there's just a single GC app behind the API, so you have yaml where we need two?] 
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # --- Salesforce settings ---
  salesforce_api_uri              = local.salesforce_api_uri
  salesforce_api_uri_enterprise   = local.salesforce_api_uri_enterprise
  salesforce_id_server            = "${local.salesforce_api_uri}/services/oauth2/token"
  salesforce_id_server_enterprise = "${local.salesforce_api_uri_enterprise}/services/oauth2/token"
  trade_experience_project_id     = "trade-experience-dev-35966"
  content_api_project_id          = "content-api-dev-18783"
  jobs_management_project_id      = "jobs-management-dev-33113"
  capi_project_id                 = "capi-staging-27323"

  # ---- env Vars -----
  content_api_uri           = local.content_api_uri
  content_api_iap_client_id = local.content_api_iap_client_id
  consumer_api_uri          = local.consumer_api_uri

  # ---- Should be passed to any project ----
  project_name   = local.project_name
  environment    = local.environment
  project_folder = local.environment

  # ---- Admin Auth ----
  salesforce_fqdn        = local.salesforce_fqdn
  firebase_app_id        = local.firebase_app_id
  firebase_auth_domain   = local.firebase_auth_domain
  client_id              = local.client_id
  allowed_frame_ancestor = local.allowed_frame_ancestor
  external_url           = local.external_url
  admin_app_service_name = local.admin_app_service_name
  short_api_id           = local.short_api_id

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/apigateway.admin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iam.serviceAccountAdmin" : concat(
      local.project_team_admin,
    )
    "roles/resourcemanager.projectIamAdmin" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountKeyAdmin" : concat(
      local.project_team_admin,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.editor" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/secretmanager.secretVersionManager" : concat(
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/logging.viewAccessor" : concat(
      local.viewers,
    )
    "roles/run.admin" : concat(
      local.project_team,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/storage.objectViewer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    ),
    "roles/logging.privateLogViewer" : concat(
      local.project_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
      local.project_team
    ),
    "roles/secretmanager.secretVersionAdder" : concat(
      local.project_team_admin,
      local.project_team
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    pushGatewayTriggerApi = {
      name                         = "api-gateway-push"
      description                  = "Builds and releases to dev on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = ".*"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.gateway
      included_files_filter        = ["api-gateway/**"]
    }

    pr_trigger = {
      name                         = "PR-to-Feature"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.salesforce-gcp-integ
      excluded_files_filter        = ["emulator/**", "src/JobsManagement/**"] # Can be commented out
    }
    pr_to_main_trigger = {
      name                         = "PR-to-Main"
      description                  = "Builds and releases to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.salesforce-gcp-integ
      excluded_files_filter        = ["emulator/**", "src/JobsManagement/**"] # Can be commented out
    }
    commit_to_main_trigger = {
      name                         = "commit-to-Main"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.salesforce-gcp-integ
      excluded_files_filter        = ["emulator/**", "src/JobsManagement/**"] # Can be commented out
    }
    admin_ui_pr_to_main_trigger = {
      name                         = "Admin-UI-PR-to-Main"
      description                  = "Builds and releases Admin UI to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "src/JobsManagement/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.sf_integ_admin_ui
      included_files_filter        = ["src/JobsManagement/**"] # Can be commented out
    }
    admin_ui_commit_to_main_trigger = {
      name                         = "Admin-UI-commit-to-Main"
      description                  = "Builds and releases Admin UI to dev on any main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = "salesforce-gcp-integration"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "src/JobsManagement/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.sf_integ_admin_ui
      included_files_filter        = ["src/JobsManagement/**"] # Can be commented out
    }
    # standalone emulator
    build_firestore_emulator = {
      name                         = "firestore-emulator"
      description                  = "Builds Docker image on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-emulator"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      excluded_files_filter        = ["README.md"]
    }
  }

  cloud_run_env_variables = {
    salesforce-gcp-integ = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "GCP_Development"
      },
      {
        name  = "LawFirmStorageBucketConfig__BucketName"
        value = "credit_control_storage_bucket-77"
      }
    ]
    sf_integ_admin_ui = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "GCP_Development"
      }
    ]
  }

  cloud_run_parameters = {
    salesforce-gcp-integ = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    }
    sf_integ_admin_ui = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    }
  }

  firebase_notification_targets = "@salesforce-integ-alerts-dev"

  cloud_build_triggers_for_admin_auth = {
    development_trigger = {
      name                         = "Feature-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
    }
    staging_trigger = {
      name                         = "PR-to-Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR, for ${basename(dirname(get_terragrunt_dir()))} to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }
}
