terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    "../../../projects/${local.project_name}/template/*.tpl*",
  ]
}

inputs = {
  # Default inputs
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment

  app_env = local.environment


  sink_destination_bucket = "sre-logs"

  cloudbuild_triggers = {
    devTrigger = {
      name                         = "feature"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-nlp-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "ce-cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "feature"
        _SERVICE_NAME   = "nlp-service"
        _VERSION        = "development"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = true
        _LB_URL         = "nlp-dev.gcp.cathex.io"
      }
      excluded_files_filter = []
    }
    stagingTrigger = {
      name                         = "pr-to-main"
      description                  = "Builds and releases to staging on pr to main."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-nlp-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "ce-cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "pr-to-main"
        _SERVICE_NAME   = "nlp-service"
        _VERSION        = "dev"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = false
        _LB_URL         = "nlp-dev.gcp.cathex.io"
      }
      excluded_files_filter = []
    }
    prodTrigger = {
      name                         = "main"
      description                  = "Builds and releases to production from main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-nlp-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "ce-cloudbuild.yaml"
      env_variables = {
        _GCP_REGION     = "europe-west2"
        _TRIGGER_NAME   = "main"
        _SERVICE_NAME   = "nlp-service"
        _VERSION        = "dev"
        _GCR_HOSTNAME   = "eu.gcr.io"
        _PLATFORM       = "managed"
        _ENABLE_TRAFFIC = false
        _LB_URL         = "nlp-dev.gcp.cathex.io"
      }
      excluded_files_filter = []
    }
  }


  # Instance Template vars

  source_image_family  = "cos-stable"
  source_image_project = "cos-cloud"
  disk_size_gb         = "20"
  machine_type         = "custom-2-12288"
  env_workers          = "2"


  # Instance Group vars
  target_size = 1
  health_check = {
    type                = "http"
    initial_delay_sec   = 900
    check_interval_sec  = 20
    healthy_threshold   = 2
    timeout_sec         = 15
    unhealthy_threshold = 3
    response            = ""
    proxy_header        = "NONE"
    port                = 8080
    request             = ""
    request_path        = "/health/live"
    host                = ""
  }

  firehose_nlp_stream = "cathex-dev-datalake"
}
