terraform {
  source = "./../../..//projects/search"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  env = local.map_environment_to_env[local.environment]

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_github_env_vars = {
    _PROJECT_ENV  = basename(dirname(get_terragrunt_dir()))
    _VERSION      = "dev"
    _TRIGGER_NAME = "Github CI Build"
  }

  search_service_emulator_env_variables = {}

  excluded_files_filter_for_jobs_and_emulator = [
    "docs/**",
    "emulator/**",
    "services/search-onspd/SearchIndexer.ONSPDLoader/**",
    "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader.FunctionalTests/**"
  ]

  excluded_files_filter_for_jobs = [
    "docs/**",
    "services/search-onspd/SearchIndexer.ONSPDLoader/**",
    "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader/**",
    "services/search-place-name/SearchIndexer.PlaceNameLoader.FunctionalTests/**"
  ]

  # Accounts for team defined IAM permissions
  viewer_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  scheduler_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  developer_access = [
    "group:<EMAIL>"
  ]

  support_access = [
    "group:<EMAIL>"
  ]

  team_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  publisher_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]

  consumer_app_developer_access = [
    "group:<EMAIL>"
  ]

  retool_api_access = ["serviceAccount:<EMAIL>"]

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
}

inputs = {
  # Standard inputs
  region         = "europe-west2"
  environment    = "dev"
  project_folder = "development"

  # Accounts for team defined IAM permissions
  project_static_permissions = {
    "roles/run.admin" : local.team_access,
    "roles/iam.serviceAccountUser" : local.team_access,
    "roles/pubsub.admin" : local.team_access,
    "roles/cloudbuild.builds.editor" : local.team_access,
    "roles/compute.admin" : local.team_access,
    "roles/cloudbuild.builds.builder" : local.team_access,
    "roles/pubsub.publisher" : local.publisher_access,
    "roles/pubsub.subscriber" : local.publisher_access,
    "roles/run.invoker" : concat(
      local.support_access,
      local.developer_access,
      local.retool_api_access
    ),
    "roles/cloudbuild.builds.viewer" : local.viewer_access,
    "roles/viewer" : local.viewer_access,
    "roles/cloudtasks.queueAdmin" : local.project_team,
    "roles/cloudscheduler.admin" : local.scheduler_access,
    "roles/artifactregistry.repoAdmin" : local.project_team,
    "roles/run.developer" : local.consumer_app_developer_access,
  }

  # Cloudbuild inputs
  cloudbuild_triggers = {
    devTrigger = {
      name                         = "Feature"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "services/combined_cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = local.excluded_files_filter_for_jobs_and_emulator
    }
    stagingTrigger = {
      name                         = "PR-to-Main"
      description                  = "Builds and releases to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/combined_cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = local.excluded_files_filter_for_jobs_and_emulator
    }
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/combined_cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = local.excluded_files_filter_for_jobs_and_emulator
    }
    # Triggers for emulator
    devEmulatorTrigger = {
      name                         = "Feature-for-Emulator"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.search_service_emulator_env_variables
      included_files_filter        = ["emulator/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    stagingEmulatorTrigger = {
      name                         = "PR-to-Main-for-Emulator"
      description                  = "Builds and releases to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.search_service_emulator_env_variables
      included_files_filter        = ["emulator/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    productionEmulatorTrigger = {
      name                         = "Main-for-Emulator"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.search_service_emulator_env_variables
      included_files_filter        = ["emulator/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    devTriggerSearchNodeService = {
      name                         = "Feature-Node-Service"
      description                  = "Builds and releases Search Node Service to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-node-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = []
    }
    devTriggerSearchONSPDLoader = {
      name                         = "Feature-ONSPDLoader"
      description                  = "Builds and releases ONSPDLoader to dev on any non-main commit"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "services/search-onspd/SearchIndexer.ONSPDLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-onspd/SearchIndexer.ONSPDLoader/**", "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**"]
    }
    devTriggerSearchPlaceNameLoader = {
      name                         = "Feature-PlaceNameLoader"
      description                  = "Builds and releases PlaceNameLoader to dev on any non-main commit"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "services/search-place-name/SearchIndexer.PlaceNameLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-place-name/SearchIndexer.PlaceNameLoader/**", "services/search-place-name/SearchIndexer.PlaceNameLoader.FunctionalTests/**"]
    }
    stagingTriggerSearchNodeService = {
      name                         = "PR-to-Main-Node-Service"
      description                  = "Builds and releases Search Node Service to dev on an opened PR for dev to be on par with staging"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-node-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = []
    }
    stagingTriggerONSPDLoader = {
      name                         = "PR-Main-ONSPD-Loader"
      description                  = "Builds and releases Search ONSPD Loader to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-onspd/SearchIndexer.ONSPDLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-onspd/SearchIndexer.ONSPDLoader/**", "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**"]
    }
    stagingTriggerPlaceNameLoader = {
      name                         = "PR-Main-PlaceName-Loader"
      description                  = "Builds and releases Search PlaceNameLoader to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-place-name/SearchIndexer.PlaceNameLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-place-name/SearchIndexer.PlaceNameLoader/**", "services/search-place-name/SearchIndexer.PlaceNameLoader.FunctionalTests/**"]
    }
    productionTriggerSearchNodeService = {
      name                         = "Main-Search-Node-Service"
      description                  = "Builds and releases Search Node Service to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-node-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = []
    }
    productionTriggerONSPDLoader = {
      name                         = "Main-Search-ONSPD-Loader"
      description                  = "Builds and releases ONSPD Loader to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-onspd/SearchIndexer.ONSPDLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-onspd/SearchIndexer.ONSPDLoader/**", "services/search-onspd/SearchIndexer.ONSPDLoader.FunctionalTests/**"]
    }
    productionTriggerPlaceNameLoader = {
      name                         = "Main-PlaceName-Loader"
      description                  = "Builds and releases PlaceNameLoader to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-place-name/SearchIndexer.PlaceNameLoader/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-place-name/SearchIndexer.PlaceNameLoader/**", "services/search-place-name/SearchIndexer.PlaceNameLoader.FunctionalTests/**"]
    }
    # Triggers for search service api gateway config file change
    devApiGatewayTrigger = {
      name                         = "Feature-API-Gateway"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^feature.*$"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["api-gateway/spec.yaml"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    stagingApiGatewayTrigger = {
      name                         = "PR-Api-Gateway"
      description                  = "Builds and releases to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["api-gateway/spec.yaml"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    productionApiGatewayTrigger = {
      name                         = "Main-Api-Gateway"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["api-gateway/spec.yaml"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    devTriggerSearchWorker = {
      name                         = "Feature-Search-Worker"
      description                  = "Builds and releases Search Worker to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "services/search-worker/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-worker/**", "services/search-service/SearchService.Contracts/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    stagingTriggerSearchWorker = {
      name                         = "PR-to-Main-Search-Worker"
      description                  = "Builds and releases Search Worker to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-worker/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-worker/**", "services/search-service/SearchService.Contracts/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    productionTriggerSearchWorker = {
      name                         = "Main-Search-Worker"
      description                  = "Builds and releases Search Worker to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "services/search-worker/cloudbuild.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = ["services/search-worker/**", "services/search-service/SearchService.Contracts/**"]
      excluded_files_filter        = local.excluded_files_filter_for_jobs
    }
    mainTriggerE2e = {
      name                         = "Main-e2e"
      description                  = "Builds e2e tests to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "e2e/cloud-build.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = []
    }
    devTriggerE2e = {
      name                         = "Feature-e2e"
      description                  = "Builds e2e tests to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "e2e/cloud-build.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = []
    }
    prTriggerE2e = {
      name                         = "PR-Main-e2e"
      description                  = "Builds e2e tests to dev on an opened PR."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "e2e/cloud-build.yaml"
      env_variables                = { _VERSION = local.env }
      included_files_filter        = []
      excluded_files_filter        = ["docs/**"]
    }
  }
  cloudbuild_plain_triggers = {
    githubCITrigger = {
      name                         = "GitHub-CI-Trigger"
      description                  = "Builds CLI image for Github to enable comments."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-search-service"
      branch_regex                 = ".*"
      invert_regex                 = false
      filename                     = "gcp-scripts/cloudbuild-github-image.yaml"
      env_variables                = local.cloud_build_github_env_vars
      included_files_filter        = ["gcp-scripts/Dockerfile-github-ci", "gcp-scripts/cloudbuild-github-image.yaml"]
    }
  }

  # Creates topics
  search_service_topics = [
    "search-query",
    "search-indexer-trade-profile",
    "ab-search-request",
    "search-indexer-searchable",
    "search-indexer-trade-card"
  ]

  # Pubsub inputs

  # Elastic Search Url
  elastic_search_url = "https://gcp-search-team-elastic-cloud-dev-cpu-optimised.es.europe-west2.gcp.elastic-cloud.com:9243"

  #CORS origin domains
  cors_origin = "https://*.cathex.team, https://*.checkatrade.com, https://*.appspot.com, http://localhost:3000"

  #Kinesis stream names
  kinesis_stream_name         = "cathex-dev-datalake"
  kinesis_stream_name_offline = ""

  #APM env variables
  dd_trace_enabled = "true"

  dd_env = "development"

  cloud_scheduler_pull_fairshare_leads = "*/5 * * * *"

  cloud_scheduler_pull_fairshares = "*/5 * * * *"

  cloud_scheduler_pull_fairshares_delete = "*/5 * * * *"

  cloud_scheduler_pull_fairshares_daily = "*/5 * * * *"

  cloud_scheduler_pull_fairshares_hourly = "*/5 * * * *"

  cloud_scheduler_pull_lead_surplus = "*/5 * * * *"

  cloud_scheduler_pull_campaign_event = "*/5 * * * *"

  cloud_scheduler_pull_campaign_event_dlq = "0 22 * * *"

  cloud_scheduler_pull_trade_profile = "*/5 * * * *"

  cloud_scheduler_pull_secure_contacts = "*/5 * * * *"

  cloud_scheduler_cleanup_lead_surplus = "10 03 * * *"

  cloud_scheduler_pull_review_metrics = "*/5 * * * *"

  cloud_scheduler_pull_categories = "45 23 * * *"

  cloud_scheduler_pull_company_account_type_update = "*/5 * * * *"

  cloud_scheduler_pull_company_beta_groups_update = "*/5 * * * *"

  cloud_scheduler_cleanup_searchable = "5 21 * * *"

  cloud_scheduler_pull_trade_experience_album = "*/5 * * * *"

  cloud_scheduler_pull_trade_experience_company = "*/5 * * * *"

  cloud_scheduler_pull_payment_onboarding = "*/5 * * * *"

  cloud_scheduler_pull_generic = "*/5 * * * *"

  # Cloudrun inputs
  container_concurrency        = 80
  ranking_optimised_weight     = 1.2
  ranking_not_optimised_weight = 0.8
  offline_tests_enabled        = false
  offline_tests                = "SearchV1,LeadsRankingV9,StaticDistanceV1,StaticDistanceV2,StaticDistanceV3"
  use_campaign_data            = true
  use_services_data            = true

  authorized_email_addresses = "checkatrade.com,cathex.io"
  authorized_issuers         = "accounts.google.com,securetoken.google.com/reviews-dev-44869"
  authorized_role            = ""

  trade_profile_auto_reply = 200


  # Maximum number of Cloud Run instances to scale to
  max_scale = {
    search-indexer      = 2
    search-service      = 30
    search-worker       = 30
    search-node-service = 2
    search-admin        = 2
  }

  # Pubsub Alert variables
  alert_duration  = "last_1m"
  threshold_value = "10"

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "last_1m"
  threshold_value_memory = "0.9"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "last_1m"
  threshold_value_cpu = "0.9"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "last_1m"
  threshold_value_response_codes = "0.5"

  # Cloud Tasks Push URL
  cloud_tasks_trade_push_url = "https://search-indexer-kk3ghzipma-nw.a.run.app/api/v1/trade-profile"
  cloud_tasks_delay          = 120

  # D+I's project only exists in dev and prod so we can't use wildcarding for staging
  # so instead we'll just hardcode
  data_stats_project_id = "data-stats-service-dev-24217"

}
