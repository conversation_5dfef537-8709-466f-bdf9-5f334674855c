# there is a readme for this project in folder projects/web-office/readme.md

terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to the Terraform templates
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----

  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be decalred in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----

  project_name       = local.project_name
  environment        = local.environment
  env                = local.env
  gcp_local_networks = ["*********/24"]
  ip_cidr_range      = "*********/24"

  # Cloud build triggers

  cloud_build_triggers = {
    weboffice_image_scheduled = {
      name                         = "WO-image-schduled-build"
      description                  = "Web office image scheduled buil that Runs every tues and weds"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = false
      manual_trigger_enabled       = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-weboffice-deploy"
      branch_or_tag_ref            = "refs/heads/main"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {

      }
      excluded_files_filter = []
      included_files_filter = []
    }
  }

}
