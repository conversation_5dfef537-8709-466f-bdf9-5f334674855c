terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  # Teams definition for new v2 IAM module
  project_team = [
    "group:<EMAIL>",
    "group:<EMAIL>",
    "group:<EMAIL>"
  ]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]

  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
  )

}

inputs = {
  # Default inputs
  region         = "europe-west2"
  environment    = local.env
  project_folder = local.environment
  project_name   = local.project_name

  project_static_permissions = {
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin,
    )
    "roles/run.admin" : concat(
      local.project_team,
    )
    "roles/pubsub.admin" : concat(
      local.project_team,
    )
    "roles/appengine.appAdmin" : concat(
      local.project_team,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
    )

    "roles/firebase.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/apigateway.admin" : concat(
      local.project_team_admin,
    )
    "roles/apigateway.viewer" : concat(
      local.project_team,
    )
    "roles/serviceusage.serviceUsageConsumer" : concat(
      local.project_team,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team_admin,
    )
  }

  appengine_location = "europe-west"

  # Cloudbuild inputs
  cloudbuild_triggers = {
    devTrigger = {
      name                         = "Feature"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-consumer-area"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION                                     = "europe-west2"
        _GCR_HOSTNAME                                   = "eu.gcr.io"
        _SERVICE_NAME                                   = "consumer-api"
        _PLATFORM                                       = "managed"
        _VERSION                                        = local.env
        _CONTENT_API_VERSION                            = "Basic"
        _DATA_SERVICES_PROJECT_ID                       = "content-api-dev-18783"
        _REVIEW_SERVICES_PROJECT_ID                     = "reviews-dev-44869"
        _TRIGGER_NAME                                   = "feature"
        _USER_DATA_REQUESTS_TOPIC                       = "content-api-user-req"
        _USER_DATA_TOPIC                                = "content-api-user-res"
        _REVIEWS_BULK_REQUESTS_TOPIC                    = "reviews-bulk-req"
        _SIB_LIST_IDS                                   = "[2,3,4]"
        _SIB_MARKETING_LIST_ID                          = "4"
        _MARKETING_OPT_IN_TOPIC                         = "marketing-opt-in"
        _MARKETING_OPT_OUT_TOPIC                        = "marketing-opt-out"
        _MARKETING_UPDATES_TOPIC                        = "marketing-prefs-updated"
        _VERIFY_REVIEW_TOPIC                            = "verify-review"
        _VERIFY_REVIEW_REPLY_TOPIC                      = "verify-review-reply"
        _HOMEOWNER_EVENT_TOPIC                          = "homeowner-event"
        _REVIEW_VERIFIED_TOPIC                          = "review-verified"
        _CORS_WHITELIST                                 = "*"
        _FIRESTORE_BATCH_WRITE_SIZE                     = "500"
        _LIMIT_BY_ACCOUNT_CREATION                      = false
        _HERITAGE_SMS_VERIFICATION_URL                  = "https://www.dev.checkatrade.com/handlers/SmsConfirmation.ashx"
        _PARTNER_API_SMS_VERIFICATION_STATUS_UPDATE_URL = "https://partnerapi.dev.checkatrade.com/api/feedback"
        _KALEYRA_ENABLED                                = true
        _KALEYRA_USER                                   = "CheckatradeAPI"
        _KALEYRA_LONG_CODE                              = "***********"
        _COMMS_SERVICE_PROJECT_ID                       = "communication-dev-26905"
        _COMMS_CONSUMER_USERS_TOPIC                     = "consumer-updated"
        _COMMS_SERVICE_TASKS_QUEUE_NAME                 = "communication-import-tasks"
        _COMMS_SERVICE_APP_URL                          = "https://communication-api-t52tf5vcta-nw.a.run.app"
      }
      included_files_filter = []
    },
    stagingTrigger = {
      name                         = "PR-to-Main"
      description                  = "Builds and releases to dev on an opened PR for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-consumer-area"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION                                     = "europe-west2"
        _GCR_HOSTNAME                                   = "eu.gcr.io"
        _SERVICE_NAME                                   = "consumer-api"
        _PLATFORM                                       = "managed"
        _VERSION                                        = local.env
        _CONTENT_API_VERSION                            = "Basic"
        _DATA_SERVICES_PROJECT_ID                       = "content-api-dev-18783"
        _REVIEW_SERVICES_PROJECT_ID                     = "reviews-dev-44869"
        _TRIGGER_NAME                                   = "pr-to-main"
        _USER_DATA_REQUESTS_TOPIC                       = "content-api-user-req"
        _USER_DATA_TOPIC                                = "content-api-user-res"
        _REVIEWS_BULK_REQUESTS_TOPIC                    = "reviews-bulk-req"
        _SIB_LIST_IDS                                   = "[2,3,4]"
        _SIB_MARKETING_LIST_ID                          = "4"
        _MARKETING_OPT_IN_TOPIC                         = "marketing-opt-in"
        _MARKETING_OPT_OUT_TOPIC                        = "marketing-opt-out"
        _MARKETING_UPDATES_TOPIC                        = "marketing-prefs-updated"
        _VERIFY_REVIEW_TOPIC                            = "verify-review"
        _VERIFY_REVIEW_REPLY_TOPIC                      = "verify-review-reply"
        _HOMEOWNER_EVENT_TOPIC                          = "homeowner-event"
        _REVIEW_VERIFIED_TOPIC                          = "review-verified"
        _CORS_WHITELIST                                 = "*"
        _FIRESTORE_BATCH_WRITE_SIZE                     = "500"
        _LIMIT_BY_ACCOUNT_CREATION                      = false
        _HERITAGE_SMS_VERIFICATION_URL                  = "https://www.dev.checkatrade.com/handlers/SmsConfirmation.ashx"
        _PARTNER_API_SMS_VERIFICATION_STATUS_UPDATE_URL = "https://partnerapi.dev.checkatrade.com/api/feedback"
        _KALEYRA_ENABLED                                = true
        _KALEYRA_USER                                   = "CheckatradeAPI"
        _KALEYRA_LONG_CODE                              = "***********"
        _COMMS_SERVICE_PROJECT_ID                       = "communication-dev-26905"
        _COMMS_CONSUMER_USERS_TOPIC                     = "consumer-updated"
        _COMMS_SERVICE_TASKS_QUEUE_NAME                 = "communication-import-tasks"
        _COMMS_SERVICE_APP_URL                          = "https://communication-api-t52tf5vcta-nw.a.run.app"
      }
      included_files_filter = []
    },
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-consumer-area"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = {
        _GCP_REGION                                     = "europe-west2"
        _GCR_HOSTNAME                                   = "eu.gcr.io"
        _SERVICE_NAME                                   = "consumer-api"
        _PLATFORM                                       = "managed"
        _VERSION                                        = local.env
        _CONTENT_API_VERSION                            = "Basic"
        _DATA_SERVICES_PROJECT_ID                       = "content-api-dev-18783"
        _REVIEW_SERVICES_PROJECT_ID                     = "reviews-dev-44869"
        _TRIGGER_NAME                                   = "main"
        _USER_DATA_REQUESTS_TOPIC                       = "content-api-user-req"
        _USER_DATA_TOPIC                                = "content-api-user-res"
        _REVIEWS_BULK_REQUESTS_TOPIC                    = "reviews-bulk-req"
        _SIB_LIST_IDS                                   = "[2,3,4]"
        _SIB_MARKETING_LIST_ID                          = "4"
        _MARKETING_OPT_IN_TOPIC                         = "marketing-opt-in"
        _MARKETING_OPT_OUT_TOPIC                        = "marketing-opt-out"
        _MARKETING_UPDATES_TOPIC                        = "marketing-prefs-updated"
        _VERIFY_REVIEW_TOPIC                            = "verify-review"
        _VERIFY_REVIEW_REPLY_TOPIC                      = "verify-review-reply"
        _HOMEOWNER_EVENT_TOPIC                          = "homeowner-event"
        _REVIEW_VERIFIED_TOPIC                          = "review-verified"
        _CORS_WHITELIST                                 = "*"
        _FIRESTORE_BATCH_WRITE_SIZE                     = "500"
        _LIMIT_BY_ACCOUNT_CREATION                      = false
        _HERITAGE_SMS_VERIFICATION_URL                  = "https://www.dev.checkatrade.com/handlers/SmsConfirmation.ashx"
        _PARTNER_API_SMS_VERIFICATION_STATUS_UPDATE_URL = "https://partnerapi.dev.checkatrade.com/api/feedback"
        _KALEYRA_ENABLED                                = true
        _KALEYRA_USER                                   = "CheckatradeAPI"
        _KALEYRA_LONG_CODE                              = "***********"
        _COMMS_SERVICE_PROJECT_ID                       = "communication-dev-26905"
        _COMMS_CONSUMER_USERS_TOPIC                     = "consumer-updated"
        _COMMS_SERVICE_TASKS_QUEUE_NAME                 = "communication-import-tasks"
        _COMMS_SERVICE_APP_URL                          = "https://communication-api-t52tf5vcta-nw.a.run.app"
      }
      included_files_filter = []
    }
  }
  # Pubsub Alert variables
  alert_duration  = "60s"
  threshold_value = "10"
  trigger_count   = "1"

  content_api_project_id     = "content-api-dev-18783"
  jobs_management_project_id = "jobs-management-dev-33113"
  reviews_project_id         = "reviews-dev-44869"
  reviews_project_number     = "414297497709"

  # Cloud Run inputs
  cloud_run_env_variables = {
    consumer-api = []
  }

  cloud_run_parameters = {
    consumer-api = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
    }
  }

  # Cloud Run Memory Alert variables
  alert_duration_memory  = "60s"
  threshold_value_memory = "0.9"
  trigger_count_memory   = "1"

  # Cloud Run CPU Alert variables
  alert_duration_cpu  = "60s"
  threshold_value_cpu = "0.9"
  trigger_count_cpu   = "1"

  # Cloud Run Response Code Alert variables
  alert_duration_response_codes  = "60s"
  threshold_value_response_codes = "0.5"
  trigger_count_response_codes   = "1"

  jobs_board_notification_targets        = "@slack-jobs-board-alerts-non-prod"
  communication_api_notification_targets = "@slack-communication-alerts-non-prod"

  capi_reviews_public_endpoint = "https://api.staging.checkatrade.com/v1/consumer-public/review"
}
