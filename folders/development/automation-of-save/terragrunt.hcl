terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  region       = "europe-west2"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloudbuild_env_variables = {
    emulator = {}
    campaign-management-api = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "campaign-management-api"
      _VERSION        = local.env
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    campaign-management-processor = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "campaign-management-processor"
      _VERSION        = local.env
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }
    campaign-management-rejection-app = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "campaign-management-rejection-app"
      _VERSION        = local.env
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed"
      _ENABLE_TRAFFIC = false
    }

    gateway = {
      _GCP_REGION           = "europe-west2"
      _SERVICE_NAME         = "campaign-management-api"
      _VERSION              = local.env
      _PLATFORM             = "managed"
      _API_GATEWAY_NAME     = "campaign-management-gateway-gw"
      _API_GATEWAY_API_NAME = "campaign-management-gateway"
    }
  }
}

inputs = {
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment
  region         = local.region

  viewer_access = [
    "group:<EMAIL>"
  ]

  developer_access = [
    "group:<EMAIL>"
  ]

  team_access = [
    "group:<EMAIL>"
  ]

  publisher_access = [
    "group:<EMAIL>"
  ]

  cloudbuild_triggers = {

    # api dev
    pushTriggerApi = {
      name                         = "api-push"
      description                  = "Builds and releases to dev on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-api"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["campaign-management-api"],
        {
          _TRIGGER_NAME   = "feature"
          _ENABLE_TRAFFIC = true
      })
      excluded_files_filter = ["emulator/**"]
    }


    # api-gateway
    pushGatewayTriggerApi = {
      name                         = "api-gateway-push"
      description                  = "Builds and releases to dev on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-api"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.gateway
      included_files_filter        = ["api-gateway/**"]
    }


    # processor dev
    pushTriggerProcessor = {
      name                         = "processor-push"
      description                  = "Builds and releases to dev on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-processor"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["campaign-management-processor"],
        {
          _TRIGGER_NAME   = "feature"
          _ENABLE_TRAFFIC = true
      })
      excluded_files_filter = ["emulator/**"]
    }

    # rejection app dev
    pushTriggerRejection = {
      name                         = "rejection-push"
      description                  = "Builds and releases to dev on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-rejection-app"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["campaign-management-rejection-app"],
        {
          _TRIGGER_NAME   = "feature"
          _ENABLE_TRAFFIC = true
      })
      excluded_files_filter = ["emulator/**"]
    }

    # emulator
    pushEmulatorTriggerApi = {
      name                         = "emulator-push"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-api"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }

    # emulator
    pushEmulatorTriggerProcessor = {
      name                         = "emulator-processor-push"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-processor"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }

    # emulator
    pushEmulatorTriggerRejectionApp = {
      name                         = "emulator-rejection-push"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "campaign-management-rejection-app"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }
  }

  cloud_run_parameters = {
    campaign-management-api = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 3
    }
    campaign-management-processor = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 3
    }
    campaign-management-rejection-app = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 3
    }
  }

  # Cloudrun variables
  cloud_run_env_variables = {
    campaign-management-api = [
      {
        name  = "REVIEWS_ENDPOINT"
        value = "https://preview-partnerapi.checkatrade.com"
      },
      {
        name  = "REVIEWS_USER_ID"
        value = "880b9911-5e95-4867-ba9a-43f800c2ef77"
      },
      {
        name  = "REVIEWS_IPADDRESS"
        value = "**************"
      },
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "GCP_Development"
      },
    ]
    campaign-management-processor = [
      {
        name  = "CAMPAIGN_XAPI_ENDPOINT"
        value = "https://dev-campaign-management-api.mulesoft.dev.checkatrade.net"

      },
      {
        name  = "CAMPAIGN_CONTACT_LIST_ID"
        value = "10"
      },
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "GCP_Development"
      },
      {
        name  = "CAMPAIGN_XAPI_CLIENT_ID"
        value = "4ed7cae99d1d41c59eba34111c34ee91"
      },
    ]
    campaign-management-rejection-app = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "GCP_Development"
      },
    ]
  }

  # Pubsub Alert variables
  alert_duration  = "60s"
  threshold_value = "10"
  trigger_count   = "1"
}
