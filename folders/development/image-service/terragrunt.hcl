terraform {
  source = "./../../..//projects/image-service"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  developer_access = [
    "group:<EMAIL>"
  ]

  github_owner  = "cat-home-experts"
  github_repo   = "gcp-image-service-v2"
  branch_regex  = "^main$"
  builder_regex = ".*"

  collection = "image-data"
}

inputs = {
  # Default inputs
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment


  consent_screen_app_title = "Image Service Dev" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project
  image_service_collection = local.collection
  bucket_name              = "cathex-image-service-dev"
  transition_bucket_name   = "cathex-image-service-aws-transition-dev"
  cloud_build_pull         = {}

  # ---- IAM config ----
  # Permissions for humans only
  project_static_permissions = {
    "roles/workflows.invoker" : concat(
      local.developer_access
    )
  }

  # Cloudbuild v2 triggers
  cloud_build_push = {
    feature_backend = {
      name         = "feature-backend"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = true
      branch       = local.branch_regex
      filename     = "apps/backend/cloudbuild.yaml"
      substitutions = {
        _SERVICE_NAME = "url-generator"
      }
      included_files = [
        "apps/backend/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
        "docker-compose.yaml",
        ".pre-commit-config.yaml",
        "**/README.md",
        ".github/**",
        ".tool-versions",
        ".gitignore"
      ]
    },
    main_backend = {
      name         = "main-backend"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/backend/cloudbuild.yaml"
      substitutions = {
        _SERVICE_NAME = "image-backend"
      }
      included_files = [
        "apps/backend/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
    },
    feature_api = {
      name         = "feature-api"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = true
      branch       = local.branch_regex
      filename     = "apps/api/cloudbuild.yaml"
      included_files = [
        "apps/api/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-api"
      }
    },
    main_api = {
      name         = "main-api"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/api/cloudbuild.yaml"
      included_files = [
        "apps/api/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-api"
      }
    },
    feature_processor = {
      name         = "feature-processor"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = true
      branch       = local.branch_regex
      filename     = "apps/processor/cloudbuild.yaml"
      included_files = [
        "apps/processor/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-processor"
      }
    },
    main_processor = {
      name         = "main-processor"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/processor/cloudbuild.yaml"
      included_files = [
        "apps/processor/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-processor"
      }
    },
    feature_vetting = {
      name         = "feature-vetting"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = true
      branch       = local.branch_regex
      filename     = "apps/vetting/cloudbuild.yaml"
      included_files = [
        "apps/vetting/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-vetting"
      }
    },
    main_vetting = {
      name         = "main-vetting"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/vetting/cloudbuild.yaml"
      included_files = [
        "apps/vetting/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
      substitutions = {
        _SERVICE_NAME = "image-vetting"
      }
    },
    feature_workflow = {
      name         = "feature-workflow"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = true
      branch       = local.branch_regex
      filename     = "workflow/cloudbuild.yaml"
      included_files = [
        "workflow/workflow.yaml.j2",
        "workflow/tests/**"
      ]
      substitutions = {
      },
    },
    main_workflow = {
      name         = "main-workflow"
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "workflow/cloudbuild.yaml"
      included_files = [
        "workflow/workflow.yaml.j2",
        "workflow/tests/**"
      ]
      substitutions = {
      },
    },
    feature_image_service_v1 = {
      name         = "feature-image-service-v1"
      description  = "Builds and releases to dev on any non main commit."
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = true
      branch       = local.branch_regex
      filename     = "apps/image-service-v1/cloudbuild.yaml"
      substitutions = {
        _SERVICE_NAME = "image-service-v1"
      }
      included_files = [
        "apps/image-service-v1/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
    },
    main_image_service_v1 = {
      name         = "main-image-service-v1"
      description  = "Builds and releases to default on any non main commit."
      owner        = local.github_owner
      repository   = local.github_repo
      invert_regex = false
      branch       = local.branch_regex
      filename     = "apps/image-service-v1/cloudbuild.yaml"
      substitutions = {
        _SERVICE_NAME = "image-service-v1"
      }
      included_files = [
        "apps/image-service-v1/**"
      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
    },
    main_image_service_migration = {
      name         = "main-image-service-migration"
      description  = "Builds and releases to default on any non main commit."
      owner        = local.github_owner
      repository   = "image-migration"
      invert_regex = false
      branch       = local.branch_regex
      filename     = "cloudbuild.yaml"
      substitutions = {
        _SERVICE_NAME = "image-migration"

      }
      included_files = [

      ]
      ignored_files = [
        "**/*Dockerfile*",
        "**/*.dockerignore",
      ]
    },
    # feature_frontend = { # disabeling front end for the time being
    #   name         = "feature-frontend"
    #   owner        = local.github_owner
    #   repository   = local.github_repo
    #   invert_regex = true
    #   branch       = local.branch_regex
    #   filename     = "apps/frontend/cloud_build.yaml"
    #   substitutions = {

    #   }
    #   included_files = [
    #     "apps/frontend/*"
    #   ]
    #   ignored_files = [
    #     "**/*Dockerfile*",
    #     "**/*.dockerignore",
    #     "docker-compose.yaml",
    #     ".pre-commit-config.yaml",
    #     "**/README.md",
    #     ".github/**",
    #     ".tool-versions",
    #     ".gitignore"
    #   ]
    # },
    # main_frontend = { # disabeling front end for the time being
    #   name         = "main-frontend"
    #   owner        = local.github_owner
    #   repository   = local.github_repo
    #   invert_regex = false
    #   branch       = local.branch_regex
    #   filename     = "apps/frontend/cloud_build.yaml"
    #   substitutions = {

    #   }
    #   included_files = [
    #     "apps/frontend/*"
    #   ]
    #   ignored_files = [
    #     "**/*Dockerfile*",
    #     "**/*.dockerignore",
    #   ]
    # },
  }

  #alerts slack channel
  notifications = "@slack-image-service-dev-alerts"

}
