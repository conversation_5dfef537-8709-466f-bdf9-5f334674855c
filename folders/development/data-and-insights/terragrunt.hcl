terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloudbuild_env_vars = {
    data-and-insights-analytics = {
      _GCP_REGION   = "europe-west2"
      _SERVICE_NAME = "analytics-web-app"
      _VERSION      = local.env
      _GCR_HOSTNAME = "eu.gcr.io"
      _PLATFORM     = "managed"
    }
    feature-trigger = {
      _TRIGGER_NAME = "feature"
    }
    PR-trigger = {
      _TRIGGER_NAME = "PR to main"
    }
    main-trigger = {
      _TRIGGER_NAME = "main"
    }
  }

  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}

inputs = {
  project_name     = local.project_name
  environment      = local.env
  project_folder   = local.environment
  provider_enabled = true

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  viewers            = local.viewers


  project_static_permissions = {
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.admin" : concat(
      local.project_team_admin,
    )
    "roles/errorreporting.admin" : concat(
      local.project_team_admin,
    )
    "roles/run.admin" : concat(
      local.project_team_admin,
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudscheduler.admin" : concat(
      local.project_team_admin
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin
    )
    "roles/artifactregistry.admin" : concat(
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/storage.admin" : concat(
      local.project_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
    )
    "roles/datastore.user" : concat(
      local.project_team,
    )
    "roles/pubsub.admin" : concat(
      local.project_team_admin,
    )
  }

  consent_screen_app_title = "Analytics Dev" ## !! Needs to be hardcoded if you don't want to destroy your IAP brand and having to rebuild your entire project

  # Cloudbuild inputs
  cloudbuild_triggers = {
    # Triggers for web app
    devTrigger = {
      name                         = "Feature-for-WebApp"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "analytics-webapp"
      branch_regex                 = "^master$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = merge(local.cloudbuild_env_vars["data-and-insights-analytics"], local.cloudbuild_env_vars["feature-trigger"])
      excluded_files_filter        = ["codefresh/**", "deploy/**"]
    }
    stagingTrigger = {
      name                         = "PR-to-Main-for-WebApp"
      description                  = "Builds and releases to dev on an opened PR for dev to be on par with staging"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "analytics-webapp"
      branch_regex                 = "^master$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = merge(local.cloudbuild_env_vars["data-and-insights-analytics"], local.cloudbuild_env_vars["PR-trigger"])
      excluded_files_filter        = ["codefresh/**", "deploy/**"]
    }
    productionTrigger = {
      name                         = "Main-for-WebApp"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "analytics-webapp"
      branch_regex                 = "^master$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = merge(local.cloudbuild_env_vars["data-and-insights-analytics"], local.cloudbuild_env_vars["main-trigger"])
      excluded_files_filter        = ["codefresh/**", "deploy/**"]
    }
  }

  cloud_run_env_variables = [
    {
      name  = "PRESTO_PORT"
      value = "443"
    },
    {
      name  = "PRESTO_PORT_OOH"
      value = "8443"
    },
    {
      name  = "PRESTO_CATALOG"
      value = "hive"
    },
    {
      name  = "PRESTO_SCHEMA"
      value = "data"
    },
    {
      name  = "APP_NAME"
      value = "mapi"
    },
    {
      name  = "REGION"
      value = "eu-west-2"
    },
    {
      name  = "ENVIRONMENT"
      value = "development"
    },
    {
      name  = "STREAM_NAME"
      value = "cathex-dev-datalake"
    }
  ]


  cloud_run_parameters = {
    data-and-insights-web-app = {
      cpu                   = "1000m"
      memory                = "2048Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 80
      vpc_access_egress     = "all-traffic" # Can be 'all-traffic' or 'private-ranges-only'


    }
  }


  # Redis

  redis_version           = "REDIS_6_X"
  memory_size_gb          = 3
  transit_encryption_mode = "DISABLED"


  # Cloud Scheduler

  url_suffix = "data_preload"
  schedule   = "45 7 * * *"

  # Datadog

  jobs_board_notification_targets = "@********************************"
  campaigns_notification_targets  = "@slack-campaigns-alerts-non-prod"

}
