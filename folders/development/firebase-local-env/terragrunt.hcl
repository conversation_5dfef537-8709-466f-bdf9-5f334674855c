terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.0.11"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloud_build_env_vars = {
    _GCR_HOSTNAME = "eu.gcr.io",
    _PLATFORM     = "managed",
    _PROJECT_ENV  = local.environment,
    _VERSION      = local.env,
    _TRIGGER_NAME = "${local.project_name}-${local.env}"
  }

  cloud_build_env_vars_main = merge(
    local.cloud_build_env_vars, {
      _TRIGGER_NAME = "Main"
    }
  )

  cloud_build_env_vars_pr = merge(
    local.cloud_build_env_vars, {
      _TRIGGER_NAME = "PR to Main"
    }
  )

  cloud_build_env_vars_feature = merge(
    local.cloud_build_env_vars, {
      _TRIGGER_NAME = "Feature"
    }
  )

  cloud_build_env_vars_emulator = merge(
    local.cloud_build_env_vars, {
      _FIREBASE_TOOLS_VERSION = "11.20.0",
      _TRIGGER_NAME           = "Emulator"
    }
  )

  cloud_build_env_vars_api = merge(
    local.cloud_build_env_vars, {
      _TRIGGER_NAME = "API"
    }
  )

  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be decalred in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name             = local.project_name
  environment              = local.environment
  consent_screen_app_title = "firebase-local-env"

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
  }


  # ---- Cloudbuild triggers config ----
  # Please make sure to add any deploy triggers to IAM in this format
  #
  # "serviceAccount:build-main@${module.project.id}.iam.gserviceaccount.com",       # Cloud build Main
  # "serviceAccount:build-pr-to-main@${module.project.id}.iam.gserviceaccount.com", # Cloud build PR
  # "serviceAccount:build-feature@${module.project.id}.iam.gserviceaccount.com",    # Cloud build feature
  #

  cloud_build_triggers = {
    devTrigger = {
      name                         = "Feature"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-local-env"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars_feature
      excluded_files_filter        = ["emulator/**"] # Can be commented out
    }
    stagingTrigger = {
      name                         = "PR-to-Main"
      description                  = "Builds and releases to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-local-env"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars_pr
      excluded_files_filter        = ["emulator/**"] # Can be commented out
    }
    productionTrigger = {
      name                         = "Main"
      description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-local-env"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars_main
      excluded_files_filter        = ["emulator/**"] # Can be commented out
    }
    emulatorTrigger = {
      name                         = "Emulator-Trigger"
      description                  = "Builds and releases the Emululator container for building"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-local-env"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars_emulator
      included_files_filter        = ["emulator/**"] # Can be commented out
    }
    apiTrigger = {
      name                         = "API-Trigger"
      description                  = "Builds and releases the API container for building"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-firebase-local-env"
      branch_regex                 = "^main$"
      invert_regex                 = false
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "api/cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars_api
      included_files_filter        = ["api/**"] # Can be commented out
    }
  }

  notifications = ""
}
