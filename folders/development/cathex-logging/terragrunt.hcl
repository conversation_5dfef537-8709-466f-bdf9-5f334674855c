terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name             = basename(get_terragrunt_dir())
  environment_longname     = basename(dirname(get_terragrunt_dir()))
  environment              = local.map_environment_to_env[local.environment_longname]
  region                   = "europe-west2"
  consent_screen_app_title = local.project_name
  repo_name                = "toolshed-dotnet-logging"
  organisation             = "cat-home-experts"


  cloudbuild_env_variables = {
    logging-test-services = {
      _GCP_REGION       = "europe-west2"
      _API_SERVICE_NAME = "logging-test-api"
      _VERSION          = "dev"
      _GCR_HOSTNAME     = "eu.gcr.io"
      _PLATFORM         = "managed"
      _ENABLE_TRAFFIC   = false
    }
  }


  # ---- only if env needed in this local block ----
  env = local.map_environment_to_env[local.environment_longname]
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    integration = "int"
    poc         = "poc"
  })

  # ------------ For Atlantis ------------
  # If you want to disable its creation, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment_longname]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    #"../../../projects/${local.project_name}/template/*.tpl*"  # example for the projects API gateways
  ]

  cloud_build_env_vars = {
    _GAR_HOSTNAME = "eu-docker.pkg.dev"
    _PLATFORM     = "managed",
    _PROJECT_ENV  = local.environment,
  }


  # ---- IAM config ----

  # NOTE: Replace <my-project> with the actual name of the project you're creating

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name   = local.project_name
  environment    = local.environment
  project_folder = local.environment_longname
  region         = local.region
  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    pushTriggerServices = {
      name                         = "logging-test-push"
      description                  = "Builds and releases to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["logging-test-services"],
        {
          _TRIGGER_NAME   = "feature"
          _ENABLE_TRAFFIC = true
      })
      included_files_filter = ["source/Cathex.Logging/**", "source/Cathex.Logging.TestHarness.Api/**"] # Can be commented out
    }
  }

  # Cloudrun inputs
  cloud_run_env_variables = {
    logging-test-api = [
      {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = "GCP_Development"
      }
    ]
  }

  cloud_run_parameters = {
    logging-test-api = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    }
  }
  # Cloud Run vars
  #container_concurrency = 3
  #initial_scale         = 0
  #min_scale             = 0
  #max_scale             = 1

}


