terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })


  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    # "../../../projects/${local.project_name}/template/*.tpl*", # example of API gateway template trigger
  ]


  # ------------ Cloudbuild ------------
  repo_name    = "directories"
  organisation = "cat-home-experts"
  cloud_build_env_vars = {
    emulator = {}
    directories_webapp = {
      _SERVICE_NAME              = "directories-webapp"
      _GAR_HOSTNAME              = "eu-docker.pkg.dev"
      _PLATFORM                  = "managed"
      _PROJECT_ENV               = local.environment
      _ENABLE_ROUTE_BOOKINGS_NEW = "true"
      _SALESFORCE_URL            = "https://checkatrade--dev5.sandbox.my.salesforce.com"
      _AZURE_CLIENT_ID           = local.azure_client_id
      _MAPBOX_ACCESS_TOKEN       = "pk.eyJ1IjoiY2hlY2thdHJhZGUiLCJhIjoiY201bXJlYW03MDM0MTJqcjQ3MHp4MXg5NiJ9.U7I-xsfbtV9ETY4ENa647Q" # gitleaks:allow
    }
    gateway = {
      _GCP_REGION           = "europe-west2"
      _SERVICE_NAME         = "directories-webapp"
      _VERSION              = local.map_environment_to_env[local.environment]
      _PLATFORM             = "managed"
      _API_GATEWAY_NAME     = "directories-webapp-gw"
      _API_GATEWAY_API_NAME = "directories-webapp"
    }
  }


  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  content_api_uri                                   = "https://content-api-x2k2mja74q-nw.a.run.app"
  content_api_iap_client_id                         = "161849807894-cgr6d2ed9tg479jm6l3aus9643c5vmp1.apps.googleusercontent.com"
  cloud_build_trigger_web_app_excluded_files_filter = ["api-gateway/**", "**/*.md", "scripts/**"]
  azure_client_id                                   = "39a84afc-9f7c-4563-9700-b9b3a3a8edec"
  azure_openai_api_key                              = "4e1d4cafaab748a78d4181732118a514"
  secure_contacts_api_uri                           = "https://secure-contacts-api-5krtc7h7da-nw.a.run.app"
  secure_contacts_api_iap_client_id                 = "367652344333-ap31stt4gnehhflj5d5rca4mks3mv48s.apps.googleusercontent.com"
  artwork_bucketname                                = "directories-artwork-storage-22"
  api_cors_origins                                  = ["https://dev.ops-tooling-app.checkatrade.com", "https://ops-tooling-app-y7kjeuv2iq-nw.a.run.app"]
  campaigns_api_uri                                 = "https://campaigns-api-1035650116410.europe-west2.run.app"

  azure = {
    authority     = "https://login.microsoftonline.com/e59cb63b-8731-41b4-8032-403e6dee2247/v2.0"
    audience      = "api://${local.azure_client_id}"
    valid_issuers = ["https://sts.windows.net/e59cb63b-8731-41b4-8032-403e6dee2247/"]
  }

  salesforce = {
    authority     = "https://checkatrade--dev5.sandbox.my.salesforce.com"
    audience      = "3MVG9buXpECUESHj55btFaoXyUxeFYXMPPShF0NnF8vbkSWQAJrO5kYGNnBBS_s_loTUWEVnyyFo8Jen7j4yN"
    valid_issuers = ["https://checkatrade--dev5.sandbox.my.salesforce.com"]
  }

  # cloud SQL
  cloud_sql_disk_size = 250
}

# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- env Vars -----
  content_api_uri                   = local.content_api_uri
  content_api_iap_client_id         = local.content_api_iap_client_id
  azure_openai_api_key              = local.azure_openai_api_key
  secure_contacts_api_uri           = local.secure_contacts_api_uri
  secure_contacts_api_iap_client_id = local.secure_contacts_api_iap_client_id
  artwork_bucketname                = local.artwork_bucketname
  api_cors_origins                  = local.api_cors_origins
  azure                             = local.azure
  salesforce                        = local.salesforce
  campaigns_api_uri                 = local.campaigns_api_uri

  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment

  project_folder = "development"

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/iam.securityAdmin" : concat(
      local.project_team_admin,
    )
    "roles/apigateway.admin" : concat(
      local.project_team_admin,
    )
    "roles/appengine.appAdmin" : concat(
      local.project_team_admin,
    )
    "roles/cloudsql.admin" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/cloudsql.instanceUser" : concat(
      local.project_team_admin,
    )
    "roles/appengine.deployer" : concat(
      local.project_team,
      local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
    )
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/iam.serviceAccountTokenCreator" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/storage.objectViewer" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/storage.objectUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/cloudscheduler.jobRunner" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/run.admin" : concat(
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/secretmanager.secretVersionManager" : concat(
      local.project_team_admin,
    )
    "roles/secretmanager.secretAccessor" : concat(
      local.project_team_admin,
    )
    "roles/storage.admin" : concat(
      local.project_team_admin
    )
    "roles/cloudfunctions.developer" : concat(
      local.project_team_admin
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    pushGatewayTriggerApi = {
      name                         = "api-gateway-push"
      description                  = "Builds and releases to dev on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = local.repo_name
      branch_regex                 = ".*"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.gateway
      included_files_filter        = ["api-gateway/**"]
    }

    devBranchTrigger = {
      name                         = "Push-from-dev-branch"
      description                  = "Builds and releases to dev on any dev branch."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.directories_webapp
      excluded_files_filter        = local.cloud_build_trigger_web_app_excluded_files_filter
    }
    mainBranchTrigger = {
      name                         = "Push-from-main-branch"
      description                  = "Builds and releases to dev on any push from main."
      disabled                     = true
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.directories_webapp
      excluded_files_filter        = local.cloud_build_trigger_web_app_excluded_files_filter
    }
    build_firestore_emulator = {
      name                         = "firestore-emulator"
      description                  = "Builds Docker image on any commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = "gcp-firebase-emulator"
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.emulator
      excluded_files_filter        = ["README.md"]
    }
  }

  cloud_run_parameters = {
    directories-webapp = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 30
    }
  }

  replication_private_ip = "*************"

  #cloud SQL
  cloud_sql_disk_size = local.cloud_sql_disk_size
}
