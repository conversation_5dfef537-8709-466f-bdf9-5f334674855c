terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to your project Terraform files
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terrargunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })


  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.env}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
    # "../../../projects/${local.project_name}/template/*.tpl*", # example of API gateway template trigger
  ]

  # ------------ Cloudbuild ------------
  repo_name    = "campaigns"
  organisation = "cat-home-experts"
  cloud_build_env_vars = {
    gateway = {
      _GCP_REGION                  = "europe-west2"
      _SERVICE_NAME                = "campaigns-api"
      _VERSION                     = local.environment
      _PLATFORM                    = "managed"
      _API_GATEWAY_NAME            = "campaigns-api-gw"
      _API_GATEWAY_API_NAME        = "campaigns-api"
      _TRADE_EXPERIENCE_PROJECT_ID = "trade-experience-dev-35966"
    }
    campaigns-api = {
      _GAR_HOSTNAME                = "europe-west2-docker.pkg.dev"
      _PLATFORM                    = "managed"
      _PROJECT_ENV                 = local.environment
      _SERVICE_NAME                = "campaigns-api"
      _ENABLE_TRAFFIC              = false
      _CORS_OPTIONS_ALLOWED_ORIGIN = "https://membersapp-development.checkatrade.com, https://trade-experience-dev-35966.nw.r.appspot.com, http://localhost:19006"
    }
    campaigns-core = {
      _PROJECT_ENV = local.environment
    }
  }

  functions_env_vars = {
    _PROJECT_ENV = local.env,
  }

  jobs_env_vars = {
    _PROJECT_ENV = local.env,
  }

  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  qa_team            = ["group:<EMAIL>"]
  qa_team_admin      = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )

  # Admin Auth Vars
  salesforce_fqdn        = "checkatrade--enterprise.sandbox.my.salesforce.com"
  firebase_app_id        = "1:1035650116410:web:80696df247c4465dba3e03"
  firebase_auth_domain   = "campaigns-dev-39316.firebaseapp.com"
  client_id              = "3MVG9LlLrkcRhGHb0CW9EfJ9bRcAdRBMFEwbXE3p8baexxYT.Q2fYRSExqoQ14cfpM8tW50NJ64ECj8TBR1u0" # Salesforce ConsumerKey
  allowed_frame_ancestor = "https://checkatrade--enterprise.sandbox.lightning.force.com"
  external_url           = "https://campaigns-admin-dev.checkatrade.com"
  admin_app_service_name = "campaigns-admin"    # Backend service for Salesforce Admin embedded app
  short_api_id           = "campaigns-admin-ui" # _API_ID that comes from the module includes the full path
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be declared in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----
  project_name = local.project_name
  environment  = local.environment

  # D+I's project only exists in dev and prod so we can't use wildcarding for staging
  # so instead we'll just hardcode
  data_stats_project_id = "data-stats-service-dev-24217"

  logging_retention_days = 3

  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers
  everyone           = local.everyone

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/iam.serviceAccountUser" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/viewer" : concat(
      local.everyone,
    )
    "roles/logging.viewAccessor" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/logging.viewer" : concat(
      local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
      local.project_team_admin,
    )
    "roles/firebase.admin" : concat(
      local.project_team_admin,
      local.project_team
    )
    "roles/cloudbuild.builds.editor" : concat(
      local.project_team,
      local.project_team_admin,
      local.qa_team,
      local.qa_team_admin,
    )
    "roles/run.developer" : concat(
      local.project_team,
    )
    "roles/run.admin" : concat(
      local.project_team_admin,
    )
    "roles/pubsub.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/pubsub.editor" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/datastore.user" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/servicemanagement.admin" : concat(
      local.project_team,
      local.project_team_admin,
    )
    "roles/serviceusage.serviceUsageAdmin" : concat(
      local.project_team_admin,
    )
    "roles/secretmanager.admin" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/serviceusage.apiKeysAdmin" : concat(
      local.project_team_admin,
      local.project_team,
    )
    "roles/cloudscheduler.viewer" : concat(
      local.project_team,
    )
    "roles/cloudscheduler.admin" : concat(
      local.project_team_admin,
    )
    "roles/bigquery.dataOwner" : concat(
      local.project_team,
    )
    "roles/bigquery.admin" : concat(
      local.project_team_admin,
    )
    "roles/apigateway.admin" : concat(
      local.project_team_admin
    )
  }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {
    #####################################
    # API cloud build triggers
    #####################################
    apiDevTrigger = {
      name                         = "Api-Feature"
      description                  = "Builds and releases Campaigns Api to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "service/cloudbuild.campaigns.api.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-api
      included_files_filter        = ["service/**", "contracts/**", "common/**"]
    }
    apiStagingTrigger = {
      name                         = "Api-PR-to-Main"
      description                  = "Builds and releases Campaigns Api to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "service/cloudbuild.campaigns.api.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-api
      included_files_filter        = ["service/**", "contracts/**", "common/**"]
    }
    apiProductionTrigger = {
      name                         = "Api-Main"
      description                  = "Builds and releases Campaigns Api to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "service/cloudbuild.campaigns.api.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-api
      included_files_filter        = ["service/**", "contracts/**", "common/**"]
    }
    #####################################
    # Core & Contracts cloud build triggers
    #####################################
    coreDevTrigger = {
      name                         = "Core-Feature"
      description                  = "Builds and releases Campaigns Core to dev on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      filename                     = "common/cloudbuild.campaigns.core.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-core
      included_files_filter        = ["common/**", "contracts/**"]
    }
    coreStagingTrigger = {
      name                         = "Core-PR-to-Main"
      description                  = "Builds and releases Campaigns Core to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "common/cloudbuild.campaigns.core.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-core
      included_files_filter        = ["common/**", "contracts/**"]
    }
    coreProductionTrigger = {
      name                         = "Core-Main"
      description                  = "Builds and releases Campaigns Core to dev on a commit to main, for dev to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "common/cloudbuild.campaigns.core.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-core
      included_files_filter        = ["common/**", "contracts/**"]
    }
    #####################################
    # Functions cloud build triggers
    #####################################
    functionsDevTrigger = {
      name                         = "Functions-PR-to-Main"
      description                  = "Builds and releases Functions to dev on an opened PR to main"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = local.functions_env_vars
      filename                     = "functions/cloudbuild.functions.yaml"
      included_files_filter        = ["functions/**", "contracts/**", "common/**"]
    }
    functionsProductionTrigger = {
      name                         = "Functions-Main"
      description                  = "Builds and releases Functions to dev on push to main, for dev to be on par with production"
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = local.functions_env_vars
      filename                     = "functions/cloudbuild.functions.yaml"
      included_files_filter        = ["functions/**", "contracts/**", "common/**"]
    }
    #####################################
    # DataStrem cloud build triggers
    #####################################
    dataStreamDevTrigger = {
      name                         = "DataStream-Feature"
      description                  = "Builds and releases data-stream to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables                = {}
      filename                     = "data-stream/cloudbuild.data-stream.yaml"
      included_files_filter        = ["data-stream/**"]
    }
    dataStreamStagingTrigger = {
      name                         = "DataStream-PR-to-Main"
      description                  = "Builds and releases data-stream to dev on an opened PR, for dev to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = {}
      filename                     = "data-stream/cloudbuild.data-stream.yaml"
      included_files_filter        = ["data-stream/**"]
    }
    dataStreamProductionTrigger = {
      name                         = "DataStream-Main"
      description                  = "Builds and releases data-stream to dev on push to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = {}
      filename                     = "data-stream/cloudbuild.data-stream.yaml"
      included_files_filter        = ["data-stream/**"]
    }
    #####################################
    # Emulator cloud build trigger
    #####################################
    emulatorTrigger = {
      name                         = "Emulator"
      description                  = "Builds and releases emulator so that other builds can work."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.campaigns-api
      included_files_filter        = ["emulator/**"]
    }
    #####################################
    # Api Gateway cloud build trigger
    #####################################
    pushGatewayTriggerApi = {
      name                         = "Gateway-api-push"
      description                  = "Builds and releases API Gateway to dev on push to main."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = ".*"
      invert_regex                 = false
      filename                     = "api-gateway/cloudbuild.yaml"
      env_variables                = local.cloud_build_env_vars.gateway
      included_files_filter        = ["api-gateway/**", "spec/**"]
    }
    #####################################
    # Firebase cloud build triggers
    #####################################
    firebaseDevTrigger = {
      name                         = "Firebase-Feature"
      description                  = "Runs tests on dev on any non-main commit."
      filename                     = "firebase/cloudbuild.yaml"
      included_files_filter        = ["firebase/**"]
      disabled                     = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
      env_variables = {
        _TRIGGER_NAME = "feature"
        _VERSION      = "dev"
      }
    }
    firebaseStagingTrigger = {
      name                         = "Firebase-PR-to-Main"
      description                  = "Tests and deploys firebase config to dev on an opened PR to main, for dev to be on par with staging."
      filename                     = "firebase/cloudbuild.yaml"
      included_files_filter        = ["firebase/**"]
      disabled                     = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _TRIGGER_NAME = "feature"
        _VERSION      = "dev"
      }
    }
    firebaseProductionTrigger = {
      name                         = "Firebase-Main"
      description                  = "Tests and deploys firebase config to dev on any push to main, for dev to be on par with production."
      filename                     = "firebase/cloudbuild.yaml"
      included_files_filter        = ["firebase/**"]
      disabled                     = false
      owner                        = local.organisation
      repo_name                    = local.repo_name
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _TRIGGER_NAME = "feature"
        _VERSION      = "dev"
      }
    }
    #####################################
    # Jobs cloud build triggers
    #####################################
    jobsDevTrigger = {
      name                         = "Jobs-PR-to-Main"
      description                  = "Builds and releases Jobs to dev on an opened PR to main"
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      owner                        = local.organisation
      repo_name                    = local.repo_name
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables                = local.jobs_env_vars
      filename                     = "jobs/cloudbuild.jobs.yaml"
      # TODO: add ["contracts/**", "common/**"] when PoC will be approved
      included_files_filter = ["jobs/**"]
    }
    # TODO: jobsStaging and jobsProduction triggers when PoC will be approved
  }

  cloud_run_parameters = {
    campaigns-api = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 10
    },
    campaigns-admin = {
      cpu                   = "1000m"
      memory                = "512Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 10
    }
  }

  # Cloud Run inputs
  cloud_run_env_variables = {
    campaigns-api = [
      {
        "name"  = "CORS_OPTIONS_ALLOWED_ORIGIN"
        "value" = local.cloud_build_env_vars["campaigns-api"]._CORS_OPTIONS_ALLOWED_ORIGIN
      },
      {
        "name"  = "LoggingConfig__NamespaceLogLevelOverrides__Cathex.Campaigns.Core.Clients"
        "value" = "Debug"
      },
      {
        "name"  = "CloudRunConfig__DeployedEnvironment"
        "value" = local.environment
      }
    ],
    campaigns-admin = []
  }

  #####################################
  # Admin auth inputs
  #####################################
  salesforce_fqdn        = local.salesforce_fqdn
  firebase_app_id        = local.firebase_app_id
  firebase_auth_domain   = local.firebase_auth_domain
  client_id              = local.client_id
  allowed_frame_ancestor = local.allowed_frame_ancestor
  external_url           = local.external_url
  admin_app_service_name = local.admin_app_service_name
  short_api_id           = local.short_api_id

  cloud_build_triggers_for_admin_auth = {
    development_trigger = {
      name                         = "Feature-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = true
    }
    staging_trigger = {
      name                         = "PR-to-Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on an opened PR, for ${basename(dirname(get_terragrunt_dir()))} to be on par with staging."
      disabled                     = false
      push_trigger_enabled         = false
      pull_request_trigger_enabled = true
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
    production_trigger = {
      name                         = "Main-for-Admin-Auth"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on a commit to main, for ${basename(dirname(get_terragrunt_dir()))} to be on par with production."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
    }
  }

  jobs_board_notification_targets = "@slack-jobs-board-alerts-non-prod"
  product_catalog_api_base_url    = "https://api.checkatrade.com/v1/product-catalog"
  finance_api_base_url            = "https://api.staging.checkatrade.com/v1/finance/product-catalog"

  capi_service_accounts = [
    # this is the poorly named finance service, not the Product Catalog
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    # Shared service accounts for the same services
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
  ]
}
