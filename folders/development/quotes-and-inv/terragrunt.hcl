terraform {
  source = "./../../..//projects/quotes-and-inv"
}

include {
  path = find_in_parent_folders()
}

locals {
  terraform_version = "1.3.9"

  gcp_region = "europe-west2"

  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))

  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v${local.terraform_version}"

  team_access = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  owner_access = ["group:<EMAIL>"]

  email_service_project_id = "email-service-prod-17114"
}

inputs = {
  project_name = local.project_name
  environment  = local.environment

  project_static_permissions = {
    "roles/run.developer" : local.team_access,
    "roles/iam.serviceAccountUser" : local.team_access
    "roles/secretmanager.secretAccessor" : local.team_access,
    "roles/secretmanager.secretVersionManager" : local.owner_access,
    "roles/serviceusage.apiKeysViewer" : local.owner_access,
    "roles/cloudbuild.builds.editor" : local.owner_access,
    "roles/logging.viewer" : local.owner_access,
    "roles/iam.serviceAccountTokenCreator" : local.team_access,
    "roles/artifactregistry.reader" : local.team_access,
    "roles/artifactregistry.repoAdmin" : local.owner_access,
  }

  cloudbuild_triggers_pdf_generator = {
    main_trigger = {
      name                         = "pdf-generator--main"
      description                  = "Builds and releases to ${basename(dirname(get_terragrunt_dir()))} on any merge to main."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _EMAIL_SERVICE_PROJECT_ID = local.email_service_project_id
        _ENABLE_TRAFFIC           = "false"
        _GCP_REGION               = local.gcp_region
        _GCR_HOSTNAME             = "${local.gcp_region}-docker.pkg.dev"
        _MACHINE_TYPE             = "E2"
        _PLATFORM                 = "managed"
        _SERVICE_NAME             = "pdf-generator"
        _TRIGGER_NAME             = "main"
        _VERSION                  = local.environment
      }
      disabled = false
    }
  }

  cloudbuild_triggers_base_docker_images = {
    dotnet_aspnet_libx_trigger = {
      name                         = "dotnet-aspnet-libx--main"
      description                  = "Builds and pushes the docker image on any commit."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION   = local.gcp_region
        _TRIGGER_NAME = "main"
        _VERSION      = local.environment
      }
      filename              = "base-docker-images/dotnet-aspnet-8-libx/cloudbuild.yaml"
      included_files_filter = ["base-docker-images/dotnet-aspnet-8-libx/**"]

      disabled = false
    }

    dotnet_sdk_libx_trigger = {
      name                         = "dotnet-sdk-libx--main"
      description                  = "Builds and pushes the docker image on any commit."
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      branch_regex                 = "^main$"
      invert_regex                 = false
      env_variables = {
        _GCP_REGION   = local.gcp_region
        _TRIGGER_NAME = "main"
        _VERSION      = local.environment
      }
      filename              = "base-docker-images/dotnet-sdk-8-libx/cloudbuild.yaml"
      included_files_filter = ["base-docker-images/dotnet-sdk-8-libx/**"]

      disabled = false
    }
  }

  cloud_run_pdf_generator_env_variables = [
    {
      "name"  = "LOG_LEVEL_DEFAULT"
      "value" = "Error"
    },
    {
      "name"  = "EMAIL_SERVICE_PROJECT_ID"
      "value" = local.email_service_project_id
    },
    {
      "name"  = "DD_TRACE_ENABLED"
      "value" = "true"
    },
    {
      "name"  = "DD_APM_ENABLED"
      "value" = "true"
    }
  ]

  cloud_run_pdf_generator_parameters = {
    cpu                   = "4000m"
    memory                = "2048Mi"
    max_scale             = 30
    min_scale             = 0
    initial_scale         = 0
    container_concurrency = 40
  }
}
