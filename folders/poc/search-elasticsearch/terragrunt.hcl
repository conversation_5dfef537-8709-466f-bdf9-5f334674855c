terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}" # Links this config to the Terraform templates
}

include {
  path = find_in_parent_folders() # Required to link this config to the top-level 'terragunt.hcl' file
}

# -------  The objects below are locally computed but never inserted into the Terraform remote state -------
# To switch to a stateful mode, you need to pass their values as an input, see the next block.
# Locals are useful for factorization or better readibility when using Terraform functions.
# ----------------------------------------------------------------------------------------------------------
locals {
  # ---- Should be created for any project ----
  project_name = "${basename(get_terragrunt_dir())}"
  environment  = "${basename(dirname(get_terragrunt_dir()))}"
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })


  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project      = true
  atlantis_project_name = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]


  # ------------ Cloudbuild ------------
  cloud_build_env_vars = {
    _GCR_HOSTNAME = "eu.gcr.io",
    _PLATFORM     = "managed",
    _PROJECT_ENV  = local.environment,
  }


  # ---- IAM config ----

  # Teams definitions
  project_team       = ["group:<EMAIL>", "user:<EMAIL>", "user:<EMAIL>"]
  project_team_admin = local.project_team
  qa_team            = local.project_team
  qa_team_admin      = local.project_team
  viewers            = local.project_team
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.qa_team,
    local.qa_team_admin,
    local.viewers,
  )
}


# -------  The objects below are part of the environment variables injection into your Terraform code -------
# They all need to be decalred in your variables.tf
# ------------------------------------------------------------------------------------------------------------
inputs = {
  # ---- Should be passed to any project ----

  project_name = local.project_name
  environment  = local.environment


  # ---- IAM config ----

  # Permissions for humans only
  project_team       = local.project_team
  project_team_admin = local.project_team_admin
  qa_team            = local.qa_team
  qa_team_admin      = local.qa_team_admin
  viewers            = local.viewers

  roles_list = [
    {
      name               = "role-search-document-read"
      indices            = ["cat-search-*"]
      index_privileges   = ["read"]
      cluster_privileges = []
    },
    {
      name               = "role-search-document-write"
      indices            = ["cat-search-*"]
      index_privileges   = ["create_doc", "write"]
      cluster_privileges = []
    },
    {
      name               = "role-search-index-read"
      indices            = ["cat-search-*"]
      index_privileges   = ["index", "view_index_metadata"]
      cluster_privileges = []
    },
    {
      name               = "role-search-index-write"
      indices            = ["*"]
      index_privileges   = ["create_index", "delete_index", "manage"]
      cluster_privileges = []
    },
    {
      name               = "role-search-document-delete"
      indices            = ["cat-search-injection-candidates"]
      index_privileges   = ["delete"]
      cluster_privileges = []
    },
    # {
    #   name               = "role-health-read"
    #   indices            = [""]
    #   index_privileges   = []
    #   cluster_privileges = ["monitor"]
    # },

  ]

  users_list = [
    {
      username  = "search-team-elastic-cloud-search-indexer-dev"
      full_name = ""
      roles     = ["role-search-document-read", "role-search-document-write", "role-search-index-read", "role-search-index-write", "role-search-document-delete", "role-health-read", "superuser"]
      email     = ""
      enabled   = true
    },
    {
      username  = "search-team-elastic-cloud-search-service-dev"
      full_name = ""
      roles     = ["role-search-document-read", "role-search-index-read", "role-health-read", "kibana_admin", "superuser"]
      email     = ""
      enabled   = true
    },
  ]

  # ⚠️ Remove what is not required, POLP... Principle of Least Privilege
  project_static_permissions = {
    "roles/owner" : concat(
      local.project_team,
    )

  }
  # project_static_permissions = {
  #   "roles/appengine.appAdmin" : concat(
  #     local.project_team_admin,
  #   )
  #   "appengine.deployer" : concat(
  #     local.project_team,
  #     local.project_team_admin, # Keeping in mind that a user should be part of one project group only, either in project_team or project_team_admin
  #   )
  #   "roles/iam.serviceAccountUser" : concat(
  #     local.project_team,
  #     local.project_team_admin,
  #   )
  #   "roles/cloudbuild.builds.editor" : concat(
  #     local.project_team,
  #     local.project_team_admin,
  #     local.qa_team,
  #     local.qa_team_admin,
  #   )
  #   "roles/viewer" : concat(
  #     local.everyone,
  #   )
  #   "roles/logging.viewAccessor" : concat(
  #     local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
  #     local.project_team_admin,
  #   )
  #   "roles/logging.viewer" : concat(
  #     local.project_team, # You'll have to remove the 'roles/viewer' from local.everyone if you want to enforce that rule
  #     local.project_team_admin,
  #   )
  # }


  # ---- Cloudbuild triggers config ----

  cloud_build_triggers = {}
  # cloud_build_triggers = {
  #   devTrigger = {
  #     name                         = "Feature-for-My-Project"
  #     description                  = "Builds and releases to dev on any non main commit."
  #     disabled                     = false
  #     push_trigger_enabled         = true
  #     pull_request_trigger_enabled = false
  #     owner                        = "cat-home-experts"
  #     repo_name                    = "my-project"
  #     branch_regex                 = "^main$"
  #     invert_regex                 = true
  #     filename                     = "cloudbuild.yaml"
  #     env_variables                = local.cloud_build_env_vars
  #     excluded_files_filter        = ["emulator/**"] # Can be commented out
  #   }
  #   stagingTrigger = {
  #     name                         = "PR-to-Main-for-My-Project"
  #     description                  = "Builds and releases to dev on an opened PR, for dev to be on par with staging."
  #     disabled                     = false
  #     push_trigger_enabled         = false
  #     pull_request_trigger_enabled = true
  #     owner                        = "cat-home-experts"
  #     repo_name                    = "my-project"
  #     branch_regex                 = "^main$"
  #     invert_regex                 = false
  #     filename                     = "cloudbuild.yaml"
  #     env_variables                = local.cloud_build_env_vars
  #     excluded_files_filter        = ["emulator/**"] # Can be commented out
  #   }
  #   productionTrigger = {
  #     name                         = "Main-for-My-Project"
  #     description                  = "Builds and releases to dev on a commit to main, for dev to be on par with production."
  #     disabled                     = false
  #     push_trigger_enabled         = true
  #     pull_request_trigger_enabled = false
  #     owner                        = "cat-home-experts"
  #     repo_name                    = "my-project"
  #     branch_regex                 = "^main$"
  #     invert_regex                 = false
  #     filename                     = "cloudbuild.yaml"
  #     env_variables                = local.cloud_build_env_vars
  #     excluded_files_filter        = ["emulator/**"] # Can be commented out
  #   }
  # }
}
