terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

include {
  path = find_in_parent_folders()
}

locals {
  project_name = basename(get_terragrunt_dir())
  environment  = basename(dirname(get_terragrunt_dir()))
  env          = local.map_environment_to_env[local.environment]
  region       = "europe-west2"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]

  cloudbuild_env_variables = {
    api = {
      _GCP_REGION     = "europe-west2"
      _SERVICE_NAME   = "api"
      _VERSION        = "poc"
      _GCR_HOSTNAME   = "eu.gcr.io"
      _PLATFORM       = "managed",
      _ENABLE_TRAFFIC = false
    }
    emulator = {}
  }


  project_team       = ["group:<EMAIL>", "group:<EMAIL>"]
  project_team_admin = ["group:<EMAIL>"]
  viewers = [
    "group:<EMAIL>",
    "group:<EMAIL>",
  ]
  everyone = concat(
    local.project_team,
    local.project_team_admin,
    local.viewers,
  )
}


inputs = {
  project_name   = local.project_name
  environment    = local.env
  project_folder = local.environment
  region         = local.region

  project_static_permissions = {
    "roles/run.developer" : local.project_team,
    "roles/iam.serviceAccountUser" : local.project_team,
    "roles/cloudbuild.builds.editor" : local.project_team,
    "roles/run.invoker" : concat(
      local.project_team,
      local.project_team_admin,
    ),
    "roles/viewer" : local.viewers,
  }

  cloudbuild_triggers = {

    #api
    pocTriggerApi = {
      name                         = "api-feature"
      description                  = "Builds and releases to poc on any non main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-cloud-run-dotnet-core-template"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "cloudbuild.yaml"
      env_variables = merge(local.cloudbuild_env_variables["api"],
        {
          _TRIGGER_NAME   = "feature"
          _ENABLE_TRAFFIC = true
          _PUBSUB_SA      = "<EMAIL>"
          _BASE_URL       = "api-bhhczffxjq-nw.a.run.app"
          _REQ_TOPICS     = "test-topic" # comma separated?
      })
      excluded_files_filter = ["emulator/**"]
    }

    # Triggers for emulator
    emulatorTrigger = {
      name                         = "Feature-for-Emulator"
      description                  = "Builds and releases to dev on any non-main commit."
      disabled                     = false
      push_trigger_enabled         = true
      pull_request_trigger_enabled = false
      owner                        = "cat-home-experts"
      repo_name                    = "gcp-cloud-run-dotnet-core-template"
      branch_regex                 = "^main$"
      invert_regex                 = true
      comment_control              = "COMMENTS_DISABLED"
      filename                     = "emulator/cloudbuild.yaml"
      env_variables                = local.cloudbuild_env_variables.emulator
      included_files_filter        = ["emulator/**"]
    }
  }

  # Cloudrun inputs
  cloud_run_env_variables = {
    api = []
  }

  cloud_run_parameters = {
    api = {
      cpu                   = "1000m"
      memory                = "1024Mi"
      max_scale             = 2
      min_scale             = 0
      initial_scale         = 0
      container_concurrency = 2
    }
  }

  apis = []


}
