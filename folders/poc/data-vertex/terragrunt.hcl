include {
  path = find_in_parent_folders()
}

terraform {
  source = "./../../..//projects/${basename(get_terragrunt_dir())}"
}

locals {
  project_name       = basename(get_terragrunt_dir())
  environment        = basename(dirname(get_terragrunt_dir()))
  project_team_admin = "<EMAIL>"
  team_email         = "<EMAIL>"
  map_environment_to_env = tomap({
    dev         = "dev"
    development = "dev"
    staging     = "stg"
    production  = "prod"
    business    = "biz"
    core        = "core"
    poc         = "poc"
    integration = "int"
  })

  # ------------ For Atlantis ------------
  # If you want to prevent Atlantis from seeing your project, switch the below variable to false
  atlantis_project           = true
  atlantis_project_name      = "${local.project_name}-${local.map_environment_to_env[local.environment]}"
  atlantis_terraform_version = "v1.3.9"
  extra_atlantis_dependencies = [
    "config.enc.yaml",
  ]
}

inputs = {
  project_name       = local.project_name
  environment        = local.environment
  project_team_admin = local.project_team_admin
  team_email         = local.team_email
}
