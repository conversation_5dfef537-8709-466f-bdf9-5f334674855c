# Should iacbot update your PR Status Check
pr_status_enabled: true

# Maximum allowable findings, above which
# PR status check will fail
pr_status_thresholds:
    critical: 0
    high: 0
    medium: 50
    low: 999

# Force the status check to pass.  Checks will still be run.
# Effectively the same as raising thresholds to high values.
pr_status_force_pass_enabled: true

# Set to true if you want iacbot to add PR comments
pr_comments_enabled: true

# If set to true, a PR comment will be added, even if
# the status check passes.
pr_comments_on_passed_check_enabled: true
