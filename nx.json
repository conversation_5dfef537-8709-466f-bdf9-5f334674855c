{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "main", "release": {"projects": ["src/sdk/*", "src/types/*"], "projectsRelationship": "independent", "version": {"conventionalCommits": true, "generatorOptions": {"fallbackCurrentVersionResolver": "disk", "skipLockFileUpdate": true}}, "git": {"commitMessage": "publish: release [skip ci]"}, "changelog": {"automaticFromRef": true, "projectChangelogs": {"createRelease": "github", "file": false}, "renderOptions": {"authors": false}}, "conventionalCommits": {"types": {"ci": false, "platform": false, "publish": false, "tests": false, "docs": {"semverBump": "patch"}, "chore": {"semverBump": "patch"}, "refactor": {"semverBump": "patch"}}}}, "targetDefaults": {"build": {"cache": true, "dependsOn": ["^build"]}, "test": {"cache": true, "dependsOn": ["^build"]}, "lint": {"cache": true}, "start:dev": {"dependsOn": ["^build"], "cache": false}}}