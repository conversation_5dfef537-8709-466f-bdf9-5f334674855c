base:
  global:
    environment: production

  applications:
    trade-app-bff:
      resources:
        requests:
          memory: 512Mi
          cpu: 400m
        limits:
          memory: 1024Mi
      autoscaling:
        enabled: true
        minimum: 1
        maximum: 20
      monitoring:
        availability:
          enabled: false
          message: "trade-bff prod availability below target threshold @slack-trade-experience-alerts-prod"
          priority: 2
        filteredAvailability:
          enabled: true
          availabilityTarget: 96
          message: "trade-bff prod availability below target threshold @slack-trade-experience-alerts-prod"
          priority: 2
          excludeStatusCodes:
            - "404"
        crashes:
          enabled: true
          maxRestarts: 10
          message: "trade-bff prod crashed more than 10 times in the last 5 minutes @slack-trade-experience-alerts-prod"
          priority: 2
      env:
        CAPI_GCP_PROJECT_ID: capi-production-21756
        COMMS_API_URL: https://communication-api-k2eccir75a-nw.a.run.app
        COMMS_GCP_PROJECT_ID: communication-prod-39311
        COMMS_REPORT_USER_VERSION: V1
        CONTENT_API_URL: https://content-api-2o3rbep6jq-nw.a.run.app
        ENTERPRISE_API_ENABLED: true
        ENTERPRISE_API_URL: http://api-opportunity.core-opportunity.svc.cluster.local:3000
        GCP_SA_FIREBASE_AUTH: <EMAIL>
        MEDIA_SERVICE_API_URL: https://api.checkatrade.com/v1/media-service
        MITEK_ENVIRONMENT: revet-prod
        NATIONAL_ACCOUNTS_API_URL: https://europe-west2-national-accounts-prod-37614.cloudfunctions.net/nationalAccounts
        SALESFORCE_INTEGRATION_GCP_PROJECT_ID: salesforce-integ-prod-38869
        SEARCH_API_URL: https://search-gateway-gw-kt7n3o6.nw.gateway.dev/api/v1
        TRADE_FIREBASE_APP_PROJECT_ID: cat-trades
        TRADE_SIGNUP_WEB_URL: https://member-onboarding.checkatrade.com
        CONSUMER_WEB_URL: https://www.checkatrade.com
        TRADE_WEB_URL: https://membersapp.checkatrade.com
      pubsub:
        enabled: true
        topics:
          - name: payments-off-platform-job-events
            additionalPublishers:
              - name: bff-consumer-public
          - name: employee-consent
          - name: self-service-requests
            subscriptions:
              - type: push
                endpoint: https://salesforce-gcp-integ-zydxszivba-nw.a.run.app/pubsub/self-service-requests
                retryPolicy:
                  minimumBackoff: 5s
                  maximumBackoff: 600s
                deadLetterPolicy:
                  enabled: true
                  maxDeliveryAttempts: 5
