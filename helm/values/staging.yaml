base:
  global:
    environment: staging

  applications:
    trade-app-bff:
      autoscaling:
        enabled: true
        minimum: 0
        maximum: 15
      monitoring:
        availability:
          enabled: false
          message: "trade-bff staging availability below target threshold @slack-trade-experience-alerts-non-prod"
          priority: 3
        filteredAvailability:
          enabled: true
          availabilityTarget: 96
          message: "trade-bff staging availability below target threshold @slack-trade-experience-alerts-non-prod"
          priority: 3
          excludeStatusCodes:
            - "404"
        crashes:
          enabled: true
          maxRestarts: 10
          message: "trade-bff staging crashed more than 10 times in the last 5 minutes @slack-trade-experience-alerts-non-prod"
          priority: 3
      env:
        CAPI_GCP_PROJECT_ID: capi-staging-27323
        COMMS_API_URL: https://communication-api-66w7cgdjcq-nw.a.run.app
        COMMS_GCP_PROJECT_ID: communication-stg-24665
        COMMS_REPORT_USER_VERSION: V1
        CONTENT_API_URL: https://content-api-inhqerqcdq-nw.a.run.app
        ENTERPRISE_API_ENABLED: true
        ENTERPRISE_API_URL: http://api-opportunity.core-opportunity.svc.cluster.local:3000
        GCP_SA_FIREBASE_AUTH: <EMAIL>
        MEDIA_SERVICE_API_URL: https://api.staging.checkatrade.com/v1/media-service
        MITEK_ENVIRONMENT: revet-test
        NATIONAL_ACCOUNTS_API_URL: https://europe-west2-national-accounts-stg-49062.cloudfunctions.net/nationalAccounts
        SALESFORCE_INTEGRATION_GCP_PROJECT_ID: salesforce-integ-stg-22832
        SEARCH_API_URL: https://search-gateway-gw-ah4z6de9.nw.gateway.dev/api/v1
        TRADE_FIREBASE_APP_PROJECT_ID: cat-trades-preview
        TRADE_SIGNUP_WEB_URL: https://member-onboarding.checkatrade.dev
        CONSUMER_WEB_URL: https://frontend-staging.checkatrade.com
        TRADE_WEB_URL: https://membersapp-staging.checkatrade.com
      pubsub:
        enabled: true
        topics:
          - name: payments-off-platform-job-events
            additionalPublishers:
              - name: bff-consumer-public
          - name: employee-consent
          - name: self-service-requests
            subscriptions:
              - type: push
                endpoint: https://salesforce-gcp-integ-dffmhy3q7q-nw.a.run.app/pubsub/self-service-requests
                retryPolicy:
                  minimumBackoff: 5s
                  maximumBackoff: 600s
                deadLetterPolicy:
                  enabled: true
                  maxDeliveryAttempts: 5

  sharedServiceAccount: true
