base:
  global:
    owner: trade
    system: core-trade-bff
    _defaults:
      namespace: core-trade-bff
      env:
        NODE_ENV: production
      serviceAccount:
        enabled: true

  applications:
    _defaults:
      metrics:
        enabled: false
      tracing:
        apm:
          enabled: true

    trade-app-bff:
      enabled: true
      env:
        ADDRESS_API_URL: http://address.core-address.svc.cluster.local:3000
        AD_MANAGER_API_URL: http://ad-manager.core-media-placement.svc.cluster.local:3000
        ALBUM_API_URL: http://core-trade-album.core-trade-album.svc.cluster.local:3000
        COMMS_GCP_OFF_PLATFORM_JOB_TOPIC: payments-off-platform-job-events
        SELF_SERVICE_REQUESTS_TOPIC: self-service-requests
        CONSUMER_API_URL: http://consumer.core-consumer.svc.cluster.local:3000
        CONSUMER_MYHOME_API_URL: http://consumer-myhome.core-consumer-myhome.svc.cluster.local:3000
        DD_PROFILING_ENABLED: true
        EMPLOYEE_CONSENT_TOPIC: employee-consent
        FINANCE_API_URL: http://api-product-catalog.core-finance.svc.cluster.local:3000
        JOBS_API_URL: http://jobs.core-job.svc.cluster.local:3000
        MATCHING_SPAM_API_URL: http://matching-ai.core-matching.svc.cluster.local:3000/spam
        METRICS_API_URL: http://review-metrics.core-review.svc.cluster.local:3000
        MITEK_CLIENT_ID: secret::mitek-client-id
        MITEK_CLIENT_SECRET: secret::mitek-revet-client-secret
        MITEK_API_URL: https://www.hooyu.com/api
        PAYMENT_API_URL: http://payment.core-payment.svc.cluster.local:3000
        QUOTING_API_URL: http://quoting.core-quoting.svc.cluster.local:3000
        QUOTING_TOKEN_EXPIRATION: 172800
        QUOTING_TOKEN_SIGNING_KEY: secret::quoting-token-signing-key
        CONSENT_EMAIL_TOKEN_SIGNING_KEY: secret::consent-email-token-signing-key
        CONSENT_EMAIL_TOKEN_EXPIRATION: 1440
        REVIEW_API_URL: http://reviews.core-review.svc.cluster.local:3000
        REVIEWS_SUMMARY_API_URL: http://reviews-summariser.core-review.svc.cluster.local:3000
        SCHEDULING_API_URL: http://scheduling.core-scheduling.svc.cluster.local:3000
        SERVICE_CATALOG_API_URL: http://service-catalog.core-service-catalog.svc.cluster.local:3000
        STREAM_CHAT_API_KEY: secret::consumer-stream-chat-api-key
        STREAM_CHAT_API_SECRET: secret::consumer-stream-chat-api-secret
        STREAM_CHAT_TOKEN_EXPIRATION: 3600
        REDIRECTION_ENABLED: true
        REDIRECTION_JOB_DUE_HOURS: 24 #redirect after this time
        REDIRECTION_JOB_EXPIRY_HOURS: 48 #don't redirect if job is older then
        REDIRECTION_JOB_EXPIRY_URGENT_HOURS: 4 #don't redirect if job is older then
        REDIRECTION_MAX_ACCEPTS: 2
        REDIRECTION_MAX_OPPORTUNITIES: 6
        REFERRAL_FACTORY_URL: https://referral-factory.com/api/v1
        REFERRAL_FACTORY_API_KEY: secret::referral-factory-api-key
        TRADE_DATA_SERVICE_API_URL: http://core-trade-data.core-trade-data.svc.cluster.local:3000
        VETTING_SERVICE_API_URL: http://vetting-api.core-trade-vetting.svc.cluster.local:3000
        REPORT_RECORDS_PER_PAGE: 1000
      service:
        basePath: /v2/trade-app
        routes:
          - path: /accounts
          - path: /ad-manager/[^/]+
          - path: /ad-manager/advertisers/[^/]+/campaigns
          - path: /appointment/cancellation-reasons
          - path: /appointment/types
          - path: /appointments
          - path: /appointments/[^/]+
          - path: /appointments/[^/]+/cancel
          - path: /appointments/[^/]+/reschedule
          - path: /archived-jobs
          - path: /archived-jobs/[^/]+
          - path: /auth/[^/]+
          - path: /consumers/[^/]+/report
          - path: /campaigns/stats
          - path: /job/cancel-reasons
          - path: /job/reject-reasons
          - path: /jobs
          - path: /jobs/[^/]+
          - path: /jobs/[^/]+/accept
          - path: /jobs/[^/]+/appointments
          - path: /jobs/[^/]+/book
          - path: /jobs/[^/]+/cancel
          - path: /jobs/[^/]+/complete
          - path: /jobs/[^/]+/mark-as-read
          - path: /jobs/[^/]+/note
          - path: /jobs/[^/]+/property-facts
          - path: /jobs/[^/]+/reject
          - path: /jobs/[^/]+/request-address
          - path: /jobs/[^/]+/review/requests
          - path: /jobs/count-unread
          - path: /team
          - path: /team/[^/]+
          - path: /team/invites
          - path: /team/pending-invites
          - path: /team/[^/]+/persons
          - path: /team/[^/]+/vetting
          - path: /team/[^/]+/consent-email
          - path: /team/invite/[^/]+
          - path: /team/counters
          - path: /team/vetting-consent-status
          - path: /team/subcontractor/[^/]+
          - path: /team/persons/[^/]+
          - path: /member-details
          - path: /member-info
          - path: /person/[^/]+/accreditations
          - path: /person/[^/]+/required-accreditations
          - path: /person/[^/]+/accreditations/[^/]+
          - path: /payments/activities
          - path: /payments/balance
          - path: /payments/balance-accounts
          - path: /payments/external-tax-information
          - path: /payments/onboard
          - path: /payments/onboarding-information
          - path: /payments/pay-by-phone/payment-details
          - path: /payments/pay-by-phone/payment-methods
          - path: /payments/pay-by-phone/payment-request
          - path: /payments/payment-request
          - path: /payments/payment-request/[^/]+
          - path: /payments/payment-request/[^/]+/cancel
          - path: /payments/payment-request/off-platform-job
          - path: /payments/payment-requests/opportunity/[^/]+
          - path: /payments/payment-requests/quote/[^/]+
          - path: /payments/payment-requests/quote/[^/]+/metrics
          - path: /payments/report/activities-statement
          - path: /payments/session
          - path: /payments/split-payment/[^/]+
          - path: /payments/tax-information
          - path: /quotes
          - path: /quotes/[^/]+
          - path: /quotes/[^/]+/details
          - path: /quotes/[^/]+/draft
          - path: /quotes/[^/]+/share
          - path: /quotes/[^/]+/token
          - path: /quotes/job/[^/]+
          - path: /quotes/opportunity/[^/]+
          - path: /review-reply/[^/]+/unpublish
          - path: /review-request/manual
          - path: /manual-review-requests
          - path: /manual-review-requests/[^/]+/reminder
          - path: /revetting/status
          - path: /revetting/url
          - path: /reviews
          - path: /reviews/[^/]+
          - path: /reviews/[^/]+/reply
          - path: /reviews/[^/]+/report
          - path: /reviews/metrics
          - path: /reviews/job-requests
          - path: /reviews/summary/[^/]+/trade
          - path: /referral
          - path: /self-service/company-name
          - path: /self-service/member-email
          - path: /self-service/member-phone
          - path: /self-service/member-trading-address
          - path: /self-service/member-admin-address
          - path: /self-service/update-person
          - path: /self-service/add-person
          - path: /self-service/delete-person
          - path: /service-catalog/service
          - path: /service-catalog/services
          - path: /service-catalog/service/[^/]+/version
          - path: /service-catalog/service/[^/]+/version/[^/]+
          - path: /service-catalog/service/[^/]+/version/[^/]+/status
          - path: /zuora/account
          - path: /zuora/account/[^/]+
          - path: /zuora/payment-methods/[^/]+
          - path: /project
          - path: /project/[^/]+
          - path: /projects

  sharedServiceAccount: true
