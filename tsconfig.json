{
  "compileOnSave": true,
  "compilerOptions": {
    // Emit
    "declaration": true,
    "declarationMap": true,
    "target": "esnext",
    "lib": ["esnext"],
    "outDir": "${configDir}/dist",
    "sourceMap": true,

    // Type Checking
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "alwaysStrict": true,
    "exactOptionalPropertyTypes": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "skipLibCheck": true,
    "strict": true,
    "strictBindCallApply": true,
    "strictFunctionTypes": true,
    "strictNullChecks": true,
    "strictPropertyInitialization": false,

    // Modules
    "isolatedModules": false,
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "resolveJsonModule": true,

    // Interop Constraints
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,

    // Experimental Options
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,

    // JavaScript Support
    "allowJs": false,
    "checkJs": false
  },
  // Files
  "include": ["${configDir}/src/**/*.ts"],
  "exclude": ["${configDir}/node_modules", "${configDir}/**/*.test.ts"]
}
