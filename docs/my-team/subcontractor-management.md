# Subcontractor Management

## Purpose

The trade app will support all CRUD operations for a sub-contractor. Below are the sequence diagrams for each

## Create sub-contractor

```mermaid
sequenceDiagram
  actor Trade
  participant TradeBFF as Trade BFF
  participant TradeData as Trade Data
  participant Identity
  participant Braze
  participant TradeVettingProcessor as Trade Vetting Processor
  participant TradeVetting as Trade Vetting
  actor SubContractor as Sub-contractor

  Trade ->> TradeBFF: Adds Sub-contractor
  TradeBFF ->> TradeData: Create trade record
  TradeData ->> TradeVettingProcessor: Sub-contractor record created
  TradeData ->> Identity: Check id exists
  Identity -->> TradeData: id status
  TradeData ->> Trade: Prompt user status (if exists)
  TradeData ->> Identity: Create id account (it not exists)
  TradeData ->> Braze: Send invite
  Braze ->> SubContractor: Send invite comms
  Braze -->> Trade: Invite sent to sub-contractor
  SubContractor ->> TradeData: Invite accepted
  TradeData -->> Trade: Sub-contractor accepted
  TradeData ->> TradeVetting: Initiate vetting
```

## Delete sub-contractor

```mermaid
sequenceDiagram
  actor Trade
  participant TradeBFF as Trade BFF
  participant TradeData as Trade Data
  participant TradeVettingProcessor as Trade Vetting Processor
  participant Braze
  actor SubContractor as Sub-contractor

  Trade ->> TradeBFF: Delete sub contractor
  TradeBFF ->> TradeData: Soft delete sub contractor record
  TradeData ->> TradeVettingProcessor: Remove sub contractor event
  TradeVettingProcessor ->> Braze: Sub contractor removed event
  Braze ->> SubContractor: Send removal confirmation comms
  TradeData -->> Trade: Sub-contractor removed
```

## Read sub-contractor

```mermaid
sequenceDiagram
  actor Trade
  participant TradeBFF as Trade BFF
  participant TradeData as Trade Data
  participant TradeVetting as Trade Vetting

  Trade ->> TradeBFF: Click to open My team
  TradeBFF ->> TradeData: Get sub contractors
  TradeBFF ->> TradeVetting: Get vetting status(es)
```

## Update sub-contractor

```mermaid
sequenceDiagram
  actor Trade
  participant TradeBFF as Trade BFF
  participant TradeData as Trade Data
  participant TradeVettingProcessor as Trade Vetting Processor
  participant TradeVetting as Trade Vetting
  participant Braze
  actor SubContractor as Sub-contractor

  Trade ->> TradeBFF: Updates Sub-contractor
  TradeBFF ->> TradeData: Update trade record
  TradeData ->> TradeVettingProcessor: Sub-contractor updated
  TradeVettingProcessor ->> TradeVetting: Update Vetting
  TradeVettingProcessor ->> Braze: Send new vetting checks
  Braze ->> SubContractor: Send new vetting check comms
```