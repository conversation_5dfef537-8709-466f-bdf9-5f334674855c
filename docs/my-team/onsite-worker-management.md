# Onsite Worker Management

## Purpose

The trade app will support all CRUD operations for an employee. Below are the sequence diagrams for each.

## Create Employee

```mermaid
sequenceDiagram
  actor TC as Trade Company
  participant BFF as Trade BFF
  participant TDS as Trade Data Service
  participant TVS as Trade Vetting Service
  participant PS as Pub/Sub
  participant COMS as Comms Service
  participant B<PERSON> as Braze
  actor E<PERSON> as Trade Employee
  
  TC ->> BFF: Add Employee
  BFF ->> TDS: Add Employee
  TDS -->> BFF: Add confirmation
  BFF -->> TC: Add confirmation
  TC ->> BFF: Check vetting consent
  BFF ->> TVS: Check vetting consent
  TVS -->> BFF: Consent required
  BFF ->> BFF: Generate magic link with token
  BFF ->> PS: Publish event with magic link
  PS ->> COMS: Broadcast consent email event    
  COMS ->> BZ: Send consent email event details
  BZ ->> BZ: Validate magic link
  BZ ->> EMP: Send email with magic link
```

## Read Employee

```mermaid
sequenceDiagram
  actor tr as Trade
  participant bff as Trade BFF
  participant td as Trade Data
  participant tv as Trade Vetting
  tr-->bff: Opens "My Team"
  bff-->td: Get Employees
  bff-->tv: Get Vetting Status
```

## Update Employee

```mermaid
sequenceDiagram
  actor tr as Trade
  participant bff as Trade BFF
  participant td as Trade Data
  participant tvp as Trade Vetting Processor
  participant tv as Trade Vetting
  participant ta as Trade Accreditations
  participant b as Braze
  actor e as employee
  tr-->bff: Updates Employee
  bff-->td: Update Employee
  td-->tvp: Employee Updated Event
  tvp-->tv: Update Vetting
  tvp-->ta: Update Accreditations
  tvp-->b: Confirm / Request new checks
  b-->e: Email requesting new check
```

## Delete Employee

```mermaid
sequenceDiagram
  actor tr as Trade
  participant bff as Trade BFF
  participant td as Trade Data
  participant tvp as Trade Vetting Processor
  participant b as Braze
  actor e as employee
  tr-->bff: Deletes Employee
  bff-->td: Employee link removed
  td-->tvp: Delete Employee event
  tvp-->b: Employee Removed Event
  b-->e: Sends removal notification
```

## Dependencies
We'll have a dependency on the Trade Data service having the available functionality to support these flows.