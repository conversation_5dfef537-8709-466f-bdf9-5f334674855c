#!/bin/bash
set -e

echo "Formatting yaml files with Prettier..."
npx prettier --write "./client-specs/*.yaml"

echo "Generating client ASYNCAPI types into 'src/api/trade-app-bff/types/asyncapi-types'..."
npx @asyncapi/cli generate models typescript ./client-specs/self-service-asyncapi.yaml -o src/types/asyncapi-types --tsModelType=interface

echo "Formatting generated ASYNCAPI types with Prettier..."
npx prettier --write "./src/types/asyncapi-types/*.ts"

echo "Client ASYNCAPI API types generated successfully!"