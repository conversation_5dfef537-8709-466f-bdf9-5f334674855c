import { referralFactory<PERSON>pi } from "../../../src/lib/api-common";
import {
  TEST_COMPANY_ID,
  TEST_USER_EMAIL,
  createCompany,
  getTestRequestWithValidToken,
} from "../../helpers";

const TEST_CAMPAIGN_ID = 35191;

const postUserResults = {
  code: "ABC123",
  campaign_id: TEST_CAMPAIGN_ID,
  url: "https://referral.example.com/ABC123",
  sharing: [
    {
      social: "instagram",
      url: "https://www.instagram.com/checkatrade/",
    },
  ],
};

const getUserResults = {
  ...postUserResults,
};

describe("GET /referral", () => {
  let getUserMock: jest.SpyInstance;
  let postUserMock: jest.SpyInstance;

  beforeEach(async () => {
    jest.clearAllMocks();
    await createCompany(TEST_COMPANY_ID);
    getUserMock = jest.spyOn(referralFactoryApi, "getUser");
    postUserMock = jest.spyOn(referralFactoryApi, "postUser");
  });

  it("returns a referral data for existing user ", async () => {
    getUserMock.mockResolvedValue(getUserResults);
    const request = getTestRequestWithValidToken();

    const response = await request.get(
      `/referral?campaignId=${TEST_CAMPAIGN_ID}`,
    );
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      code: getUserResults.code,
      campaignId: getUserResults.campaign_id,
      url: getUserResults.url,
      sharing: getUserResults.sharing,
    });
    expect(getUserMock).toHaveBeenCalledWith({
      campaignId: TEST_CAMPAIGN_ID,
      email: TEST_USER_EMAIL,
      logger: expect.any(Object),
    });
  });

  it("returns a referral data for new user", async () => {
    getUserMock.mockResolvedValue(null);
    postUserMock.mockResolvedValue(postUserResults);
    const request = getTestRequestWithValidToken();

    const response = await request.get(
      `/referral?campaignId=${TEST_CAMPAIGN_ID}`,
    );
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      code: getUserResults.code,
      campaignId: getUserResults.campaign_id,
      url: getUserResults.url,
      sharing: getUserResults.sharing,
    });
    expect(postUserMock).toHaveBeenCalledWith({
      campaignId: TEST_CAMPAIGN_ID,
      email: TEST_USER_EMAIL,
      name: `Company ${TEST_COMPANY_ID}`,
      logger: expect.any(Object),
    });
  });
});
