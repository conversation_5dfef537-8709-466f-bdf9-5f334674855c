import {
  MetricTypes,
  ReviewMetricsPeriod,
  metricsSDK,
} from "@checkatrade/metrics-sdk";

import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const metricsData = {
  totalReviewCount: 8,
  verifiedCount: 5,
  companyId: 0,
  date: "",
  period: ReviewMetricsPeriod.YEAR,
  complaintCount: 1,
  complaintPercentage: 12.5,
  publishedCount: 2,
  unVerifiedCount: 4,
  reviewReplyRate: 10,
  connectedJobRate: 10,

  courtesy: { maxScore: 0, scoreCount: 0, scoreAverage: 0 },
  qualityOfWorkmanship: { maxScore: 0, scoreCount: 0, scoreAverage: 0 },
  reliabilityAndTimekeeping: { maxScore: 0, scoreCount: 0, scoreAverage: 0 },
  tidiness: { maxScore: 0, scoreCount: 0, scoreAverage: 0 },
  noWorkCarriedOut: { maxScore: 0, scoreCount: 0, scoreAverage: 0 },
  communication: { maxScore: 3, scoreCount: 2, scoreAverage: 1 },
  notRecommendedReasonCount: {
    AFTERCARE: 5,
  },
};
const getMockExternalMetrics = (): MetricTypes.Api.Metrics.External.Reply => ({
  year: metricsData,
  month: metricsData,
  lifeTime: metricsData,
});

describe("GET /reviews/metrics", () => {
  const getExternalMock = jest.fn();

  beforeEach(() => {
    jest.spyOn(metricsSDK, "metrics").mockReturnValue({
      getExternal: getExternalMock,
    } as never);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Should return transformed metrics for valid token", async () => {
    const mockMetrics = getMockExternalMetrics();
    const expectedData = {
      totalReviewCount: 8,
      verifiedCount: 5,
      complaintCount: 1,
      complaintPercentage: 12.5,
      publishedCount: 2,
      reviewReplyRate: 10,
      connectedJobRate: 10,
      communication: { scoreAverage: 1, scoreCount: 2 },
      courtesy: { scoreAverage: 0, scoreCount: 0 },
      qualityOfWorkmanship: { scoreAverage: 0, scoreCount: 0 },
      reliabilityAndTimekeeping: { scoreAverage: 0, scoreCount: 0 },
      tidiness: { scoreAverage: 0, scoreCount: 0 },
    };
    const expectedMetricsResponse = {
      year: expectedData,
      month: expectedData,
      lifeTime: expectedData,
    };
    getExternalMock.mockResolvedValue(mockMetrics);
    const request = getTestRequestWithValidToken();

    const response = await request.get("/reviews/metrics");

    expect(response.status).toBe(200);
    expect(response.body).toEqual(expectedMetricsResponse);
    expect(getExternalMock).toHaveBeenCalledWith(TEST_COMPANY_ID);
  });

  it("Should return 401 for invalid token", async () => {
    const request = getTestRequestWithInvalidToken();

    const response = await request.get("/reviews/metrics");

    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/reviews/metrics`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
