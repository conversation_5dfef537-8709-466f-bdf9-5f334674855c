import {
  ReviewStatus,
  ReviewType,
  ReviewTypes,
  reviewSDK,
} from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const getMockReview = (): ReviewTypes.Api.Public.Get.Response => ({
  companyId: 0,
  title: "Title",
  review: "Review body",
  type: ReviewType.WORK_NOT_CARRIED,
  id: faker.string.uuid(),
  status: ReviewStatus.NOT_PUBLISHED,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

describe("GET /reviews/:reviewId", () => {
  const getReviewMock = jest.fn();

  beforeEach(() => {
    jest.spyOn(reviewSDK, "public").mockReturnValue({
      getReview: getReviewMock,
    } as never);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Should return a review for valid token", async () => {
    const reviewMock = getMockReview();
    getReviewMock.mockResolvedValue(reviewMock);

    const request = await getTestRequestWithValidToken();
    const response = await request.get(`/reviews/${reviewMock.id}`);
    expect(response.status).toBe(200);
    expect(response.body).toEqual(reviewMock);
    expect(getReviewMock).toHaveBeenCalledWith(reviewMock.id, {
      companyId: TEST_COMPANY_ID,
    });
  });

  it("Should return a 401 for invalid token", async () => {
    const request = await getTestRequestWithInvalidToken();
    const reviewMock = getMockReview();
    getReviewMock.mockResolvedValue(reviewMock);
    const response = await request.get(`/reviews/${reviewMock.id}`);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/reviews/${reviewMock.id}`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
    expect(getReviewMock).not.toHaveBeenCalled();
  });
});
