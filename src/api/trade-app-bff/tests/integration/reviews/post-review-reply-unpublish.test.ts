import {
  ReviewReplyStatus,
  ReviewTypes,
  reviewSDK,
} from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const getMockResponse = (): ReviewTypes.Api.Public.CreateReply.Response => ({
  id: faker.string.uuid(),
  reply: "reply text",
  status: ReviewReplyStatus.NOT_PUBLISHED,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

describe("POST /review-reply/:replyId/unpublish", () => {
  const postUnpublishReplyMock = jest.fn();

  const postUnpublishReplyBody = {
    companyId: TEST_COMPANY_ID,
  };

  beforeEach(() => {
    jest.spyOn(reviewSDK, "public").mockReturnValue({
      postUnpublishReply: postUnpublishReplyMock,
    } as never);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Should return 200 and call postUnpublishReply for valid token", async () => {
    const mockResponse = getMockResponse();
    postUnpublishReplyMock.mockResolvedValue(mockResponse);
    const request = await getTestRequestWithValidToken();
    const replyId = mockResponse.id;

    const response = await request
      .post(`/review-reply/${replyId}/unpublish`)
      .send(postUnpublishReplyBody);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockResponse);
    expect(postUnpublishReplyMock).toHaveBeenCalledWith(
      replyId,
      postUnpublishReplyBody,
    );
  });

  it("Should return 401 for invalid token", async () => {
    const request = await getTestRequestWithInvalidToken();

    const replyId = faker.string.uuid();
    const response = await request
      .post(`/review-reply/${replyId}/unpublish`)
      .send(postUnpublishReplyBody);

    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/review-reply/${replyId}/unpublish`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
