import { reviewSDK } from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";

import { formatPhoneNumber } from "../../../src/controllers/reviews/format-phone-number";
import {
  TEST_COMPANY_ID,
  createCompanies,
  createCompany,
  getTestRequestWithValidToken,
} from "../../helpers";
import { findReviewRequest } from "../../helpers/firestore/find-review-requests";
import { getFakeMobileNumber } from "../../helpers/test-mobile";

describe("POST /manual-review-requests", () => {
  const postManualReviewRequestMock = jest.fn();

  beforeEach(async () => {
    await createCompanies(TEST_COMPANY_ID);
    await createCompany(TEST_COMPANY_ID);
    jest.spyOn(reviewSDK, "public").mockReturnValue({
      postManualReviewRequest: postManualReviewRequestMock,
    } as never);
  });

  const manualReviewRequestBody = {
    consumerName: faker.person.fullName(),
    email: faker.internet.email(),
    mobile: formatPhoneNumber(getFakeMobileNumber()),
  };

  const mockManualReviewResponse = {
    ...manualReviewRequestBody,
    id: faker.string.uuid(),
    companyId: TEST_COMPANY_ID,
    requestCount: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  it("should call reviewSDK and create firestore document", async () => {
    const request = getTestRequestWithValidToken();
    postManualReviewRequestMock.mockResolvedValue(mockManualReviewResponse);

    const response = await request
      .post("/manual-review-requests")
      .send(manualReviewRequestBody);

    expect(response.status).toBe(201);

    const postManualReviewRequestMockResponse = {
      companyId: TEST_COMPANY_ID,
      companyName: "Company 1030757",
      companyUniqueName: "company-1030757",
      consumerName: manualReviewRequestBody.consumerName,
      email: manualReviewRequestBody.email,
      mobile: manualReviewRequestBody.mobile,
    };

    expect(postManualReviewRequestMock).toHaveBeenCalledWith(
      postManualReviewRequestMockResponse,
    );

    const reviewRequestsDb = await findReviewRequest(
      TEST_COMPANY_ID,
      manualReviewRequestBody.consumerName,
      manualReviewRequestBody.email,
      manualReviewRequestBody.mobile,
    );

    expect(reviewRequestsDb).not.toBeNull();

    expect(response.body).toEqual({
      canSendReminder: false,
      consumerName: manualReviewRequestBody.consumerName,
      dateCreated: expect.any(String),
      id: reviewRequestsDb?.id,
      mobile: formatPhoneNumber(manualReviewRequestBody.mobile),
      email: manualReviewRequestBody.email,
      sent: false,
    });
  });

  it("email is optional", async () => {
    const request = getTestRequestWithValidToken();
    postManualReviewRequestMock.mockResolvedValue({
      mockManualReviewResponse,
    });

    const manualReviewRequestBodyWithoutEmail = {
      consumerName: faker.person.fullName(),
      mobile: getFakeMobileNumber(),
    };

    const response = await request
      .post("/manual-review-requests")
      .send(manualReviewRequestBodyWithoutEmail);
    expect(response.status).toBe(201);

    const postManualReviewRequestMockResponse = {
      companyId: TEST_COMPANY_ID,
      companyName: "Company 1030757",
      companyUniqueName: "company-1030757",
      consumerName: manualReviewRequestBodyWithoutEmail.consumerName,
      email: undefined,
      mobile: formatPhoneNumber(manualReviewRequestBodyWithoutEmail.mobile),
    };

    expect(postManualReviewRequestMock).toHaveBeenCalledWith(
      postManualReviewRequestMockResponse,
    );

    const reviewRequestsDb = await findReviewRequest(
      TEST_COMPANY_ID,
      manualReviewRequestBodyWithoutEmail.consumerName,
      undefined,
      formatPhoneNumber(manualReviewRequestBodyWithoutEmail.mobile),
    );

    expect(reviewRequestsDb).not.toBeNull();
  });

  it("phone is optional", async () => {
    const request = getTestRequestWithValidToken();
    postManualReviewRequestMock.mockResolvedValue({
      mockManualReviewResponse,
    });

    const manualReviewRequestBodyWithoutPhone = {
      consumerName: faker.person.fullName(),
      email: faker.internet.email(),
    };

    const response = await request
      .post("/manual-review-requests")
      .send(manualReviewRequestBodyWithoutPhone);

    expect(response.status).toBe(201);

    const postManualReviewRequestMockResponse = {
      companyId: TEST_COMPANY_ID,
      companyName: "Company 1030757",
      companyUniqueName: "company-1030757",
      consumerName: manualReviewRequestBodyWithoutPhone.consumerName,
      email: manualReviewRequestBodyWithoutPhone.email,
      mobile: undefined,
    };

    expect(postManualReviewRequestMock).toHaveBeenCalledWith(
      postManualReviewRequestMockResponse,
    );

    const reviewRequestsDb = await findReviewRequest(
      TEST_COMPANY_ID,
      manualReviewRequestBodyWithoutPhone.consumerName,
      manualReviewRequestBodyWithoutPhone.email,
      undefined,
    );

    expect(reviewRequestsDb).not.toBeNull();
  });
});
