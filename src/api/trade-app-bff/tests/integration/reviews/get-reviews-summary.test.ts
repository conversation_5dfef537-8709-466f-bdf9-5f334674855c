import { reviewsSummarySDK } from "@checkatrade/reviews-summary-sdk";
import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const mockSummaryData = {
  summary: faker.lorem.paragraph(),
  strengths: [faker.lorem.sentence(), faker.lorem.sentence()],
  improvements: [faker.lorem.sentence()],
};

const mockApiResponse = {
  companyId: TEST_COMPANY_ID,
  updatedAt: new Date().toISOString(),
  summary: mockSummaryData,
};

describe("GET /reviews/summary/:companyId/:summaryType", () => {
  const getSummaryMock = jest.fn();
  const testSummaryType = "trade";

  beforeEach(() => {
    jest.spyOn(reviewsSummarySDK, "summary").mockReturnValue({
      getSummary: getSummaryMock,
    } as never);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Should return summary data for valid token", async () => {
    getSummaryMock.mockResolvedValue(mockApiResponse);
    const request = getTestRequestWithValidToken();
    const url = `/reviews/summary/${TEST_COMPANY_ID}/${testSummaryType}`;

    const response = await request.get(url);

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockApiResponse);
    expect(getSummaryMock).toHaveBeenCalledWith(
      TEST_COMPANY_ID,
      testSummaryType,
    );
  });

  it("Should return 401 for invalid token", async () => {
    const request = getTestRequestWithInvalidToken();
    const url = `/reviews/summary/${TEST_COMPANY_ID}/${testSummaryType}`;

    const response = await request.get(url);

    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: url,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
    expect(getSummaryMock).not.toHaveBeenCalled();
  });
});
