import { faker } from "@faker-js/faker";
import dayjs from "dayjs";

import { mapReviewRequestDocToResponse } from "../../../src/controllers/reviews/mappers/map-review-request-doc-to-response";
import { createReviewRequest } from "../../../src/services/firebase/firestore/review-request";
import { ReviewRequestDoc } from "../../../src/services/firebase/firestore/schemas/review-request";
import { createCompanies, getTestRequestWithValidToken } from "../../helpers";
import { mockFirestoreReviewRequest } from "../../helpers/test-review-request";

describe("GET /review-requests", () => {
  let testCompanyId: number;
  let oneWeekReviewRequest: ReviewRequestDoc;
  let threeMonthsReviewRequest: ReviewRequestDoc;
  let newReviewRequest: ReviewRequestDoc;

  beforeEach(async () => {
    testCompanyId = faker.number.int({ min: 1000, max: 900000 }); //generate a random company id for testing
    await createCompanies(testCompanyId);

    const threeMonthsAgo = dayjs().subtract(3, "months").toDate();
    const oneWeekAgo = dayjs().subtract(1, "weeks").toDate();

    oneWeekReviewRequest = mockFirestoreReviewRequest(oneWeekAgo);
    threeMonthsReviewRequest = mockFirestoreReviewRequest(threeMonthsAgo);
    newReviewRequest = mockFirestoreReviewRequest();

    await createReviewRequest(testCompanyId, oneWeekReviewRequest);
    await createReviewRequest(testCompanyId, threeMonthsReviewRequest);
    await createReviewRequest(testCompanyId, newReviewRequest);
  });

  afterEach(() => {
    //remove test company
  });

  it("should return 200 and list of review requests", async () => {
    const request = await getTestRequestWithValidToken(testCompanyId);
    const response = await request.get("/manual-review-requests?size=5");

    const data = response.body.data;

    expect(response.status).toBe(200);
    expect(data.length).toBe(2);
    expect(response.body.lastReviewRequestId).toBe(data[data.length - 1].id);
    expect(data).toEqual([
      {
        ...mapReviewRequestDocToResponse(newReviewRequest),
        id: expect.any(String),
        dateCreated: expect.any(String),
        canSendReminder: false,
      },
      {
        ...mapReviewRequestDocToResponse(oneWeekReviewRequest),
        phone: undefined,
        id: expect.any(String),
        dateCreated: expect.any(String),
        canSendReminder: true,
      },
    ]);
  });

  it("should return paginated data", async () => {
    const request = await getTestRequestWithValidToken(testCompanyId);
    const firstPageResponse = await request.get(
      "/manual-review-requests?size=1",
    );

    const firstPageData = firstPageResponse.body.data;

    expect(firstPageResponse.status).toBe(200);
    expect(firstPageData.length).toBe(1);
    expect(firstPageResponse.body.lastReviewRequestId).toBe(
      firstPageData[firstPageData.length - 1].id,
    );

    expect(firstPageData).toEqual([
      {
        ...mapReviewRequestDocToResponse(newReviewRequest),
        id: expect.any(String),
        dateCreated: expect.any(String),
        canSendReminder: false,
      },
    ]);

    const secondPageResponse = await request.get(
      "/manual-review-requests?size=1&lastReviewRequestId=" +
        firstPageResponse.body.lastReviewRequestId,
    );
    expect(secondPageResponse.status).toBe(200);
    expect(secondPageResponse.body.data.length).toBe(1);
    expect(secondPageResponse.body.data).toEqual([
      {
        ...mapReviewRequestDocToResponse(oneWeekReviewRequest),
        id: expect.any(String),
        dateCreated: expect.any(String),
        canSendReminder: true,
      },
    ]);

    const thirdPageResponse = await request.get(
      "/manual-review-requests?size=1&lastReviewRequestId=" +
        secondPageResponse.body.lastReviewRequestId,
    );
    expect(thirdPageResponse.status).toBe(200);
    expect(thirdPageResponse.body.data.length).toBe(0);
  });
});
