import {
  ReviewReplyStatus,
  ReviewTypes,
  reviewSDK,
} from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const getMockResponse = (): ReviewTypes.Api.Public.CreateReply.Response => ({
  id: faker.string.uuid(),
  reply: "reply text",
  status: ReviewReplyStatus.NOT_PUBLISHED,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

describe("POST /reviews/:reviewId/reply", () => {
  const postReviewReplyMock = jest.fn();

  const postReplyBody = {
    reply: "reply text",
    companyId: TEST_COMPANY_ID,
  };

  beforeEach(() => {
    jest.spyOn(reviewSDK, "public").mockReturnValue({
      postReviewReply: postReviewReplyMock,
    } as never);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Should return 201 and call postReviewReply for valid token", async () => {
    const mockResponse = getMockResponse();

    postReviewReplyMock.mockResolvedValue(mockResponse);

    const request = await getTestRequestWithValidToken();

    const reviewId = faker.string.uuid();
    const response = await request
      .post(`/reviews/${reviewId}/reply`)
      .send(postReplyBody);

    expect(response.status).toBe(201);
    expect(response.body).toEqual(mockResponse);
    expect(postReviewReplyMock).toHaveBeenCalledWith(reviewId, postReplyBody);
  });

  it("Should return 401 for invalid token", async () => {
    const request = await getTestRequestWithInvalidToken();

    const reviewId = faker.string.uuid();
    const response = await request
      .post(`/reviews/${reviewId}/reply`)
      .send(postReplyBody);

    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/reviews/${reviewId}/reply`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
