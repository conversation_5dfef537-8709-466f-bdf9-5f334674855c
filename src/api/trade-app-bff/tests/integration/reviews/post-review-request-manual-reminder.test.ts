import { reviewSDK } from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";
import dayjs from "dayjs";

import { formatPhoneNumber } from "../../../src/controllers/reviews/format-phone-number";
import {
  createReviewRequest,
  getReviewRequest,
} from "../../../src/services/firebase/firestore/review-request";
import {
  TEST_COMPANY_ID,
  createCompanies,
  createCompany,
  getTestRequestWithValidToken,
} from "../../helpers";
import { getFakeMobileNumber } from "../../helpers/test-mobile";

const getManualReviewRequestBody = () => ({
  consumerName: faker.person.fullName(),
  email: faker.internet.email(),
  mobile: formatPhoneNumber(getFakeMobileNumber()),
});

describe("POST /manual-review-requests/:id/reminder", () => {
  const postManualReviewRequestMock = jest.fn();

  beforeEach(async () => {
    await createCompanies(TEST_COMPANY_ID);
    await createCompany(TEST_COMPANY_ID);
    jest.spyOn(reviewSDK, "public").mockReturnValue({
      postManualReviewRequest: postManualReviewRequestMock,
    } as never);
  });

  it("should send reminder and update review-request document", async () => {
    const request = getTestRequestWithValidToken();
    const manualReviewRequestBody = getManualReviewRequestBody();

    const { id } = await createReviewRequest(TEST_COMPANY_ID, {
      fullName: manualReviewRequestBody.consumerName,
      email: manualReviewRequestBody.email,
      phone: manualReviewRequestBody.mobile,
      sent: false,
      dateCreated: dayjs().subtract(25, "hours").toDate(),
    });

    const response = await request
      .post(`/manual-review-requests/${id}/reminder`)
      .send({});

    expect(response.status).toBe(200);

    expect(postManualReviewRequestMock).toHaveBeenCalledWith({
      companyId: TEST_COMPANY_ID,
      companyName: "Company 1030757",
      companyUniqueName: "company-1030757",
      consumerName: manualReviewRequestBody.consumerName,
      email: manualReviewRequestBody.email,
      mobile: manualReviewRequestBody.mobile,
    });

    const reviewRequestsDocument = await getReviewRequest(TEST_COMPANY_ID, id!);

    expect(reviewRequestsDocument?.sent).toBe(true);

    expect(response.body).toEqual({
      canSendReminder: false,
      consumerName: manualReviewRequestBody.consumerName,
      dateCreated: expect.any(String),
      email: manualReviewRequestBody.email,
      id: reviewRequestsDocument?.id,
      mobile: formatPhoneNumber(manualReviewRequestBody.mobile),
      sent: true,
    });
  });

  it("should not allow to send reminder before 24h", async () => {
    const request = getTestRequestWithValidToken();
    const manualReviewRequestBody = getManualReviewRequestBody();

    const { id } = await createReviewRequest(TEST_COMPANY_ID, {
      fullName: manualReviewRequestBody.consumerName,
      email: manualReviewRequestBody.email,
      phone: manualReviewRequestBody.mobile,
      sent: false,
      dateCreated: dayjs().subtract(1, "hours").toDate(),
    });

    const response = await request
      .post(`/manual-review-requests/${id}/reminder`)
      .send({});

    expect(response.status).toBe(409);

    expect(postManualReviewRequestMock).not.toHaveBeenCalled();
  });

  it("should not allow to send reminder when it was already sent", async () => {
    const request = getTestRequestWithValidToken();
    const manualReviewRequestBody = getManualReviewRequestBody();

    const { id } = await createReviewRequest(TEST_COMPANY_ID, {
      fullName: manualReviewRequestBody.consumerName,
      email: manualReviewRequestBody.email,
      phone: manualReviewRequestBody.mobile,
      sent: true,
      dateCreated: dayjs().subtract(28, "hours").toDate(),
    });

    const response = await request
      .post(`/manual-review-requests/${id}/reminder`)
      .send({});

    expect(response.status).toBe(409);

    expect(postManualReviewRequestMock).not.toHaveBeenCalled();
  });
});
