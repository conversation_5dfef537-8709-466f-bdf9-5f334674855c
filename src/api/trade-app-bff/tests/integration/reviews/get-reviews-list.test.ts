import { consumerSDK } from "@checkatrade/consumer-sdk";
import {
  ReviewStatus,
  ReviewType,
  ReviewTypes,
  reviewSDK,
} from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const mockReview = {
  companyId: 123,
  title: "Title",
  review: "Review body",
  type: ReviewType.WORK_NOT_CARRIED,
  id: faker.string.uuid(),
  status: ReviewStatus.NOT_PUBLISHED,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

const getMockReviewWithConsumerId =
  (): ReviewTypes.Api.Public.Get.Response => ({
    ...mockReview,
    consumerId: faker.string.uuid(),
  });

const getMockReviewWithReviewer = (): ReviewTypes.Api.Public.Get.Response => ({
  ...mockReview,
  reviewer: {
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    postcode: "SW19 1JJ",
  },
});

describe("GET /reviews", () => {
  const getReviewsMock = jest.fn();
  const getConsumerMock = jest.fn();

  beforeEach(() => {
    jest.spyOn(reviewSDK, "public").mockReturnValue({
      getReviews: getReviewsMock,
    } as never);

    jest.spyOn(consumerSDK, "service").mockReturnValue({
      getConsumer: getConsumerMock,
    } as never);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Should return a list of reviews when token is valid", () => {
    const expectedGetReviewsParameters = {
      companyId: TEST_COMPANY_ID,
      contextCompanyId: TEST_COMPANY_ID,
      page: 1,
      size: 10,
    };

    it("with reviewer mapped from consumer", async () => {
      const reviewMock = getMockReviewWithConsumerId();
      getReviewsMock.mockResolvedValue({ data: [reviewMock] });
      const consumerMock = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        primaryAddress: { postcode: "SW19 1JJ" },
      };
      getConsumerMock.mockResolvedValue(consumerMock);
      const request = await getTestRequestWithValidToken();

      const response = await request.get("/reviews");

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        data: [
          {
            ...reviewMock,
            reviewer: {
              firstName: consumerMock.firstName,
              lastName: consumerMock.lastName,
              postcode: consumerMock.primaryAddress.postcode,
            },
          },
        ],
      });

      expect(getReviewsMock).toHaveBeenCalledWith(expectedGetReviewsParameters);
    });

    it("with reviewer mapped from review.reviewer", async () => {
      const reviewMock = getMockReviewWithReviewer();
      getReviewsMock.mockResolvedValue({ data: [reviewMock] });
      getConsumerMock.mockResolvedValue({});
      const request = await getTestRequestWithValidToken();
      const response = await request.get("/reviews");

      expect(response.status).toBe(200);

      expect(response.body).toEqual({
        data: [{ ...reviewMock }],
      });

      expect(getReviewsMock).toHaveBeenCalledWith(expectedGetReviewsParameters);
    });

    it("with empty reviewer", async () => {
      const reviewMock = { ...mockReview, reviewer: null };
      getReviewsMock.mockResolvedValue({ data: [reviewMock] });
      getConsumerMock.mockResolvedValue({});
      const request = await getTestRequestWithValidToken();

      const response = await request.get("/reviews");

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        data: [
          {
            ...reviewMock,
            reviewer: {
              firstName: "",
              lastName: "",
              postcode: "",
            },
          },
        ],
      });

      expect(getReviewsMock).toHaveBeenCalledWith(expectedGetReviewsParameters);
    });
  });

  it("Should return a 401 for invalid token", async () => {
    const request = await getTestRequestWithInvalidToken();

    const response = await request.get("/reviews");

    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: "/reviews",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
