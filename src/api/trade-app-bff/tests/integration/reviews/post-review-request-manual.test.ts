import { reviewSDK } from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";

import { formatPhoneNumber } from "../../../src/controllers/reviews/format-phone-number";
import {
  TEST_COMPANY_ID,
  createCompany,
  getTestRequestWithValidToken,
} from "../../helpers";

describe("POST /review-request/manual", () => {
  const postManualReviewRequestMock = jest.fn();

  beforeEach(async () => {
    await createCompany(TEST_COMPANY_ID);
    jest.spyOn(reviewSDK, "public").mockReturnValue({
      postManualReviewRequest: postManualReviewRequestMock,
    } as never);
  });

  const manualReviewRequestBody = {
    consumerName: faker.person.fullName(),
    email: faker.internet.email(),
    mobile: formatPhoneNumber("07544111222"),
  };

  const mockManualReviewResponse = {
    ...manualReviewRequestBody,
    id: faker.string.uuid(),
    companyId: TEST_COMPANY_ID,
    requestCount: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  it("should call reviewSDK", async () => {
    const request = getTestRequestWithValidToken();
    postManualReviewRequestMock.mockResolvedValue(mockManualReviewResponse);

    const response = await request
      .post("/review-request/manual")
      .send(manualReviewRequestBody);

    expect(response.status).toBe(201);

    const postManualReviewRequestMockResponse = {
      companyId: TEST_COMPANY_ID,
      companyName: "Company 1030757",
      companyUniqueName: "company-1030757",
      consumerName: manualReviewRequestBody.consumerName,
      email: manualReviewRequestBody.email,
      mobile: manualReviewRequestBody.mobile,
    };

    expect(postManualReviewRequestMock).toHaveBeenCalledWith(
      postManualReviewRequestMockResponse,
    );
  });
});
