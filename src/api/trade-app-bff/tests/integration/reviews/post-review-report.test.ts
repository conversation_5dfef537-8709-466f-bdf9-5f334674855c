import {
  ReviewStatus,
  ReviewType,
  ReviewTypes,
  reviewSDK,
} from "@checkatrade/review-sdk";
import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  getTestRequestWithValidNonMemberToken,
  getTestRequestWithValidToken,
} from "../../helpers";

describe("POST /reviews/:reviewId/report", () => {
  const payload = {
    description: "description",
    reason: "HARASSMENT_OR_ABUSE",
  };

  const mockResponse: ReviewTypes.Api.Public.Report.Response = {
    type: ReviewType.WORK_NOT_CARRIED,
    companyId: TEST_COMPANY_ID,
    title: "Title",
    review: "Review",
    id: "id",
    status: ReviewStatus.NOT_PUBLISHED,
    createdAt: "2025-01-01T00:00:00.000Z",
    updatedAt: "2025-01-01T00:00:00.000Z",
  };

  let reviewId: string;
  const postReportReview = jest.fn();

  beforeEach(() => {
    reviewId = faker.string.uuid();
    jest
      .spyOn(reviewSDK, "public")
      .mockReturnValue({ postReportReview } as never);
    (postReportReview as jest.Mock).mockResolvedValue(mockResponse);
  });

  it("should send report if user is a member", async () => {
    const response = await getTestRequestWithValidToken()
      .post(`/reviews/${reviewId}/report`)
      .send(payload);

    expect(response.status).toEqual(201);
    expect(response.body).toEqual(mockResponse);
  });

  it("should throw a forbidden error if user is not a member", async () => {
    const response = await getTestRequestWithValidNonMemberToken()
      .post(`/reviews/${reviewId}/report`)
      .send(payload);

    expect(response.status).toEqual(403);
  });
});
