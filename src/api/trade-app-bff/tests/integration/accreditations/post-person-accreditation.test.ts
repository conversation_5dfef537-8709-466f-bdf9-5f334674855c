import { faker } from "@faker-js/faker";
import dayjs from "dayjs";
import { Response } from "supertest";

import * as accreditationsService from "../../../src/services/accreditations";
import {
  createAccreditations,
  getTestRequestWithValidToken,
} from "../../helpers";

const getUrl = (personId: string) => `/person/${personId}/accreditations`;

describe("postPersonAccreditation", () => {
  let personId: string;
  let companyId: number;

  beforeEach(async () => {
    await createAccreditations();

    personId = faker.string.uuid();
    companyId = faker.number.int({ min: 1000, max: 9999 });
    jest
      .spyOn(accreditationsService.accreditations, "isPersonInCompany")
      .mockResolvedValue(true);
  });

  it("should return 404 if accreditation does not exist", async () => {
    const request = getTestRequestWithValidToken(companyId);

    const mockBody = {
      accreditationId: 777777,
    };

    const response: Response = await request
      .post(getUrl(personId))
      .send(mockBody);

    expect(response.status).toBe(404);
  });

  it("should return 400 if expiryDate is missing but is required for accreditation", async () => {
    const request = getTestRequestWithValidToken(companyId);

    const mockBody = {
      accreditationId: 10,
      proof: [
        {
          fileName: "proof1.jpg",
          fileSize: 1024 * 12,
          reference: "ABC123",
          uploadedDate: dayjs().toISOString(),
          mimeType: "image/jpeg",
        },
      ],
    };

    const response: Response = await request
      .post(getUrl(personId))
      .send(mockBody);

    expect(response.status).toBe(400);
    expect(response.body.detail).toContain("expiryDate is required");
  });

  it("should return 400 if expiryDate is in the past", async () => {
    const request = getTestRequestWithValidToken(companyId);

    const mockBody = {
      accreditationId: 10,
      expiryDate: dayjs().subtract(1, "day").toISOString(),
      proof: [
        {
          fileName: "proof1.jpg",
          fileSize: 1024 * 12,
          reference: "ABC123",
          uploadedDate: dayjs().toISOString(),
          mimeType: "image/jpeg",
        },
      ],
    };

    const response: Response = await request
      .post(getUrl(personId))
      .send(mockBody);

    expect(response.status).toBe(400);
    expect(response.body.detail).toContain("cannot be in the past");
  });

  it("should return 200 on valid request", async () => {
    const request = getTestRequestWithValidToken(companyId);

    const mockBody = {
      accreditationId: 20,
    };

    const response: Response = await request
      .post(getUrl(personId))
      .send(mockBody);

    expect(response.status).toBe(200);
  });

  it("should return PersonAccreditationResponse", async () => {
    const request = getTestRequestWithValidToken(companyId);
    const accreditationId = 20;

    const mockBody = {
      accreditationId,
    };

    const response: Response = await request
      .post(getUrl(personId))
      .send(mockBody);

    const savedPersonAccreditation =
      await accreditationsService.accreditations.getPersonAccreditation(
        companyId,
        personId,
        mockBody.accreditationId,
      );

    expect(savedPersonAccreditation).toBeTruthy();

    expect(response.body).toEqual({
      accreditationId,
      companyId,
      id: savedPersonAccreditation?.id,
      accreditationName: "Test accreditation 20",
      accreditationLogo: "logo20/path",
      canExpire: false,
      proof: [],
      personId,
      status: "Active",
      statusDate: expect.any(String),
      statusText: expect.stringContaining("Active from"),
    });
  });

  it("should override deleted PersonAccreditationResponse", async () => {
    const request = getTestRequestWithValidToken(companyId);
    const accreditationId = 20;

    const mockBody = {
      accreditationId,
    };

    await request.post(getUrl(personId)).send(mockBody);

    const savedPersonAccreditation =
      await accreditationsService.accreditations.getPersonAccreditation(
        companyId,
        personId,
        mockBody.accreditationId,
      );

    await accreditationsService.accreditations.deletePersonAccreditation(
      savedPersonAccreditation!,
    );
    const deletedPersonAccreditation =
      await accreditationsService.accreditations.getPersonAccreditation(
        companyId,
        personId,
        mockBody.accreditationId,
      );
    expect(deletedPersonAccreditation?.history.slice(-1)[0]).toEqual({
      platform: "TRADE_APP",
      type: "DELETED",
      updatedDate: expect.any(Date),
    });

    expect(deletedPersonAccreditation?.isDeleted).toBeTruthy();
  });

  it("should update existing accreditation", async () => {
    const accreditationId = 10;
    const originalExpiryDate = dayjs().add(1, "day").toISOString();
    const updatedExpiryDate = dayjs().add(30, "day").toISOString();

    const mockBody = {
      accreditationId,
      expiryDate: originalExpiryDate,
      proof: [
        {
          fileName: "file1.jpg",
          fileSize: 1024 * 12,
          reference: "ABC111",
          uploadedDate: dayjs().toISOString(),
          mimeType: "image/jpeg",
        },
      ],
    };

    const request = getTestRequestWithValidToken(companyId);

    await request.post(getUrl(personId)).send(mockBody);

    const mockUpdateBody = {
      ...mockBody,
      expiryDate: updatedExpiryDate,
      proof: [
        {
          fileName: "file2.jpg",
          fileSize: 2024 * 12,
          reference: "ABC222",
          uploadedDate: dayjs().toISOString(),
          mimeType: "image/jpeg",
        },
      ],
    };

    const updateResponse = await request
      .post(getUrl(personId))
      .send(mockUpdateBody);

    const updatedPersonAccreditation =
      await accreditationsService.accreditations.getPersonAccreditation(
        companyId,
        personId,
        mockBody.accreditationId,
      );

    expect(updatedPersonAccreditation?.expiryDate).toEqual(
      dayjs(updatedExpiryDate).toDate(),
    );
    expect(updatedPersonAccreditation?.proof?.[0]?.fileName).toEqual(
      "file2.jpg",
    );
    expect(updateResponse.body).toEqual({
      accreditationId: 10,
      accreditationLogo: "logo10/path",
      proof: [
        {
          fileName: "file2.jpg",
          fileSize: 24288,
          mimeType: "image/jpeg",
          reference: "ABC222",
          uploadedDate: expect.any(String),
        },
      ],
      accreditationName: "Test accreditation 10",
      canExpire: true,
      companyId,
      id: updatedPersonAccreditation?.id,
      personId,
      status: "Pending Review",
      statusDate: expect.any(String),
      statusText: expect.toStartWith("Added"),
    });
  });
});
