import { faker } from "@faker-js/faker";
import { Response } from "supertest";

import { accreditations } from "../../../src/services/accreditations";
import {
  createPersonAccreditation,
  getTestRequestWithValidToken,
} from "../../helpers";

const getUrl = (personId: string, accreditationId: number) =>
  `/person/${personId}/accreditations/${accreditationId}`;

describe("GET personAccreditation", () => {
  let companyId: number;
  let personId: string;

  beforeEach(async () => {
    companyId = faker.number.int({ min: 100, max: 99999 });
    personId = faker.string.uuid();

    await createPersonAccreditation(companyId, personId, 10);
    await createPersonAccreditation(companyId, personId, 20);
    await createPersonAccreditation(companyId, personId, 30);

    const personAccreditationToDelete =
      await accreditations.getPersonAccreditation(companyId, personId, 20);

    if (!personAccreditationToDelete) {
      throw new Error("Failed to create personAccreditation to delete"); //for jest runner
    }

    await accreditations.deletePersonAccreditation(
      personAccreditationToDelete!,
    );
  });

  it("should return 200 on valid request", async () => {
    const request = getTestRequestWithValidToken(companyId);

    const response: Response = await request.get(getUrl(personId, 10));

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      accreditationId: 10,
      canExpire: true,
      proof: [],
      companyId,
      id: expect.any(String),
      personId,
      accreditationName: "Test accreditation 10",
      accreditationLogo: "logo10/path",
      status: "Pending Review",
      statusDate: expect.any(String),
      statusText: expect.toStartWith("Added"),
    });
  });
});
