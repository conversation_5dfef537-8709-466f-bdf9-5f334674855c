import { faker } from "@faker-js/faker";
import { Response } from "supertest";

import { accreditations } from "../../../src/services/accreditations";
import {
  AccreditationMutationType,
  AccreditationPlatform,
  AccreditationStatusType,
} from "../../../src/services/firebase/firestore/schemas/person-accreditations";
import {
  createPersonAccreditation,
  getTestRequestWithValidToken,
} from "../../helpers";

const getUrl = (personId: string) => `/person/${personId}/accreditations`;

describe("GET personAccreditations", () => {
  let companyId: number;
  let personId: string;

  beforeEach(async () => {
    companyId = faker.number.int({ min: 100, max: 99999 });
    personId = faker.string.uuid();

    await createPersonAccreditation(companyId, personId, 10);
    await createPersonAccreditation(companyId, personId, 20);
    await createPersonAccreditation(companyId, personId, 30, {
      isDeleted: false,
      modifiedDate: new Date(),
      status: AccreditationStatusType.Pending,
      approvalType: null,
      history: [
        {
          reason: null, //null can be set by AdvisorUI
          updatedBy: null, //null can be set by AdvisorUI
          platform: AccreditationPlatform.TradeApp,
          type: AccreditationMutationType.Submitted,
          updatedDate: new Date(),
        },
      ],
    });

    const personAccreditationToDelete =
      await accreditations.getPersonAccreditation(companyId, personId, 20);
    if (!personAccreditationToDelete) {
      throw new Error("Failed to create personAccreditation to delete"); //for jest runner
    }

    await accreditations.deletePersonAccreditation(
      personAccreditationToDelete!,
    );
  });

  it("should return list of nonDeleted personAccreditations", async () => {
    const request = getTestRequestWithValidToken(companyId);

    const response: Response = await request.get(getUrl(personId));

    expect(response.status).toBe(200);
    expect(response.body).toHaveLength(2);
  });
});
