import { faker } from "@faker-js/faker";
import { Response } from "supertest";

import * as accreditationsService from "../../../src/services/accreditations";
import { getTestRequestWithValidToken } from "../../helpers";

const getUrl = (personId: string, accreditationId: number) =>
  `/person/${personId}/accreditations/${accreditationId}`;

describe("DELETE personAccreditation", () => {
  beforeEach(async () => {
    jest
      .spyOn(accreditationsService.accreditations, "isPersonInCompany")
      .mockResolvedValue(true);
  });

  it("should return 200 on valid request", async () => {
    const personId = faker.string.uuid();
    const accreditationId = faker.number.int({ min: 1000, max: 9999 });
    const companyId = faker.number.int({ min: 1000, max: 9999 });
    const request = getTestRequestWithValidToken(companyId);

    const response: Response = await request.delete(
      getUrl(personId, accreditationId),
    );

    expect(response.status).toBe(200);
  });
});
