import { VettingConsentStatus } from "@checkatrade/trade-bff-types";

import { vetting } from "../../../src/services/vetting";
import { getTestRequest, getTestRequestWithValidToken } from "../../helpers";

const teamCompanyId = 937739;
const testPersonId = "1d46805f-9268-44d7-b240-54ec0f126038";

describe("PUT /team/:personId/vetting success", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 and vetting data", async () => {
    jest.spyOn(vetting, "updateTradeVetting").mockResolvedValue({
      id: testPersonId,
      externalId: "1d46805f-9268-44d7-b240-54ec0f126038",
      consent: VettingConsentStatus.Granted,
      dateConsentUpdated: new Date().toISOString(),
      dateCreated: new Date().toISOString(),
      dateModified: new Date().toISOString(),
    });

    const response = await getTestRequestWithValidToken(teamCompanyId)
      .put(`/team/${testPersonId}/vetting`)
      .send({
        consent: VettingConsentStatus.Granted,
      });

    expect(response.status).toEqual(200);
    expect(response.body).toBeDefined();
  });
});

describe("PUT /team/:personId/vetting reject", () => {
  it("should return 400 when x-trade-company-id header is missing", async () => {
    const response = await getTestRequest()
      .put(`/team/${testPersonId}/vetting`)
      .send({
        consent: "Granted",
      });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: `/team/${testPersonId}/vetting`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should send 400 and reject requests with missing body", async () => {
    const response = await getTestRequestWithValidToken()
      .put(`/team/${testPersonId}/vetting`)
      .send({});

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "body must have required property 'consent'",
      instance: `/team/${testPersonId}/vetting`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });
});
