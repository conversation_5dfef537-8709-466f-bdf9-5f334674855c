import {
  FormattedOpportunityStatus,
  FulfilmentType,
} from "@checkatrade/jobs-sdk";

import { searchApi } from "../../../src/lib/api-common";
import {
  TEST_CONSUMER_ID,
  TEST_JOB_ID,
  type TestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";
import {
  mockConsumer,
  mockDetail,
  mockJob,
  testPartialJobResponseBody,
} from "../../helpers";
import {
  consumerSdkTradeMock,
  mockConsumerSdkTrade,
} from "../../helpers/consumer-sdk.mock";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mediaServiceSdkServiceMock,
  mockMediaServiceSdkService,
} from "../../helpers/mocks/media-service-sdk.mock";

describe("POST Job complete", () => {
  let request: TestRequest;

  const tradeMock = {
    companyId: 123,
    uniqueName: "unique-trade-completer",
    name: "test-trade-completer",
    logoUrl: "",
  };

  const mediaAttachments = [
    {
      status: "PASSED_BY_HUMAN",
      id: "1",
      url: "https://example.png",
      thumbnailUrl: "https://example.com/thumb.png",
      mimetype: "image/png",
      isPublished: true,
    },
  ];

  const completeJobMock = {
    ...mockJob,
    status: "COMPLETED" as const,
  };

  const expectedResponse = {
    ...testPartialJobResponseBody,
    category: {
      ...testPartialJobResponseBody.category,
      label: "categoryLabelFromSearchApi",
    },
    address: {
      postcode: "NW8 7RG",
      city: "Correct city",
    },
    preferredStart: {
      id: mockJob.preferredStart.id,
      title: "Preferred start",
    },
    status: FormattedOpportunityStatus.COMPLETED,
    consumer: {
      ...mockConsumer,
      id: TEST_CONSUMER_ID,
    },
    details: [mockDetail],
    createdAt: expect.any(String),
    tradeViewed: false,
    mediaAttachmentIds: [],
    mediaAttachments: [],
    fulfilmentType: FulfilmentType.ENQUIRY,
    jobRefinementForm: mockJob.jobRefinementForm,
  };

  let getTradeSpy: jest.SpyInstance;
  let completeJobSpy: jest.SpyInstance;
  let getConsumerSpy: jest.SpyInstance;

  beforeEach(() => {
    request = getTestRequestWithValidToken();

    getTradeSpy = jest.spyOn(searchApi, "getTrade");
    mockJobsSdkTrade();
    completeJobSpy = jobsSdkTradeMock.completeJob;
    mockConsumerSdkTrade();
    getConsumerSpy = consumerSdkTradeMock.getConsumer;
    mockMediaServiceSdkService();
    mediaServiceSdkServiceMock.getByIds.mockResolvedValue({
      data: mediaAttachments,
    });
    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue("categoryLabelFromSearchApi");

    getTradeSpy.mockResolvedValue(tradeMock);
    completeJobSpy.mockResolvedValue(completeJobMock);
    getConsumerSpy.mockResolvedValue(mockConsumer);
  });

  describe("Valid token", () => {
    it("should send 200 on successful update", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/complete`);
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(expectedResponse);
    });

    it("should return 200 if body of request is empty json", async () => {
      const response = await request
        .post(`/jobs/${TEST_JOB_ID}/complete`)
        .set("content-type", "application/json")
        .send("");

      expect(response.status).toEqual(200);
    });

    it("should include mediaAttachments resolved from mediaAttachmentIds", async () => {
      completeJobSpy.mockResolvedValue({
        ...completeJobMock,
        mediaAttachmentIds: mediaAttachments.map((m) => m.id),
      });

      const response = await request.post(`/jobs/${TEST_JOB_ID}/complete`);
      expect(response.body.mediaAttachments).toEqual(mediaAttachments);
    });
  });

  describe("Invalid token", () => {
    beforeEach(async () => {
      request = await getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId: 4321 }),
        4321,
      );
    });

    it("should send 401 on invalid token", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/complete`);
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: `/jobs/${TEST_JOB_ID}/complete`,
      });
    });
  });
});
