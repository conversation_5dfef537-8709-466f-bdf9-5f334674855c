import {
  FormattedOpportunityStatus,
  FulfilmentType,
} from "@checkatrade/jobs-sdk";
import { Test } from "supertest";
import TestAgent from "supertest/lib/agent";

import { searchApi } from "../../../src/lib/api-common";
import {
  TEST_CONSUMER_ID,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
} from "../../helpers";
import {
  mockConsumer,
  mockDetail,
  mockJob,
  testPartialJobResponseBody,
} from "../../helpers";
import { mockInvalidTokenJWT } from "../../helpers";
import {
  consumerSdkTradeMock,
  mockConsumerSdkTrade,
} from "../../helpers/consumer-sdk.mock";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mediaServiceSdkServiceMock,
  mockMediaServiceSdkService,
} from "../../helpers/mocks/media-service-sdk.mock";

const GET_PUT_JOB_NOTE_URL = (jobId: string) => `/jobs/${jobId}/note`;

const mediaAttachments = [
  {
    status: "PASSED_BY_HUMAN",
    id: "1",
    url: "https://example.png",
    thumbnailUrl: "https://example.com/thumb.png",
    mimetype: "image/png",
    isPublished: true,
  },
];

const expectedResponse = {
  ...testPartialJobResponseBody,
  category: {
    ...testPartialJobResponseBody.category,
    label: "categoryLabelFromSearchApi",
  },
  address: {
    postcode: "NW8 7RG",
    city: "Correct city",
  },
  preferredStart: {
    id: mockJob.preferredStart.id,
    title: "Preferred start",
  },
  status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
  consumer: {
    ...mockConsumer,
    id: TEST_CONSUMER_ID,
  },
  details: [mockDetail],
  createdAt: expect.any(String),
  tradeViewed: false,
  mediaAttachmentIds: [],
  mediaAttachments: [],
  fulfilmentType: FulfilmentType.ENQUIRY,
  jobRefinementForm: mockJob.jobRefinementForm,
};

describe("PUT Job Note", () => {
  let request: TestAgent<Test>;
  let updateJobNoteSpy: jest.SpyInstance;
  let getConsumerSpy: jest.SpyInstance;

  describe("Valid token", () => {
    beforeEach(() => {
      jest
        .spyOn(searchApi, "getCategoryName")
        .mockResolvedValue("categoryLabelFromSearchApi");

      request = getTestRequestWithValidToken();
      mockJobsSdkTrade();
      updateJobNoteSpy = jobsSdkTradeMock.updateJobNote;
      mockConsumerSdkTrade();
      getConsumerSpy = consumerSdkTradeMock.getConsumer;
      mockMediaServiceSdkService();
      mediaServiceSdkServiceMock.getByIds.mockResolvedValue({
        data: mediaAttachments,
      });

      updateJobNoteSpy.mockResolvedValue(mockJob);
      getConsumerSpy.mockResolvedValue(mockConsumer);
    });

    it("should send 200 with correct payload", async () => {
      const response = await request
        .put(GET_PUT_JOB_NOTE_URL(mockJob.id))
        .send({ note: "note" });
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(expectedResponse);
    });

    it("should include mediaAttachments resolved from mediaAttachmentIds", async () => {
      updateJobNoteSpy.mockResolvedValue({
        ...mockJob,
        mediaAttachmentIds: mediaAttachments.map((m) => m.id),
      });

      const response = await request
        .put(GET_PUT_JOB_NOTE_URL(mockJob.id))
        .send({ note: "note" });
      expect(response.body.mediaAttachments).toEqual(mediaAttachments);
    });
  });

  describe("Invalid token", () => {
    beforeEach(async () => {
      request = await getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId: 4321 }),
        4321,
      );
    });

    it("should send 401 on invalid token", async () => {
      const response = await request
        .put(GET_PUT_JOB_NOTE_URL(mockJob.id))
        .send({ note: "note" });
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: GET_PUT_JOB_NOTE_URL(mockJob.id),
      });
    });
  });
});
