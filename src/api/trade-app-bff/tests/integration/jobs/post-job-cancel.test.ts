import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import {
  FormattedOpportunityStatus,
  FulfilmentType,
} from "@checkatrade/jobs-sdk";

import { searchApi } from "../../../src/lib/api-common";
import {
  TEST_COMPANY_ID,
  TEST_CONSUMER_ID,
  TEST_JOB_ID,
  type TestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockConsumer,
  mockDetail,
  mockInvalidTokenJWT,
  mockJob,
  testPartialJobResponseBody,
} from "../../helpers";
import {
  consumerSdkTradeMock,
  mockConsumerSdkTrade,
} from "../../helpers/consumer-sdk.mock";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mediaServiceSdkServiceMock,
  mockMediaServiceSdkService,
} from "../../helpers/mocks/media-service-sdk.mock";

describe("POST Job cancel", () => {
  let request: TestRequest;

  const tradeMock = {
    companyId: 123,
    uniqueName: "unique-trade-acceptor",
    name: "test-trade-acceptor",
    logoUrl: "",
  };

  const mediaAttachments = [
    {
      status: "PASSED_BY_HUMAN",
      id: "1",
      url: "https://example.png",
      thumbnailUrl: "https://example.com/thumb.png",
      mimetype: "image/png",
      isPublished: true,
    },
  ];

  const cancelJobMock = {
    ...mockJob,
    status: "CANCELLED" as const,
  };

  const expectedResponse = {
    ...testPartialJobResponseBody,
    category: {
      ...testPartialJobResponseBody.category,
      label: "categoryLabelFromSearchApi",
    },
    address: {
      postcode: "NW8 7RG",
      city: "Correct city",
    },
    preferredStart: {
      id: mockJob.preferredStart.id,
      title: "Preferred start",
    },
    status: FormattedOpportunityStatus.CANCELLED,
    consumer: {
      ...mockConsumer,
      id: TEST_CONSUMER_ID,
    },
    details: [mockDetail],
    createdAt: expect.any(String),
    tradeViewed: false,
    mediaAttachmentIds: [],
    mediaAttachments: [],
    fulfilmentType: FulfilmentType.ENQUIRY,
    jobRefinementForm: mockJob.jobRefinementForm,
  };

  let freezeChannelSpy: jest.SpyInstance;
  let getTradeSpy: jest.SpyInstance;
  let getJobSpy: jest.SpyInstance;
  let cancelJobSpy: jest.SpyInstance;
  let getConsumerSpy: jest.SpyInstance;

  beforeEach(() => {
    request = getTestRequestWithValidToken();

    freezeChannelSpy = jest.spyOn(chatSDK, "freezeChannel");
    getTradeSpy = jest.spyOn(searchApi, "getTrade");
    mockJobsSdkTrade();
    getJobSpy = jobsSdkTradeMock.getJob;
    cancelJobSpy = jobsSdkTradeMock.cancelJob;
    mockConsumerSdkTrade();
    getConsumerSpy = consumerSdkTradeMock.getConsumer;
    mockMediaServiceSdkService();
    mediaServiceSdkServiceMock.getByIds.mockResolvedValue({
      data: mediaAttachments,
    });
    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue("categoryLabelFromSearchApi");

    freezeChannelSpy.mockResolvedValue(true);
    getTradeSpy.mockResolvedValue(tradeMock);
    getJobSpy.mockResolvedValue(mockJob);
    cancelJobSpy.mockResolvedValue(cancelJobMock);
    getConsumerSpy.mockResolvedValue(mockConsumer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Valid token", () => {
    it("should send 200 on successful update", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/cancel`);
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(expectedResponse);
    });

    it("should send smart message when status is CANCELLED", async () => {
      await request.post(`/jobs/${TEST_JOB_ID}/cancel`);
      expect(freezeChannelSpy).toHaveBeenCalledWith({
        channelId: "channelId",
        message: {
          opportunityId: "channelId",
          smartType: SmartMessageType.OPPORTUNITY_CANCELLED,
          text: "The trade has cancelled this job",
        },
        userId: TEST_COMPANY_ID,
        logger: expect.anything(),
      });
    });

    it("should NOT freeze the channel, when job status is already CANCELLED", async () => {
      getJobSpy.mockResolvedValue(cancelJobMock);
      await request.post(`/jobs/${TEST_JOB_ID}/cancel`);
      expect(freezeChannelSpy).toHaveBeenCalledTimes(0);
    });

    it("should include mediaAttachments resolved from mediaAttachmentIds", async () => {
      cancelJobSpy.mockResolvedValue({
        ...cancelJobMock,
        mediaAttachmentIds: mediaAttachments.map((m) => m.id),
      });

      const response = await request.post(`/jobs/${TEST_JOB_ID}/cancel`);
      expect(response.body.mediaAttachments).toEqual(mediaAttachments);
    });
  });

  describe("Invalid token", () => {
    beforeEach(async () => {
      request = await getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId: 4321 }),
        4321,
      );
    });

    it("should send 401 on invalid token", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/cancel`);
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: `/jobs/${TEST_JOB_ID}/cancel`,
      });
    });
  });
});
