import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";

import {
  TEST_JOB_ID,
  type TestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

describe("POST request address", () => {
  let request: TestRequest;
  let sendSmartMessageSpy: jest.SpyInstance;
  let getChannelsByIdSpy: jest.SpyInstance;

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Valid token", () => {
    beforeEach(() => {
      sendSmartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
      getChannelsByIdSpy = jest.spyOn(chatSDK, "getChannelsById");
      mockJobsSdkTrade();
      request = getTestRequestWithValidToken();
    });

    describe("When the job is not found against the trades account", () => {
      beforeEach(() => {
        jobsSdkTradeMock.getJob.mockResolvedValue(null);
      });

      it("should send 403 when the address is requested", async () => {
        const response = await request.post(
          `/jobs/${TEST_JOB_ID}/request-address`,
        );

        expect(response.status).toEqual(401);
        expect(response.body).toEqual({
          type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
          status: 401,
          title: "Unauthorized",
          detail: "Job not found",
          instance: `/jobs/${TEST_JOB_ID}/request-address`,
        });
      });
    });

    describe("When there is a channel for the job", () => {
      beforeEach(() => {
        jobsSdkTradeMock.getJob.mockReturnValue({
          opportunityId: "test-channel-id",
        });
        getChannelsByIdSpy.mockResolvedValue([{}]);
        sendSmartMessageSpy.mockResolvedValue({});
      });

      it("should send 200 when the address is requested", async () => {
        const response = await request.post(
          `/jobs/${TEST_JOB_ID}/request-address`,
        );

        expect(response.status).toEqual(200);
        expect(response.body).toEqual({});
        expect(getChannelsByIdSpy).toHaveBeenCalledWith("test-channel-id");
        expect(sendSmartMessageSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            smartType: SmartMessageType.ADDRESS_REQUESTED,
            text: "The trade has requested the consumer's address.",
            addressId: "",
          }),
        );
      });
    });

    describe("When there is no channel for the job", () => {
      beforeEach(() => {
        jobsSdkTradeMock.getJob.mockReturnValue({
          opportunityId: TEST_JOB_ID,
        });
        getChannelsByIdSpy.mockResolvedValue([]);
      });

      it("should send 422 when there is no channel for the opportunity", async () => {
        const response = await request.post(
          `/jobs/${TEST_JOB_ID}/request-address`,
        );

        expect(response.status).toEqual(422);
        expect(response.body).toEqual({
          type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/422",
          status: 422,
          title: "Unprocessable Entity",
          detail: "No channel found for this job",
          instance: `/jobs/${TEST_JOB_ID}/request-address`,
        });
      });
    });
  });

  describe("Invalid token", () => {
    beforeEach(async () => {
      request = await getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId: 4321 }),
        4321,
      );
    });

    it("should send 401 on invalid token", async () => {
      const response = await request.post(
        `/jobs/${TEST_JOB_ID}/request-address`,
      );
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: `/jobs/${TEST_JOB_ID}/request-address`,
      });
    });
  });
});
