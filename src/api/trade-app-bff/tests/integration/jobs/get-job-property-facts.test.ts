import { NotFoundError } from "@checkatrade/errors";

import { searchApi } from "../../../src/lib/api-common";
import {
  TestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
  mockPropertyFacts,
} from "../../helpers";
import { mockJob } from "../../helpers";
import {
  consumerMyhomeSdkTradeMock,
  mockConsumerMyhomeSdkTrade,
} from "../../helpers/consumer-myhome-sdk";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

describe("GET Job property facts", () => {
  let request: TestRequest;
  let getJobsSpy: jest.SpyInstance;
  let getPropertyFactsForConsumerSpy: jest.SpyInstance;

  it("should send 401 and reject requests without a valid auth token", async () => {
    // Arrange
    request = await getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId: 4321 }),
      4321,
    );

    // Act
    const response = await request.get(`/jobs/${mockJob.id}/property-facts`);

    // Assert
    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/jobs/${mockJob.id}/property-facts`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  describe("when the consumer profile exists", () => {
    beforeEach(async () => {
      request = getTestRequestWithValidToken();

      mockJobsSdkTrade();
      getJobsSpy = jobsSdkTradeMock.getJob;
      getJobsSpy.mockResolvedValue(mockJob);
      jest
        .spyOn(searchApi, "getCategoryName")
        .mockResolvedValue("categoryLabelFromSearchApi");
      mockConsumerMyhomeSdkTrade();
      getPropertyFactsForConsumerSpy =
        consumerMyhomeSdkTradeMock.propertyFactsForConsumer.get;
      getPropertyFactsForConsumerSpy.mockResolvedValue(mockPropertyFacts);
    });

    it("should return an empty object if uprn not set on job", async () => {
      // Act
      const response = await request.get(`/jobs/${mockJob.id}/property-facts`);

      // Assert
      expect(getJobsSpy).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({});
    });

    it("should return trade job with chimnie data if uprn set", async () => {
      const uprn = "1234567890";
      getJobsSpy.mockResolvedValueOnce({
        ...mockJob,
        address: { ...mockJob.address, uprn },
      });

      // Act
      const response = await request.get(`/jobs/${mockJob.id}/property-facts`);

      // Assert
      expect(getJobsSpy).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(mockPropertyFacts);
    });

    it("should return 404, if the job does not exist", async () => {
      // Arrange
      getJobsSpy.mockRejectedValue(
        new NotFoundError("The requested resource was not found"),
      );

      // Act
      const response = await request.get(`/jobs/${mockJob.id}/property-facts`);

      // Assert
      expect(response.status).toEqual(404);
      expect(response.body).toEqual({
        detail: "The requested resource was not found",
        instance: `/jobs/${mockJob.id}/property-facts`,
        status: 404,
        title: "Not Found",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404",
      });
    });
  });
});
