import { NotFoundError } from "@checkatrade/errors";
import { Test } from "supertest";
import TestAgent from "supertest/lib/agent";

import {
  getTestRequestFromJWTPayload,
  mockArchivedJob,
  mockJob,
  mockValidTokenJWT,
} from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

describe("GET Archived Job", () => {
  let request: TestAgent<Test>;
  let getArchivedJobSpy: jest.SpyInstance;

  afterEach(() => jest.clearAllMocks());

  it("should send 401 and reject requests without a valid auth token", async () => {
    // Arrange
    const invalidCompanyId = 4444;
    request = await getTestRequestFromJWTPayload({}, invalidCompanyId);

    // Act
    const response = await request.get(`/archived-jobs/${mockJob.id}`);

    // Assert
    expect(response.status).toEqual(401);
  });

  describe("when valid auth token exists", () => {
    beforeEach(async () => {
      const companyId = 1234;
      request = getTestRequestFromJWTPayload(
        mockValidTokenJWT({ companyId }),
        companyId,
      );
      mockJobsSdkTrade();
      getArchivedJobSpy = jobsSdkTradeMock.getArchivedJob;
      getArchivedJobSpy.mockResolvedValue(mockArchivedJob);
    });

    it("should return trade archived job ", async () => {
      // Act
      const response = await request.get(`/archived-jobs/${mockJob.id}`);

      // Assert
      expect(response.status).toEqual(200);
      expect(getArchivedJobSpy).toHaveBeenCalled();
      expect(response.body).toEqual({ ...mockArchivedJob });
    });

    it("should return 404, if the job does not exist", async () => {
      // Arrange
      getArchivedJobSpy.mockRejectedValue(new NotFoundError());

      // Act
      const response = await request.get(`/archived-jobs/${mockJob.id}`);

      // Assert
      expect(response.status).toEqual(404);
    });
  });
});
