import { chatSDK } from "@checkatrade/chat-sdk";
import {
  FormattedOpportunityStatus,
  FulfilmentType,
} from "@checkatrade/jobs-sdk";

import { searchApi } from "../../../src/lib/api-common";
import {
  TEST_CONSUMER_ID,
  TEST_JOB_ID,
  type TestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";
import {
  mockConsumer,
  mockDetail,
  mockJob,
  testPartialJobResponseBody,
} from "../../helpers";
import {
  consumerSdkTradeMock,
  mockConsumerSdkTrade,
} from "../../helpers/consumer-sdk.mock";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mediaServiceSdkServiceMock,
  mockMediaServiceSdkService,
} from "../../helpers/mocks/media-service-sdk.mock";

describe("POST Job mark as read", () => {
  let request: TestRequest;

  const tradeMock = {
    companyId: 123,
    uniqueName: "unique-trade-acceptor",
    name: "test-trade-acceptor",
    logoUrl: "",
  };

  const mediaAttachments = [
    {
      status: "PASSED_BY_HUMAN",
      id: "1",
      url: "https://example.png",
      thumbnailUrl: "https://example.com/thumb.png",
      mimetype: "image/png",
      isPublished: true,
    },
  ];

  const viewedJobMock = {
    ...mockJob,
    tradeViewed: true,
  };

  const expectedResponse = {
    ...testPartialJobResponseBody,
    category: {
      ...testPartialJobResponseBody.category,
      label: "categoryLabelFromSearchApi",
    },
    address: {
      postcode: "NW8 7RG",
      city: "Correct city",
    },
    preferredStart: {
      id: mockJob.preferredStart.id,
      title: "Preferred start",
    },
    status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
    consumer: {
      ...mockConsumer,
      id: TEST_CONSUMER_ID,
    },
    details: [mockDetail],
    createdAt: expect.any(String),
    tradeViewed: true,
    mediaAttachmentIds: [],
    mediaAttachments: [],
    fulfilmentType: FulfilmentType.ENQUIRY,
    jobRefinementForm: mockJob.jobRefinementForm,
  };

  let sendSmartMessageSpy: jest.SpyInstance;
  let getTradeSpy: jest.SpyInstance;
  let markJobAsReadSpySpy: jest.SpyInstance;
  let getConsumerSpy: jest.SpyInstance;

  beforeEach(() => {
    request = getTestRequestWithValidToken();

    sendSmartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    getTradeSpy = jest.spyOn(searchApi, "getTrade");
    mockJobsSdkTrade();
    markJobAsReadSpySpy = jobsSdkTradeMock.markJobAsRead;
    mockConsumerSdkTrade();
    getConsumerSpy = consumerSdkTradeMock.getConsumer;
    mockMediaServiceSdkService();
    mediaServiceSdkServiceMock.getByIds.mockResolvedValue({
      data: mediaAttachments,
    });
    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue("categoryLabelFromSearchApi");

    sendSmartMessageSpy.mockReturnValue(Promise.resolve());
    getTradeSpy.mockResolvedValue(tradeMock);
    markJobAsReadSpySpy.mockResolvedValue(viewedJobMock);
    getConsumerSpy.mockResolvedValue(mockConsumer);
  });

  describe("Valid token", () => {
    it("should send 200 on successful update", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/mark-as-read`);
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(expectedResponse);
    });

    it("should return 200 if body of request is empty json", async () => {
      const response = await request
        .post(`/jobs/${TEST_JOB_ID}/mark-as-read`)
        .set("content-type", "application/json")
        .send("");

      expect(response.status).toEqual(200);
    });

    it("should include mediaAttachments resolved from mediaAttachmentIds", async () => {
      markJobAsReadSpySpy.mockResolvedValue({
        ...viewedJobMock,
        mediaAttachmentIds: mediaAttachments.map((m) => m.id),
      });

      const response = await request.post(`/jobs/${TEST_JOB_ID}/mark-as-read`);
      expect(response.body.mediaAttachments).toEqual(mediaAttachments);
    });
  });

  describe("Invalid token", () => {
    beforeEach(() => {
      request = getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId: 4321 }),
        4321,
      );
    });

    it("should send 401 on invalid token", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/mark-as-read`);
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: `/jobs/${TEST_JOB_ID}/mark-as-read`,
      });
    });
  });
});
