import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import {
  FormattedOpportunityStatus,
  FulfilmentType,
} from "@checkatrade/jobs-sdk";

import { config } from "../../../src/config";
import { searchApi } from "../../../src/lib/api-common";
import { redirect } from "../../../src/services/redirect";
import {
  TEST_COMPANY_ID,
  TEST_CONSUMER_ID,
  TEST_JOB_ID,
  type TestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockConsumer,
  mockDetail,
  mockInvalidTokenJWT,
  mockJob,
  testPartialJobResponseBody,
} from "../../helpers";
import {
  consumerSdkTradeMock,
  mockConsumerSdkTrade,
} from "../../helpers/consumer-sdk.mock";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mediaServiceSdkServiceMock,
  mockMediaServiceSdkService,
} from "../../helpers/mocks/media-service-sdk.mock";

describe("POST Job reject", () => {
  let request: TestRequest;

  const tradeMock = {
    companyId: 123,
    uniqueName: "unique-trade-acceptor",
    name: "test-trade-acceptor",
    logoUrl: "",
  };

  const mediaAttachments = [
    {
      status: "PASSED_BY_HUMAN",
      id: "1",
      url: "https://example.png",
      thumbnailUrl: "https://example.com/thumb.png",
      mimetype: "image/png",
      isPublished: true,
    },
  ];

  const rejectJobMock = {
    ...mockJob,
    status: "REQUEST_REJECTED" as const,
  };

  const expectedResponse = {
    ...testPartialJobResponseBody,
    category: {
      ...testPartialJobResponseBody.category,
      label: "categoryLabelFromSearchApi",
    },
    address: {
      postcode: "NW8 7RG",
      city: "Correct city",
    },
    preferredStart: {
      id: mockJob.preferredStart.id,
      title: "Preferred start",
    },
    status: FormattedOpportunityStatus.REQUEST_REJECTED,
    consumer: {
      ...mockConsumer,
      id: TEST_CONSUMER_ID,
    },
    details: [mockDetail],
    createdAt: expect.any(String),
    tradeViewed: false,
    mediaAttachmentIds: [],
    mediaAttachments: [],
    fulfilmentType: FulfilmentType.ENQUIRY,
    jobRefinementForm: mockJob.jobRefinementForm,
  };

  let freezeChannelSpy: jest.SpyInstance;
  let getTradeSpy: jest.SpyInstance;
  let getJobSpy: jest.SpyInstance;
  let rejectJobSpy: jest.SpyInstance;
  let getConsumerSpy: jest.SpyInstance;
  let redirectSpy: jest.SpyInstance;

  beforeEach(() => {
    config.redirection.enabled = false;
    redirectSpy = jest.spyOn(redirect, "redirectOnReject");
    request = getTestRequestWithValidToken();

    freezeChannelSpy = jest.spyOn(chatSDK, "freezeChannel");
    getTradeSpy = jest.spyOn(searchApi, "getTrade");
    mockJobsSdkTrade();
    getJobSpy = jobsSdkTradeMock.getJob;
    rejectJobSpy = jobsSdkTradeMock.rejectJob;
    mockConsumerSdkTrade();
    getConsumerSpy = consumerSdkTradeMock.getConsumer;
    mockMediaServiceSdkService();
    mediaServiceSdkServiceMock.getByIds.mockResolvedValue({
      data: mediaAttachments,
    });
    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue("categoryLabelFromSearchApi");

    freezeChannelSpy.mockResolvedValue(true);
    getTradeSpy.mockResolvedValue(tradeMock);
    getJobSpy.mockResolvedValue(mockJob);
    rejectJobSpy.mockResolvedValue(rejectJobMock);
    getConsumerSpy.mockResolvedValue(mockConsumer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Valid token", () => {
    it("should send 200 on successful update", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/reject`);
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(expectedResponse);
    });

    it("should send smart message when status is REQUEST_REJECTED", async () => {
      await request.post(`/jobs/${TEST_JOB_ID}/reject`);
      expect(freezeChannelSpy).toHaveBeenCalledWith({
        channelId: "channelId",
        message: {
          jobId: TEST_JOB_ID,
          smartType: SmartMessageType.JOB_REJECTED,
          text: "This job has been declined by the trade",
        },
        userId: TEST_COMPANY_ID,
        logger: expect.anything(),
      });
    });

    it("should NOT freeze the channel, when job status is already REQUEST_REJECTED", async () => {
      getJobSpy.mockResolvedValue(rejectJobMock);
      await request.post(`/jobs/${TEST_JOB_ID}/reject`);
      expect(freezeChannelSpy).toHaveBeenCalledTimes(0);
    });

    it("should call redirect when redirection is enabled", async () => {
      config.redirection.enabled = true;
      redirectSpy.mockResolvedValue(true);
      const response = await request.post(`/jobs/${TEST_JOB_ID}/reject`);
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(expectedResponse);
      expect(redirectSpy).toHaveBeenCalledWith(
        TEST_JOB_ID,
        TEST_COMPANY_ID,
        expect.any(Date),
        expect.any(String),
        expect.anything(),
      );
    });

    it("should include mediaAttachments resolved from mediaAttachmentIds", async () => {
      rejectJobSpy.mockResolvedValue({
        ...rejectJobMock,
        mediaAttachmentIds: mediaAttachments.map((m) => m.id),
      });

      const response = await request.post(`/jobs/${TEST_JOB_ID}/reject`);
      expect(response.body.mediaAttachments).toEqual(mediaAttachments);
    });
  });

  describe("Invalid token", () => {
    beforeEach(async () => {
      request = await getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId: 4321 }),
        4321,
      );
    });

    it("should send 401 on invalid token", async () => {
      const response = await request.post(`/jobs/${TEST_JOB_ID}/reject`);
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: `/jobs/${TEST_JOB_ID}/reject`,
      });
    });
  });
});
