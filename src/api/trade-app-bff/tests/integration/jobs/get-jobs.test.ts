import {
  FulfilmentType,
  OpportunityStatus,
  TradePagedJobResponse,
} from "@checkatrade/jobs-sdk";
import { Test } from "supertest";
import TestAgent from "supertest/lib/agent";

import { searchApi } from "../../../src/lib/api-common";
import {
  TEST_CONSUMER_ID,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";
import {
  consumerSdkTradeMock,
  mockConsumerSdkTrade,
} from "../../helpers/consumer-sdk.mock";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mediaServiceSdkServiceMock,
  mockMediaServiceSdkService,
} from "../../helpers/mocks/media-service-sdk.mock";
import {
  mockConsumer,
  mockJob,
  testPartialJobResponseBody,
} from "../../helpers/test-jobs";

describe("GET Jobs", () => {
  const mockPaginatedResponse: TradePagedJobResponse = {
    data: [mockJob],
    page: 0,
    size: 10,
    total: 1,
  };

  const mediaAttachments = [
    {
      status: "PASSED_BY_HUMAN",
      id: "1",
      url: "https://example.png",
      thumbnailUrl: "https://example.com/thumb.png",
      mimetype: "image/png",
      isPublished: true,
    },
  ];

  let request: TestAgent<Test>;
  let getJobsSpy: jest.SpyInstance;
  let getConsumerSpy: jest.SpyInstance;

  it("should send 401 and reject requests without a valid auth token", async () => {
    request = await getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId: 4321 }),
      4321,
    );
    const response = await request.get("/jobs");

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: "/jobs",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  describe("when the trade profile exists", () => {
    beforeEach(async () => {
      request = getTestRequestWithValidToken();

      mockJobsSdkTrade();
      getJobsSpy = jobsSdkTradeMock.getJobs;
      getJobsSpy.mockResolvedValue(mockPaginatedResponse);
      jest
        .spyOn(searchApi, "getCategoryName")
        .mockResolvedValue("categoryLabelFromSearchApi");
      mockConsumerSdkTrade();
      getConsumerSpy = consumerSdkTradeMock.getConsumers;
      getConsumerSpy.mockResolvedValue([mockConsumer]);
      mockMediaServiceSdkService();
      mediaServiceSdkServiceMock.getByIds.mockResolvedValue({ data: [] });
    });

    it("should return paginated trade jobs ", async () => {
      const response = await request.get("/jobs");

      expect(getJobsSpy).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        data: [
          {
            id: expect.any(String),
            ...testPartialJobResponseBody,
            category: {
              ...testPartialJobResponseBody.category,
              label: "categoryLabelFromSearchApi",
            },
            channelId: mockJob.opportunityId,
            status: OpportunityStatus.REQUEST_ACCEPTED,
            createdAt: expect.any(String),
            consumer: {
              ...mockConsumer,
              id: TEST_CONSUMER_ID,
            },
            tradeViewed: false,
            fulfilmentType: FulfilmentType.ENQUIRY,
          },
        ],
        page: 0,
        size: 10,
        total: 1,
      });
    });

    test.each([[1, 2, 3]])("should pass correct page %s", async (page) => {
      const mockPaginatedResponse: TradePagedJobResponse = {
        data: Array(10).fill(mockJob),
        page,
        size: 10,
        total: 15,
      };

      getJobsSpy.mockResolvedValue(mockPaginatedResponse);

      const response = await request.get(`/jobs?page=${page}`);

      expect(getJobsSpy).toHaveBeenCalledWith({ page, size: 10 });
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        data: expect.any(Array),
        page: page,
        size: 10,
        total: 15,
      });
      expect(response.body.data.length).toEqual(10);
    });

    it("should use correct filter for ARCHIVE tab", async () => {
      await request.get("/jobs?tab=ARCHIVE");

      expect(getJobsSpy).toHaveBeenCalledWith({
        "page": 1,
        "size": 10,
        "filter[in]": [
          OpportunityStatus.REQUEST_REJECTED,
          OpportunityStatus.CANCELLED,
          OpportunityStatus.V2_WITHDRAWN,
          OpportunityStatus.V2_CANCELLED,
        ],
      });
    });

    it("should use correct filter for BOOKED tab", async () => {
      await request.get("/jobs?tab=BOOKED");

      expect(getJobsSpy).toHaveBeenCalledWith({
        page: 1,
        size: 10,
        filter: OpportunityStatus.V2_BOOKED,
      });
    });

    it("should use correct filter for COMPLETED tab", async () => {
      await request.get("/jobs?tab=COMPLETED");

      expect(getJobsSpy).toHaveBeenCalledWith({
        "filter[in]": [
          OpportunityStatus.COMPLETED,
          OpportunityStatus.V2_COMPLETED,
          OpportunityStatus.V2_WORK_FINISHED,
        ],
        "page": 1,
        "size": 10,
      });
    });

    it("should use correct filter for INTERESTED tab", async () => {
      await request.get("/jobs?tab=INTERESTED");

      expect(getJobsSpy).toHaveBeenCalledWith({
        "page": 1,
        "size": 10,
        "filter[in]": [
          OpportunityStatus.REQUEST_ACCEPTED,
          OpportunityStatus.SCHEDULED,
          OpportunityStatus.SCHEDULE_CANCELLED,
          OpportunityStatus.V2_INTERESTED,
        ],
      });
    });

    it("should use correct filter for REVIEWABLE tab", async () => {
      await request.get("/jobs?tab=REVIEWABLE");

      expect(getJobsSpy).toHaveBeenCalledWith({
        "page": 1,
        "size": 10,
        "filter[in]": [
          OpportunityStatus.REQUEST_ACCEPTED,
          OpportunityStatus.SCHEDULED,
          OpportunityStatus.SCHEDULE_CANCELLED,
          OpportunityStatus.V2_INTERESTED,
          OpportunityStatus.COMPLETED,
          OpportunityStatus.V2_WORK_FINISHED,
        ],
      });
    });

    it("should use correct filter for NEW tab", async () => {
      await request.get("/jobs?tab=NEW");

      expect(getJobsSpy).toHaveBeenCalledWith({
        "filter[in]": [
          OpportunityStatus.REQUESTED,
          OpportunityStatus.V2_REQUESTED,
        ],
        "page": 1,
        "size": 10,
      });
    });

    it("should use correct filter for ALL tab", async () => {
      await request.get("/jobs?tab=ALL");

      expect(getJobsSpy).toHaveBeenCalledWith({
        "filter[nin]": [
          OpportunityStatus.CANCELLED,
          OpportunityStatus.V2_CANCELLED,
          OpportunityStatus.V2_WITHDRAWN,
        ],
        "page": 1,
        "size": 10,
      });
    });

    it("should return 400 if page param is incorrect ", async () => {
      const response = await request.get("/jobs").query({ page: -1 });

      expect(response.status).toEqual(400);
      expect(response.body).toEqual({
        detail: "querystring/page must be >= 1",
        instance: "/jobs?page=-1",
        status: 400,
        title: "Bad Request",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
      });
    });

    it("should return matchingAttachment array", async () => {
      getJobsSpy.mockResolvedValue({
        data: [
          {
            ...mockJob,
            mediaAttachmentIds: mediaAttachments.map((ma) => ma.id),
          },
        ],
        page: 0,
        size: 10,
        total: 1,
      });
      mediaServiceSdkServiceMock.getByIds.mockResolvedValue({
        data: mediaAttachments,
      });

      const response = await request.get("/jobs");

      expect(mediaServiceSdkServiceMock.getByIds).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body.data).toContainEqual(
        expect.objectContaining({
          mediaAttachments,
        }),
      );
    });
  });
});
