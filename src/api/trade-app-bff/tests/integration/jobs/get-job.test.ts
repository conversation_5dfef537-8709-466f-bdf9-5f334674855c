import { NotFoundError } from "@checkatrade/errors";
import {
  FormattedOpportunityStatus,
  FulfilmentType,
} from "@checkatrade/jobs-sdk";
import { faker } from "@faker-js/faker";

import { searchApi } from "../../../src/lib/api-common";
import {
  TEST_CONSUMER_ID,
  TestRequest,
  createMockJob,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockConsumer,
  mockDetail,
  mockInvalidTokenJWT,
  mockJob,
  testPartialJobResponseBody,
} from "../../helpers";
import {
  consumerSdkTradeMock,
  mockConsumerSdkTrade,
} from "../../helpers/consumer-sdk.mock";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mediaServiceSdkServiceMock,
  mockMediaServiceSdkService,
} from "../../helpers/mocks/media-service-sdk.mock";

describe("GET Job", () => {
  let request: TestRequest;
  let getJobsSpy: jest.SpyInstance;
  let getConsumerSpy: jest.SpyInstance;

  const mediaAttachments = [
    {
      status: "PASSED_BY_HUMAN",
      id: "1",
      url: "https://example.png",
      thumbnailUrl: "https://example.com/thumb.png",
      mimetype: "image/png",
      isPublished: true,
    },
  ];

  it("should send 401 and reject requests without a valid auth token", async () => {
    // Arrange
    request = await getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId: 4321 }),
      4321,
    );

    // Act
    const response = await request.get(`/jobs/${mockJob.id}`);

    // Assert
    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/jobs/${mockJob.id}`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  describe("when the consumer profile exists", () => {
    beforeEach(async () => {
      request = getTestRequestWithValidToken();

      mockJobsSdkTrade();
      getJobsSpy = jobsSdkTradeMock.getJob;
      getJobsSpy.mockResolvedValue({
        ...mockJob,
        mediaAttachmentIds: ["1"],
      });
      jest
        .spyOn(searchApi, "getCategoryName")
        .mockResolvedValue("categoryLabelFromSearchApi");
      mockConsumerSdkTrade();
      getConsumerSpy = consumerSdkTradeMock.getConsumer;
      getConsumerSpy.mockResolvedValue(mockConsumer);
      mockMediaServiceSdkService();
      mediaServiceSdkServiceMock.getByIds.mockResolvedValue({
        data: mediaAttachments,
      });
    });

    it("should return trade job ", async () => {
      // Act
      const response = await request.get(`/jobs/${mockJob.id}`);

      // Assert
      expect(getJobsSpy).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        id: expect.any(String),
        ...testPartialJobResponseBody,
        category: {
          ...testPartialJobResponseBody.category,
          label: "categoryLabelFromSearchApi",
        },
        address: {
          postcode: "NW8 7RG",
          city: "Correct city",
        },
        preferredStart: {
          id: response.body.preferredStart.id,
          title: "Preferred start",
        },
        status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
        channelId: mockJob.opportunityId,
        consumer: {
          ...mockConsumer,
          id: TEST_CONSUMER_ID,
        },
        details: [mockDetail],
        createdAt: expect.any(String),
        tradeViewed: false,
        mediaAttachmentIds: ["1"],
        mediaAttachments,
        fulfilmentType: FulfilmentType.ENQUIRY,
        jobRefinementForm: mockJob.jobRefinementForm,
      });
    });

    it("should return trade booking job ", async () => {
      // Arrange
      const bookingId = faker.string.uuid();
      getJobsSpy.mockResolvedValue(
        createMockJob(FulfilmentType.BOOKING_ENQUIRY, {
          id: bookingId,
          createdAt: faker.date.recent().toISOString(),
        }),
      );
      jest
        .spyOn(searchApi, "getCategoryName")
        .mockResolvedValue("categoryLabelFromSearchApi");

      // Act
      const response = await request.get(`/jobs/${mockJob.id}`);

      // Assert
      expect(getJobsSpy).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        id: expect.any(String),
        ...testPartialJobResponseBody,
        category: {
          ...testPartialJobResponseBody.category,
          label: "categoryLabelFromSearchApi",
        },
        address: {
          postcode: "NW8 7RG",
          city: "Correct city",
        },
        preferredStart: {
          id: response.body.preferredStart.id,
          title: "Preferred start",
        },
        status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
        channelId: mockJob.opportunityId,
        consumer: {
          ...mockConsumer,
          id: TEST_CONSUMER_ID,
        },
        details: [mockDetail],
        createdAt: expect.any(String),
        tradeViewed: false,
        mediaAttachmentIds: [],
        mediaAttachments: [],
        fulfilmentType: FulfilmentType.BOOKING_ENQUIRY,
        jobRefinementForm: mockJob.jobRefinementForm,
        booking: {
          createdAt: expect.any(String),
          id: bookingId,
        },
      });
    });

    it("should return 404, if the job does not exist", async () => {
      // Arrange
      getJobsSpy.mockRejectedValue(
        new NotFoundError("The requested resource was not found"),
      );

      // Act
      const response = await request.get(`/jobs/${mockJob.id}`);

      // Assert
      expect(response.status).toEqual(404);
      expect(response.body).toEqual({
        detail: "The requested resource was not found",
        instance: `/jobs/${mockJob.id}`,
        status: 404,
        title: "Not Found",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404",
      });
    });
  });
});
