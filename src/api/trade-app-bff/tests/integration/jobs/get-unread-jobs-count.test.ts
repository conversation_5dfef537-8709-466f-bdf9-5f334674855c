import { Test } from "supertest";
import TestAgent from "supertest/lib/agent";

import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

const path = "/jobs/count-unread";

describe("GET Unread Jobs Count", () => {
  const mockResponse = {
    count: 10,
  };

  let request: TestAgent<Test>;
  let getUnreadJobsCountSpy: jest.SpyInstance;

  it("should send 401 and reject requests without a valid auth token", async () => {
    request = await getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId: 4321 }),
      4321,
    );
    const response = await request.get(path);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: path,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  describe("when the trade profile exists", () => {
    beforeEach(async () => {
      request = getTestRequestWithValidToken();

      mockJobsSdkTrade();
      getUnreadJobsCountSpy = jobsSdkTradeMock.getUnreadJobsCount;
      getUnreadJobsCountSpy.mockResolvedValue(mockResponse);
    });

    it("should return job count ", async () => {
      const response = await request.get(path);

      expect(getUnreadJobsCountSpy).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        count: 10,
      });
    });
  });
});
