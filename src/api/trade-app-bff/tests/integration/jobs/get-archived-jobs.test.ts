import {
  ArchivedJobQuery,
  TradePagedArchivedJobResponse,
} from "@checkatrade/jobs-sdk";
import { Test } from "supertest";
import TestAgent from "supertest/lib/agent";

import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockArchivedJob,
  mockInvalidTokenJWT,
} from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

describe("GET Archived Jobs", () => {
  const mockPaginatedResponse: TradePagedArchivedJobResponse = {
    data: [mockArchivedJob],
    page: 0,
    size: 10,
    total: 1,
  };

  let request: TestAgent<Test>;
  let getJobsSpy: jest.SpyInstance;

  afterEach(() => jest.clearAllMocks());

  it("should send 401 and reject requests without a valid auth token", async () => {
    request = await getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId: 4321 }),
      4321,
    );
    const response = await request.get("/archived-jobs");

    expect(response.status).toEqual(401);
  });

  describe("when the trade profile exists", () => {
    beforeEach(async () => {
      request = getTestRequestWithValidToken();

      mockJobsSdkTrade();
      getJobsSpy = jobsSdkTradeMock.getArchivedJobs;
      getJobsSpy.mockResolvedValue(mockPaginatedResponse);
    });

    it("should return paginated trade jobs ", async () => {
      const response = await request.get("/archived-jobs");

      expect(getJobsSpy).toHaveBeenCalled();
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        data: [{ ...mockArchivedJob }],
        page: 0,
        size: 10,
        total: 1,
      });
    });

    test.each([[1, 2, 3]])("should pass correct page %s", async (page) => {
      const mockPaginatedResponse: TradePagedArchivedJobResponse = {
        data: Array(10).fill(mockArchivedJob),
        page,
        size: 10,
        total: 15,
      };

      getJobsSpy.mockResolvedValue(mockPaginatedResponse);

      const response = await request.get(`/archived-jobs?page=${page}`);

      expect(getJobsSpy).toHaveBeenCalledWith({ page, size: 10 });
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        data: expect.any(Array),
        page: page,
        size: 10,
        total: 15,
      });
      expect(response.body.data.length).toEqual(10);
    });

    test.each([
      [ArchivedJobQuery.All],
      [ArchivedJobQuery.Archive],
      [ArchivedJobQuery.Completed],
      [ArchivedJobQuery.Interested],
    ])("should use correct filter", async (filter) => {
      await request.get(`/archived-jobs?filter=${filter}`);

      expect(getJobsSpy).toHaveBeenCalledWith({
        page: 1,
        size: 10,
        filter,
      });
    });

    it("should return 400 if page param is incorrect ", async () => {
      const response = await request.get("/archived-jobs").query({ page: -1 });
      expect(response.status).toEqual(400);
    });
  });
});
