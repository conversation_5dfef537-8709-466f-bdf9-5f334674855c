import { chatSDK } from "@checkatrade/chat-sdk";
import { FormattedOpportunityStatus } from "@checkatrade/jobs-sdk";
import { faker } from "@faker-js/faker";
import dayjs from "dayjs";
import nock from "nock";
import { Response } from "supertest";

import { config } from "../../../../../src/config";
import {
  SmartMessageGroup,
  SmartMessageType,
} from "../../../../../src/lib/api-common";
import {
  createJobReviewRequest,
  getJobReviewRequestByJobId,
} from "../../../../../src/services/firebase/firestore/job-review-request";
import {
  TEST_COMPANY_ID,
  TestRequest,
  createCompanies,
  getTestRequest,
  getTestRequestWithValidToken,
  nockCommsReviewRequest,
  nockFindChannel,
  nockGetMessageById,
  nockGetTradeError,
  nockGetTradeSuccess,
  nockTradeJob,
} from "../../../../helpers";

const websiteUrl = config.websites.consumerSiteUrl;

describe("POST review requests", () => {
  let jobId: string;

  beforeEach(async () => {
    jobId = faker.string.uuid();
    await createCompanies(TEST_COMPANY_ID);
  });

  it("should send 401 and reject requests without a valid auth token", async () => {
    const response = await getTestRequest()
      .set("x-trade-company-id", TEST_COMPANY_ID)
      .post(`/jobs/${jobId}/review/requests`);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Missing authorization header",
      instance: `/jobs/${jobId}/review/requests`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should send 400 when job id is not a valid uuid", async () => {
    const response = await getTestRequest().post(
      `/jobs/invalid-job-id/review/requests`,
    );

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: 'params/jobId must match format "uuid"',
      instance: "/jobs/invalid-job-id/review/requests",
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  describe("when job id is valid and user is authenticated", () => {
    let request: TestRequest;

    beforeEach(() => {
      request = getTestRequestWithValidToken();
    });

    it("should return an error when job service returns an error", async () => {
      nockGetTradeError({ companyId: TEST_COMPANY_ID });
      nockTradeJob({ jobId });

      const response = await request.post(`/jobs/${jobId}/review/requests`);
      expect(response.status).toEqual(400);
      expect(response.body).toEqual({
        detail: "Invalid or unknown company ID",
        instance: `/jobs/${jobId}/review/requests`,
        status: 400,
        title: "Bad Request",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
      });
    });

    it.each([
      FormattedOpportunityStatus.CANCELLED,
      FormattedOpportunityStatus.REQUEST_REJECTED,
    ])(`should send 409, when job status is %s`, async (status) => {
      nockGetTradeSuccess({ companyId: TEST_COMPANY_ID });
      nockTradeJob({ jobId, status });
      const response = await request.post(`/jobs/${jobId}/review/requests`);

      expect(response.status).toEqual(409);
      expect(response.body).toEqual({
        detail: "Job is not in a state that can be reviewed",
        instance: `/jobs/${jobId}/review/requests`,
        status: 409,
        title: "Conflict",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/409",
      });
    });

    describe("when job status is valid", () => {
      let opportunityId: string;
      let sendSmartMessageSpy: jest.SpyInstance;
      let commReviewRequest: nock.Scope;
      let consumerId: string;
      let categoryId: number;
      let status: FormattedOpportunityStatus;

      beforeEach(() => {
        opportunityId = faker.string.uuid();
        consumerId = faker.string.uuid();
        categoryId = faker.number.int(3);
        status = FormattedOpportunityStatus.REQUEST_ACCEPTED;
        sendSmartMessageSpy = jest
          .spyOn(chatSDK, "sendSmartMessage")
          .mockResolvedValue(
            {} as Awaited<ReturnType<typeof chatSDK.sendSmartMessage>>,
          );

        nockGetTradeSuccess({ companyId: TEST_COMPANY_ID });
        nockTradeJob({ jobId, opportunityId, consumerId, categoryId, status });
      });

      describe("when no review requests are sent yet", () => {
        let response: Response;

        beforeEach(async () => {
          nockFindChannel(opportunityId);
          jest.spyOn(chatSDK, "getChannelSmartMessages").mockResolvedValue({
            group: {} as Record<SmartMessageGroup, string>,
            type: {} as Record<SmartMessageType, string>,
          } as Awaited<ReturnType<typeof chatSDK.getChannelSmartMessages>>);

          commReviewRequest = nockCommsReviewRequest({
            consumerId,
            type: SmartMessageType.REVIEW_REQUESTED,
            job: {
              id: jobId,
              categoryId,
            },
            trade: {
              companyId: TEST_COMPANY_ID,
              name: `Test company ${TEST_COMPANY_ID}`,
              uniqueName: `testcompany${TEST_COMPANY_ID}`,
            },
          });

          response = await request.post(`/jobs/${jobId}/review/requests`);
        });

        it("should return 200", async () => {
          expect(response.status).toEqual(200);
          expect(response.body).toEqual({ success: true });
        });

        it("should send correct smart message", async () => {
          const reviewUrl = `${websiteUrl}/give-feedback/trades/testcompany${TEST_COMPANY_ID}?categoryId=${categoryId}&jobId=${jobId}`;
          expect(sendSmartMessageSpy).toHaveBeenCalledTimes(1);
          expect(sendSmartMessageSpy).toHaveBeenCalledWith({
            logger: expect.any(Object),
            channelId: opportunityId,
            reviewId: "",
            senderId: TEST_COMPANY_ID,
            smartType: SmartMessageType.REVIEW_REQUESTED,
            text: `Trade requested a review of this job: ${reviewUrl}`,
            reviewRequest: {
              url: reviewUrl,
            },
          });
        });

        it("should make a valid request to comms service", async () => {
          expect(commReviewRequest.isDone()).toBeTruthy();
        });
      });

      describe("when an initial review request was sent", () => {
        let response: Response;

        beforeEach(async () => {
          jest.spyOn(chatSDK, "getChannelSmartMessages").mockResolvedValue({
            group: {
              [SmartMessageGroup.REVIEW]: "init-review-message",
            } as Record<SmartMessageGroup, string>,
            type: {
              [SmartMessageType.REVIEW_REQUESTED]: "init-review-message",
            } as Record<SmartMessageType, string>,
          } as Awaited<ReturnType<typeof chatSDK.getChannelSmartMessages>>);

          commReviewRequest = nockCommsReviewRequest({
            consumerId,
            type: SmartMessageType.REVIEW_REQUEST_REMINDER,
            job: {
              id: jobId,
              categoryId,
            },
            trade: {
              companyId: TEST_COMPANY_ID,
              name: `Test company ${TEST_COMPANY_ID}`,
              uniqueName: `testcompany${TEST_COMPANY_ID}`,
            },
          });
        });

        describe("and less than a day have passed since the init message was sent", () => {
          beforeEach(async () => {
            nockGetMessageById({
              messageId: "init-review-message",
              createdAt: new Date(),
            });
            response = await request.post(`/jobs/${jobId}/review/requests`);
          });

          it("should return 400", () => {
            expect(response.status).toEqual(400);
            expect(response.body).toEqual({
              detail:
                "Review reminder cannot be sent in less than a day after the initial review request",
              instance: `/jobs/${jobId}/review/requests`,
              status: 400,
              title: "Bad Request",
              type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
            });
          });

          it("should create jobReviewRequest with sent:false", async () => {
            const createdJobReviewRequest = await getJobReviewRequestByJobId(
              TEST_COMPANY_ID,
              jobId,
            );
            expect(createdJobReviewRequest?.sent).toBeFalsy();
          });
        });

        describe("and at least a day have passed since the init message was sent", () => {
          beforeEach(async () => {
            nockGetMessageById({
              messageId: "init-review-message",
              createdAt: new Date(Date.now() - 24 * 3600 * 1000),
            });
            response = await request.post(`/jobs/${jobId}/review/requests`);
          });

          it("should return 200", async () => {
            expect(response.status).toEqual(200);
            expect(response.body).toEqual({ success: true });
          });

          it("should send correct smart message", async () => {
            const reviewUrl = `${websiteUrl}/give-feedback/trades/testcompany${TEST_COMPANY_ID}?categoryId=${categoryId}&jobId=${jobId}`;
            expect(sendSmartMessageSpy).toHaveBeenCalledTimes(1);
            expect(sendSmartMessageSpy).toHaveBeenCalledWith({
              logger: expect.any(Object),
              channelId: opportunityId,
              reviewId: "",
              senderId: TEST_COMPANY_ID,
              smartType: SmartMessageType.REVIEW_REQUEST_REMINDER,
              text: "Trade reminded about review requested for this job",
              reviewRequest: {
                url: reviewUrl,
              },
            });
          });

          it("should make a valid request to comms service", async () => {
            expect(commReviewRequest.isDone()).toBeTruthy();
          });

          it("should create jobReviewRequest with sent:true", async () => {
            const createdJobReviewRequest = await getJobReviewRequestByJobId(
              TEST_COMPANY_ID,
              jobId,
            );
            expect(createdJobReviewRequest?.sent).toBeTruthy();
          });
        });
      });

      describe("when both initial request and reminder have been sent", () => {
        let response: Response;

        beforeEach(async () => {
          jest.spyOn(chatSDK, "getChannelSmartMessages").mockResolvedValue({
            group: {
              [SmartMessageGroup.REVIEW]: "init-review-message",
            } as Record<SmartMessageGroup, string>,
            type: {
              [SmartMessageType.REVIEW_REQUESTED]: "init-review-message",
              [SmartMessageType.REVIEW_REQUEST_REMINDER]: "reminder-message",
            } as Record<SmartMessageType, string>,
          } as Awaited<ReturnType<typeof chatSDK.getChannelSmartMessages>>);

          nockGetMessageById({
            messageId: "init-review-message",
            createdAt: new Date(),
          });

          response = await request.post(`/jobs/${jobId}/review/requests`);
        });

        it("should return 400", () => {
          expect(response.status).toEqual(400);
          expect(response.body).toEqual({
            detail: "Review reminder cannot be sent more than once",
            instance: `/jobs/${jobId}/review/requests`,
            status: 400,
            title: "Bad Request",
            type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
          });
        });

        it("should create jobReviewRequest with sent:true", async () => {
          const createdJobReviewRequest = await getJobReviewRequestByJobId(
            TEST_COMPANY_ID,
            jobId,
          );
          expect(createdJobReviewRequest?.sent).toBeTruthy();
        });
      });

      describe("when jobReviewRequest exists", () => {
        beforeEach(async () => {
          sendSmartMessageSpy = jest
            .spyOn(chatSDK, "sendSmartMessage")
            .mockResolvedValue(
              {} as Awaited<ReturnType<typeof chatSDK.sendSmartMessage>>,
            );

          commReviewRequest = nockCommsReviewRequest({
            consumerId,
            type: SmartMessageType.REVIEW_REQUEST_REMINDER,
            job: {
              id: jobId,
              categoryId,
            },
            trade: {
              companyId: TEST_COMPANY_ID,
              name: `Test company ${TEST_COMPANY_ID}`,
              uniqueName: `testcompany${TEST_COMPANY_ID}`,
            },
          });
        });

        it("should return 400 if reminder was sent", async () => {
          await createJobReviewRequest(TEST_COMPANY_ID, {
            jobId,
            sent: true,
          });

          const response = await request.post(`/jobs/${jobId}/review/requests`);
          expect(response.body).toEqual({
            detail: "Review reminder cannot be sent more than once",
            instance: `/jobs/${jobId}/review/requests`,
            status: 400,
            title: "Bad Request",
            type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
          });
        });

        it("should return 400 if reminder requested less then 24 hours", async () => {
          await createJobReviewRequest(TEST_COMPANY_ID, {
            jobId,
            sent: false,
            dateCreated: dayjs().subtract(10, "hours").toDate(),
          });

          const response = await request.post(`/jobs/${jobId}/review/requests`);
          expect(response.body).toEqual({
            detail:
              "Review reminder cannot be sent in less than a day after the initial review request",
            instance: `/jobs/${jobId}/review/requests`,
            status: 400,
            title: "Bad Request",
            type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
          });
        });

        it("should sent the reminder", async () => {
          await createJobReviewRequest(TEST_COMPANY_ID, {
            jobId,
            sent: false,
            dateCreated: dayjs().subtract(26, "days").toDate(),
          });
          const response = await request.post(`/jobs/${jobId}/review/requests`);

          expect(response.body).toEqual({ success: true });

          const jobReviewRequestDoc = await getJobReviewRequestByJobId(
            TEST_COMPANY_ID,
            jobId,
          );
          expect(jobReviewRequestDoc?.sent).toBeTruthy();

          const reviewUrl = `${websiteUrl}/give-feedback/trades/testcompany${TEST_COMPANY_ID}?categoryId=${categoryId}&jobId=${jobId}`;
          expect(sendSmartMessageSpy).toHaveBeenCalledWith({
            logger: expect.any(Object),
            channelId: opportunityId,
            reviewId: "",
            senderId: TEST_COMPANY_ID,
            smartType: SmartMessageType.REVIEW_REQUEST_REMINDER,
            text: "Trade reminded about review requested for this job",
            reviewRequest: {
              url: reviewUrl,
            },
          });
        });
      });
    });
  });
});
