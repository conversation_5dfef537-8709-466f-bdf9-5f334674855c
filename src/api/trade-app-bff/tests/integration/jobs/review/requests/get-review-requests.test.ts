import { chat<PERSON><PERSON> } from "@checkatrade/chat-sdk";
import { faker } from "@faker-js/faker";
import { Response } from "supertest";

import {
  SmartMessageGroup,
  SmartMessageType,
} from "../../../../../src/lib/api-common";
import { createJobReviewRequest } from "../../../../../src/services/firebase/firestore/job-review-request";
import {
  TEST_COMPANY_ID,
  TestRequest,
  getTestRequest,
  getTestRequestFromJWTPayload,
  mockValidTokenJWT,
  nockFindChannel,
  nockGetMessageById,
  nockGetTradeError,
  nockTradeJob,
} from "../../../../helpers";

describe("GET review requests", () => {
  let jobId: string;

  beforeEach(() => {
    jobId = faker.string.uuid();
  });

  it("should send 401 and reject requests without a valid auth token", async () => {
    const response = await getTestRequest()
      .set("x-trade-company-id", TEST_COMPANY_ID)
      .post(`/jobs/${jobId}/review/requests`);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Missing authorization header",
      instance: `/jobs/${jobId}/review/requests`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should send 400 when job id is not a valid uuid", async () => {
    const response = await getTestRequest().post(
      `/jobs/invalid-job-id/review/requests`,
    );

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: 'params/jobId must match format "uuid"',
      instance: "/jobs/invalid-job-id/review/requests",
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  describe("when job id is valid and user is authenticated", () => {
    let request: TestRequest;

    beforeEach(() => {
      request = getTestRequestFromJWTPayload(
        mockValidTokenJWT({ companyId: TEST_COMPANY_ID }),
        TEST_COMPANY_ID,
      );
    });

    it("should return an error when job service returns an error", async () => {
      nockGetTradeError({ companyId: TEST_COMPANY_ID });
      nockTradeJob({ jobId });

      const response = await request.post(`/jobs/${jobId}/review/requests`);
      expect(response.status).toEqual(400);
      expect(response.body).toEqual({
        detail: "Invalid or unknown company ID",
        instance: `/jobs/${jobId}/review/requests`,
        status: 400,
        title: "Bad Request",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
      });
    });

    describe("when job status is valid", () => {
      let opportunityId: string;

      beforeEach(() => {
        nockTradeJob({ jobId });

        jest.spyOn(chatSDK, "getChannelSmartMessages").mockResolvedValue({
          group: {},
          type: {},
        } as Awaited<ReturnType<typeof chatSDK.getChannelSmartMessages>>);
      });

      describe("when no review requests are sent yet", () => {
        let response: Response;

        beforeEach(async () => {
          opportunityId = "opportunity-id";
          nockFindChannel(opportunityId);
          response = await request.get(`/jobs/${jobId}/review/requests`);
        });

        it("should return 200 with empty list", async () => {
          expect(response.status).toEqual(200);
          expect(response.body).toEqual([]);
        });
      });

      describe("when only the initial review request was sent", () => {
        let response: Response;

        beforeEach(async () => {
          jest.spyOn(chatSDK, "getChannelSmartMessages").mockResolvedValue({
            group: {
              [SmartMessageGroup.REVIEW]: "init-review-message",
            },
            type: {
              [SmartMessageType.REVIEW_REQUESTED]: "init-review-message",
            },
          } as Awaited<ReturnType<typeof chatSDK.getChannelSmartMessages>>);

          nockGetMessageById({
            messageId: "init-review-message",
            createdAt: "2024-06-19T00:00:00.000Z",
          });

          response = await request.get(`/jobs/${jobId}/review/requests`);
        });

        it("should return 200, with correct list of items", () => {
          expect(response.status).toEqual(200);
          expect(response.body).toEqual([
            {
              createdAt: "2024-06-19T00:00:00.000Z",
              type: "REVIEW_REQUESTED",
            },
          ]);
        });
      });

      describe("when both initial request and reminder have been sent", () => {
        let response: Response;

        beforeEach(async () => {
          jest.spyOn(chatSDK, "getChannelSmartMessages").mockResolvedValue({
            group: {
              [SmartMessageGroup.REVIEW]: "init-review-message",
            },
            type: {
              [SmartMessageType.REVIEW_REQUESTED]: "init-review-message",
              [SmartMessageType.REVIEW_REQUEST_REMINDER]: "reminder-message",
            },
          } as Awaited<ReturnType<typeof chatSDK.getChannelSmartMessages>>);

          nockGetMessageById({
            messageId: "init-review-message",
            createdAt: "2024-06-17T00:00:00.000Z",
          });

          nockGetMessageById({
            messageId: "reminder-message",
            createdAt: "2024-06-19T00:00:00.000Z",
          });

          response = await request.get(`/jobs/${jobId}/review/requests`);
        });

        it("should return 200, with correct list of messages", () => {
          expect(response.status).toEqual(200);
          expect(response.body).toEqual([
            {
              createdAt: "2024-06-17T00:00:00.000Z",
              type: "REVIEW_REQUESTED",
            },
            {
              createdAt: "2024-06-17T00:00:00.000Z",
              type: "REVIEW_REQUEST_REMINDER",
            },
          ]);
        });
      });

      describe("when review request exists in the firestore", () => {
        let response: Response;

        it("should return only Requested type if reminder was not sent", async () => {
          const dateCreated = new Date("2024-06-17T00:00:00.000Z");
          await createJobReviewRequest(TEST_COMPANY_ID, {
            jobId,
            sent: false,
            dateCreated,
          });

          response = await request.get(`/jobs/${jobId}/review/requests`);
          expect(response.status).toEqual(200);
          expect(response.body).toEqual([
            {
              createdAt: dateCreated.toISOString(),
              type: "REVIEW_REQUESTED",
            },
          ]);
        });

        it("should return Requested and Reminder type if reminder was sent", async () => {
          const dateCreated = new Date("2024-06-17T00:00:00.000Z");
          await createJobReviewRequest(TEST_COMPANY_ID, {
            jobId,
            sent: true,
            dateCreated,
          });

          response = await request.get(`/jobs/${jobId}/review/requests`);
          expect(response.status).toEqual(200);
          expect(response.body).toEqual([
            {
              createdAt: dateCreated.toISOString(),
              type: "REVIEW_REQUESTED",
            },
            {
              createdAt: dateCreated.toISOString(),
              type: "REVIEW_REQUEST_REMINDER",
            },
          ]);
        });
      });
    });
  });
});
