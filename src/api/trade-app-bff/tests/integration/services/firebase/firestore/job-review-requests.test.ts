import { faker } from "@faker-js/faker";

import {
  createJobReviewRequest,
  getJobReviewRequestByJobId,
} from "../../../../../src/services/firebase/firestore/job-review-request";
import { JobReviewRequestDoc } from "../../../../../src/services/firebase/firestore/schemas/job-review-request";
import { createCompanies } from "../../../../helpers";

describe("jobReviewRequest firestore service", () => {
  describe("createJobReviewRequest", () => {
    let testCompanyId: number;

    beforeEach(async () => {
      testCompanyId = faker.number.int({ min: 1000, max: 900000 }); //generate a random company id for testing
      await createCompanies(testCompanyId);
    });

    it("should create a job review request", async () => {
      const jobId = faker.string.uuid();

      const jobReviewRequest: JobReviewRequestDoc = {
        jobId,
        sent: false,
      };

      const result = await createJobReviewRequest(
        testCompanyId,
        jobReviewRequest,
      );

      expect(result).toEqual({
        id: expect.any(String),
        jobId,
        dateCreated: expect.any(Object),
        sent: false,
      });
    });
  });

  describe("getJobReviewRequest", () => {
    let testCompanyId: number;
    let jobId: string;

    beforeEach(async () => {
      testCompanyId = faker.number.int({ min: 1000, max: 900000 }); //generate a random company id for testing
      jobId = faker.string.uuid();
      await createCompanies(testCompanyId);
    });

    it("should return a jobReviewRequest if exists", async () => {
      const jobReviewRequestDoc = await createJobReviewRequest(testCompanyId, {
        jobId,
        sent: false,
      });

      const jobReviewRequest = await getJobReviewRequestByJobId(
        testCompanyId,
        jobId,
      );
      expect(jobReviewRequest).toEqual({
        jobId,
        id: jobReviewRequestDoc.id,
        dateCreated: expect.any(Object),
        sent: false,
      });
    });
  });
});
