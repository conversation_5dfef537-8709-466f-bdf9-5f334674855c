import { faker } from "@faker-js/faker";
import dayjs from "dayjs";

import {
  createReviewRequest,
  getReviewRequest,
  getReviewRequestList,
} from "../../../../../src/services/firebase/firestore/review-request";
import { createCompanies } from "../../../../helpers";
import { mockFirestoreReviewRequest } from "../../../../helpers/test-review-request";

describe("getReviewRequestList firestore service", () => {
  let testCompanyId: number;

  beforeEach(async () => {
    testCompanyId = faker.number.int({ min: 1000, max: 900000 }); //generate a random company id for testing
    await createCompanies(testCompanyId);
    const threeMonthsAgo = dayjs().subtract(3, "month").toDate();
    const reviewRequest = mockFirestoreReviewRequest(threeMonthsAgo);

    await createReviewRequest(testCompanyId, reviewRequest);
    for (let i = 0; i < 10; i++) {
      const reviewRequest1 = mockFirestoreReviewRequest();
      await createReviewRequest(testCompanyId, reviewRequest1);
    }
  });

  afterEach(() => {
    //remove test company
  });
  it("should not return reviewRequests older than 3 months", async () => {
    const resp = await getReviewRequestList(testCompanyId, 20);
    expect(resp.data).toHaveLength(10);
  });

  it("should return pageSize number of reviewRequests", async () => {
    const pageSize = 5;
    const resp = await getReviewRequestList(testCompanyId, pageSize);
    expect(resp.data).toHaveLength(5);
  });

  it("lastVisible reviewRequest should be one of the returned reviewRequests", async () => {
    const pageSize = 5;
    const resp = await getReviewRequestList(testCompanyId, pageSize);

    expect(resp.data.map((req) => req.id)).toContain(resp.lastReviewRequestId);
  });

  it("should return correct reviewRequest object", async () => {
    const pageSize = 1;
    const resp = await getReviewRequestList(testCompanyId, pageSize);
    const reviewRequestId = resp.data[0].id ?? "";
    const reviewRequest = await getReviewRequest(
      testCompanyId,
      reviewRequestId,
    );
    expect(resp.data[0]).toEqual({
      id: reviewRequestId,
      fullName: reviewRequest?.fullName,
      dateCreated: reviewRequest?.dateCreated,
      email: reviewRequest?.email,
      phone: reviewRequest?.phone,
      sent: false,
    });
  });

  it("should return 2 pages of data", async () => {
    const PAGE_SIZE = 5;
    let count: number;

    //first page of data. 5 elements returned
    let resp = await getReviewRequestList(testCompanyId, PAGE_SIZE);
    count = resp.data.length;
    expect(count).toBe(PAGE_SIZE);
    const lastVisibleId = resp.lastReviewRequestId;

    //second page of data. 5 elements returned
    resp = await getReviewRequestList(testCompanyId, PAGE_SIZE, lastVisibleId);
    expect(resp.data.length).toBe(PAGE_SIZE);
    count += resp.data.length;

    //third page of data. 0 elements returned (no more data available)
    resp = await getReviewRequestList(
      testCompanyId,
      PAGE_SIZE,
      resp.lastReviewRequestId,
    );
    expect(resp.data.length).toBe(0); //no more data available
    expect(resp.lastReviewRequestId).toBe(undefined); //same lastVisible id
    expect(count).toBe(10);
  });
});
