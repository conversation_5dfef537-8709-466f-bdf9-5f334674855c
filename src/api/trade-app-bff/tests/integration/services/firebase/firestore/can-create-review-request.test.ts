import { faker } from "@faker-js/faker";
import dayjs from "dayjs";

import {
  canCreateReviewRequest,
  createReviewRequest,
} from "../../../../../src/services/firebase/firestore/review-request";
import { ReviewRequestDoc } from "../../../../../src/services/firebase/firestore/schemas/review-request";
import { createCompanies } from "../../../../helpers";
import { getFakeMobileNumber } from "../../../../helpers/test-mobile";
import { mockFirestoreReviewRequest } from "../../../../helpers/test-review-request";

describe("reviewRequest firestore service", () => {
  let testCompanyId: number;
  let oldReviewRequest: ReviewRequestDoc;
  let newReviewRequest: ReviewRequestDoc;

  beforeEach(async () => {
    testCompanyId = faker.number.int({ min: 1000, max: 900000 }); //generate a random company id for testing
    await createCompanies(testCompanyId);

    const oneWeekAgo = dayjs().subtract(1, "week").toDate();

    oldReviewRequest = mockFirestoreReviewRequest(oneWeekAgo);
    newReviewRequest = mockFirestoreReviewRequest();

    await createReviewRequest(testCompanyId, oldReviewRequest);
    await createReviewRequest(testCompanyId, newReviewRequest);
  });

  afterEach(() => {
    //remove test company
  });

  describe("can createReviewRequest", () => {
    it("should allow to crate review for new email and phone", async () => {
      const resp = await canCreateReviewRequest(testCompanyId, {
        email: faker.internet.email(),
        phone: getFakeMobileNumber(),
      });
      expect(resp).toBe(true);
    });

    it("should not allow to crate review for existing email", async () => {
      const resp = await canCreateReviewRequest(testCompanyId, {
        email: newReviewRequest.email,
      });
      expect(resp).toBe(false);
    });

    it("should not allow to crate review for existing phone", async () => {
      const resp = await canCreateReviewRequest(testCompanyId, {
        phone: newReviewRequest.phone,
      });
      expect(resp).toBe(false);
    });

    it("should not allow to crate review for existing phone or email", async () => {
      const resp = await canCreateReviewRequest(testCompanyId, {
        phone: newReviewRequest.phone,
        email: newReviewRequest.email,
      });
      expect(resp).toBe(false);
    });

    it("should allow to create review request if matching phone or email were requested 7 days ago", async () => {
      const resp = await canCreateReviewRequest(testCompanyId, {
        phone: oldReviewRequest.phone,
        email: oldReviewRequest.email,
      });
      expect(resp).toBe(true);
    });
  });
});
