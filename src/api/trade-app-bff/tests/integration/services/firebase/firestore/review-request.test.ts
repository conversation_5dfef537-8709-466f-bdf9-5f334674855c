import { faker } from "@faker-js/faker";

import {
  createReviewRequest,
  getReviewRequest,
  updateReviewRequest,
} from "../../../../../src/services/firebase/firestore/review-request";
import { createCompanies } from "../../../../helpers";
import { getFakeMobileNumber } from "../../../../helpers/test-mobile";
import { mockFirestoreReviewRequest } from "../../../../helpers/test-review-request";

describe("reviewRequest firestore service", () => {
  let testCompanyId: number;

  beforeEach(async () => {
    testCompanyId = faker.number.int({ min: 1000, max: 900000 }); //generate a random company id for testing
    await createCompanies(testCompanyId);
  });

  afterEach(() => {
    //remove test company
  });

  describe("createReviewRequest", () => {
    it("should create a review request", async () => {
      const reviewRequest = mockFirestoreReviewRequest();

      const { id } = await createReviewRequest(testCompanyId, reviewRequest);

      const reviewRequestDoc = await getReviewRequest(testCompanyId, id!);

      expect(reviewRequestDoc).toEqual({
        ...reviewRequest,
        dateCreated: expect.any(Object),
        id,
      });
    });

    it("email is optional", async () => {
      const reviewRequest = {
        fullName: faker.person.fullName(),
        phone: getFakeMobileNumber(),
        sent: false,
      };

      const { id } = await createReviewRequest(testCompanyId, reviewRequest);

      const reviewRequestDoc = await getReviewRequest(testCompanyId, id!);

      expect(reviewRequestDoc).toEqual({
        ...reviewRequest,
        dateCreated: expect.any(Object),
        id,
      });
    });

    it("phone is optional", async () => {
      const reviewRequest = {
        fullName: faker.person.fullName(),
        email: faker.internet.email(),
        sent: false,
      };

      const { id } = await createReviewRequest(testCompanyId, reviewRequest);

      const reviewRequestDoc = await getReviewRequest(testCompanyId, id!);

      expect(reviewRequestDoc).toEqual({
        ...reviewRequest,
        dateCreated: expect.any(Object),
        id,
      });
    });
  });

  describe("updateReviewRequest", () => {
    it("should update sent property", async () => {
      const reviewRequest = mockFirestoreReviewRequest();

      const { id } = await createReviewRequest(testCompanyId, reviewRequest);

      await updateReviewRequest(testCompanyId, id!, { sent: true });

      const reviewRequestDoc = await getReviewRequest(testCompanyId, id!);
      expect(reviewRequestDoc?.sent).toEqual(true);
    });
  });
});
