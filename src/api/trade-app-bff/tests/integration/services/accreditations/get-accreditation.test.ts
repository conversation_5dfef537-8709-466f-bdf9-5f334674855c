import { accreditations } from "../../../../src/services/accreditations";
import { createAccreditations } from "../../../helpers";

describe("getAccreditationById", () => {
  beforeAll(async () => {
    await createAccreditations();
  });

  it("should return undefined if accreditation does not exist", async () => {
    const accreditation = await accreditations.getAccreditationById(1234);
    expect(accreditation).toBeUndefined();
  });

  it("should return accreditation if it exists", async () => {
    const accreditation = await accreditations.getAccreditationById(20);

    expect(accreditation).toEqual({
      accreditationId: 20,
      canExpire: false,
      isDeleted: false,
      logoFilename: "logo20/path",
      name: "Test accreditation 20",
      requiresApproval: false,
    });
  });
});
