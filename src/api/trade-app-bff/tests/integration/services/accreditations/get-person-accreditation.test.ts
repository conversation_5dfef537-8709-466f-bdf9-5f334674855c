import { faker } from "@faker-js/faker";

import { accreditations } from "../../../../src/services/accreditations";
import {
  AccreditationMutationType,
  AccreditationPlatform,
  AccreditationStatusType,
} from "../../../../src/services/firebase/firestore/schemas/person-accreditations";
import { createPersonAccreditation } from "../../../helpers";

describe("getPersonAccreditation", () => {
  let companyId: number;
  let personId: string;

  beforeEach(async () => {
    companyId = faker.number.int({ min: 100, max: 99999 });
    personId = faker.string.uuid();

    await createPersonAccreditation(companyId, personId, 10);
    await createPersonAccreditation(companyId, personId, 20);
    await createPersonAccreditation(companyId, personId, 30);
  });

  it("should return undefined if accreditation does not exist", async () => {
    const companyId = 123;
    const personId = "123-123";
    const accreditationId = 20;
    const personAccreditation = await accreditations.getPersonAccreditation(
      companyId,
      personId,
      accreditationId,
    );

    expect(personAccreditation).toBeUndefined();
  });

  it("should return personAccreditation if it exists", async () => {
    const personAccreditation = await accreditations.getPersonAccreditation(
      companyId,
      personId,
      10,
    );

    expect(personAccreditation).toEqual({
      id: expect.any(String),
      accreditationId: 10,
      companyId,
      status: AccreditationStatusType.Pending,
      registrationNumber: "registration-number",
      history: [
        {
          platform: AccreditationPlatform.TradeApp,
          type: AccreditationMutationType.Submitted,
          updatedDate: expect.any(Date),
        },
      ],
      isDeleted: false,
      modifiedDate: expect.any(Date),
      personId,
    });
  });

  it("should throw if more than one personAccreditations found for the same person", async () => {
    await createPersonAccreditation(companyId, personId, 30); //duplicate entry

    await expect(
      accreditations.getPersonAccreditation(companyId, personId, 30),
    ).rejects.toThrow("More than one personAccreditation document found");
  });
});
