import { WorkCategoryMinimal } from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import { accreditations } from "../../../../src/services/accreditations";
import { createCategories } from "../../../helpers";

describe("GET requiredAccreditations", () => {
  let categoryId: number;
  let accreditationId: number;

  beforeEach(async () => {
    categoryId = faker.number.int({ min: 100, max: 99999 });
    accreditationId = faker.number.int({ min: 100, max: 99999 });

    await createCategories(categoryId, {
      name: "Test Category",
      subCategories: [
        {
          accreditationsNeeded: [
            {
              id: accreditationId,
              name: "Test Accreditation",
              deleted: false,
            },
          ],
          name: "Grass Cutting",
          primary: true,
          webCatId: 1211,
        },
      ],
    });
    const categoryId2 = faker.number.int({ min: 100, max: 99999 });
    await createCategories(categoryId2, {
      name: "Test Category",
      subCategories: [
        {
          accreditationsNeeded: [],
          name: "Grass Cutting",
          primary: true,
          webCatId: 1212,
        },
      ],
    });
  });

  it("should return list of matching accreditations", async () => {
    const workCategories: WorkCategoryMinimal = [
      {
        id: categoryId.toString(),
        subCategories: [
          {
            id: "1211",
          },
        ],
      },
    ];

    const resp = await accreditations.getRequiredAccreditations(workCategories);
    expect(resp).toHaveLength(1);
    expect(resp?.[0]).toEqual(accreditationId);
  });
});
