import { faker } from "@faker-js/faker";

import { accreditations } from "../../../../src/services/accreditations";
import {
  AccreditationMutationType,
  AccreditationPlatform,
  AccreditationStatusType,
} from "../../../../src/services/firebase/firestore/schemas/person-accreditations";
import { createPersonAccreditation } from "../../../helpers";

describe("getPersonAccreditation", () => {
  let companyId: number;
  let personId: string;

  beforeEach(async () => {
    companyId = faker.number.int({ min: 100, max: 99999 });
    personId = faker.string.uuid();

    await createPersonAccreditation(companyId, personId, 10);
    await createPersonAccreditation(companyId, personId, 20);
    await createPersonAccreditation(companyId, personId, 30, {
      isDeleted: false,
      modifiedDate: new Date(),
      expiryDate: null,
      approvedDate: null,
      status: AccreditationStatusType.Pending,
      registrationNumber: null,
      history: [
        {
          reason: null, //null can be set by AdvisorUI
          updatedBy: null, //null can be set by AdvisorUI
          platform: AccreditationPlatform.TradeApp,
          type: AccreditationMutationType.Submitted,
          updatedDate: new Date(),
        },
      ],
    });

    const personAccreditationToDelete =
      await accreditations.getPersonAccreditation(companyId, personId, 20);
    if (!personAccreditationToDelete) {
      throw new Error("Failed to create personAccreditation to delete"); // for jest runner
    }

    await accreditations.deletePersonAccreditation(personAccreditationToDelete);
  });

  it("should return array of personAccreditations", async () => {
    const personAccreditation = await accreditations.getPersonAccreditations(
      companyId,
      personId,
    );

    expect(personAccreditation).toHaveLength(3);
  });

  it("should return an empty array if personAccreditations don't exist", async () => {
    const nonExisingPersonId = faker.string.uuid();

    const personAccreditation = await accreditations.getPersonAccreditations(
      companyId,
      nonExisingPersonId,
    );

    expect(personAccreditation).toHaveLength(0);
  });
});
