import { faker } from "@faker-js/faker";

import { accreditations } from "../../../../src/services/accreditations";
import {
  AccreditationStatusType,
  PersonAccreditation,
} from "../../../../src/services/firebase/firestore/schemas/person-accreditations";

describe("createPersonAccreditation servcie", () => {
  it("creates a new person accreditation", async () => {
    const companyId = 555;
    const personId = faker.string.uuid();

    const personAccreditation: PersonAccreditation = {
      accreditationId: 20,
      personId,
      companyId,
      status: AccreditationStatusType.Pending,
      isDeleted: false,
      history: [],
    };

    await accreditations.setPersonAccreditation(personAccreditation);
  });
});
