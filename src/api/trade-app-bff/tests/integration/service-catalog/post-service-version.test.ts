import { Quantifier, StatusType } from "@checkatrade/service-catalog-types";
import { faker } from "@faker-js/faker";
import { afterEach } from "node:test";

import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";
import {
  mockConsumerSdkTrade,
  serviceCatalogSdkTradeMock,
} from "../../helpers/service-catalog-sdk.mock";

const serviceId = faker.string.uuid();
const companyId = 123;

const mockServiceVersionBody = {
  categoryId: 456,
  name: "Professional Plumbing Service Installation and Repair",
  description: faker.lorem.paragraph({
    min: 5,
    max: 10,
  }),
  priceInPence: 5000,
  quantifier: Quantifier.PerHour,
  homeownerPriceRelatedNotes: "Price excludes parts and materials",
  whatIsIncluded: ["Pipe inspection", "Installation", "Testing"],
  whatIsNotIncluded: ["Materials", "Additional repairs"],
  homeownerNotes: "Please ensure access to the property",
};

const mockCreatedServiceVersion = {
  id: faker.string.uuid(),
  serviceId,
  companyId,
  status: StatusType.Draft,
  categoryId: mockServiceVersionBody.categoryId,
  name: mockServiceVersionBody.name,
  description: mockServiceVersionBody.description,
  whatIsIncluded: mockServiceVersionBody.whatIsIncluded,
  whatIsNotIncluded: mockServiceVersionBody.whatIsNotIncluded,
  homeownerNotes: mockServiceVersionBody.homeownerNotes,
  lineItems: [
    {
      priceInPence: mockServiceVersionBody.priceInPence,
      unit: mockServiceVersionBody.quantifier,
      homeownerPriceRelatedNotes:
        mockServiceVersionBody.homeownerPriceRelatedNotes,
    },
  ],
};

describe("POST service version", () => {
  beforeEach(() => {
    mockConsumerSdkTrade();
    serviceCatalogSdkTradeMock.serviceVersions.createServiceVersion.mockResolvedValue(
      mockCreatedServiceVersion,
    );
    serviceCatalogSdkTradeMock.serviceVersions.updateServiceVersion.mockResolvedValue(
      mockCreatedServiceVersion,
    );
    serviceCatalogSdkTradeMock.serviceVersions.getServiceHistory.mockResolvedValue(
      {
        currentVersion: null,
      },
    );
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should return 200 with the created service version", async () => {
    const request = getTestRequestWithValidToken(companyId);

    const response = await request
      .post(`/service-catalog/service/${serviceId}/version`)
      .send(mockServiceVersionBody);

    expect(
      serviceCatalogSdkTradeMock.serviceVersions.getServiceHistory,
    ).toHaveBeenCalledWith(companyId.toString(), serviceId);
    expect(
      serviceCatalogSdkTradeMock.serviceVersions.createServiceVersion,
    ).toHaveBeenCalledWith(
      companyId.toString(),
      serviceId,
      mockServiceVersionBody,
    );

    expect(response.status).toEqual(200);
    expect(response.body).toMatchObject(mockCreatedServiceVersion);
  });

  it("should update the current service version if status is DRAFT or IN_REVIEW", async () => {
    // Arrange
    serviceCatalogSdkTradeMock.serviceVersions.getServiceHistory.mockResolvedValue(
      {
        currentVersion: {
          id: faker.string.uuid(),
          status: StatusType.Draft,
        },
      },
    );

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .post(`/service-catalog/service/${serviceId}/version`)
      .send(mockServiceVersionBody);

    // Assert
    expect(
      serviceCatalogSdkTradeMock.serviceVersions.updateServiceVersion,
    ).toHaveBeenCalledWith(
      companyId.toString(),
      serviceId,
      expect.any(String),
      expect.objectContaining({
        ...mockServiceVersionBody,
        status: StatusType.Draft,
      }),
    );
    expect(response.status).toEqual(200);
    expect(response.body).toMatchObject(mockCreatedServiceVersion);
  });

  it("should return a server error if error occurs during service version creation", async () => {
    // Arrange
    serviceCatalogSdkTradeMock.serviceVersions.createServiceVersion.mockImplementation(
      () => {
        throw new Error("Failed to create service version");
      },
    );
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .post(`/service-catalog/service/${serviceId}/version`)
      .send(mockServiceVersionBody);

    // Assert
    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/service-catalog/service/${serviceId}/version`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });

  it("should return 401 when token is invalid", async () => {
    // Arrange
    const badRequest = getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId }),
      companyId,
    );

    // Act
    const response = await badRequest
      .post(`/service-catalog/service/${serviceId}/version`)
      .send(mockServiceVersionBody);

    // Assert
    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/service-catalog/service/${serviceId}/version`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should return 400 when required fields are missing", async () => {
    // Arrange
    const request = getTestRequestWithValidToken(companyId);
    const invalidBody = {
      categoryId: 456,
      description:
        "This is a valid description that meets minimum length requirements but name is missing",
      whatIsIncluded: ["Pipe inspection"],
      whatIsNotIncluded: ["Materials"],
      priceInPence: 5000,
      quantifier: Quantifier.PerHour,
      homeownerPriceRelatedNotes: "Price excludes parts and materials",
    };

    // Act
    const response = await request
      .post(`/service-catalog/service/${serviceId}/version`)
      .send(invalidBody);

    // Assert
    expect(response.status).toEqual(400);
    expect(response.body.detail).toEqual(
      "body must have required property 'name', body/description must NOT have fewer than 100 characters",
    );
  });
});
