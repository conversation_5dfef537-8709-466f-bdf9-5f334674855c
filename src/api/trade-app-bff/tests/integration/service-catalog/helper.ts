import { StatusType } from "@checkatrade/service-catalog-types";
import { faker } from "@faker-js/faker";

export const mockServiceVersion = {
  id: faker.string.uuid() as `${string}-${string}-${string}-${string}-${string}`,
  companyId: faker.number.int({ min: 1000, max: 9999 }),
  serviceId:
    faker.string.uuid() as `${string}-${string}-${string}-${string}-${string}`,
  categoryId: faker.number.int({ min: 1000, max: 9999 }),
  name: faker.lorem.words(2),
  description: faker.lorem.sentence(),
  homeownerNotes: faker.lorem.sentence(),
  whatIsIncluded: [],
  whatIsNotIncluded: [],
  days: [],
  lineItems: [],
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  status: StatusType.PublishedActive,
};
