import { InternalServerError } from "@checkatrade/errors";
import { ActivityType, paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

import { getTestRequestWithValidToken } from "../../helpers";

const companyId = 337953;

const mockCounts = {
  [ActivityType.Expired]: 0,
  [ActivityType.Completed]: 0,
  [ActivityType.PaymentPending]: 1,
  [ActivityType.Refunded]: 0,
  [ActivityType.Authorized]: 0,
  [ActivityType.Cancelled]: 0,
  [ActivityType.Paid]: 0,
  [ActivityType.ChargebackReversed]: 0,
  [ActivityType.Chargeback]: 0,
  [ActivityType.Payout]: 1,
  [ActivityType.Topup]: 1,
};

const mockGetActivitiesResponse = {
  pagination: {
    page: 1,
    size: 10,
    total: 2,
  },
  data: [
    {
      entityId: faker.string.uuid(),
      paymentLinkId: `PL-${faker.string.uuid()}`,
      companyId: String(companyId),
      amount: 964,
      type: "PAYMENT_PENDING",
      consumerName: "Test Testerson",
      reference: "Boiler repair at AB12 3CD",
      description: "Custom reference number",
      date: "2024-09-11T14:46:43.567Z",
    },
    {
      entityId: faker.string.uuid(),
      paymentLinkId: `PL-${faker.string.uuid()}`,
      companyId: String(companyId),
      amount: 964,
      type: "PAYOUT",
      consumerName: null,
      reference: null,
      description: null,
      date: "2024-09-11T14:45:44.293Z",
    },
    {
      entityId: faker.string.uuid(),
      paymentLinkId: null,
      companyId: String(companyId),
      amount: 964,
      type: "TOPUP",
      consumerName: null,
      reference: null,
      description: "Custom reference number",
      date: "2024-09-11T14:45:44.293Z",
    },
  ],
  counts: mockCounts,
};

describe("GET payment activities", () => {
  const mockGetActivities = paymentSDK.trade.activities
    .getActivities as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with expected response", async () => {
    // Arrange
    mockGetActivities.mockResolvedValue(mockGetActivitiesResponse);
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request.get("/payments/activities");

    // Assert
    expect(response.status).toEqual(200);
    expect(response.body).toMatchObject(mockGetActivitiesResponse);
  });

  it("should paginate properly", async () => {
    // Arrange
    mockGetActivities.mockResolvedValue(mockGetActivitiesResponse);
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request.get(
      "/payments/activities?pageSize=50&pageNumber=2",
    );

    // Assert
    expect(response.status).toEqual(200);
    expect(paymentSDK.trade.activities.getActivities).toHaveBeenCalledWith(
      String(companyId),
      expect.objectContaining({ pageNumber: 2, pageSize: 50 }),
    );
  });

  it("throws error if something goes wrong with Payments SDK", async () => {
    // Arrange
    mockGetActivities.mockRejectedValue(new InternalServerError());
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request.get("/payments/activities");

    // Assert
    expect(response.status).toEqual(500);
    expect(response.body).toMatchObject({
      detail: "An unexpected error occurred",
      instance: "/payments/activities",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });
});
