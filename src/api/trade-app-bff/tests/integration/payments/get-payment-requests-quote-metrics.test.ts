import { ForbiddenError } from "@checkatrade/errors";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

import { getTestRequestWithValidToken } from "../../helpers";
import {
  mockQuotingSdkTrade,
  quotingSdkTradeMock,
} from "../../helpers/mocks/quoting-sdk.mock";

jest.mock("@checkatrade/payment-sdk");

const mockPaymentSdk = jest.mocked(paymentSDK);

describe("GET payment requests quote metrics", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should call the sdk with the correct params and return 200 with expected response", async () => {
    // Arrange
    const expectedResponse = {
      hasProcessedPayments: true,
      totalPaid: 300,
      totalDue: 1000,
      totalOutstanding: 700,
    };
    const quoteId = faker.string.alphanumeric(10);
    mockQuotingSdkTrade();
    quotingSdkTradeMock.getQuote.mockResolvedValue({ id: quoteId });
    const mockGetPaymentRequestMetricsForQuote = mockPaymentSdk.trade
      .paymentRequests.getPaymentRequestMetricsForQuote as jest.Mock;
    mockGetPaymentRequestMetricsForQuote.mockResolvedValue(expectedResponse);

    const companyId = 123;
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(`/payments/payment-requests/quote/${quoteId}/metrics`)
      .send();

    // Assert
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestMetricsForQuote,
    ).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestMetricsForQuote,
    ).toHaveBeenCalledWith(quoteId);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(expectedResponse);
  });

  it("should return 403 when the provided quoteId does not belong to the trade's company", async () => {
    // Arrange
    const expectedResponse = {
      hasProcessedPayments: true,
      totalPaid: 300,
      totalDue: 1000,
      totalOutstanding: 700,
    };
    const quoteId = faker.string.alphanumeric(10);
    mockQuotingSdkTrade();
    quotingSdkTradeMock.getQuote.mockRejectedValue(
      new ForbiddenError("You are not authorized to perform this action"),
    );

    const mockGetPaymentRequestMetricsForQuote = mockPaymentSdk.trade
      .paymentRequests.getPaymentRequestMetricsForQuote as jest.Mock;
    mockGetPaymentRequestMetricsForQuote.mockResolvedValue(expectedResponse);

    const companyId = 123;
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(`/payments/payment-requests/quote/${quoteId}/metrics`)
      .send();

    // Assert
    expect(response.status).toEqual(403);
    expect(response.body.detail).toBe(
      "You are not authorized to perform this action",
    );
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestMetricsForQuote,
    ).not.toHaveBeenCalledTimes(1);
  });
});
