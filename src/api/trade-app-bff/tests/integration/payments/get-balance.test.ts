import { paymentSDK } from "@checkatrade/payment-sdk";

import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";

const mockGetBalancePaymentResponse = {
  balance: {
    currency: "GBP",
    balance: 0,
    available: 0,
    reserved: 0,
    pending: 0,
  },
  outstandingBalance: 110,
};

describe("GET balance", () => {
  const companyId = 123;
  const mockGetBalancePayment = paymentSDK.trade.balance
    .getBalance as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with expected response", async () => {
    mockGetBalancePayment.mockResolvedValue(mockGetBalancePaymentResponse);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(`/payments/balance`);

    expect(response.body).toMatchObject(mockGetBalancePaymentResponse);
    expect(response.status).toEqual(200);
  });

  it("should return a server error if error occurs during core payment api call", async () => {
    mockGetBalancePayment.mockImplementation(() => {
      throw new Error("bad things happened");
    });

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(`/payments/balance`);

    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/payments/balance`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });

    expect(response.status).toEqual(500);
  });

  it("should return 401 when token is invalid", async () => {
    const companyId = 13;
    const badRequest = getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId }),
      companyId,
    );

    const response = await badRequest.get(`/payments/balance`);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/payments/balance`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
