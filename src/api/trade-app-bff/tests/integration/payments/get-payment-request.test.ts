import { PaymentRequestStatus, paymentSDK } from "@checkatrade/payment-sdk";
import { PaymentType } from "@checkatrade/payment-types";
import { faker } from "@faker-js/faker";
import dayjs from "dayjs";

import { searchApi } from "../../../src/lib/api-common";
import { getTestRequestWithValidToken } from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

export const mockSearchSdkTrade = () => {
  jest.spyOn(searchApi, "getCategoryName").mockResolvedValue("Tap repair");
};

const now = Date.now();
const companyId = 123456;

const TRADE_SPLIT = {
  amount: { currency: "GBP", value: 966 },
  type: "TRADER",
};

const CHECKATRADE_SPLIT = {
  amount: { currency: "GBP", value: 34 },
  type: "CHECKATRADE",
};

const mockPaymentRequestId = faker.string.uuid();

const mockPaymentApiRequestData = {
  amount: {
    currency: "GBP",
    value: 10000,
  },
  description: "Fix tap in AB1 2CD",
  dueDate: String(dayjs(now).add(5, "days").toISOString()),
  returnUrl: "www.checkatrade.com",
  reference: mockPaymentRequestId,
  jobId: faker.string.uuid(),
  companyId: companyId.toString(),
  consumerId: faker.string.uuid(),
  firstName: "Joe",
  lastName: "Doe",
  emailAddress: "<EMAIL>",
  splits: [TRADE_SPLIT, CHECKATRADE_SPLIT],
};

const mockMinResponseData = {
  id: mockPaymentRequestId,
  status: PaymentRequestStatus.PaymentPending,
  reference: mockPaymentRequestId,
  requestCreatedAt: new Date(),
  totalAmount: mockPaymentApiRequestData.amount.value,
  commissionAmount: CHECKATRADE_SPLIT.amount.value,
  tradeAmount: TRADE_SPLIT.amount.value,
  companyId: String(companyId),
  jobTitle: "12321 - PO8 2SJ",
  consumerName: "Some geezer",
  consumerEmail: "<EMAIL>",
  paymentLinkId: `PL${faker.string.alphanumeric({ casing: "upper", length: 17 })}`,
  paymentUrl: "www.adyen-payment-link.com",
  currency: "GBP",
  expiryDate: new Date(),
  dueDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockResponseData = {
  ...mockMinResponseData,
  jobId: "0191e811-e09d-7-2dcd-3b67316a91a45aea",
  opportunityId: faker.string.uuid(),
  consumerId: "0191e811-e09d-7-2dcd-3b67316a91a45aea",
  checkoutUrl: "www.checkout-link.com",
  description: "Boiler repair in AB12 3CD",
  jobReference: "12321 - PO8 2SJ",
  paymentType: PaymentType.PaymentLink,
};

const mockOffPlatformResponseData = {
  ...mockMinResponseData,
  paymentUrl: "www.adyen-payment-link.com",
  checkoutUrl: "www.checkout-link.com",
  description: "Boiler repair in AB12 3CD",
  paymentLinkId: `PL${faker.string.alphanumeric({ casing: "upper", length: 17 })}`,
  paymentType: PaymentType.PayByPhone,
};

describe("GET payment request", () => {
  const mockGetPaymentRequest = paymentSDK.trade.paymentRequest
    .getPaymentRequest as jest.Mock;
  let getJobSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    mockJobsSdkTrade();
    mockSearchSdkTrade();

    getJobSpy = jobsSdkTradeMock.getJob;
    getJobSpy.mockResolvedValue({ categoryId: "12321", postcode: "PO8 2SJ" });
  });

  it("should return 200 with expected response", async () => {
    // Arrange
    mockGetPaymentRequest.mockResolvedValue(mockResponseData);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(`/payments/payment-request/${mockResponseData.id}`)
      .send();

    // Assert
    expect(mockGetPaymentRequest).toHaveBeenCalledTimes(1);
    expect(response.body.jobReference).toEqual(mockResponseData.jobReference);
    expect(response.body.description).toEqual(mockResponseData.description);
    expect(response.body.paymentType).toEqual(PaymentType.PaymentLink);
    expect(response.status).toEqual(200);
  });

  it("should return 200 with expected response when min data returned", async () => {
    // Arrange
    mockGetPaymentRequest.mockResolvedValue(mockMinResponseData);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(`/payments/payment-request/${mockResponseData.id}`)
      .send();

    // Assert
    expect(mockGetPaymentRequest).toHaveBeenCalledTimes(1);
    expect(response.status).toEqual(200);
    expect(response.body.paymentType).toBeFalsy();
    expect(response.body.jobReference).toBeFalsy();
    expect(response.body.description).toBeFalsy();
    expect(response.body.jobId).toBeFalsy();
    expect(response.body.opportunityId).toBeFalsy();
    expect(response.body.consumerId).toBeFalsy();
    expect(response.body.checkoutUrl).toBeFalsy();
  });

  it("should use description for the job title if job data is missing", async () => {
    // Arrange
    mockGetPaymentRequest.mockResolvedValue(mockOffPlatformResponseData);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(`/payments/payment-request/${mockOffPlatformResponseData.id}`)
      .send();

    // Assert
    expect(mockGetPaymentRequest).toHaveBeenCalledTimes(1);
    expect(response.body.jobTitle).toEqual(
      mockOffPlatformResponseData.description,
    );
    expect(response.body.paymentType).toEqual(PaymentType.PayByPhone);
    expect(response.status).toEqual(200);
  });

  it("should return server error if paymentRequestData is missing", async () => {
    // Arrange
    mockGetPaymentRequest.mockResolvedValue(null);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(`/payments/payment-request/invalid-id`)
      .send();

    // Assert
    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/payments/payment-request/invalid-id`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });
});
