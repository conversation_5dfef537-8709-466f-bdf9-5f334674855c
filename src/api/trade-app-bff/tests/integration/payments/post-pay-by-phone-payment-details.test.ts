import { paymentSDK } from "@checkatrade/payment-sdk";

import { getTestRequestWithValidToken } from "../../helpers";

describe("POST pay-by-phone-payment-details", () => {
  const mockPaymentSDK = paymentSDK.trade.payByPhone
    .postPaymentDetails as jest.Mock;

  it("should return a 200 when the request is successful", async () => {
    const adyenPaymentDetailsRequest = {
      details: {
        MD: "mdValue",
        PaRes: "paResValue",
      },
      paymentData: "Ab02b4c0!BQABAgCJN1wRZuGJmq8dMncmypvknj9s7l5Tj...",
    };

    const adyenPaymentDetailsResponse = {
      pspReference: "8515232733321252",
      resultCode: "Authorised",
      additionalData: {
        liabilityShift: "true",
        refusalReasonRaw: "AUTHORISED",
      },
    };

    mockPaymentSDK.mockResolvedValue({
      vendor: "adyen",
      vendorData: adyenPaymentDetailsResponse,
    });

    const result = await getTestRequestWithValidToken()
      .post("/payments/pay-by-phone/payment-details")
      .send({
        vendor: "adyen",
        vendorData: adyenPaymentDetailsRequest,
      });

    expect(result.status).toBe(200);
    expect(result.body).toEqual({
      vendor: "adyen",
      vendorData: adyenPaymentDetailsResponse,
    });
  });
});
