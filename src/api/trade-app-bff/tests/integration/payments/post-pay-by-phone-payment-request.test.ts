import { authTrade } from "@checkatrade/auth-trade";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { SplitType } from "@checkatrade/payment-types";
import { faker } from "@faker-js/faker/locale/en_GB";
import { Static } from "@sinclair/typebox";

import { PostCreatePayByPhonePaymentRequestBodySchema } from "../../../src/controllers/payments/schemas/PostCreatePayByPhonePaymentRequest.types";
import * as jobReferenceUtils from "../../../src/controllers/payments/utils/get-job-reference";
import { searchApi } from "../../../src/lib/api-common";
import { getTestRequestWithValidToken } from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

jest.mock("@checkatrade/auth-trade");
jest.mock("../../../src/controllers/payments/utils/get-job-reference");

const description = "Fix tap in AB1 2CD";

const MOCK_JOB = {
  categoryId: "12345",
  description,
  postcode: faker.location.zipCode(),
};

const MOCK_PAYMENT_REQUEST = {
  amount: { currency: "GBP", value: 10000 },
  description: "Test payment",
  dueDate: new Date().toISOString(),
  companyId: faker.string.uuid(),
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  emailAddress: faker.internet.email(),
  splits: [
    { amount: { currency: "GBP", value: 9000 }, type: SplitType.Trader },
    { amount: { currency: "GBP", value: 1000 }, type: SplitType.Checkatrade },
  ],
};

const MOCK_PAYMENT_CHECKOUT_DATA: Static<
  typeof PostCreatePayByPhonePaymentRequestBodySchema
>["vendorData"] = {
  amount: { currency: "GBP", value: 10000 },
  paymentMethod: {
    type: "scheme",
    holderName: faker.person.fullName(),
    encryptedCardNumber: "test",
    encryptedSecurityCode: "test",
    encryptedExpiryMonth: "test",
    encryptedExpiryYear: "test",
    brand: "visa",
  },
  merchantAccount: "CheckatradeCOM",
  returnUrl: "www.checkatrade.com",
  reference: faker.string.uuid(),
};

const mockResponse = {
  vendor: "adyen",
  vendorData: {
    action: {
      type: "redirect",
      url: "https://ruddy-dwell.co/",
    },
    resultCode: "Authorised",
  },
};

describe("POST pay-by-phone-payment-request", () => {
  let getJobsSpy: jest.SpyInstance;
  const mockPostCreatePaymentRequest = paymentSDK.trade.payByPhone
    .postCreatePaymentRequest as jest.Mock;
  const mockAuthTrade = authTrade as jest.Mock;
  const mockGetJobReference = jest.spyOn(jobReferenceUtils, "getJobReference");

  beforeEach(() => {
    jest.clearAllMocks();
    mockJobsSdkTrade();
    getJobsSpy = jobsSdkTradeMock.getJob;
    getJobsSpy.mockResolvedValue(MOCK_JOB);

    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue("Job Category Name");

    mockAuthTrade.mockResolvedValue({ companyId: "123", token: "test-token" });
    mockGetJobReference.mockResolvedValue("Test Job Reference");
  });

  it("should return 201 when request is successful", async () => {
    mockPostCreatePaymentRequest.mockResolvedValue(mockResponse);

    const request = getTestRequestWithValidToken();
    const payload: Static<typeof PostCreatePayByPhonePaymentRequestBodySchema> =
      {
        paymentRequest: MOCK_PAYMENT_REQUEST,
        vendorData: MOCK_PAYMENT_CHECKOUT_DATA,
        vendor: "adyen",
      };

    const result = await request
      .post("/payments/pay-by-phone/payment-request")
      .send(payload);

    expect(result.status).toBe(201);
    expect(result.body).toEqual(mockResponse);
  });

  it("should contain OFF_PLATFORM job source when jobId is not provided", async () => {
    mockPostCreatePaymentRequest.mockResolvedValue(mockResponse);

    const request = getTestRequestWithValidToken();

    const payload: Static<typeof PostCreatePayByPhonePaymentRequestBodySchema> =
      {
        paymentRequest: MOCK_PAYMENT_REQUEST,
        vendorData: MOCK_PAYMENT_CHECKOUT_DATA,
        vendor: "adyen",
      };

    await request.post("/payments/pay-by-phone/payment-request").send(payload);

    expect(mockPostCreatePaymentRequest).toHaveBeenCalledWith(
      expect.objectContaining({
        paymentRequest: expect.objectContaining({ jobSource: "OFF_PLATFORM" }),
      }),
    );
  });

  it("responds with 500 if there is an error during payment request creation", async () => {
    mockPostCreatePaymentRequest.mockImplementation(() => {
      throw new Error("Failed to create payment request");
    });

    const request = getTestRequestWithValidToken();
    const payload: Static<typeof PostCreatePayByPhonePaymentRequestBodySchema> =
      {
        paymentRequest: MOCK_PAYMENT_REQUEST,
        vendorData: MOCK_PAYMENT_CHECKOUT_DATA,
        vendor: "adyen",
      };

    const result = await request
      .post("/payments/pay-by-phone/payment-request")
      .send(payload);

    expect(result.status).toBe(500);
    expect(result.body).toEqual({
      detail: "An unexpected error occurred",
      instance: "/payments/pay-by-phone/payment-request",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });
});
