import {
  JobSource,
  PaymentRequestStatus,
  paymentSDK,
} from "@checkatrade/payment-sdk";
import { SplitType } from "@checkatrade/payment-types";
import { faker } from "@faker-js/faker/locale/en_GB";
import { PubSub } from "@google-cloud/pubsub";
import { Static } from "@sinclair/typebox";
import dayjs from "dayjs";

import { PostCreatePaymentRequestRequestOffPlatformJobSchema } from "../../../src/controllers/payments/schemas/PostCreatePaymentRequestOffPlatformJob.types";
import { searchApi } from "../../../src/lib/api-common";
import { createCompany, getTestRequestWithValidToken } from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

const TEST_COMPANY_ID = faker.number.int({ min: 1000, max: 900000 }); //
const now = Date.now();
const channelId = faker.string.uuid();
const description = "Fix tap in AB1 2CD";

const TRADE_SPLIT = {
  amount: { currency: "GBP", value: 966 },
  type: SplitType.Trader,
};

const CHECKATRADE_SPLIT = {
  amount: { currency: "GBP", value: 34 },
  type: SplitType.Checkatrade,
};

const MOCK_JOB = {
  categoryId: "12345",
  description,
  postcode: faker.location.zipCode(),
};

const MOCK_PAYMENT_REQUEST = {
  amount: { currency: "GBP", value: 10000 },
  description,
  reference: faker.string.uuid(),
  dueDate: String(dayjs(now).add(5, "days").toISOString()),
  returnUrl: "www.checkatrade.com",
  jobId: faker.string.uuid(),
  jobReference: `Job Category Name - ${MOCK_JOB.postcode}`,
  companyId: TEST_COMPANY_ID.toString(),
  consumerId: faker.string.uuid(),
  opportunityId: channelId,
  firstName: "Joe",
  lastName: "Doe",
  emailAddress: "<EMAIL>",
  splits: [TRADE_SPLIT, CHECKATRADE_SPLIT],
};

const MOCK_CONSUMER = {
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  phoneNumber: faker.phone.number(),
  emailAddress: faker.internet.email(),
};

const CREATE_PAYMENT_REQUEST_RESPONSE = {
  link: {
    id: faker.string.uuid(),
    status: "PAYMENT_PENDING",
    url: "www.adyen-payment-link.com",
  },
  paymentRequest: {
    currency: "GBP",
    totalAmount: MOCK_PAYMENT_REQUEST.amount.value,
    tradeAmount: TRADE_SPLIT.amount.value,
    commissionAmount: CHECKATRADE_SPLIT.amount.value,
    companyId: String(TEST_COMPANY_ID),
    jobId: faker.string.uuid(),
    jobReference: `Job Category Name - ${MOCK_JOB.postcode}`,
    consumerId: faker.string.uuid(),
    opportunityId: channelId,
    consumerName: "test",
    consumerEmail: "<EMAIL>",
    paymentUrl: "www.adyen-payment-link.com",
    reference: "Boiler repair in AB12 3CD",
    paymentLinkId: `PL${faker.string.alphanumeric({ casing: "upper", length: 17 })}`,
    status: PaymentRequestStatus.PaymentPending,
    expiryDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    dueDate: new Date(),
    reason: faker.string.alphanumeric(10),
  },
  paymentId: faker.string.uuid(),
};

const TOKEN_DATA = {
  job: MOCK_JOB,
  company: {
    companyId: TEST_COMPANY_ID,
    companyName: `Company ${TEST_COMPANY_ID}`,
  },
  consumer: {
    ...MOCK_CONSUMER,
    emailAddress: MOCK_PAYMENT_REQUEST.emailAddress,
  },
};

describe("POST post-payment-request-off-platform-job", () => {
  const mockCreatePaymentRequest = paymentSDK.trade.paymentRequest
    .postCreatePaymentRequest as jest.Mock;

  let getJobsSpy: jest.SpyInstance;

  beforeEach(async () => {
    jest.clearAllMocks();
    await createCompany(TEST_COMPANY_ID);

    mockJobsSdkTrade();
    getJobsSpy = jobsSdkTradeMock.getJob;
    getJobsSpy.mockResolvedValue(MOCK_JOB);

    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue("Job Category Name");
  });

  it("responds with 201 if request is successful", async () => {
    // Arrange
    mockCreatePaymentRequest.mockResolvedValue(CREATE_PAYMENT_REQUEST_RESPONSE);
    const publishMessage = new PubSub().topic("12345")
      .publishMessage as jest.Mock;

    const request = getTestRequestWithValidToken(TEST_COMPANY_ID);
    const payload: Static<
      typeof PostCreatePaymentRequestRequestOffPlatformJobSchema
    > = {
      paymentRequest: MOCK_PAYMENT_REQUEST,
      job: MOCK_JOB,
      consumer: MOCK_CONSUMER,
    };

    // Act
    const result = await request
      .post("/payments/payment-request/off-platform-job")
      .send(payload);

    // Assert
    expect(mockCreatePaymentRequest).toHaveBeenCalledWith({
      ...MOCK_PAYMENT_REQUEST,
      jobSource: JobSource.OffPlatform,
      tokenData: TOKEN_DATA,
    });
    expect(publishMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.any(Buffer),
        attributes: {
          eventType: "payment-for-off-platform-job-created",
          schemaVersion: "1.0",
          correlationId: expect.any(String),
          timestamp: expect.any(String),
        },
      }),
    );
    expect(result.status).toBe(201);
  });

  it.each(["id", "url"] as const)(
    "responds with 500 if link is missing %s in response",
    async (missingAttribute) => {
      // Arrange
      const generateFailedResponse = () => {
        const base = { ...CREATE_PAYMENT_REQUEST_RESPONSE };
        delete base.link[missingAttribute];
        return base;
      };
      mockCreatePaymentRequest.mockResolvedValue(generateFailedResponse());

      const request = getTestRequestWithValidToken(TEST_COMPANY_ID);
      const payload: Static<
        typeof PostCreatePaymentRequestRequestOffPlatformJobSchema
      > = {
        paymentRequest: MOCK_PAYMENT_REQUEST,
        job: MOCK_JOB,
        consumer: MOCK_CONSUMER,
      };

      // Act
      const result = await request
        .post("/payments/payment-request/off-platform-job")
        .send(payload);

      // Assert
      expect(mockCreatePaymentRequest).toHaveBeenCalledWith({
        ...MOCK_PAYMENT_REQUEST,
        jobSource: JobSource.OffPlatform,
        tokenData: TOKEN_DATA,
      });
      expect(result.status).toBe(500);
    },
  );
});
