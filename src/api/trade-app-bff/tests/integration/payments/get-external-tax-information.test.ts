import { paymentSDK } from "@checkatrade/payment-sdk";

import { getTestRequestWithValidToken } from "../../helpers";

const mockResponseData = {
  traderType: 1,
  taxIdentifiers: {
    vatNumber: "GB*********",
    companyRegistrationNumber: "*********",
  },
};

describe("GET external tax information", () => {
  const m = paymentSDK.trade.onboarding.getExternalTaxInformation as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with expected response", async () => {
    // Arrange
    const companyId = 123456;
    m.mockResolvedValue(mockResponseData);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request
      .get("/payments/external-tax-information")
      .send();

    // Assert
    expect(m).toHaveBeenCalledTimes(1);
    expect(response.status).toEqual(200);
    expect(response.body).toMatchObject(mockResponseData);
  });

  it("should return 500 if there is an issue with fetching profile data", async () => {
    // Arrange
    const companyId = 1111111;
    const request = getTestRequestWithValidToken(companyId);
    m.mockImplementation(() => {
      throw new Error("Company not found");
    });
    // Act

    const response = await request
      .get("/payments/external-tax-information")
      .send();

    // Assert
    expect(response.status).toEqual(500);
    expect(response.body).toMatchObject({
      detail: "An unexpected error occurred",
      instance: "/payments/external-tax-information",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });
});
