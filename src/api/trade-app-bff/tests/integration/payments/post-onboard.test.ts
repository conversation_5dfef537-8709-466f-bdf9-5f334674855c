import {
  OnboardingStatus,
  OnboardingType,
  paymentSDK,
} from "@checkatrade/payment-sdk";

import ContentAPI from "../../../src/controllers/payments/contentapi";
import { PersonType } from "../../../src/controllers/payments/schemas/Shared.types";
import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";

describe("POST onboard", () => {
  const companyId = 123;
  const primaryCategoryId = 1;

  const companyName = "Test Company Ltd";
  const companyAddress = {
    postcode: "12345",
    city: "Test City",
    town: "Test Town",
    county: "Test County",
    country: "Test Country",
    street: "123 Test Street",
  };
  const traderType = 1;
  const vatNum = "*********";
  const phoneNumbers = ["07*********", "07987654321"];
  const emails = ["<EMAIL>", "<EMAIL>"];

  const hostedOnboardingUrl = "https://example.com";

  describe("Valid token", () => {
    const mockOnboardSoleAndOrganisation = paymentSDK.trade.onboarding
      .onboardSoleAndOrganisation as jest.Mock;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should return 200 with expected response", async () => {
      jest.spyOn(ContentAPI, "getProfileData").mockResolvedValue({
        payload: {
          core: {
            ltdCompanyRegisteredName: companyName,
            name: "Bob Builder",
            personalPhoneNumbers: phoneNumbers,
            personalEmails: emails,
            vatRegistrationNo: vatNum,
            companyPrimaryPostalAddress: companyAddress,
            traderType,
            contacts: [],
          },
          profile: {
            businessOwnership: [
              {
                firstName: "Bob",
                lastName: "Builder",
                birthdate: new Date("1990-01-01").toISOString(),
                personTypeId: PersonType.PrimaryOwner,
                inactive: false,
                isDeleted: false,
              },
              {
                firstName: "John",
                lastName: "Wick",
                birthdate: new Date("1988-01-01").toISOString(),
                personTypeId: PersonType.PrimaryOwner,
                inactive: false,
                isDeleted: false,
              },
              {
                firstName: "John",
                lastName: "McClane",
                birthdate: new Date("1988-01-01").toISOString(),
                personTypeId: PersonType.SecondaryOwner,
                inactive: false,
                isDeleted: false,
              },
            ],
          },
        },
      });

      const onboardResponseMock = {
        hostedOnboardingUrl,
        soleProprietorshipId: "123",
        legalEntityId: "123",
        accountHolderId: "123",
        balanceAccountId: "123",
        businessLineId: "123",
        storeId: "123",
        paymentMethods: [],
        status: OnboardingStatus.Enrolled,
        type: OnboardingType.SoleProprietorship,
      };

      mockOnboardSoleAndOrganisation.mockResolvedValueOnce(onboardResponseMock);

      const request = getTestRequestWithValidToken(companyId);
      const response = await request
        .post(`/payments/onboard`)
        .query({ clientType: "WEB" })
        .send({ companyId, primaryCategoryId });

      expect(mockOnboardSoleAndOrganisation).toHaveBeenCalledTimes(1);
      expect(mockOnboardSoleAndOrganisation).toHaveBeenCalledWith({
        companyId: companyId.toString(),
        clientType: "WEB",
        legalName: "Test Company Ltd",
        phoneNumber: "+************",
        registeredAddress: {
          city: "Test City",
          country: "GB",
          postalCode: "12345",
          street: "123 Test Street",
        },
        type: "organisation",
        vatNumber: "GB*********",
        countryOfGoverningLaw: "GB",
        primaryCategoryId: 1,
        webAddress: "https://checkatrade.com",
      });

      expect(response.body).toEqual({
        hostedOnboardingUrl: "https://example.com",
        legalName: "Test Company Ltd",
        phoneNumber: "+************",
        registeredAddress: expect.objectContaining({
          city: "Test City",
          country: "GB",
          postalCode: "12345",
          street: "123 Test Street",
        }),
        traderType: 1,
        type: "organisation",
        vatNumber: "GB*********",
      });

      expect(response.status).toEqual(200);
    });

    it("should return 200 with expected response with minimum", async () => {
      jest.spyOn(ContentAPI, "getProfileData").mockResolvedValue({
        payload: {
          core: {
            name: companyName,
            personalPhoneNumbers: [],
            personalEmails: [],
            companyPrimaryPostalAddress: companyAddress,
            traderType,
            contacts: [],
          },
          profile: {
            businessOwnership: [
              {
                firstName: "Bob",
                lastName: "Builder",
                birthdate: new Date("1990-01-01").toISOString(),
                personTypeId: PersonType.PrimaryOwner,
                inactive: false,
                isDeleted: false,
              },
              {
                firstName: "John",
                lastName: "Wick",
                birthdate: new Date("1988-01-01").toISOString(),
                personTypeId: PersonType.PrimaryOwner,
                inactive: false,
                isDeleted: false,
              },
              {
                firstName: "John",
                lastName: "McClane",
                birthdate: new Date("1988-01-01").toISOString(),
                personTypeId: PersonType.SecondaryOwner,
                inactive: false,
                isDeleted: false,
              },
            ],
          },
        },
      });

      const onboardResponseMock = {
        hostedOnboardingUrl,
        soleProprietorshipId: "123",
        legalEntityId: "123",
        accountHolderId: "123",
        balanceAccountId: "123",
        businessLineId: "123",
        storeId: "123",
        paymentMethods: [],
        status: OnboardingStatus.Enrolled,
        type: OnboardingType.SoleProprietorship,
      };

      mockOnboardSoleAndOrganisation.mockResolvedValueOnce(onboardResponseMock);

      const request = getTestRequestWithValidToken(companyId);
      const response = await request
        .post(`/payments/onboard`)
        .query({ clientType: "WEB" })
        .send({ companyId, primaryCategoryId });

      expect(mockOnboardSoleAndOrganisation).toHaveBeenCalledTimes(1);
      expect(mockOnboardSoleAndOrganisation).toHaveBeenCalledWith({
        companyId: companyId.toString(),
        clientType: "WEB",
        legalName: "Test Company Ltd",
        phoneNumber: "",
        registeredAddress: {
          street: companyAddress.street,
          city: companyAddress.city,
          postalCode: companyAddress.postcode,
          country: "GB",
        },
        type: "organisation",
        countryOfGoverningLaw: "GB",
        primaryCategoryId: 1,
        webAddress: "https://checkatrade.com",
      });

      expect(response.body).toEqual({
        hostedOnboardingUrl: "https://example.com",
        legalName: "Test Company Ltd",
        phoneNumber: "",
        registeredAddress: expect.objectContaining({
          street: companyAddress.street,
          city: companyAddress.city,
          postalCode: companyAddress.postcode,
          country: "GB",
        }),
        traderType: 1,
        type: "organisation",
      });

      expect(response.status).toEqual(200);
    });

    it("should return a server error if error occurs during profile fetching", async () => {
      jest.spyOn(ContentAPI, "getProfileData").mockImplementation(() => {
        throw new Error("error");
      });

      const request = getTestRequestWithValidToken(companyId);
      const response = await request
        .post(`/payments/onboard`)
        .query({ clientType: "WEB" })
        .send({ companyId, primaryCategoryId });

      expect(response.body).toEqual({
        detail: "An unexpected error occurred",
        instance: `/payments/onboard?clientType=WEB`,
        status: 500,
        title: "Internal Server Error",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
      });

      expect(response.status).toEqual(500);
    });
  });

  describe("Invalid token", () => {
    it("should return 401", async () => {
      const badRequest = getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId }),
        companyId,
      );

      const response = await badRequest
        .post(`/payments/onboard`)
        .query({ clientType: "WEB" })
        .send({ companyId, primaryCategoryId });

      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        detail: "Invalid auth token",
        instance: `/payments/onboard?clientType=WEB`,
        status: 401,
        title: "Unauthorized",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      });
    });
  });
});
