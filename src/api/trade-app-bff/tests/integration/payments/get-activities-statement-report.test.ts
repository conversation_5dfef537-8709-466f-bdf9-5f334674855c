import { InternalServerError } from "@checkatrade/errors";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { ActivityStatementType } from "@checkatrade/payment-types";
import { faker } from "@faker-js/faker";

import { config } from "../../../src/controllers/payments/config";
import { getTestRequestWithValidToken } from "../../helpers";

const companyId = faker.number.int({ min: 10000, max: 99999 });
const mockGetActivitiesStatementReportResponse = {
  pagination: {
    page: 1,
    size: 10,
    total: 2,
  },
  data: [
    {
      id: faker.string.uuid(),
      amount: 1000,
      type: ActivityStatementType.Topup,
      status: "PAID",
      paymentType: null,
      paymentSource: null,
      consumerName: null,
      consumerEmail: null,
      reference: null,
      description: "A description",
      paidAt: new Date(2024, 10, 5),
      createdAt: new Date(2024, 10, 4),
    },
  ],
};
describe("GET payment activities statement report", () => {
  const mockGetActivitiesStatementReport = paymentSDK.trade.activities
    .getActivitiesStatement as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    config.reportRecordsPerPage = 2;
  });

  it("should return 200 with expected response", async () => {
    // Arrange
    mockGetActivitiesStatementReport.mockResolvedValue(
      mockGetActivitiesStatementReportResponse,
    );
    const request = getTestRequestWithValidToken(companyId);
    const fromDate = faker.date.recent().toISOString();
    const toDate = new Date().toISOString();

    // Act
    const response = await request.get(
      `/payments/report/activities-statement?fromDate=${fromDate}&toDate=${toDate}`,
    );

    // Assert
    expect(response.status).toEqual(200);
    expect(response.headers["content-type"]).toEqual("text/csv");
    expect(response.headers["content-disposition"]).toEqual(
      `attachment; filename="checkatrade-payments-${fromDate}-${toDate}.csv"`,
    );
    expect(response.text).toContain(
      "Payment Requested Date,Paid At,Customer Name,Customer Email,Type,Payment Type,Status,Amount (GBP),Id,Job Reference,Description",
    );
  });

  it("should handle multiple pages of data and include the header only on the first page", async () => {
    // Arrange
    const generateActivitiesStatementReportResponse = () => ({
      id: faker.string.uuid(),
      amount: 1000,
      type: ActivityStatementType.Topup,
      status: "PAID",
      paymentType: null,
      paymentSource: null,
      consumerName: null,
      consumerEmail: null,
      reference: null,
      description: "A description",
      paidAt: new Date(2024, 10, 5),
      createdAt: new Date(2024, 10, 4),
    });

    mockGetActivitiesStatementReport.mockResolvedValueOnce({
      data: Array.from(
        { length: 2 },
        generateActivitiesStatementReportResponse,
      ),
      pagination: {
        page: 1,
        size: 2,
        total: 3,
      },
    });
    mockGetActivitiesStatementReport.mockResolvedValueOnce({
      data: Array.from(
        { length: 1 },
        generateActivitiesStatementReportResponse,
      ),
      pagination: {
        page: 2,
        size: 1,
        total: 3,
      },
    });

    const request = getTestRequestWithValidToken(companyId);
    const fromDate = faker.date.recent().toISOString();
    const toDate = new Date().toISOString();

    // Act
    const response = await request.get(
      `/payments/report/activities-statement?fromDate=${fromDate}&toDate=${toDate}`,
    );

    // Assert
    expect(response.status).toEqual(200);
    expect(response.headers["content-type"]).toEqual("text/csv");
    expect(mockGetActivitiesStatementReport).toHaveBeenCalledTimes(2);

    const csvLines = response.text.split("\n");
    // Header on the first page
    expect(csvLines[0]).toMatch(
      "Payment Requested Date,Paid At,Customer Name,Customer Email,Type,Payment Type,Status,Amount (GBP),Id,Job Reference,Description",
    );
    // First row of data
    expect(csvLines[1]).not.toEqual("");

    // Header should appear only once
    csvLines.splice(0, 1); // Remove headers from the array
    expect(csvLines).not.toContain(
      "Payment Requested Date,Paid At,Customer Name,Customer Email,Type,Payment Type,Status,Amount (GBP),Id,Job Reference,Description",
    );
  });

  it("throws error if fromDate and toDate are not provided", async () => {
    // Arrange
    mockGetActivitiesStatementReport.mockRejectedValue(
      new InternalServerError(),
    );
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request.get("/payments/report/activities-statement");

    // Assert
    expect(response.status).toEqual(400);
    expect(response.body).toMatchObject({
      detail:
        "querystring must have required property 'fromDate', querystring must have required property 'toDate'",
      instance: "/payments/report/activities-statement",
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });
});
