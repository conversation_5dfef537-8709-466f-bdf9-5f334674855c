import { paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const getAuthedRequest = () => {
  const companyId = faker.number.int(1000000);
  const request = getTestRequestWithValidToken(companyId);

  return { companyId, request };
};

describe("POST terminal session", () => {
  const sdkData = "testSdkData";
  const setupToken = "testToken";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Valid token", () => {
    const mockPostTerminalSessionRequest = paymentSDK.trade.terminalSession
      .postTerminalSession as jest.Mock;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should return 200 and data on successful call", async () => {
      // Arrange
      mockPostTerminalSessionRequest.mockResolvedValue({ sdkData });
      const { request, companyId } = getAuthedRequest();

      // Act
      const response = await request
        .post("/payments/session")
        .send({ companyId: String(companyId), setupToken });

      // Assert
      expect(response.status).toEqual(200);
      expect(
        paymentSDK.trade.terminalSession.postTerminalSession,
      ).toHaveBeenCalledWith({
        companyId: String(companyId),
        setupToken,
      });
      expect(response.body).toEqual({ sdkData });
    });
  });

  describe("Invalid token", () => {
    it("should send 401 on invalid token", async () => {
      // Arrange
      const request = getTestRequestWithInvalidToken();

      // Act
      const response = await request
        .post("/payments/session")
        .send({ companyId: "1234", setupToken });

      // Assert
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: "/payments/session",
      });
    });
  });
});
