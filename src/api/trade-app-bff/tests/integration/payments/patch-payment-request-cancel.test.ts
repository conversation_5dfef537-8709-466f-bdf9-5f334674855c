import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { PaymentRequestStatus, paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

import { getTestRequestWithValidToken } from "../../helpers";

const companyId = 123456;
const paymentLinkId = `PL${faker.string.alphanumeric({ casing: "upper", length: 17 })}`;

const mockResponseData = {
  id: paymentLinkId,
  currency: "GBP",
  totalAmount: 4000,
  tradeAmount: 200,
  commissionAmount: 1000,
  companyId: String(companyId),
  opportunityId: "a8aa9c6f-c51e-489c-a0a3-20d4f5821dd6",
  consumerName: "Some geezer",
  consumerEmail: "<EMAIL>",
  paymentId: paymentLinkId,
  paymentUrl: "www.adyen-payment-link.com",
  reference: "Boiler repair in AB12 3CD",
  jobReference: "12321 - PO8 2SJ",
  description: "Custom payment reference",
  paymentLinkId,
  requestCreatedAt: new Date(),
  urlId: "something",
  urlExpiry: new Date(),
  status: PaymentRequestStatus.Cancelled,
  expiryDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  dueDate: new Date(),
};

const mockSmartMessage = { id: "test-smart-message" };

describe("PATCH payment request cancel", () => {
  const mockGetPaymentRequest = paymentSDK.trade.paymentRequest
    .getPaymentRequest as jest.Mock;
  const mockCancelPaymentRequest = paymentSDK.trade.paymentRequest
    .patchPaymentRequestCancel as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should successfully cancel payment request when right param and body request passed", async () => {
    // Arrange
    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    mockCancelPaymentRequest.mockResolvedValue({});
    mockGetPaymentRequest.mockResolvedValue(mockResponseData);
    smartMessageSpy.mockResolvedValue(mockSmartMessage as never);

    const reason = faker.lorem.sentence();

    // Act
    const request = getTestRequestWithValidToken(companyId);
    const response = await request
      .patch(`/payments/payment-request/${paymentLinkId}/cancel`)
      .send({ reason });

    // Assert
    expect(mockCancelPaymentRequest).toHaveBeenCalledTimes(1);
    expect(mockGetPaymentRequest).toHaveBeenCalledTimes(1);
    expect(mockGetPaymentRequest).toHaveBeenCalledWith(paymentLinkId);
    expect(smartMessageSpy).toHaveBeenCalledTimes(1);
    expect(smartMessageSpy).toHaveBeenCalledWith({
      channelId: mockResponseData.opportunityId,
      senderId: companyId,
      smartType: SmartMessageType.PAYMENT_REQUEST_NEW,
      text: "Payment for Boiler repair in AB12 3CD has been cancelled.",
      paymentRequestId: mockResponseData.id,
      paymentRequest: {
        id: mockResponseData.paymentId,
        dueDate: mockResponseData.dueDate,
        reference: mockResponseData.reference,
        jobReference: mockResponseData.jobReference,
        description: mockResponseData.description,
        amount: {
          value: mockResponseData.totalAmount,
          currency: mockResponseData.currency,
        },
        status: mockResponseData.status,
        paymentLinkId: mockResponseData.paymentLinkId,
        paymentUrl: mockResponseData.paymentUrl,
        consumerName: mockResponseData.consumerName,
      },
      logger: expect.any(Object),
    });
    expect(response.status).toEqual(200);
  });

  it("should return 400 bad request when reason not present in body request", async () => {
    // Arrange
    const paymentLinkId = `PL${faker.string.uuid()}`;
    const reason = faker.lorem.sentence();

    // Act
    const request = getTestRequestWithValidToken(companyId);
    const response = await request
      .patch(`/payments/payment-request/${paymentLinkId}/cancel`)
      .send({ propertyNotExist: reason });

    // Assert
    expect(response.status).toEqual(400);
  });
});
