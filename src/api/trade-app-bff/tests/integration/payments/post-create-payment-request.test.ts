import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { PaymentRequestStatus, paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";
import dayjs from "dayjs";

import { searchApi } from "../../../src/lib/api-common";
import * as GetCompany from "../../../src/services/firebase/firestore/get-company";
import { getTestConsumerResponse } from "../../factories";
import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
  nockTradeConsumerProfileSuccess,
} from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

const now = Date.now();
const companyId = 123456;
const channelId = faker.string.uuid();

const TRADE_SPLIT = {
  amount: { currency: "GBP", value: 966 },
  type: "TRADER",
};

const CHECKATRADE_SPLIT = {
  amount: { currency: "GBP", value: 34 },
  type: "CHECKATRADE",
};

const mockJob = {
  categoryId: 123,
  postcode: "AB1 2CD",
};

const mockPaymentApiRequestData = {
  amount: {
    currency: "GBP",
    value: 10000,
  },
  reference: "Fix tap in AB1 2CD",
  dueDate: String(dayjs(now).add(5, "days").toISOString()),
  returnUrl: "www.checkatrade.com",
  jobId: faker.string.uuid(),
  jobReference: `Job Category Name - ${mockJob.postcode}`,
  description: "Custom reference identifier",
  companyId: companyId.toString(),
  consumerId: faker.string.uuid(),
  opportunityId: channelId,
  firstName: "Joe",
  lastName: "Doe",
  emailAddress: "<EMAIL>",
  splits: [TRADE_SPLIT, CHECKATRADE_SPLIT],
};

const mockResponseData = {
  link: {
    id: faker.string.uuid(),
    status: "PAYMENT_PENDING",
    url: "www.adyen-payment-link.com",
  },
  paymentRequest: {
    currency: "GBP",
    totalAmount: mockPaymentApiRequestData.amount.value,
    tradeAmount: TRADE_SPLIT.amount.value,
    commissionAmount: CHECKATRADE_SPLIT.amount.value,
    companyId: String(companyId),
    jobId: faker.string.uuid(),
    jobReference: mockPaymentApiRequestData.jobReference,
    description: mockPaymentApiRequestData.description,
    consumerId: faker.string.uuid(),
    opportunityId: channelId,
    consumerName: "test",
    consumerEmail: "<EMAIL>",
    paymentUrl: "www.adyen-payment-link.com",
    reference: "Boiler repair in AB12 3CD",
    paymentLinkId: `PL${faker.string.alphanumeric({ casing: "upper", length: 17 })}`,
    status: PaymentRequestStatus.PaymentPending,
    expiryDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    dueDate: new Date(),
    reason: faker.string.alphanumeric(10),
  },
  paymentId: faker.string.uuid(),
};

const mockSmartMessage = { id: "test-smart-message" };

const mockCompanyDoc = {
  id: 1234,
  tradeId: 1000,
  name: "Company 1234",
};

describe("POST create payment request", () => {
  const mockPostCreatePaymentRequest = paymentSDK.trade.paymentRequest
    .postCreatePaymentRequest as jest.Mock;

  let getJobsSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(chatSDK, "getChannelById").mockResolvedValue({
      id: channelId,
    } as unknown as ReturnType<(typeof chatSDK)["getChannelById"]>);

    mockJobsSdkTrade();
    getJobsSpy = jobsSdkTradeMock.getJob;
    getJobsSpy.mockResolvedValue(mockJob);

    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue("Job Category Name");

    jest
      .spyOn(GetCompany, "getCompanyDoc")
      .mockResolvedValueOnce(mockCompanyDoc);
  });

  it("should return 200 with expected response", async () => {
    //Arrange
    nockTradeConsumerProfileSuccess({
      id: mockPaymentApiRequestData.consumerId,
    });
    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    mockPostCreatePaymentRequest.mockResolvedValue(mockResponseData);
    smartMessageSpy.mockResolvedValue(mockSmartMessage as never);

    const request = getTestRequestWithValidToken(companyId);

    //Act
    const response = await request.post(`/payments/payment-request`).send({
      paymentRequest: mockPaymentApiRequestData,
    });

    const { firstName, lastName, email, phone } = getTestConsumerResponse();
    //Assert
    expect(mockPostCreatePaymentRequest).toHaveBeenCalledTimes(1);
    expect(mockPostCreatePaymentRequest).toHaveBeenCalledWith(
      {
        ...mockPaymentApiRequestData,
        jobSource: "JOB_SERVICE",
        tokenData: {
          company: {
            companyId,
            companyName: mockCompanyDoc.name,
          },
          job: {
            categoryId: String(mockJob.categoryId),
            description: mockPaymentApiRequestData.description,
            postcode: mockJob.postcode,
          },
          consumer: {
            firstName,
            lastName,
            emailAddress: email,
            phoneNumber: phone,
          },
        },
      },
      true,
    );

    expect(smartMessageSpy).toHaveBeenCalledTimes(1);
    expect(smartMessageSpy).toHaveBeenCalledWith({
      channelId,
      senderId: companyId,
      smartType: SmartMessageType.PAYMENT_REQUEST_NEW,
      // Note: text required for stream agent (moderation)
      text: `Trader has requested £100.00 from the customer`,
      paymentRequestId: mockResponseData.link.id,
      paymentRequest: {
        id: mockResponseData.paymentId,
        dueDate: new Date(mockPaymentApiRequestData.dueDate as string),
        reference: mockResponseData.paymentId,
        description: mockPaymentApiRequestData.description,
        jobReference: mockPaymentApiRequestData.jobReference,
        amount: mockPaymentApiRequestData.amount,
        status: mockResponseData.link.status,
        paymentLinkId: mockResponseData.link.id,
        paymentUrl: mockResponseData.link.url,
        consumerName: mockResponseData.paymentRequest.consumerName,
      },
      logger: expect.any(Object),
    });

    expect(response.status).toEqual(201);
    expect(response.body).toMatchObject({
      ...mockResponseData,
      paymentRequest: {
        ...mockResponseData.paymentRequest,
        createdAt: mockResponseData.paymentRequest.createdAt.toISOString(),
        updatedAt: mockResponseData.paymentRequest.updatedAt.toISOString(),
        expiryDate: mockResponseData.paymentRequest.expiryDate.toISOString(),
        dueDate: mockResponseData.paymentRequest.dueDate.toISOString(),
      },
    });
  });

  it("should call paymentSdk with parameter returnToChatAfterPayment=false and return 200 even if channelId doesn't exists", async () => {
    //Arrange
    nockTradeConsumerProfileSuccess({
      id: mockPaymentApiRequestData.consumerId,
    });
    jest.spyOn(chatSDK, "getChannelById").mockResolvedValue(null);
    jest
      .spyOn(chatSDK, "sendSmartMessage")
      .mockRejectedValue("Channel doesn't exist");

    mockPostCreatePaymentRequest.mockResolvedValue(mockResponseData);
    const request = getTestRequestWithValidToken(companyId);

    //Act
    const response = await request.post(`/payments/payment-request`).send({
      paymentRequest: mockPaymentApiRequestData,
    });

    const { firstName, lastName, email, phone } = getTestConsumerResponse();
    //Assert
    expect(mockPostCreatePaymentRequest).toHaveBeenCalledTimes(1);
    expect(mockPostCreatePaymentRequest).toHaveBeenCalledWith(
      {
        ...mockPaymentApiRequestData,
        jobSource: "JOB_SERVICE",
        tokenData: {
          company: {
            companyId,
            companyName: mockCompanyDoc.name,
          },
          job: {
            categoryId: String(mockJob.categoryId),
            description: mockPaymentApiRequestData.description,
            postcode: mockJob.postcode,
          },
          consumer: {
            firstName,
            lastName,
            emailAddress: email,
            phoneNumber: phone,
          },
        },
      },
      false,
    );

    expect(response.status).toEqual(201);
    expect(response.body).toMatchObject({
      ...mockResponseData,
      paymentRequest: {
        ...mockResponseData.paymentRequest,
        createdAt: mockResponseData.paymentRequest.createdAt.toISOString(),
        updatedAt: mockResponseData.paymentRequest.updatedAt.toISOString(),
        expiryDate: mockResponseData.paymentRequest.expiryDate.toISOString(),
        dueDate: mockResponseData.paymentRequest.dueDate.toISOString(),
      },
    });
  });

  it("should call paymentSdk with consumer information from req body if consumer sdk doesn't return the data", async () => {
    nockTradeConsumerProfileSuccess(
      { id: mockPaymentApiRequestData.consumerId },
      {
        firstName: undefined,
        lastName: undefined,
      },
    );
    const request = getTestRequestWithValidToken(companyId);
    await request.post(`/payments/payment-request`).send({
      paymentRequest: mockPaymentApiRequestData,
    });

    const { email, phone } = getTestConsumerResponse();
    expect(mockPostCreatePaymentRequest).toHaveBeenCalledWith(
      expect.objectContaining({
        tokenData: expect.objectContaining({
          consumer: {
            firstName: mockPaymentApiRequestData.firstName,
            lastName: mockPaymentApiRequestData.lastName,
            emailAddress: email,
            phoneNumber: phone,
          },
        }),
      }),
      true,
    );
  });

  it("should return a 400 error if consumerId is not provided", async () => {
    const request = getTestRequestWithValidToken(companyId);
    const response = await request.post(`/payments/payment-request`).send({
      paymentRequest: { ...mockPaymentApiRequestData, consumerId: undefined },
    });

    expect(response.status).toBe(400);
  });

  it("should return a server error if payment request id or url are not returned when creating payment request", async () => {
    //Arrange
    mockPostCreatePaymentRequest.mockResolvedValue({
      id: null,
      status: null,
      url: null,
    });

    const request = getTestRequestWithValidToken(companyId);

    //Act
    const response = await request.post(`/payments/payment-request`).send({
      paymentRequest: mockPaymentApiRequestData,
    });

    // Assert
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/payments/payment-request`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });

    expect(response.status).toEqual(500);
  });

  it("should return a server error if error occurs during payment request creation", async () => {
    //Arrange
    mockPostCreatePaymentRequest.mockImplementation(() => {
      throw new Error("Something went wrong");
    });
    const request = getTestRequestWithValidToken(companyId);

    //Act
    const response = await request.post(`/payments/payment-request`).send({
      paymentRequest: mockPaymentApiRequestData,
    });

    //Assert
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/payments/payment-request`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });

    expect(response.status).toEqual(500);
  });

  it("should return a server error if error occurs during sending stream message", async () => {
    //Arrange
    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    mockPostCreatePaymentRequest.mockResolvedValue(mockResponseData);
    smartMessageSpy.mockImplementation(() => {
      throw new Error("Something went wrong");
    });
    const request = getTestRequestWithValidToken(companyId);

    //Act
    const response = await request.post(`/payments/payment-request`).send({
      paymentRequest: mockPaymentApiRequestData,
    });

    //Assert
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/payments/payment-request`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });

    expect(response.status).toEqual(500);
  });

  it("should return 401 when invalid token provided", async () => {
    //Arrange
    const badRequest = getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId }),
      companyId,
    );

    //Act
    const response = await badRequest.post(`/payments/payment-request`).send({
      paymentRequest: mockPaymentApiRequestData,
      channelId: channelId,
    });

    //Assert
    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/payments/payment-request`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
