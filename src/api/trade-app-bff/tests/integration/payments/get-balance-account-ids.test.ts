import { paymentSDK } from "@checkatrade/payment-sdk";

import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";

const mockGetBalanceAccountIdsResponse = {
  balanceAccountId: "mockBalanceAccountId",
  merchantAccountId: "mockMerchantAccountId",
};

describe("GET balanceAccountIds", () => {
  const companyId = 123;
  const mockGetBalanceAccountIds = paymentSDK.trade.onboarding
    .getBalanceAccountIds as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with expected response", async () => {
    mockGetBalanceAccountIds.mockResolvedValue(
      mockGetBalanceAccountIdsResponse,
    );

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(`/payments/balance-accounts`);

    expect(response.body).toMatchObject(mockGetBalanceAccountIdsResponse);
    expect(response.status).toEqual(200);
  });

  it("should return a server error if error occurs during core payment api call", async () => {
    mockGetBalanceAccountIds.mockImplementation(() => {
      throw new Error("bad things happened");
    });

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(`/payments/balance-accounts`);

    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/payments/balance-accounts`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });

    expect(response.status).toEqual(500);
  });

  it("should return 401 when token is invalid", async () => {
    const companyId = 13;
    const badRequest = getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId }),
      companyId,
    );

    const response = await badRequest.get(`/payments/balance-accounts`);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/payments/balance-accounts`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
