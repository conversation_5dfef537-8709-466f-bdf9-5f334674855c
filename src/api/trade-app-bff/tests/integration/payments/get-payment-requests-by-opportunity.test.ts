import { ForbiddenError, NotFoundError } from "@checkatrade/errors";
import { PaymentRequestStatus, paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

import { getTestRequestWithValidToken } from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

jest.mock("@checkatrade/payment-sdk");

const mockPaymentSdk = jest.mocked(paymentSDK);
describe("GET payment requests by opportunity", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should call the sdk with the correct params and return 200 with expected response", async () => {
    // Arrange
    mockJobsSdkTrade();
    jobsSdkTradeMock.getOpportunity.mockResolvedValue({ id: 123 });

    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity.mockResolvedValue(
      {
        data: [
          {
            id: faker.string.uuid(),
            companyId: faker.string.uuid(),
            jobId: faker.string.uuid(),
            reference: faker.string.alphanumeric(),
            consumerId: faker.string.uuid(),
            consumerEmail: faker.internet.email(),
            paymentLinkId: faker.string.uuid(),
            opportunityId: "123",
            paymentUrl: null,
            currency: faker.finance.currencyCode(),
            expiryDate: faker.date.future(),
            dueDate: faker.date.future(),
            createdAt: faker.date.recent(),
            updatedAt: faker.date.recent(),
            status: PaymentRequestStatus.Paid,
            totalAmount: faker.number.int(),
            tradeAmount: faker.number.int(),
            commissionAmount: faker.number.int(),
          },
        ],
        pagination: {
          size: 1,
          page: 1,
        },
      },
    );

    const companyId = 123;
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(
        `/payments/payment-requests/opportunity/123?orderBy=createdAt&orderDirection=ASC`,
      )
      .send();

    expect(jobsSdkTradeMock.getOpportunity).toHaveBeenCalledTimes(1);
    // Assert
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity,
    ).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity,
    ).toHaveBeenCalledWith("123", {
      orderBy: "createdAt",
      orderDirection: "ASC",
      pageNumber: 1,
      pageSize: 10,
    });

    expect(response.status).toEqual(200);
  });

  it("should return 200 when the payment sdk returns no data", async () => {
    // Arrange
    mockJobsSdkTrade();
    jobsSdkTradeMock.getOpportunity.mockResolvedValue({ id: 123 });

    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity.mockResolvedValue(
      undefined,
    );

    const request = getTestRequestWithValidToken(123);

    // Act
    const response = await request
      .get(
        `/payments/payment-requests/opportunity/123?orderBy=createdAt&orderDirection=ASC`,
      )
      .send();

    // Assert
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity,
    ).toHaveBeenCalledTimes(1);
    expect(jobsSdkTradeMock.getOpportunity).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity,
    ).toHaveBeenCalledWith("123", {
      orderBy: "createdAt",
      orderDirection: "ASC",
      pageNumber: 1,
      pageSize: 10,
    });

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      data: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
    });
  });

  it("should return 404 when the provided opportunityId is not found", async () => {
    // Arrange
    mockJobsSdkTrade();

    jobsSdkTradeMock.getOpportunity.mockRejectedValue(
      new NotFoundError("Opportunity not found"),
    );

    const request = getTestRequestWithValidToken(123);

    // Act
    const response = await request
      .get(`/payments/payment-requests/opportunity/123`)
      .send();

    // Assert
    expect(jobsSdkTradeMock.getOpportunity).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity,
    ).not.toHaveBeenCalled();

    expect(response.body.detail).toBe("Opportunity not found");
    expect(response.status).toEqual(404);
  });

  it("should return 403 when the provided opportunityId does not belong to the trade's company", async () => {
    // Arrange
    mockJobsSdkTrade();

    jobsSdkTradeMock.getOpportunity.mockRejectedValue(
      new ForbiddenError("Access to opportunity denied"),
    );

    const request = getTestRequestWithValidToken(123);

    // Act
    const response = await request
      .get(`/payments/payment-requests/opportunity/123`)
      .send();

    // Assert
    expect(jobsSdkTradeMock.getOpportunity).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity,
    ).not.toHaveBeenCalled();

    expect(response.body.detail).toBe("Access to opportunity denied");
    expect(response.status).toEqual(403);
  });
  it("should return 404 when the provided opportunityId is not found", async () => {
    // Arrange
    mockJobsSdkTrade();
    jobsSdkTradeMock.getOpportunity.mockResolvedValue(null);

    const request = getTestRequestWithValidToken(123);

    // Act
    const response = await request
      .get(`/payments/payment-requests/opportunity/123`)
      .send();

    // Assert
    expect(jobsSdkTradeMock.getOpportunity).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForOpportunity,
    ).not.toHaveBeenCalled();

    expect(response.body.detail).toBe(`No opportunity exists with id=123`);
    expect(response.status).toEqual(404);
  });
});
