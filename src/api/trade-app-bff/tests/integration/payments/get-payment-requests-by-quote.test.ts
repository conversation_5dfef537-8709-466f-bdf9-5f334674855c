import { ForbiddenError } from "@checkatrade/errors";
import { PaymentRequestStatus, paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

import { getTestRequestWithValidToken } from "../../helpers";
import {
  mockQuotingSdkTrade,
  quotingSdkTradeMock,
} from "../../helpers/mocks/quoting-sdk.mock";

jest.mock("@checkatrade/payment-sdk");

const mockPaymentSdk = jest.mocked(paymentSDK);

describe("GET payment requests by quote", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should call the sdk with the correct params and return 200 with expected response", async () => {
    // Arrange
    const quoteId = faker.string.alphanumeric(10);
    mockQuotingSdkTrade();
    quotingSdkTradeMock.getQuote.mockResolvedValue({ id: quoteId });

    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockResolvedValue(
      {
        data: [
          {
            id: faker.string.uuid(),
            companyId: faker.string.uuid(),
            jobId: faker.string.uuid(),
            reference: faker.string.alphanumeric(),
            consumerId: faker.string.uuid(),
            consumerEmail: faker.internet.email(),
            paymentLinkId: faker.string.uuid(),
            opportunityId: faker.string.uuid(),
            quoteId: quoteId,
            paymentUrl: null,
            currency: faker.finance.currencyCode(),
            expiryDate: faker.date.future(),
            dueDate: faker.date.future(),
            createdAt: faker.date.recent(),
            updatedAt: faker.date.recent(),
            status: PaymentRequestStatus.Paid,
            totalAmount: faker.number.int(),
            tradeAmount: faker.number.int(),
            commissionAmount: faker.number.int(),
          },
        ],
        pagination: {
          size: 1,
          page: 1,
        },
      },
    );

    const companyId = 123;
    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .get(
        `/payments/payment-requests/quote/${quoteId}?orderBy=createdAt&orderDirection=ASC`,
      )
      .send();

    // Assert
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledWith(quoteId, {
      orderBy: "createdAt",
      orderDirection: "ASC",
      pageNumber: 1,
      pageSize: 10,
    });

    expect(response.status).toEqual(200);
  });

  it("should return 200 when the payment sdk returns no data", async () => {
    // Arrange
    const quoteId = faker.string.alphanumeric(10);
    mockQuotingSdkTrade();
    quotingSdkTradeMock.getQuote.mockResolvedValue({ id: quoteId });

    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockResolvedValue(
      undefined,
    );

    const request = getTestRequestWithValidToken(123);

    // Act
    const response = await request
      .get(
        `/payments/payment-requests/quote/${quoteId}?orderBy=createdAt&orderDirection=ASC`,
      )
      .send();

    // Assert
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledWith(quoteId, {
      orderBy: "createdAt",
      orderDirection: "ASC",
      pageNumber: 1,
      pageSize: 10,
    });

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      data: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
    });
  });

  it("should return 403 when the provided quoteId does not belong to the trade's company", async () => {
    // Arrange
    const quoteId = faker.string.alphanumeric(10);
    mockQuotingSdkTrade();
    quotingSdkTradeMock.getQuote.mockRejectedValue(
      new ForbiddenError(
        "CompanyId from the jwt token does not match the quote's companyId",
      ),
    );
    const request = getTestRequestWithValidToken(123);

    // Act
    const response = await request
      .get(`/payments/payment-requests/quote/${quoteId}`)
      .send();

    // Assert
    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).not.toHaveBeenCalled();

    expect(response.body.detail).toBe(
      "CompanyId from the jwt token does not match the quote's companyId",
    );
    expect(response.status).toEqual(403);
  });
});
