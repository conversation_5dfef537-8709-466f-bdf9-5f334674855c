import { paymentSDK } from "@checkatrade/payment-sdk";

import { getTestRequestWithValidToken } from "../../helpers";

const companyId = 123456;

const mockPaymentMethodsResponse = {
  paymentMethods: [
    {
      brands: ["mc"],
      name: "MasterCard",
      type: "scheme",
    },
    {
      brands: ["visa"],
      name: "Visa",
      type: "scheme",
    },
  ],
};

describe("GET pay by phone payment methods", () => {
  const mockGetPaymentMethods = jest.mocked(
    paymentSDK.trade.payByPhone.getPaymentMethods,
  );

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with expected response", async () => {
    mockGetPaymentMethods.mockResolvedValue(mockPaymentMethodsResponse);

    const request = getTestRequestWithValidToken(companyId);

    const response = await request
      .get("/payments/pay-by-phone/payment-methods")
      .send();

    expect(mockGetPaymentMethods).toHaveBeenCalledTimes(1);
    expect(mockGetPaymentMethods).toHaveBeenCalledWith(String(companyId));
    expect(response.body).toEqual(mockPaymentMethodsResponse);
    expect(response.status).toEqual(200);
  });

  it("should return 500 if there is an error during payment methods fetch", async () => {
    mockGetPaymentMethods.mockImplementation(() => {
      throw new Error("Failed to fetch payment methods");
    });

    const request = getTestRequestWithValidToken(companyId);

    const response = await request
      .get("/payments/pay-by-phone/payment-methods")
      .send();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: "/payments/pay-by-phone/payment-methods",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });
});
