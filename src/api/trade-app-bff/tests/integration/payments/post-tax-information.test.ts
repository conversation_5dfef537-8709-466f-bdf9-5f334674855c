import { paymentSDK } from "@checkatrade/payment-sdk";

import { salesforce } from "../../../src/lib/api-common";
import {
  TEST_COMPANY_ID,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
} from "../../helpers";

describe("POST tax information", () => {
  const vatNumber = "GB12345678";
  const uniqueTaxpayerReference = "1234567890";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Valid token", () => {
    it("should send 204 on successful update", async () => {
      // Arrange
      jest.spyOn(salesforce, "taxInformation").mockResolvedValue("");
      const companyId = 1234;
      const request = getTestRequestWithValidToken(companyId);

      // Act
      const response = await request
        .post(`/payments/tax-information`)
        .send({ vatNumber, uniqueTaxpayerReference });

      // Assert
      expect(response.status).toEqual(204);
      expect(
        paymentSDK.trade.onboarding.updateTaxInformation,
      ).toHaveBeenCalledWith(String(companyId), true);
      expect(response.body).toEqual({});
    });

    it("should send 204 on successful update with minimum data", async () => {
      // Arrange
      const salesforceSpy = jest
        .spyOn(salesforce, "taxInformation")
        .mockResolvedValue("");
      const companyId = 12345;

      const request = getTestRequestWithValidToken(companyId);

      // Act
      const response = await request
        .post(`/payments/tax-information`)
        .send({ uniqueTaxpayerReference });

      // Assert
      expect(response.status).toEqual(204);
      expect(salesforceSpy).toHaveBeenCalledWith({
        companyId,
        uniqueTaxpayerReference,
      });
      expect(
        paymentSDK.trade.onboarding.updateTaxInformation,
      ).toHaveBeenCalledWith(String(companyId), true);
      expect(response.body).toEqual({});
    });
  });

  describe("Invalid token", () => {
    it("should send 401 on invalid token", async () => {
      // Arrange
      const request = getTestRequestFromJWTPayload({}, TEST_COMPANY_ID).auth(
        "invalid",
        {
          type: "bearer",
        },
      );

      // Act
      const response = await request
        .post(`/payments/tax-information`)
        .send({ vatNumber, uniqueTaxpayerReference });

      // Assert
      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
        status: 401,
        title: "Unauthorized",
        detail: "Invalid auth token",
        instance: `/payments/tax-information`,
      });
    });
  });
});
