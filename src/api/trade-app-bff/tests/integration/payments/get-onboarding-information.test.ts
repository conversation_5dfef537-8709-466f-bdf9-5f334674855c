import { OnboardingStatus, paymentSDK } from "@checkatrade/payment-sdk";

import { CapabilityStatus } from "../../../src/controllers/payments/schemas/GetOnboarding.types";
import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";

const mockGetOnboardingResponse = {
  providedTaxInformation: false,
  status: OnboardingStatus.Enrolled,
  type: "soleProprietorship",
  soleProprietorshipId: "LE123",
  legalEntityId: "LE456",
  accountHolderId: "AH123",
  balanceAccountId: "BA123",
  businessLineId: "SE123",
  storeId: "ST123",
  hostedOnboardingUrl: "https://fake-hosted-onboarding-url",
  paymentMethods: [
    {
      paymentMethodId: "PM123",
      paymentMethodType: "visa",
    },
    {
      paymentMethodId: "PM456",
      paymentMethodType: "mc",
    },
  ],
};

const mockCapabilitiesResponse = [
  {
    companyId: "1234",
    id: "capabilityId",
    capabilityKey: "RECEIVE_FROM_TRANSFER_INSTRUMENT",
    disabled: false,
    status: CapabilityStatus.Valid,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastEventDate: null,
  },
];

describe("GET onboarding information", () => {
  const companyId = 123;

  describe("Valid token", () => {
    const mockGetOnboarding = paymentSDK.trade.onboarding
      .getOnboarding as jest.Mock;
    const mockGetCapabilities = paymentSDK.trade.capabilities
      .getCapabilities as jest.Mock;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should return 200 with expected response", async () => {
      mockGetOnboarding.mockResolvedValue(mockGetOnboardingResponse);
      mockGetCapabilities.mockResolvedValue({ data: mockCapabilitiesResponse });

      const request = getTestRequestWithValidToken(companyId);
      const response = await request.get("/payments/onboarding-information");

      expect(response.body).toMatchObject({
        onboarding: { progress: 1, status: "ENROLLED" },
        problems: null,
        capabilities: {
          RECEIVE_FROM_PLATFORM_PAYMENTS: null,
          RECEIVE_FROM_BALANCE_ACCOUNT: null,
          SEND_TO_BALANCE_ACCOUNT: null,
          SEND_TO_TRANSFER_INSTRUMENT: null,
          RECEIVE_FROM_TRANSFER_INSTRUMENT: {
            status: "VALID",
            disabled: false,
          },
          RECEIVE_PAYMENTS: null,
        },
      });
      expect(response.status).toEqual(200);
    });

    it("should return 200 with expected response (including capability problems)", async () => {
      mockGetOnboarding.mockResolvedValue({
        ...mockGetOnboardingResponse,
        status: OnboardingStatus.RequiresFurtherAction,
      });
      mockGetCapabilities.mockResolvedValue({
        data: [
          {
            companyId: "1234",
            id: "capabilityId",
            capabilityKey: "RECEIVE_FROM_TRANSFER_INSTRUMENT",
            disabled: false,
            status: CapabilityStatus.Invalid,
            problems: [
              {
                entity: {
                  id: "*************************",
                  type: "LegalEntity",
                },
                verificationErrors: [
                  {
                    code: "2_902",
                    message: "Terms Of Service forms are not accepted.",
                    remediatingActions: [
                      {
                        code: "2_902",
                        message: "Accept TOS",
                      },
                    ],
                    type: "invalidInput",
                  },
                ],
              },
            ],
            createdAt: new Date(),
            updatedAt: new Date(),
            lastEventDate: null,
          },
        ],
      });

      const request = getTestRequestWithValidToken(companyId);
      const response = await request.get("/payments/onboarding-information");

      expect(response.body).toStrictEqual({
        onboarding: {
          progress: 1,
          status: "REQUIRES_FURTHER_ACTION",
          providedTaxInformation: false,
        },
        anyDisabledCapabilities: false,
        problems: ["Sign services agreement"],
        capabilities: {
          RECEIVE_FROM_PLATFORM_PAYMENTS: null,
          RECEIVE_FROM_BALANCE_ACCOUNT: null,
          SEND_TO_BALANCE_ACCOUNT: null,
          SEND_TO_TRANSFER_INSTRUMENT: null,
          RECEIVE_FROM_TRANSFER_INSTRUMENT: {
            status: "INVALID",
            disabled: false,
          },
          RECEIVE_PAYMENTS: null,
        },
      });
      expect(response.status).toEqual(200);
    });

    it("should return a server error if error occurs during capability call", async () => {
      mockGetCapabilities.mockImplementation(() => {
        throw new Error("no user");
      });
      mockGetOnboarding.mockResolvedValue(mockGetOnboardingResponse);

      const request = getTestRequestWithValidToken(companyId);
      const response = await request.get("/payments/onboarding-information");

      expect(response.body).toEqual({
        detail: "An unexpected error occurred",
        instance: "/payments/onboarding-information",
        status: 500,
        title: "Internal Server Error",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
      });

      expect(response.status).toEqual(500);
    });

    it("should return a server error if error occurs during onboarding call", async () => {
      mockGetCapabilities.mockResolvedValue({ data: mockCapabilitiesResponse });
      mockGetOnboarding.mockImplementation(() => {
        throw new Error("no user");
      });
      const request = getTestRequestWithValidToken(companyId);
      const response = await request.get("/payments/onboarding-information");

      expect(response.body).toEqual({
        detail: "An unexpected error occurred",
        instance: "/payments/onboarding-information",
        status: 500,
        title: "Internal Server Error",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
      });

      expect(response.status).toEqual(500);
    });
  });

  describe("Invalid token", () => {
    it("should return 401", async () => {
      const badRequest = getTestRequestFromJWTPayload(
        mockInvalidTokenJWT({ companyId }),
        companyId,
      );

      const response = await badRequest.get("/payments/onboarding-information");

      expect(response.status).toEqual(401);
      expect(response.body).toEqual({
        detail: "Invalid auth token",
        instance: "/payments/onboarding-information",
        status: 401,
        title: "Unauthorized",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      });
    });
  });
});
