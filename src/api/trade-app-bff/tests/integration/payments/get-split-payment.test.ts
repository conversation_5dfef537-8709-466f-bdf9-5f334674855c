import { paymentSDK } from "@checkatrade/payment-sdk";

import {
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";

const mockGetSplitPaymentResponse = {
  serviceCharge: 10,
  total: 110,
};

describe("GET split payment", () => {
  const companyId = 123;
  const mockGetSplitPayment = paymentSDK.trade.splitPayment
    .getSplitPayment as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with expected response", async () => {
    mockGetSplitPayment.mockResolvedValue(mockGetSplitPaymentResponse);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/payments/split-payment/100");

    expect(response.body).toMatchObject(mockGetSplitPaymentResponse);
    expect(response.status).toEqual(200);
  });

  it("should return a server error if error occurs during core payment api call", async () => {
    mockGetSplitPayment.mockImplementation(() => {
      throw new Error("bad things happened");
    });

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/payments/split-payment/100");

    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: "/payments/split-payment/100",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });

    expect(response.status).toEqual(500);
  });

  it("should return 400 when invalid pence amount provided", async () => {
    const badRequest = getTestRequestWithValidToken(companyId);

    const response = await badRequest.get("/payments/split-payment/onehundred");

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "params/penceAmount must be number",
      instance: "/payments/split-payment/onehundred",
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should return 401 when token is invalid", async () => {
    const badRequest = getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId }),
      companyId,
    );

    const response = await badRequest.get("/payments/split-payment/100");

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: "/payments/split-payment/100",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
