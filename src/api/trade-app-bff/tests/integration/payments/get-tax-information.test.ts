import ContentAPI from "../../../src/controllers/payments/contentapi";
import {
  PersonType,
  TraderType,
} from "../../../src/controllers/payments/schemas/Shared.types";
import { getTestRequestWithValidToken } from "../../helpers";

describe("GET tax information", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it.each(
    Object.values(TraderType).flatMap((traderType) => {
      if (typeof traderType === "string") return [];
      return [
        [traderType, "GB12345678"],
        [traderType, undefined],
      ];
    }),
  )(
    "should return 200 with expected response, when receiving traderType of %s and taxRegistrationNo of %s",
    async (traderType, vatRegistrationNo) => {
      // Arrange
      const companyId = 123456;
      const request = getTestRequestWithValidToken(companyId);
      jest.spyOn(ContentAPI, "getProfileData").mockResolvedValue({
        payload: {
          core: {
            vatRegistrationNo,
            traderType,
          } as never,
          profile: {
            businessOwnership: [
              {
                firstName: "Bob",
                lastName: "Builder",
                birthdate: new Date("1990-01-01").toISOString(),
                personTypeId: PersonType.PrimaryOwner,
                inactive: false,
                isDeleted: false,
              },
            ],
          },
        },
      });

      // Act
      const response = await request.get("/payments/tax-information").send();

      // Assert
      expect(response.status).toEqual(200);
      expect(response.body).toMatchObject(
        vatRegistrationNo ? { vatRegistrationNo, traderType } : { traderType },
      );
    },
  );

  it("should return 500 if there is an issue with fetching profile data", async () => {
    // Arrange
    const companyId = 123456;
    const request = getTestRequestWithValidToken(companyId);
    jest
      .spyOn(ContentAPI, "getProfileData")
      .mockRejectedValue(Error("SOME_ERROR"));

    // Act
    const response = await request.get("/payments/tax-information").send();

    // Assert
    expect(response.status).toEqual(500);
    expect(response.body).toMatchObject({
      detail: "An unexpected error occurred",
      instance: "/payments/tax-information",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });
});
