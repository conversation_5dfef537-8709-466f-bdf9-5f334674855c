import { firebase } from "../../../src/services/firebase";
import {
  TEST_CAPI_USER_ID,
  TEST_COMPANY_ID,
  TEST_USER_EMAIL,
  TEST_USER_ID,
  getTestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidNonMemberToken,
  getTestRequestWithValidPendingMemberToken,
  getTestRequestWithValidToken,
  mockValidTokenJWT,
} from "../../helpers";

describe("createCustomFirebaseToken", () => {
  beforeEach(() => {
    jest
      .spyOn(firebase.auth, "createCustomToken")
      .mockImplementation((id, claims) =>
        Promise.resolve(`test-token-${id}-${JSON.stringify(claims)}`),
      );
  });

  it("should return 401, if no token is provided", async () => {
    const response = await getTestRequest()
      .set("x-trade-company-id", "123")
      .get("/auth/createCustomFirebaseToken");
    expect(response.statusCode).toBe(401);
    expect(response.body).toEqual({
      detail: "Missing authorization header",
      instance: "/auth/createCustomFirebaseToken",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should return 400, if no trade header is provided", async () => {
    const response = await getTestRequestFromJWTPayload(
      mockValidTokenJWT({
        companyId: TEST_COMPANY_ID,
        sub: TEST_CAPI_USER_ID,
      }),
    ).get("/auth/createCustomFirebaseToken");
    expect(response.statusCode).toBe(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: "/auth/createCustomFirebaseToken",
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should return 200, if token is valid with active account", async () => {
    const response = await getTestRequestWithValidToken(
      TEST_COMPANY_ID,
      TEST_CAPI_USER_ID,
    ).get("/auth/createCustomFirebaseToken");

    expect(response.statusCode).toBe(200);
    expect(response.text).toEqual(
      `test-token-${TEST_USER_ID}-{"cat.org":{"${TEST_COMPANY_ID}":"active"},"custom_user_info":{"uid":"${TEST_CAPI_USER_ID}","email":"${TEST_USER_EMAIL}"}}`,
    );
  });

  it("should return 200, if token is valid with pending account", async () => {
    const response = await getTestRequestWithValidPendingMemberToken(
      TEST_COMPANY_ID,
      TEST_CAPI_USER_ID,
    ).get("/auth/createCustomFirebaseToken");

    expect(response.statusCode).toBe(200);
    expect(response.text).toEqual(
      `test-token-${TEST_USER_ID}-{"cat.org":{"${TEST_COMPANY_ID}":"pending"},"custom_user_info":{"uid":"${TEST_CAPI_USER_ID}","email":"${TEST_USER_EMAIL}"}}`,
    );
  });

  it("should return 200, if token is valid with suspended account", async () => {
    const response = await getTestRequestWithValidNonMemberToken(
      TEST_COMPANY_ID,
      TEST_CAPI_USER_ID,
    ).get("/auth/createCustomFirebaseToken");

    expect(response.statusCode).toBe(200);
    expect(response.text).toEqual(
      `test-token-${TEST_USER_ID}-{"cat.org":{"${TEST_COMPANY_ID}":"suspend"},"custom_user_info":{"uid":"${TEST_CAPI_USER_ID}","email":"${TEST_USER_EMAIL}"}}`,
    );
  });
});
