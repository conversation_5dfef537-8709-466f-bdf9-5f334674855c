import { chatSDK } from "@checkatrade/chat-sdk";

import {
  type TestRequest,
  getTestRequest,
  getTestRequestWithValidToken,
} from "../../helpers";

describe("Chat Routes", () => {
  let request: TestRequest;
  const chatTokenResponse = {
    accessToken: "accessToken",
    apiKey: "apiKey",
  };

  beforeEach(() => {
    request = getTestRequestWithValidToken();
    jest.spyOn(chatSDK, "generateNewToken").mockReturnValue(chatTokenResponse);
  });

  it("should return chat token response with accessToken apiKey", async () => {
    const res = await request
      .set("content-type", "application/json")
      .post("/auth/chat")
      .query({ includeCredentials: "true" });

    expect(res.statusCode).toEqual(200);
    expect(res.headers["cache-control"]).toEqual("no-store");
    expect(res.body).toEqual(chatTokenResponse);
  });

  describe("Error handling", () => {
    it("should return 400 when 'x-trade-company-id' header is missing", async () => {
      const res = await getTestRequest().post("/auth/chat").send();
      expect(res.statusCode).toEqual(400);
      expect(res.body).toEqual({
        detail: "headers must have required property 'x-trade-company-id'",
        instance: "/auth/chat",
        status: 400,
        title: "Bad Request",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
      });
    });

    it("should return 401 when trade realm header is not set", async () => {
      const res = await getTestRequest()
        .set("x-trade-company-id", "12345")
        .post("/auth/chat")
        .send();
      expect(res.statusCode).toEqual(401);
      expect(res.body).toEqual({
        detail: "Missing authorization header",
        instance: "/auth/chat",
        status: 401,
        title: "Unauthorized",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      });
    });
  });
});
