import { faker } from "@faker-js/faker";

import * as teamService from "../../../src/services/team";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const companyId = faker.number.int({ min: 1000, max: 9999 });

describe("GET /team/is-full-member-or-national-account - controller", () => {
  let getIsFullMemberOrNationalAccountSpy: jest.SpyInstance;

  beforeEach(() => {
    getIsFullMemberOrNationalAccountSpy = jest.spyOn(
      teamService.team,
      "getIsFullMemberOrNationalAccount",
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with true when member is a full member or national account", async () => {
    getIsFullMemberOrNationalAccountSpy.mockResolvedValue(true);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(
      "/team/is-full-member-or-national-account",
    );

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(true);

    expect(getIsFullMemberOrNationalAccountSpy).toHaveBeenCalledTimes(1);
    expect(getIsFullMemberOrNationalAccountSpy).toHaveBeenCalledWith(
      companyId,
      expect.anything(),
    );
  });

  it("should return 200 with false when member is not a full member or national account", async () => {
    getIsFullMemberOrNationalAccountSpy.mockResolvedValue(false);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(
      "/team/is-full-member-or-national-account",
    );

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(false);

    expect(getIsFullMemberOrNationalAccountSpy).toHaveBeenCalledTimes(1);
    expect(getIsFullMemberOrNationalAccountSpy).toHaveBeenCalledWith(
      companyId,
      expect.anything(),
    );
  });

  it("should handle error and propagate it", async () => {
    const error = new Error("Failed to get is full member or national account");
    getIsFullMemberOrNationalAccountSpy.mockRejectedValue(error);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(
      "/team/is-full-member-or-national-account",
    );

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: "/team/is-full-member-or-national-account",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });

  it("should return 401 for invalid token", async () => {
    const request = getTestRequestWithInvalidToken();
    const response = await request.get(
      "/team/is-full-member-or-national-account",
    );

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: "/team/is-full-member-or-national-account",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
