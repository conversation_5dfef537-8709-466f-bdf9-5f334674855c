import { ApiError } from "@checkatrade/errors";

import * as deleteTeamPersonService from "../../../src/services/team";
import { getTestRequest, getTestRequestWithValidToken } from "../../helpers";

const teamCompanyId = 937739;
const baseQueryParams = {
  personId: "123456",
};
const baseBodyParams = {
  type: "PERSON",
};

describe("DELETE /team/{personId}", () => {
  it("should return 200 and delete team person", async () => {
    jest
      .spyOn(deleteTeamPersonService.team, "deleteTeamPerson")
      .mockResolvedValue({
        id: baseQueryParams.personId,
      });

    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request
      .delete(`/team/${baseQueryParams.personId}`)
      .send(baseBodyParams);

    expect(response.status).toEqual(200);
  });

  it("should throw error when TDS fails to delete team person", async () => {
    jest
      .spyOn(deleteTeamPersonService.team, "deleteTeamPerson")
      .mockRejectedValue(new Error("Failed to delete team person"));

    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request
      .delete(`/team/${baseQueryParams.personId}`)
      .send(baseBodyParams);

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/team/${baseQueryParams.personId}`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });

  it("should throw error when TDS returns ApiError", async () => {
    jest
      .spyOn(deleteTeamPersonService.team, "deleteTeamPerson")
      .mockRejectedValue(new ApiError("Failed to delete team person"));

    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request
      .delete(`/team/${baseQueryParams.personId}`)
      .send(baseBodyParams);

    expect(response.status).toEqual(500);
  });

  it("should return 400 when x-trade-company-id header is missing", async () => {
    const response = await getTestRequest()
      .delete(`/team/${baseQueryParams.personId}`)
      .send(baseBodyParams);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: `/team/${baseQueryParams.personId}`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should send 400 and reject requests with missing query params", async () => {
    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request
      .delete(`/team/${baseQueryParams.personId}`)
      .query(baseBodyParams);

    expect(response.status).toEqual(400);
  });

  it("should send 400 and reject requests with missing body params", async () => {
    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request
      .delete(`/team/${baseQueryParams.personId}`)
      .send({});

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "body must have required property 'type'",
      instance: `/team/${baseQueryParams.personId}`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });
});
