import { ApiError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";
import {
  RelationshipStatus,
  UpdateTeamInviteResponse,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as teamService from "../../../src/services/team";
import { tdsHttpClient } from "../../../src/services/trade-data-service";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";
import { updateTeamInviteMock } from "../../helpers/mocks/update-team-invite-mock";

const companyId = faker.number.int({ min: 1000, max: 9999 });
const inviteId = faker.string.uuid();

describe("PUT /team/invite/:id - controller", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with data", async () => {
    jest
      .spyOn(teamService.team, "updateTeamInvite")
      .mockResolvedValue(updateTeamInviteMock as UpdateTeamInviteResponse);

    const id = faker.string.uuid();

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.put(`/team/invite/${id}`).send({
      relationshipStatus: RelationshipStatus.Accepted,
      childMemberId: "7e82cea9-6350-48e2-8572-12f03837a163",
    });

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(updateTeamInviteMock);
  });

  it("should return 401 for invalid token", async () => {
    jest
      .spyOn(teamService.team, "updateTeamInvite")
      .mockRejectedValue(new Error("Invalid token"));

    const id = faker.string.uuid();

    const request = getTestRequestWithInvalidToken(companyId);
    const response = await request.put(`/team/invite/${id}`).send({
      relationshipStatus: RelationshipStatus.Accepted,
      childMemberId: "7e82cea9-6350-48e2-8572-12f03837a163",
    });

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/team/invite/${id}`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should return InternalServerError if there is an error", async () => {
    jest
      .spyOn(teamService.team, "updateTeamInvite")
      .mockRejectedValue(new ApiError("Error updating team invite"));

    const companyId = faker.number.int({ min: 1000, max: 9999 });
    const id = faker.string.uuid();

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.put(`/team/invite/${id}`).send({
      relationshipStatus: RelationshipStatus.Accepted,
      childMemberId: "7e82cea9-6350-48e2-8572-12f03837a163",
    });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      code: "FST_ERR_BAD_STATUS_CODE",
      error: "Internal Server Error",
      message: "Called reply with an invalid status code: undefined",
      statusCode: 500,
    });
  });
});

describe("PUT /team/invite/:id - service", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const loggerMock = {
    error: jest.fn(),
  } as unknown as Logger;

  it("should return 200 with mocked data, mock http call", async () => {
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "1", // First call for getTeamMemberId returns memberId
        },
      })
      .mockResolvedValueOnce({
        data: updateTeamInviteMock, // Second call for getTeamInvite returns mocked data
      });

    jest.spyOn(tdsHttpClient, "put").mockResolvedValueOnce({
      data: updateTeamInviteMock,
    });

    const testFunction = await teamService.team.updateTeamInvite(
      {
        relationshipStatus: RelationshipStatus.Accepted,
        expiryDate: faker.date.soon().toISOString(),
      },
      inviteId,
      companyId,
      loggerMock,
    );

    expect(testFunction).toEqual(
      updateTeamInviteMock as UpdateTeamInviteResponse,
    );
  });

  it("should return 200 with mocked data and no expiry date, mock http call", async () => {
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "1", // First call for getTeamMemberId returns memberId
        },
      })
      .mockResolvedValueOnce({
        data: updateTeamInviteMock, // Second call for getTeamInvite returns mocked data
      });

    jest.spyOn(tdsHttpClient, "put").mockResolvedValueOnce({
      data: updateTeamInviteMock,
    });

    const testFunction = await teamService.team.updateTeamInvite(
      {
        relationshipStatus: RelationshipStatus.Accepted,
      },
      inviteId,
      companyId,
      loggerMock,
    );

    expect(testFunction).toEqual(
      updateTeamInviteMock as UpdateTeamInviteResponse,
    );
  });

  it("should return Error if httpClient.put returns error", async () => {
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "1", // First call for getTeamMemberId returns memberId
        },
      })
      .mockResolvedValueOnce({
        data: updateTeamInviteMock, // Second call for getTeamInvite returns mocked data
      });

    jest.spyOn(tdsHttpClient, "put").mockRejectedValue(new Error("Error"));

    await expect(
      teamService.team.updateTeamInvite(
        {
          relationshipStatus: RelationshipStatus.Accepted,
        },
        inviteId,
        companyId,
        loggerMock,
      ),
    ).rejects.toThrow("Error");

    expect(loggerMock.error).toHaveBeenCalledWith(
      {
        data: {
          relationshipStatus: RelationshipStatus.Accepted,
        },
        error: new Error("Error"),
        inviteId,
      },
      "Failed to update team invite service",
    );
  });

  it("should return Error if getTeamMemberId returns undefined", async () => {
    jest.spyOn(tdsHttpClient, "get").mockResolvedValue({
      data: {
        memberId: "", // First call for getTeamMemberId returns empty memberId
      },
    });

    await expect(
      teamService.team.updateTeamInvite(
        {
          relationshipStatus: RelationshipStatus.Accepted,
        },
        inviteId,
        companyId,
        loggerMock,
      ),
    ).rejects.toThrow(`Failed to get member id for company ${companyId}`);

    expect(loggerMock.error).toHaveBeenCalledWith(
      {
        data: {
          relationshipStatus: RelationshipStatus.Accepted,
        },
        error: new Error(`Failed to get member id for company ${companyId}`),
        inviteId,
      },
      "Failed to update team invite service",
    );
  });

  it("should return Error if getTeamInvite returns undefined", async () => {
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "1", // First call for getTeamMemberId returns memberId
        },
      })
      .mockResolvedValueOnce({
        data: {}, // Second call for getTeamInvite returns empty object
      });

    await expect(
      teamService.team.updateTeamInvite(
        {
          relationshipStatus: RelationshipStatus.Accepted,
        },
        inviteId,
        companyId,
        loggerMock,
      ),
    ).rejects.toThrow("Expected required property");

    expect(loggerMock.error).toHaveBeenCalledWith(
      {
        data: {
          relationshipStatus: RelationshipStatus.Accepted,
        },
        error: new Error("Expected required property"),
        inviteId,
      },
      "Failed to update team invite service",
    );
  });
});
