import { UpdateTeamPersonsQuery } from "@checkatrade/trade-bff-types";

import * as updateTeamPersonService from "../../../src/services/team";
import { getTestRequest, getTestRequestWithValidToken } from "../../helpers";

const personId = "4419ca05-932d-444b-8470-8adc28535dcd";

const teamPerson: UpdateTeamPersonsQuery = {
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>",
  nickname: "J<PERSON>",
  dateOfBirth: "1990-01-01T16:17:15.287Z",
  address: {
    line1: "123 Main St",
    line2: "Apt 101",
    city: "Springfield",
    county: "Simpson",
    postalCode: "12345",
  },
  email: "<EMAIL>",
  phoneNumber: "************",
  mobilePhone: "************",
  workCategories: [
    {
      id: "cat1",
      subCategories: [{ id: "sub1" }, { id: "sub2" }],
    },
  ],
};

describe("PUT /team/{personId}/person success", () => {
  beforeEach(() => {
    jest
      .spyOn(updateTeamPersonService.team, "updateTeamPerson")
      .mockResolvedValue({
        id: personId,
      });
  });

  it("should return 200 and the updated team person ID", async () => {
    const request = getTestRequestWithValidToken();
    const response = await request
      .put(`/team/${personId}/persons`)
      .send(teamPerson);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({ id: personId });
  });
});

describe("PUT /team/{personId}/person reject", () => {
  it("should return 400 when x-trade-company-id header is missing", async () => {
    const response = await getTestRequest()
      .put(`/team/${personId}/persons`)
      .send(teamPerson);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: `/team/${personId}/persons`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should return 400 when the request body is missing", async () => {
    const request = getTestRequestWithValidToken();
    const response = await request.put(`/team/${personId}/persons`).send({});

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail:
        "body must have required property 'firstName', body must have required property 'lastName', body must have required property 'dateOfBirth', body must have required property 'address', body must have required property 'email', body must have required property 'phoneNumber', body must have required property 'workCategories'",
      instance: `/team/${personId}/persons`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });
});
