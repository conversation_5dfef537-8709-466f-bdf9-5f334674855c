import {
  CompanyRole,
  VettingConsentStatus,
  VettingStatus,
  VettingStatusTVS,
} from "@checkatrade/trade-bff-types";

import * as getTeamService from "../../../src/services/team";
import * as getTeamMemberIdService from "../../../src/services/team/get-team-memberId";
import { vetting } from "../../../src/services/vetting";
import { getTestRequest, getTestRequestWithValidToken } from "../../helpers";

const teamCompanyId = 937739;
const baseQueryParams = {
  contractingType: "Employee",
  searchTerm: undefined,
};

describe("GET /team/persons success", () => {
  beforeEach(() => {
    jest.spyOn(vetting, "getTeamVettingDetails").mockResolvedValue({
      companyId: teamCompanyId,
      relationshipType: "Employee",
      companyUuid: "5c8b0d04-3797-4015-af11-35f171736976",
      workers: [
        {
          vettingStatus: VettingStatusTVS.InFlight,
          consentStatus: VettingConsentStatus.Granted,
          tradePersonId: "5c8b0d04-3797-4015-af11-35f171736976",
          externalId: "5c8b0d04-3797-4015-af11-35f171736976",
        },
        {
          vettingStatus: VettingStatusTVS.InFlight,
          consentStatus: VettingConsentStatus.Granted,
          tradePersonId: "921b94ee-1302-48b6-915c-a50924a19bd6",
          externalId: "921b94ee-1302-48b6-915c-a50924a19bd6",
        },
      ],
    });

    jest.spyOn(getTeamMemberIdService, "getTeamMemberId").mockResolvedValue({
      memberId: "5c8b0d04-3797-4015-af11-35f171736976",
    });

    jest
      .spyOn(vetting, "getCompanyVettingDetails")
      .mockResolvedValue(VettingStatus.Active);

    jest.spyOn(getTeamService.team, "getTeam").mockResolvedValue({
      data: [
        {
          id: "5c8b0d04-3797-4015-af11-35f171736976",
          fullName: "James Something",
          firstName: "James",
          lastName: "Something",
          dateOfBirth: "2012-04-22T23:00:00.000Z",
          lastUpdated: "2025-02-21T00:00:00.000Z",
          email: "<EMAIL>",
          mobilePhone: "01234567890",
          role: CompanyRole.Owner,
          phone: null,
          address: {
            line1: "123 Test Street",
            line2: "",
            county: "Test County",
            postalCode: "TE12 3ST",
            city: "Test City",
          },
        },
        {
          id: "921b94ee-1302-48b6-915c-a50924a19bd6",
          fullName: "James Something",
          firstName: "James",
          lastName: "Something",
          dateOfBirth: "2012-04-22T23:00:00.000Z",
          lastUpdated: "2025-02-21T00:00:00.000Z",
          email: "<EMAIL>",
          mobilePhone: "01234567890",
          role: CompanyRole.Owner,
          phone: null,
          address: {
            line1: "123 Test Street",
            line2: "",
            county: "Test County",
            postalCode: "TE12 3ST",
            city: "Test City",
          },
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 2,
        totalPagesCount: 3,
        totalRecordsCount: 2,
      },
    });
  });

  it("should return 200 and team data", async () => {
    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request.get(`/team/persons`).query(baseQueryParams);

    expect(response.status).toEqual(200);
    expect(response.body).toBeDefined();
    expect(response.body.data).toBeInstanceOf(Array);
    expect(response.body.data.length).toBeGreaterThan(0);
    expect(response.body.data[0]).toHaveProperty("id");
    expect(response.body.data[0]).toHaveProperty("fullName");
    expect(response.body.data[0]).toHaveProperty("vettingStatus");
    expect(response.body.data[0]).toHaveProperty("consentStatus");
    expect(response.body.pages).toBeDefined();
    expect(response.body.total).toBeDefined();
  });
});

describe("GET /team/persons reject", () => {
  it("should return 400 when x-trade-company-id header is missing", async () => {
    const response = await getTestRequest()
      .get(`/team/persons`)
      .query(baseQueryParams);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: `/team/persons?contractingType=Employee`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should send 400 and reject requests with missing query params", async () => {
    const response = await getTestRequestWithValidToken().get(`/team/persons`);

    expect(response.status).toEqual(400);
    expect(response.body.detail).toContain(
      "querystring must have required property 'contractingType'",
    );
    expect(response.body.instance).toEqual(`/team/persons`);
    expect(response.body.status).toEqual(400);
    expect(response.body.title).toEqual("Bad Request");
    expect(response.body.type).toEqual(
      "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    );
  });

  it("should send 400 and reject requests with invalid query params", async () => {
    const response = await getTestRequestWithValidToken()
      .get(`/team/persons`)
      .query({
        ...baseQueryParams,
        contractingType: "INVALID",
      });

    expect(response.status).toEqual(400);
    expect(response.body.detail).toContain(
      "querystring/contractingType must be equal to constant",
    );
    expect(response.body.instance).toEqual(
      `/team/persons?contractingType=INVALID`,
    );
    expect(response.body.status).toEqual(400);
    expect(response.body.title).toEqual("Bad Request");
    expect(response.body.type).toEqual(
      "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    );
  });
});
