import { Logger } from "@checkatrade/logging";
import { faker } from "@faker-js/faker";

import * as teamService from "../../../src/services/team";
import { tdsHttpClient } from "../../../src/services/trade-data-service";

const companyId = faker.number.int({ min: 1000, max: 9999 });
jest.mock("../../../src/services/team/get-team-memberId", () => ({
  getTeamMemberId: jest.fn().mockResolvedValue({ memberId: "mock-member-id" }),
}));

describe("GET /team/is-full-member-or-national-account - service", () => {
  const loggerMock = {
    error: jest.fn(),
  } as unknown as Logger;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return true when member is a full member or national account", async () => {
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "mock-member-id",
        },
      })
      .mockResolvedValueOnce({
        data: true,
      });

    const result = await teamService.team.getIsFullMemberOrNationalAccount(
      companyId,
      loggerMock,
    );

    expect(result).toBe(true);
    expect(tdsHttpClient.get).toHaveBeenCalledWith(
      `/members/mock-member-id/isFullMemberOrNationalAccount`,
    );
  });

  it("should return false when member is not a full member or national account", async () => {
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "mock-member-id",
        },
      })
      .mockResolvedValueOnce({
        data: false,
      });

    const result = await teamService.team.getIsFullMemberOrNationalAccount(
      companyId,
      loggerMock,
    );

    expect(result).toBe(false);
    expect(tdsHttpClient.get).toHaveBeenCalledWith(
      `/members/mock-member-id/isFullMemberOrNationalAccount`,
    );
  });

  it("should handle and log errors properly", async () => {
    const error = new Error("Failed to get is full member or national account");
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "mock-member-id",
        },
      })
      .mockRejectedValue(error);

    await expect(
      teamService.team.getIsFullMemberOrNationalAccount(companyId, loggerMock),
    ).rejects.toThrow("Failed to get is full member or national account");

    expect(loggerMock.error).toHaveBeenCalledWith(
      error,
      `Failed to get is full member or national account for company ${companyId}`,
    );
  });

  it("should return error when no memberId is returned", async () => {
    jest.spyOn(tdsHttpClient, "get").mockResolvedValueOnce({
      data: {},
    });

    const error = new Error("Expected required property");

    await expect(
      teamService.team.getIsFullMemberOrNationalAccount(companyId, loggerMock),
    ).rejects.toThrow(error);

    expect(loggerMock.error).toHaveBeenCalledWith(
      error,
      `Failed to get is full member or national account for company ${companyId}`,
    );
  });
});
