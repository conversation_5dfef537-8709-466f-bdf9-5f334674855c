import { AddTeamPersonsQuery } from "@checkatrade/trade-bff-types";

import * as addTeamPersonService from "../../../src/services/team";
import {
  TestRequest,
  getTestRequest,
  getTestRequestFromJWTPayload,
  getTestRequestWithValidToken,
  mockInvalidTokenJWT,
} from "../../helpers";

const teamPerson: AddTeamPersonsQuery = {
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>",
  nickname: "J<PERSON>",
  dateOfBirth: "1990-01-01T16:17:15.287Z",
  address: {
    line1: "123 Main St",
    line2: "Apt 101",
    city: "Springfield",
    county: "Simpson",
    postalCode: "12345",
  },
  email: "<EMAIL>",
  phoneNumber: "************",
  workCategories: [
    {
      id: "cat1",
      subCategories: [{ id: "sub1" }, { id: "sub2" }],
    },
  ],
  contractingType: "Subcontractor",
};

describe("POST /team success", () => {
  beforeEach(() => {
    jest.spyOn(addTeamPersonService.team, "addTeamPerson").mockResolvedValue({
      id: "123",
    });
  });

  it("should return 201 and the created team person ID", async () => {
    const request = getTestRequestWithValidToken();
    const response = await request.post("/team").send(teamPerson);

    expect(response.status).toEqual(201);
    expect(response.body).toEqual({ id: "123" });
  });
});

describe("POST /team reject", () => {
  it("should return 400 when x-trade-company-id header is missing", async () => {
    const response = await getTestRequest().post("/team").send(teamPerson);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: "/team",
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should send 400 and reject requests with an invalid request body", async () => {
    const response = await getTestRequest()
      .post("/team")
      .set("x-trade-company-id", "123")
      .send({});

    expect(response.status).toEqual(400);
    expect(response.body.detail).toContain(
      "body must have required property 'firstName'",
    );
    expect(response.body.instance).toEqual("/team");
    expect(response.body.status).toEqual(400);
    expect(response.body.title).toEqual("Bad Request");
    expect(response.body.type).toEqual(
      "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    );
  });
});

describe("Invalid token", () => {
  let request: TestRequest;
  beforeEach(async () => {
    request = await getTestRequestFromJWTPayload(
      mockInvalidTokenJWT({ companyId: 4321 }),
      4321,
    );
  });

  it("should return 401 on invalid token", async () => {
    const response = await request.post(`/team`).send(teamPerson);
    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      status: 401,
      title: "Unauthorized",
      detail: "Invalid auth token",
      instance: `/team`,
    });
  });
});
