import { Logger } from "@checkatrade/logging";
import { GetTeamInviteResponse } from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as teamService from "../../../src/services/team";
import { tdsHttpClient } from "../../../src/services/trade-data-service";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";
import { getTeamInviteMock } from "../../helpers/mocks/get-team-invite-mock";

const companyId = faker.number.int({ min: 1000, max: 9999 });
const inviteId = faker.string.uuid();

describe("GET /team/invite/:id - controller", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with team invite data", async () => {
    jest
      .spyOn(teamService.team, "getTeamInvite")
      .mockResolvedValue(getTeamInviteMock as GetTeamInviteResponse);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(`/team/invite/${inviteId}`);

    expect(response.status).toEqual(200);
    expect(response.body).toHaveProperty("id");
    expect(response.body).toHaveProperty("relationshipStatus");
    expect(response.body).toHaveProperty("expiryDate");
    expect(response.body).toHaveProperty("relationshipType");
    expect(response.body).toHaveProperty("emailAddress");
    expect(response.body).toHaveProperty("dateCreated");
    expect(response.body).toHaveProperty("dateUpdated");
  });

  it("should return NotFoundError if team invite is not found", async () => {
    jest.spyOn(teamService.team, "getTeamInvite").mockResolvedValue(undefined);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(`/team/invite/${inviteId}`);

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({
      detail: "Invite not found",
      instance: `/team/invite/${inviteId}`,
      status: 404,
      title: "Not Found",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404",
    });
  });

  it("should return 401 for invalid token", async () => {
    const request = getTestRequestWithInvalidToken();
    const response = await request.get(`/team/invite/${inviteId}`);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/team/invite/${inviteId}`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should return 500 when service throws an error", async () => {
    jest
      .spyOn(teamService.team, "getTeamInvite")
      .mockRejectedValue(new Error("Service error"));

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get(`/team/invite/${inviteId}`);

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: `/team/invite/${inviteId}`,
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });
});

describe("GET /team/invite/:id - service", () => {
  const loggerMock = {
    error: jest.fn(),
  } as unknown as Logger;

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return team invite data", async () => {
    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "1", // First call for getTeamMemberId returns memberId
        },
      })
      .mockResolvedValueOnce({
        data: getTeamInviteMock, // Second call for getTeamInvite returns mocked data
      });

    const result = await teamService.team.getTeamInvite(
      inviteId,
      companyId,
      loggerMock,
    );

    expect(result).toEqual(getTeamInviteMock);
    expect(tdsHttpClient.get).toHaveBeenCalledWith(
      expect.stringContaining(`/invites/${inviteId}`),
    );
  });

  it("should log and rethrow error when API call fails", async () => {
    const error = new Error("API error");
    jest.spyOn(tdsHttpClient, "get").mockRejectedValue(error);

    await expect(
      teamService.team.getTeamInvite(inviteId, companyId, loggerMock),
    ).rejects.toThrow(error);

    expect(loggerMock.error).toHaveBeenCalledWith(
      error,
      expect.stringContaining(`Failed to get team invite for id ${inviteId}`),
    );
  });
});
