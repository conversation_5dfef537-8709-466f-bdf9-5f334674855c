import { ApiError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";
import {
  GetTeamInvitesTradeDataResponse,
  VettingStatus,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as teamService from "../../../src/services/team";
import { tdsHttpClient } from "../../../src/services/trade-data-service";
import { vetting } from "../../../src/services/vetting";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";
import { getTeamInvitesMock } from "../../helpers/mocks/get-team-invites-mock";

const companyId = faker.number.int({ min: 1000, max: 9999 });

describe("GET /team/invites - controller", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with team invites data", async () => {
    jest
      .spyOn(teamService.team, "getTeamInvites")
      .mockResolvedValue(getTeamInvitesMock as GetTeamInvitesTradeDataResponse);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/team/invites");

    expect(response.status).toEqual(200);
    expect(response.body).toHaveProperty("data");
    expect(response.body).toHaveProperty("pages");
    expect(response.body).toHaveProperty("total");
    expect(response.body.data).toHaveLength(20);
    expect(response.body.data[0]).toHaveProperty("invite");
    expect(response.body.data[0]).toHaveProperty("childCompany");
  });

  it("should return 200 with empty data if no team invites are found", async () => {
    jest.spyOn(teamService.team, "getTeamInvites").mockResolvedValue(undefined);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/team/invites");

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      data: [],
      pages: 0,
      total: 0,
    });
  });

  it("should return 401 for invalid token", async () => {
    jest
      .spyOn(teamService.team, "getTeamInvites")
      .mockRejectedValue(new Error("Invalid token"));

    const request = getTestRequestWithInvalidToken();
    const response = await request.get("/team/invites");

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: "/team/invites",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should return InternalServerError if there is an error", async () => {
    jest
      .spyOn(teamService.team, "getTeamInvites")
      .mockRejectedValue(new Error("Internal server error"));

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/team/invites");

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      detail: "An unexpected error occurred",
      instance: "/team/invites",
      status: 500,
      title: "Internal Server Error",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
    });
  });

  it("should return ApiError if there is an error with this instance", async () => {
    const apiError = new ApiError("Error getting team invites", 400);
    jest.spyOn(teamService.team, "getTeamInvites").mockRejectedValue(apiError);

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/team/invites");

    expect(response.status).toEqual(500);
  });
});

describe("GET /team/invites - service", () => {
  const loggerMock = {
    error: jest.fn(),
  } as unknown as Logger;
  it("should return 200 with mocked data, mock http call", async () => {
    jest
      .spyOn(vetting, "getCompanyVettingDetails")
      .mockResolvedValue(VettingStatus.Active);

    jest
      .spyOn(tdsHttpClient, "get")
      .mockResolvedValueOnce({
        data: {
          memberId: "1", // First call for getTeamMemberId returns memberId
        },
      })
      .mockResolvedValueOnce({
        data: getTeamInvitesMock, // Second call for getTeamInvites returns mocked data
      });

    const testFunction = await teamService.team.getTeamInvites(
      companyId,
      loggerMock,
    );

    expect(testFunction).toEqual(
      getTeamInvitesMock as GetTeamInvitesTradeDataResponse,
    );
  });

  it("should return Error if getTeamMemberId returns undefined", async () => {
    jest.spyOn(tdsHttpClient, "get").mockResolvedValue({
      data: {
        memberId: "", // First call for getTeamMemberId returns empty memberId
      },
    });

    await expect(
      teamService.team.getTeamInvites(companyId, loggerMock),
    ).rejects.toThrow(`Failed to get member id for company ${companyId}`);

    expect(loggerMock.error).toHaveBeenCalledWith(
      new Error(`Failed to get member id for company ${companyId}`),
      "Failed to get team invites",
    );
  });
});
