import { faker } from "@faker-js/faker";

import * as teamService from "../../../src/services/team";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const companyId = faker.number.int({ min: 1000, max: 9999 });

describe("GET /team/counters - controller", () => {
  let getTeamSpy: jest.SpyInstance;
  let getTeamInvitesSpy: jest.SpyInstance;
  let getTeamPendingInvitesSpy: jest.SpyInstance;

  beforeEach(() => {
    getTeamSpy = jest.spyOn(teamService.team, "getTeam");
    getTeamInvitesSpy = jest.spyOn(teamService.team, "getTeamInvites");
    getTeamPendingInvitesSpy = jest.spyOn(
      teamService.team,
      "getTeamPendingInvites",
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with team counters data", async () => {
    getTeamSpy.mockResolvedValue({
      pagination: { totalRecordsCount: 5 },
      data: [],
    });

    getTeamInvitesSpy.mockResolvedValue({
      pagination: { totalRecordsCount: 3 },
      data: [],
    });

    getTeamPendingInvitesSpy.mockResolvedValue({
      pagination: { totalRecordsCount: 2 },
      data: [],
    });

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/team/counters");

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      totalEmployees: 5,
      totalSubcontractors: 3,
      totalInvites: 2,
    });

    expect(getTeamSpy).toHaveBeenCalledTimes(1);
    expect(getTeamInvitesSpy).toHaveBeenCalledTimes(1);
    expect(getTeamPendingInvitesSpy).toHaveBeenCalledTimes(1);
  });

  it("should handle rejected promises and return 0 for failed counters", async () => {
    getTeamSpy.mockRejectedValue(new Error("Failed to fetch subcontractors"));

    getTeamInvitesSpy.mockResolvedValue({
      pagination: { totalRecordsCount: 2 },
      data: [],
    });

    getTeamPendingInvitesSpy.mockResolvedValue({
      pagination: { totalRecordsCount: 1 },
      data: [],
    });

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/team/counters");

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      totalEmployees: 0,
      totalSubcontractors: 2,
      totalInvites: 1,
    });
  });

  it("should handle all rejected promises and still return counters with zeros", async () => {
    getTeamSpy.mockRejectedValue(new Error("Failed to fetch team"));
    getTeamInvitesSpy.mockRejectedValue(new Error("Failed to fetch invites"));
    getTeamPendingInvitesSpy.mockRejectedValue(
      new Error("Failed to fetch pending invites"),
    );

    const request = getTestRequestWithValidToken(companyId);
    const response = await request.get("/team/counters");

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      totalEmployees: 0,
      totalSubcontractors: 0,
      totalInvites: 0,
    });
  });

  it("should return 401 for invalid token", async () => {
    const request = getTestRequestWithInvalidToken();
    const response = await request.get("/team/counters");

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: "/team/counters",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
