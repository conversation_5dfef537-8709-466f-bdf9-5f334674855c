import { ApiError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";
import {
  InviteSubcontractorTradeDataResponse,
  RelationshipStatus,
  RelationshipType,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import { config } from "../../../src/config";
import * as teamService from "../../../src/services/team";
import { tdsHttpClient } from "../../../src/services/trade-data-service";
import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";
import { updateTeamInviteMock } from "../../helpers/mocks/update-team-invite-mock";

const mockRequestBody = {
  email: "<EMAIL>",
};
const memberId = faker.string.uuid();

describe("PUT /team/invites - controller", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with data", async () => {
    jest
      .spyOn(teamService.team, "inviteSubcontractor")
      .mockResolvedValue(
        updateTeamInviteMock as InviteSubcontractorTradeDataResponse,
      );

    const request = getTestRequestWithValidToken();
    const response = await request.post(`/team/invites`).send(mockRequestBody);

    expect(response.status).toEqual(201);
    expect(response.body).toEqual({
      id: updateTeamInviteMock.id,
      inviteUrl: `${config.websites.tradeSiteUrl}/my-team?inviteId=${updateTeamInviteMock.id}`,
    });
  });

  it("should return 401 for invalid token", async () => {
    jest
      .spyOn(teamService.team, "inviteSubcontractor")
      .mockRejectedValue(new Error("Invalid token"));

    const request = getTestRequestWithInvalidToken();
    const response = await request.post(`/team/invites`).send(mockRequestBody);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Invalid auth token",
      instance: `/team/invites`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should return InternalServerError if there is an error", async () => {
    jest
      .spyOn(teamService.team, "inviteSubcontractor")
      .mockRejectedValue(new ApiError("Error creating invite"));

    const request = getTestRequestWithValidToken();
    const response = await request.post(`/team/invites`).send(mockRequestBody);

    expect(response.status).toEqual(500);
  });
});

describe("PUT /team/invites - service", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const loggerMock = {
    error: jest.fn(),
  } as unknown as Logger;

  it("should return 200 with mocked data, mock http call", async () => {
    const mock: InviteSubcontractorTradeDataResponse = {
      id: "123456789",
      relationshipStatus: RelationshipStatus.Pending,
      expiryDate: "2026-12-31T23:59:59.999Z",
      relationshipType: RelationshipType.Subcontractor,
      emailAddress: "<EMAIL>",
      dateCreated: new Date().toISOString(),
      dateUpdated: new Date().toISOString(),
      parentMemberId: memberId,
      childMemberId: undefined,
    };

    jest.spyOn(tdsHttpClient, "get").mockResolvedValueOnce({
      data: { memberId },
    });
    jest.spyOn(tdsHttpClient, "post").mockResolvedValueOnce({
      data: mock,
    });

    const response = await teamService.team.inviteSubcontractor(
      mockRequestBody,
      TEST_COMPANY_ID,
      loggerMock,
    );

    expect(response).toEqual(mock);
  });

  it("should return Error if httpClient.put returns error", async () => {
    const error = new Error("Error");

    jest.spyOn(tdsHttpClient, "get").mockResolvedValueOnce({
      data: { memberId },
    });
    jest.spyOn(tdsHttpClient, "post").mockRejectedValue(error);

    await expect(
      teamService.team.inviteSubcontractor(
        mockRequestBody,
        TEST_COMPANY_ID,
        loggerMock,
      ),
    ).rejects.toThrow("Error");

    expect(loggerMock.error).toHaveBeenCalledWith(
      error,
      "Failed to create subcontractor invite",
    );
  });
});
