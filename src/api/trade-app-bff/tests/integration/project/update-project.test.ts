import {
  ProjectStatus,
  ProjectTypes,
  projectSDKsingleton,
} from "@checkatrade/project-sdk";
import { faker } from "@faker-js/faker";

import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

describe("PATCH /project/:projectId", () => {
  let projectId: string;
  let companyId: number;

  const createTestUpdateData = (): ProjectTypes.Api.Update.Body => ({
    projectName: faker.commerce.productName(),
    startDate: "2023-02-15",
    endDate: "2023-11-30",
  });

  beforeEach(() => {
    jest.clearAllMocks();
    projectId = faker.string.uuid();
    companyId = faker.number.int({ min: 1000, max: 9999 });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("When token is valid", () => {
    it("should return 200 on successful project update", async () => {
      const updateData = createTestUpdateData();

      const updatedProject: ProjectTypes.Api.Update.Response = {
        id: projectId,
        projectName: updateData.projectName as string,
        jobId: faker.string.uuid(),
        startDate: updateData.startDate as string,
        endDate: updateData.endDate as string,
        cost: faker.number.int({ min: 500, max: 5000 }),
        status: ProjectStatus.Draft,
        album: null,
        review: null,
      };

      jest
        .spyOn(projectSDKsingleton.public.connector, "patch")
        .mockResolvedValueOnce({ data: updatedProject });

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request
        .patch(`/project/${projectId}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(updatedProject);
    });

    it("should return 500 when a generic error occurs", async () => {
      const updateData = createTestUpdateData();

      jest
        .spyOn(projectSDKsingleton.public, "updateProject")
        .mockRejectedValueOnce(new Error("Internal error"));

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request
        .patch(`/project/${projectId}`)
        .send(updateData);

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        detail: "An unexpected error occurred",
        instance: `/project/${projectId}`,
        status: 500,
        title: "Internal Server Error",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
      });
    });
  });

  describe("When token is invalid", () => {
    it("should return 401 unauthorized", async () => {
      const updateData = createTestUpdateData();
      const request = await getTestRequestWithInvalidToken();

      const sdkSpy = jest.spyOn(projectSDKsingleton.public, "updateProject");

      const response = await request
        .patch(`/project/${projectId}`)
        .send(updateData);

      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        detail: "Invalid auth token",
        instance: `/project/${projectId}`,
        status: 401,
        title: "Unauthorized",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      });
      expect(sdkSpy).not.toHaveBeenCalled();
    });
  });
});
