import {
  ProjectStatus,
  ProjectTypes,
  projectSD<PERSON>singleton,
} from "@checkatrade/project-sdk";
import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

describe("POST /project", () => {
  let projectId: string;
  let companyId: number;

  const createTestProjectData = (): ProjectTypes.Api.Create.Body => ({
    projectName: faker.commerce.productName(),
    jobId: faker.string.uuid(),
    startDate: "2023-01-15",
    endDate: "2023-12-31",
    cost: faker.number.int({ min: 500, max: 5000 }),
    albumId: null,
    reviewId: null,
    companyId: TEST_COMPANY_ID,
  });

  beforeEach(() => {
    jest.clearAllMocks();
    projectId = faker.string.uuid();
    companyId = faker.number.int({ min: 1000, max: 9999 });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("When token is valid", () => {
    it("should return 201 on successful project creation", async () => {
      const projectData = createTestProjectData();

      const createdProject: ProjectTypes.Api.Create.Response = {
        id: projectId,
        projectName: projectData.projectName as string,
        jobId: projectData.jobId as string,
        startDate: projectData.startDate as string,
        endDate: projectData.endDate as string,
        cost: projectData.cost as number,
        status: ProjectStatus.Draft,
        album: null,
        review: null,
      };

      jest
        .spyOn(projectSDKsingleton.public.connector, "post")
        .mockResolvedValueOnce({ data: createdProject });

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request.post("/project").send(projectData);

      expect(response.status).toBe(201);
      expect(response.body).toEqual(createdProject);
    });

    it("should return 500 when a generic error occurs", async () => {
      const projectData = createTestProjectData();

      jest
        .spyOn(projectSDKsingleton.public, "postProject")
        .mockRejectedValueOnce(new Error("Internal error"));

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request.post("/project").send(projectData);

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        detail: "An unexpected error occurred",
        instance: "/project",
        status: 500,
        title: "Internal Server Error",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
      });
    });
  });

  describe("When token is invalid", () => {
    it("should return 401 unauthorized", async () => {
      const projectData = createTestProjectData();
      const request = await getTestRequestWithInvalidToken();

      const response = await request.post("/project").send(projectData);

      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        detail: "Invalid auth token",
        instance: "/project",
        status: 401,
        title: "Unauthorized",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      });
    });
  });
});
