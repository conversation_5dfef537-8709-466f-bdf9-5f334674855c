import { project<PERSON><PERSON><PERSON><PERSON> } from "@checkatrade/project-sdk";
import { faker } from "@faker-js/faker";

import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const getUrl = (projectId: string) => `/project/${projectId}`;

describe("DELETE project", () => {
  let projectId: string;
  let companyId: number;

  beforeEach(() => {
    jest.clearAllMocks();
    projectId = faker.string.uuid();
    companyId = faker.number.int({ min: 1000, max: 9999 });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("When token is valid", () => {
    it("should return 204 on successful delete", async () => {
      jest
        .spyOn(projectSDKsingleton.public.connector, "delete")
        .mockResolvedValueOnce({ data: undefined });

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request.delete(getUrl(projectId));

      expect(response.status).toBe(204);
    });

    it("should return 500 when a generic error occurs", async () => {
      jest
        .spyOn(projectSDKsingleton.public.connector, "delete")
        .mockRejectedValueOnce(new Error("Internal error"));

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request.delete(getUrl(projectId));

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        detail: "An unexpected error occurred",
        instance: `/project/${projectId}`,
        status: 500,
        title: "Internal Server Error",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
      });
    });
  });

  describe("When token is invalid", () => {
    it("should return 401 unauthorized", async () => {
      const request = await getTestRequestWithInvalidToken();

      const sdkSpy = jest.spyOn(projectSDKsingleton.public, "deleteProject");

      const response = await request.delete(getUrl(projectId));

      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        detail: "Invalid auth token",
        instance: `/project/${projectId}`,
        status: 401,
        title: "Unauthorized",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      });
      expect(sdkSpy).not.toHaveBeenCalled();
    });
  });
});
