import {
  ProjectStatus,
  ProjectTypes,
  projectSDKsingleton,
} from "@checkatrade/project-sdk";
import { faker } from "@faker-js/faker";

import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const getMockProject = (
  projectId = faker.string.uuid(),
): ProjectTypes.Api.Get.Response => ({
  id: projectId,
  status: ProjectStatus.Draft,
  jobId: faker.string.uuid(),
  projectName: faker.commerce.productName(),
  startDate: "2023-02-15",
  endDate: "2023-11-30",
  cost: faker.number.int({ min: 500, max: 5000 }),
  album: null,
  review: null,
});

describe("GET /project/:projectId", () => {
  const projectId = faker.string.uuid();
  let companyId: number;

  beforeEach(() => {
    jest.clearAllMocks();
    companyId = faker.number.int({ min: 1000, max: 9999 });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("When token is valid", () => {
    it("should return 200 and the project data", async () => {
      const projectMock = getMockProject(projectId);
      jest
        .spyOn(projectSDKsingleton.public.connector, "get")
        .mockResolvedValueOnce({ data: projectMock });

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request.get(`/project/${projectId}`);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(projectMock);
    });

    it("should return 500 when a generic error occurs", async () => {
      jest
        .spyOn(projectSDKsingleton.public, "getProject")
        .mockRejectedValueOnce(new Error("Internal error"));

      const request = await getTestRequestWithValidToken(companyId);

      const response = await request.get(`/project/${projectId}`);

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        detail: "An unexpected error occurred",
        instance: `/project/${projectId}`,
        status: 500,
        title: "Internal Server Error",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500",
      });
    });
  });

  describe("When token is invalid", () => {
    it("should return 401 unauthorized", async () => {
      const request = await getTestRequestWithInvalidToken();

      const response = await request.get(`/project/${projectId}`);

      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        detail: "Invalid auth token",
        instance: `/project/${projectId}`,
        status: 401,
        title: "Unauthorized",
        type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
      });
    });
  });
});
