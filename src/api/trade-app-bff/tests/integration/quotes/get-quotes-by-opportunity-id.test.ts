import { ForbiddenError } from "@checkatrade/errors";

import { getTestRequestWithValidToken } from "../../helpers";
import {
  mockQuotingSdkTrade,
  quotingSdkTradeMock,
} from "../../helpers/mocks/quoting-sdk.mock";

describe("GET all quotes for associated job ID", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it("should call the sdk with the correct params and return 200 with expected response", async () => {
    // Arrange
    const opportunityId = "019483f4-5407";
    const companyId = 1234;
    const request = getTestRequestWithValidToken(companyId);
    mockQuotingSdkTrade();

    const expectedResponse = {
      data: [
        {
          id: "HZyX2OudAxodYPv13tvM",
          title: "Alarms / Security - SL1 6PH",
          createdAt: "2025-03-05T13:59:43.927Z",
          totalAmount: 40,
          type: "QUOTE",
          status: "DRAFT",
          reference: "78",
          pdfFileUrl: "/trade/quotes/HZyX2OudAxodYPv13tvM/document",
          jobId: "019483f4-5407",
          companyId: 1234,
          jobIdV2: "019483f4-5407",
          opportunityId,
        },
      ],
      lastVisible: "companies/337953/quotes/HZyX2OudAxodYPv13tvM",
    };

    quotingSdkTradeMock.getQuotesByOpportunityId.mockResolvedValue(
      expectedResponse,
    );

    // Act
    const response = await request
      .get(`/quotes/opportunity/${opportunityId}`)
      .send();

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual(expectedResponse);
  });

  it("should result in 200 if no quotes are found", async () => {
    // Arrange
    const opportunityId = "019483f4-5407";
    const companyId = 1234;
    const request = getTestRequestWithValidToken(companyId);
    mockQuotingSdkTrade();

    const expectedResponse = {
      data: [],
      lastVisible: "companies/337953/quotes/HZyX2OudAxodYPv13tvM",
    };

    quotingSdkTradeMock.getQuotesByOpportunityId.mockResolvedValue(
      expectedResponse,
    );

    // Act
    const response = await request
      .get(`/quotes/opportunity/${opportunityId}`)
      .send();

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual(expectedResponse);
  });

  it("should return 403 when the provided jobId does not belong to the trade's company", async () => {
    // Arrange
    const companyId = 1234;
    const request = getTestRequestWithValidToken(companyId);
    mockQuotingSdkTrade();

    quotingSdkTradeMock.getQuotesByOpportunityId.mockRejectedValue(
      new ForbiddenError(
        "CompanyId from the jwt token does not match the quote's companyId",
      ),
    );
    // Act
    const response = await request.get(`/quotes/opportunity/wrongjobid`).send();

    // Assert
    expect(response.body.detail).toBe(
      "CompanyId from the jwt token does not match the quote's companyId",
    );
    expect(response.status).toEqual(403);
  });
});
