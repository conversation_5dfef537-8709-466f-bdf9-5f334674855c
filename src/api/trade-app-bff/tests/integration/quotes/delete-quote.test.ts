import { chatSDK } from "@checkatrade/chat-sdk";
import { PaymentRequestStatus, paymentSDK } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

import { getTestRequestWithValidToken, mockJob } from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";
import {
  mockQuotingSdkTrade,
  quotingSdkTradeMock,
} from "../../helpers/mocks/quoting-sdk.mock";

const mockSmartMessage = { id: "test-smart-message" };
const companyId = 123456;
const mockQuote = {
  id: faker.string.alphanumeric(10),
  title: "Alarms / Security - SL1 6PH",
  createdAt: "2025-03-05T13:59:43.927Z",
  totalAmount: 40,
  type: "QUOTE",
  status: "DRAFT",
  reference: "78",
  pdfFileUrl: "/trade/quotes/HZyX2OudAxodYPv13tvM/document",
  jobId: "019483f4-5407",
  companyId: 1234,
  jobIdV2: faker.string.uuid(),
};
const mockPaymentSdk = jest.mocked(paymentSDK);

const generateMockPayment = () => {
  return {
    id: faker.string.uuid(),
    companyId: companyId.toString(),
    jobId: mockQuote.jobIdV2,
    reference: faker.string.alphanumeric(),
    consumerId: faker.string.uuid(),
    consumerEmail: faker.internet.email(),
    paymentLinkId: faker.string.uuid(),
    opportunityId: mockJob.opportunityId,
    quoteId: mockQuote.id,
    paymentUrl: null,
    currency: faker.finance.currencyCode(),
    expiryDate: faker.date.future(),
    dueDate: faker.date.future(),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    status: PaymentRequestStatus.PaymentPending,
    totalAmount: faker.number.int(),
    tradeAmount: faker.number.int(),
    commissionAmount: faker.number.int(),
  };
};

describe("DELETE quote", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(chatSDK, "getChannelById").mockResolvedValue({
      id: mockJob.opportunityId,
    } as unknown as ReturnType<(typeof chatSDK)["getChannelById"]>);

    mockJobsSdkTrade();
    jobsSdkTradeMock.getJob.mockResolvedValue(mockJob);

    mockQuotingSdkTrade();
    quotingSdkTradeMock.getQuote.mockResolvedValue(mockQuote);
  });

  it("should successfully delete quote and send chat", async () => {
    // Arrange
    const now = new Date();

    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    smartMessageSpy.mockResolvedValue(mockSmartMessage as never);

    const request = getTestRequestWithValidToken(companyId);

    quotingSdkTradeMock.deleteQuote.mockResolvedValue({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });
    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockResolvedValue(
      {
        data: [],
        pagination: {
          page: 1,
          size: 0,
          pageCount: 0,
          total: 0,
          hasNext: false,
          hasPrevious: false,
        },
      },
    );

    // Act
    const response = await request.delete(`/quotes/${mockQuote.id}`).send();

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });

    expect(quotingSdkTradeMock.deleteQuote).toHaveBeenCalledTimes(1);

    expect(smartMessageSpy).toHaveBeenCalledTimes(1);
    expect(smartMessageSpy).toHaveBeenCalledWith({
      channelId: mockJob.opportunityId,
      senderId: companyId,
      smartType: "QUOTE_DELETE",
      text: "The Trade has withdrawn the quote",
      quoteId: mockQuote.id,
      quote: {
        id: mockQuote.id,
        total: mockQuote.totalAmount,
        title: mockQuote.title,
      },
      logger: expect.any(Object),
    });

    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequest.patchPaymentRequestCancel,
    ).not.toHaveBeenCalled();
  });

  it("should successfully cancel associated payments", async () => {
    // Arrange
    const now = new Date();

    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    smartMessageSpy.mockResolvedValue(mockSmartMessage as never);

    const request = getTestRequestWithValidToken(companyId);

    quotingSdkTradeMock.deleteQuote.mockResolvedValue({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });
    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockResolvedValueOnce(
      {
        data: Array.from({ length: 2 }, generateMockPayment),
        pagination: {
          page: 1,
          size: 2,
          pageCount: 2,
          total: 3,
          hasNext: true,
          hasPrevious: false,
        },
      },
    );
    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockResolvedValueOnce(
      {
        data: Array.from({ length: 1 }, generateMockPayment),
        pagination: {
          page: 2,
          size: 1,
          pageCount: 2,
          total: 3,
          hasNext: false,
          hasPrevious: true,
        },
      },
    );

    // Act
    const response = await request.delete(`/quotes/${mockQuote.id}`).send();

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });

    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledTimes(2);
    expect(
      mockPaymentSdk.trade.paymentRequest.patchPaymentRequestCancel,
    ).toHaveBeenCalledTimes(3);
  });

  it("should successfully complete when pagination hasNext not provided", async () => {
    // Arrange
    const now = new Date();

    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    smartMessageSpy.mockResolvedValue(mockSmartMessage as never);

    const request = getTestRequestWithValidToken(companyId);

    quotingSdkTradeMock.deleteQuote.mockResolvedValue({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });
    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockResolvedValue(
      {
        data: Array.from({ length: 9 }, generateMockPayment),
        pagination: { page: 1, size: 9, pageCount: 1, total: 9 },
      },
    );

    // Act
    const response = await request.delete(`/quotes/${mockQuote.id}`).send();

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });

    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequest.patchPaymentRequestCancel,
    ).toHaveBeenCalledTimes(9);
  });

  it("should successfully attempt to cancel all payments if cancelling payment fails", async () => {
    // Arrange
    const now = new Date();

    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    smartMessageSpy.mockResolvedValue(mockSmartMessage as never);

    const request = getTestRequestWithValidToken(companyId);

    quotingSdkTradeMock.deleteQuote.mockResolvedValue({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });
    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockResolvedValue(
      {
        data: Array.from({ length: 2 }, generateMockPayment),
        pagination: {
          page: 1,
          size: 2,
          pageCount: 2,
          total: 3,
          hasNext: false,
          hasPrevious: false,
        },
      },
    );
    mockPaymentSdk.trade.paymentRequest.patchPaymentRequestCancel.mockRejectedValue(
      new Error(),
    );

    // Act
    const response = await request.delete(`/quotes/${mockQuote.id}`).send();

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });

    expect(
      mockPaymentSdk.trade.paymentRequest.patchPaymentRequestCancel,
    ).toHaveBeenCalledTimes(2);
  });

  it("should successfully complete and break out if get payments fails", async () => {
    // Arrange
    const now = new Date();

    const smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
    smartMessageSpy.mockResolvedValue(mockSmartMessage as never);

    const request = getTestRequestWithValidToken(companyId);

    quotingSdkTradeMock.deleteQuote.mockResolvedValue({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });
    mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote.mockRejectedValue(
      new Error(),
    );

    // Act
    const response = await request.delete(`/quotes/${mockQuote.id}`).send();

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      quoteId: mockQuote.id,
      deletedAt: now.toString(),
    });

    expect(
      mockPaymentSdk.trade.paymentRequests.getPaymentRequestsForQuote,
    ).toHaveBeenCalledTimes(1);
    expect(
      mockPaymentSdk.trade.paymentRequest.patchPaymentRequestCancel,
    ).toHaveBeenCalledTimes(0);
  });
});
