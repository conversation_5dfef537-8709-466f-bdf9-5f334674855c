import { TestRequest, getTestRequest } from "../../helpers";
import { mockJob } from "../../helpers";

describe("GET Archived Job", () => {
  let request: TestRequest;

  it("should return 400 when no x-trade-company-id provided", async () => {
    request = await getTestRequest();

    const response = await request.get(`/archived-jobs/${mockJob.id}`);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: `/archived-jobs/${mockJob.id}`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should send 401 and reject requests without a valid auth token", async () => {
    request = await getTestRequest();

    const response = await request
      .set("x-trade-company-id", "12345")
      .get(`/archived-jobs/${mockJob.id}`);

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Missing authorization header",
      instance: `/archived-jobs/${mockJob.id}`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });
});
