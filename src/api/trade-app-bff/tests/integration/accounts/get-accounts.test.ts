import { faker } from "@faker-js/faker";

import * as GetCompany from "../../../src/services/firebase/firestore/get-company";
import { getTestRequest, getTestRequestFromJWTPayload } from "../../helpers";

describe("Accounts API", () => {
  it("should return 401, if no token is provided", async () => {
    const response = await getTestRequest().get("/accounts");

    expect(response.statusCode).toBe(401);
  });

  it("should return 200, if token is valid", async () => {
    jest.spyOn(GetCompany, "getCompanyByIds").mockResolvedValue([
      {
        id: 1234,
        tradeId: 1000,
        name: "Company 1234",
        accountEmail: faker.internet.email(),
      },
    ]);

    jest.spyOn(GetCompany, "getCompaniesByIds").mockResolvedValue([
      {
        id: 1234,
        companyId: 1234,
        details: {
          logo: {
            url: "https://s3-eu-west-1.amazonaws.com/cats3.checkatrade.com/Photos/100160/O/********-686b-4111-aab7-cb950d2a3e7a.jpg",
          },
        },
      },
    ]);

    const tokenPayload = {
      sub: "test-id",
      email: "<EMAIL>",
      accounts: [
        {
          user_id: "test-id",
          company_id: "1234",
          vetting_status: 2,
          sf_vetting_status: "pending",
        },
        {
          user_id: "test-id-2",
          company_id: "5678",
          vetting_status: 3,
          sf_vetting_status: "3",
        },
      ],
    };

    const response =
      await getTestRequestFromJWTPayload(tokenPayload).get("/accounts");

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual([
      {
        id: "test-id",
        companyId: 1234,
        traderId: 1000,
        companyName: "Company 1234",
        companyLogo:
          "https://s3-eu-west-1.amazonaws.com/cats3.checkatrade.com/Photos/100160/O/********-686b-4111-aab7-cb950d2a3e7a.jpg",
        vettingStatus: 2,
        salesforceVettingStatus: "pending",
      },
      {
        id: "test-id-2",
        companyId: 5678,
        traderId: undefined,
        companyName: undefined,
        vettingStatus: 3,
        salesforceVettingStatus: "3",
      },
    ]);
  });
});
