import { ForbiddenError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";
import { GetExperiencesResponse } from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as adManagerService from "../../../src/services/ad-manager";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const companyId = faker.number.int({ min: 1000, max: 9999 });

describe("GET /ad-manager/experiences - controller", () => {
  let getExperiencesSpy: jest.SpyInstance;

  beforeEach(() => {
    getExperiencesSpy = jest.spyOn(
      adManagerService.adManager,
      "getExperiences",
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with experiences", async () => {
    // Arrange
    const mockExperiences: GetExperiencesResponse = {
      experiences: [
        {
          experienceId: 4876,
          currencyType: 1,
          name: "Onsite Display",
          minBid: 0.5,
          maxBid: 5,
          bidStrategyPlayer: 1,
          bidStrategyLeader: 1,
          bidStrategyCompetitor: 1,
        },
        {
          experienceId: 4877,
          currencyType: 1,
          name: "Onsite Sponsored Listings",
          minBid: 0.6,
          maxBid: 5,
          bidStrategyPlayer: 1,
          bidStrategyLeader: 1,
          bidStrategyCompetitor: 1,
        },
      ],
    };

    getExperiencesSpy.mockResolvedValue(mockExperiences);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request.get("/ad-manager/experiences");

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockExperiences);
    expect(getExperiencesSpy).toHaveBeenCalledTimes(1);
  });

  it("should return 401 when not authenticated", async () => {
    // Arrange
    const request = getTestRequestWithInvalidToken();

    // Act
    const response = await request.get("/ad-manager/experiences");

    // Assert
    expect(response.status).toBe(401);
    expect(getExperiencesSpy).not.toHaveBeenCalled();
  });

  it("should return 403 when user does not have access to the company", async () => {
    // Arrange
    getExperiencesSpy.mockImplementation(() => {
      throw new ForbiddenError("Only the account owner can get experiences");
    });

    const request = getTestRequestWithValidToken(companyId + 1);

    // Act
    const response = await request.get("/ad-manager/experiences");

    // Assert
    expect(response.status).toBe(403);
    expect(response.body.detail).toContain(
      "Only the account owner can get experiences",
    );
  });
});

describe("getExperiences - service", () => {
  let mockLogger: Logger;
  let getExperiencesSpy: jest.SpyInstance;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis(),
    } as unknown as Logger;

    // Mock the getExperiences function directly
    getExperiencesSpy = jest.spyOn(
      adManagerService.adManager,
      "getExperiences",
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return experiences when successful", async () => {
    // Arrange
    const mockExperiences: GetExperiencesResponse = {
      experiences: [
        {
          experienceId: 4876,
          currencyType: 1,
          name: "Onsite Display",
          minBid: 0.5,
          maxBid: 5,
          bidStrategyPlayer: 1,
          bidStrategyLeader: 1,
          bidStrategyCompetitor: 1,
        },
      ],
    };

    getExperiencesSpy.mockResolvedValue(mockExperiences);

    // Act
    const result = await adManagerService.adManager.getExperiences(mockLogger);

    // Assert
    expect(result).toEqual(mockExperiences);
    expect(getExperiencesSpy).toHaveBeenCalledWith(mockLogger);
  });

  it("should handle errors correctly", async () => {
    // Arrange
    getExperiencesSpy.mockRejectedValue(new Error("Test error"));

    // Act & Assert
    await expect(
      adManagerService.adManager.getExperiences(mockLogger),
    ).rejects.toThrow("Test error");
  });
});
