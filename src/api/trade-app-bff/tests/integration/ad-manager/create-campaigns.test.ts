import { Logger } from "@checkatrade/logging";
import {
  BidStrategy,
  CampaignType,
  CatSearchType,
  CreateCampaignsRequest,
  CreateCampaignsResponse,
  GeographyType,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as authHelpers from "../../../src/helpers/auth-helpers";
import * as adManagerService from "../../../src/services/ad-manager";
import {
  getTestRequestWithInvalidToken,
  getTestRequestWithValidToken,
} from "../../helpers";

const companyId = faker.number.int({ min: 1000, max: 9999 });

// Create a mock campaign request for reuse in tests
const createMockCampaignRequest = (): CreateCampaignsRequest => [
  {
    type: CampaignType.MDP_SPONSORED_SEARCH,
    maxBudget: 100,
    pausedUntil: null,
    budgetPeriod: "2023-05-01",
    category: { categoryId: 123, isSearchable: true },
    subCategories: [{ categoryId: 456, parentCategoryId: 123 }],
    geographies: [{ value: "AB12", type: GeographyType.PostcodeArea }],
    mdpSponsoredSearch: {
      primaryCampaignId: "campaign-123",
      searchType: CatSearchType.LISTING,
      bidStrategy: BidStrategy.Player,
    },
  },
];

describe("POST /ad-manager/campaigns - controller", () => {
  let createCampaignsSpy: jest.SpyInstance;
  let hasAccessToCompanySpy: jest.SpyInstance;

  beforeEach(() => {
    createCampaignsSpy = jest.spyOn(
      adManagerService.adManager,
      "createCampaigns",
    );
    hasAccessToCompanySpy = jest
      .spyOn(authHelpers, "hasAccessToCompany")
      .mockReturnValue(true);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 200 with campaign IDs", async () => {
    // Arrange
    const mockRequest = createMockCampaignRequest();
    const mockResponse: CreateCampaignsResponse = [
      "campaign-id-1",
      "campaign-id-2",
    ];

    createCampaignsSpy.mockResolvedValue(mockResponse);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .post(`/ad-manager/advertisers/${companyId}/campaigns`)
      .send(mockRequest);

    // Assert
    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockResponse);
    expect(createCampaignsSpy).toHaveBeenCalledWith(
      expect.anything(),
      companyId,
      mockRequest,
      expect.any(String), // email
      expect.any(String), // userId
    );
    expect(hasAccessToCompanySpy).toHaveBeenCalled();
  });

  it("should return 401 when not authenticated", async () => {
    // Arrange
    const mockRequest = createMockCampaignRequest();
    const request = getTestRequestWithInvalidToken();

    // Act
    const response = await request
      .post(`/ad-manager/advertisers/${companyId}/campaigns`)
      .send(mockRequest);

    // Assert
    expect(response.status).toBe(401);
    expect(createCampaignsSpy).not.toHaveBeenCalled();
  });

  it("should return 403 when user doesn't have access to the company", async () => {
    // Arrange
    const mockRequest = createMockCampaignRequest();

    // Mock hasAccessToCompany to return false
    hasAccessToCompanySpy.mockReturnValue(false);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .post(`/ad-manager/advertisers/${companyId}/campaigns`)
      .send(mockRequest);

    // Assert
    expect(response.status).toBe(403);
    expect(response.body).toHaveProperty(
      "detail",
      "Only the account owner can create campaigns",
    );
    expect(createCampaignsSpy).not.toHaveBeenCalled();
  });

  it("should return 400 when no campaigns are provided", async () => {
    // Arrange
    const mockRequest: CreateCampaignsRequest = [];

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .post(`/ad-manager/advertisers/${companyId}/campaigns`)
      .send(mockRequest);

    // Assert
    expect(response.status).toBe(400);
    expect(response.body).toHaveProperty("detail");
    expect(response.body.detail).toContain("fewer than 1 items");
    expect(createCampaignsSpy).not.toHaveBeenCalled();
  });

  it("should handle service errors and return appropriate status code", async () => {
    // Arrange
    const mockRequest = createMockCampaignRequest();

    // Mock a service error with a specific status code
    const errorResponse = {
      response: {
        status: 422,
        data: { message: "Invalid campaign data" },
      },
    };
    createCampaignsSpy.mockRejectedValue(errorResponse);

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .post(`/ad-manager/advertisers/${companyId}/campaigns`)
      .send(mockRequest);

    // Assert
    expect(response.status).toBe(422);
    expect(response.body).toHaveProperty("detail", "Invalid campaign data");
    expect(createCampaignsSpy).toHaveBeenCalled();
  });

  it("should return 403 when companyId in URL doesn't match the one in the header", async () => {
    // Arrange
    const mockRequest = createMockCampaignRequest();
    const differentCompanyId = companyId + 1000; // Use a different company ID

    const request = getTestRequestWithValidToken(companyId);

    // Act
    const response = await request
      .post(`/ad-manager/advertisers/${differentCompanyId}/campaigns`)
      .send(mockRequest);

    // Assert
    expect(response.status).toBe(403);
    expect(response.body).toHaveProperty(
      "detail",
      "Company ID mismatch between URL and header",
    );
    expect(createCampaignsSpy).not.toHaveBeenCalled();
  });
});

describe("createCampaigns - service", () => {
  let mockLogger: Logger;
  let createCampaignsSpy: jest.SpyInstance;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis(),
    } as unknown as Logger;

    // Mock the createCampaigns function directly
    createCampaignsSpy = jest.spyOn(
      adManagerService.adManager,
      "createCampaigns",
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return campaign IDs when successful", async () => {
    // Arrange
    const mockRequest = createMockCampaignRequest();
    const mockResponse: CreateCampaignsResponse = [
      "campaign-id-1",
      "campaign-id-2",
    ];

    createCampaignsSpy.mockResolvedValue(mockResponse);

    // Act
    const result = await adManagerService.adManager.createCampaigns(
      mockLogger,
      companyId,
      mockRequest,
      "<EMAIL>", // email
      "test-user-id", // userId
    );

    // Assert
    expect(result).toEqual(mockResponse);
    expect(createCampaignsSpy).toHaveBeenCalledWith(
      mockLogger,
      companyId,
      mockRequest,
      "<EMAIL>", // email
      "test-user-id", // userId
    );
  });

  it("should handle service errors with response data", async () => {
    // Arrange
    const mockRequest = createMockCampaignRequest();

    // Mock an error with response data
    const errorWithResponse = {
      response: {
        status: 422,
        data: { message: "Invalid campaign data" },
      },
      message: "Request failed with status code 422",
    };

    createCampaignsSpy.mockRejectedValue(errorWithResponse);

    // Act & Assert
    await expect(
      adManagerService.adManager.createCampaigns(
        mockLogger,
        companyId,
        mockRequest,
        "<EMAIL>", // email
        "test-user-id", // userId
      ),
    ).rejects.toMatchObject({
      response: {
        status: 422,
      },
    });
  });
});
