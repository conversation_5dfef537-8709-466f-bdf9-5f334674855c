import { StreamUserType, chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { faker } from "@faker-js/faker";
import nock from "nock";
import { Response } from "supertest";

import {
  TestRequest,
  getTestRequest,
  getTestRequestWithValidToken,
  nockCommsReportConsumerV1,
  nockCommsReportConsumerV2,
  nockGetChannels,
  nockTradeJob,
} from "../../helpers";

describe("POST /consumers/:consumerId/report", () => {
  let consumerId: string;
  let jobId: string;
  let flagLastMessage: jest.SpyInstance;

  beforeEach(() => {
    consumerId = faker.string.uuid();
    jobId = faker.string.uuid();
    flagLastMessage = jest.spyOn(chatSDK, "flagLastMessage");
    flagLastMessage.mockResolvedValue(true);
  });

  it("should return 400  when x-trade-company-id header is missing", async () => {
    const response = await getTestRequest()
      .post(`/consumers/${consumerId}/report`)
      .send({ jobId });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: "headers must have required property 'x-trade-company-id'",
      instance: `/consumers/${consumerId}/report`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should send 401 and reject requests without a valid auth token", async () => {
    const response = await getTestRequest()
      .post(`/consumers/${consumerId}/report`)
      .set("x-trade-company-id", "123")
      .send({ jobId });

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Missing authorization header",
      instance: `/consumers/${consumerId}/report`,
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  it("should send 400 and reject with invalid job id", async () => {
    const response = await getTestRequest()
      .post(`/consumers/${consumerId}/report`)
      .set("x-trade-company-id", "123")
      .send({ jobId: "invalid" });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: 'body/jobId must match format "uuid"',
      instance: `/consumers/${consumerId}/report`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should send 400 and reject with invalid consumer id", async () => {
    const response = await getTestRequest()
      .post(`/consumers/invalid/report`)
      .set("x-trade-company-id", "123")
      .send({ jobId });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      detail: 'params/consumerId must match format "uuid"',
      instance: `/consumers/invalid/report`,
      status: 400,
      title: "Bad Request",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400",
    });
  });

  it("should return 404, when job id is valid, but the consumer is not of the job", async () => {
    nockTradeJob({ jobId, consumerId: faker.string.uuid() });

    const response = await getTestRequestWithValidToken()
      .post(`/consumers/${consumerId}/report`)
      .send({ jobId });

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({
      detail: "The requested resource was not found",
      instance: `/consumers/${consumerId}/report`,
      status: 404,
      title: "Not Found",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404",
    });
  });

  describe("when data is valid and trade is authenticated (v1)", () => {
    let request: TestRequest;
    let companyId: number;
    let opportunityId: string;
    let reportNock: nock.Scope;
    let response: Response;
    let smartMessageSpy: jest.SpyInstance;
    let updateChannelSpy: jest.SpyInstance;
    let freezeChannelSpy: jest.SpyInstance;

    beforeEach(async () => {
      process.env.COMMS_REPORT_USER_VERSION = "V1";
      companyId = faker.number.int(1000);
      opportunityId = faker.string.uuid();
      request = getTestRequestWithValidToken(companyId);

      nockTradeJob({ jobId, consumerId, opportunityId });

      reportNock = nockCommsReportConsumerV1({
        jobId,
        channelId: opportunityId,
        reporter: { id: String(companyId), type: StreamUserType.TRADE },
        user: { id: consumerId, type: StreamUserType.CONSUMER },
      });

      freezeChannelSpy = jest.spyOn(chatSDK, "freezeChannel");
      freezeChannelSpy.mockResolvedValue(true);
      updateChannelSpy = jest.spyOn(chatSDK, "updateChannel");
      updateChannelSpy.mockResolvedValue(true);
      smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
      smartMessageSpy.mockResolvedValue(true);

      nockGetChannels();

      response = await request
        .post(`/consumers/${consumerId}/report`)
        .send({ jobId });
    });

    it("should return 200", () => {
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({});
    });

    it("should send correct payload to comms service", () => {
      expect(reportNock.isDone()).toBeTruthy();
    });

    it("should freeze the correct channel", () => {
      expect(freezeChannelSpy).toHaveBeenCalled();
    });

    it("should flag the last message", () => {
      expect(flagLastMessage).toHaveBeenCalledWith({
        channelId: opportunityId,
        logger: expect.any(Object),
        reporterId: String(companyId),
        userId: consumerId,
      });
    });

    it("should update channel", () => {
      expect(updateChannelSpy).toHaveBeenCalledWith({
        channelId: opportunityId,
        data: { frozenFromBlock: true },
        logger: expect.anything(),
      });
    });

    it("should send a smart message", () => {
      expect(smartMessageSpy).toHaveBeenCalledWith({
        channelId: opportunityId,
        jobId,
        senderId: String(companyId),
        smartType: SmartMessageType.BLOCKED,
        text: "Chat unavailable: You can no longer send or receive messages.",
        logger: expect.anything(),
      });
    });
  });

  describe("when data is valid and trade is authenticated (v2)", () => {
    let request: TestRequest;
    let companyId: number;
    let opportunityId: string;
    let reportNock: nock.Scope;
    let response: Response;
    let smartMessageSpy: jest.SpyInstance;
    let updateChannelSpy: jest.SpyInstance;
    let freezeChannelSpy: jest.SpyInstance;

    beforeEach(async () => {
      process.env.COMMS_REPORT_USER_VERSION = "V2";
      companyId = faker.number.int(1000);
      opportunityId = faker.string.uuid();
      request = getTestRequestWithValidToken(companyId);

      nockTradeJob({ jobId, consumerId, opportunityId });

      reportNock = nockCommsReportConsumerV2({
        jobId,
        channelId: opportunityId,
        reporter: { id: String(companyId), type: StreamUserType.TRADE },
        user: { id: consumerId, type: StreamUserType.CONSUMER },
      });

      jest.spyOn(chatSDK, "updateChannel");
      freezeChannelSpy = jest.spyOn(chatSDK, "freezeChannel");
      freezeChannelSpy.mockResolvedValue(true);

      nockGetChannels();

      smartMessageSpy = jest.spyOn(chatSDK, "sendSmartMessage");
      smartMessageSpy.mockResolvedValue(true);
      updateChannelSpy = jest.spyOn(chatSDK, "updateChannel");
      updateChannelSpy.mockResolvedValue(true);

      response = await request
        .post(`/consumers/${consumerId}/report`)
        .send({ jobId });
    });

    it("should return 200", () => {
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({});
    });

    it("should send correct payload to comms service", () => {
      expect(reportNock.isDone()).toBeTruthy();
    });

    it("should freeze the correct channel", () => {
      expect(freezeChannelSpy).toHaveBeenCalled();
    });

    it("should flag the last message", () => {
      expect(flagLastMessage).toHaveBeenCalledWith({
        channelId: opportunityId,
        logger: expect.any(Object),
        reporterId: String(companyId),
        userId: consumerId,
      });
    });

    it("should update channel", () => {
      expect(updateChannelSpy).toHaveBeenCalledWith({
        channelId: opportunityId,
        data: { frozenFromBlock: true },
        logger: expect.anything(),
      });
    });

    it("should send a smart message", () => {
      expect(smartMessageSpy).toHaveBeenCalledWith({
        channelId: opportunityId,
        jobId,
        senderId: String(companyId),
        smartType: SmartMessageType.BLOCKED,
        text: "Chat unavailable: You can no longer send or receive messages.",
        logger: expect.anything(),
      });
    });
  });
});
