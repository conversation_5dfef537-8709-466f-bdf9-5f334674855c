import { faker } from "@faker-js/faker";

import {
  TEST_COMPANY_ID,
  TestRequest,
  getTestRequest,
  getTestRequestFromJWTPayload,
  mockValidTokenJWT,
} from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

describe("GET Cancel reasons", () => {
  let request: TestRequest;
  let getCancelReasonsSpy: jest.SpyInstance;

  const expectedResponse = [
    {
      id: faker.string.uuid(),
      reason: "Reason 1",
    },
    {
      id: faker.string.uuid(),
      reason: "Reason 2",
      customReason: true,
    },
  ];

  it("should send 401 and cancel requests without a valid auth token", async () => {
    const request = await getTestRequest().set(
      "x-trade-company-id",
      TEST_COMPANY_ID,
    );
    const response = await request.get("/job/cancel-reasons");

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Missing authorization header",
      instance: "/job/cancel-reasons",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  describe("when the request has a valid auth token", () => {
    beforeEach(async () => {
      const companyId = 1234;
      request = getTestRequestFromJWTPayload(
        mockValidTokenJWT({ companyId }),
        companyId,
      );
      mockJobsSdkTrade();

      getCancelReasonsSpy = jobsSdkTradeMock.getCancelReasons;
      getCancelReasonsSpy.mockResolvedValue(expectedResponse);
    });

    it("should return cancel reasons", async () => {
      const response = await request.get("/job/cancel-reasons");

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(
        expect.arrayContaining([
          {
            id: expect.any(String),
            reason: expect.any(String),
            customReason: expect.any(Boolean),
          },
        ]),
      );
    });
  });
});
