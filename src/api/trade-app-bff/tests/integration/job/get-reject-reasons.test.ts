import {
  TEST_COMPANY_ID,
  TestRequest,
  getTestRequest,
  getTestRequestFromJWTPayload,
  mockValidTokenJWT,
} from "../../helpers";
import {
  jobsSdkTradeMock,
  mockJobsSdkTrade,
} from "../../helpers/mocks/jobs-sdk.mock";

describe("GET Reject reasons", () => {
  let request: TestRequest;
  let getRejectReasonsSpy: jest.SpyInstance;

  const expectedResponse = [
    {
      id: "018fb905-c89b-7ee3-b09e-1440866a7516",
      reason: "Reason 1",
    },
    {
      id: "018fb906-5b8d-70ee-91c3-44df0bb2acde",
      reason: "Reason 2",
      customReason: true,
    },
  ];

  it("should send 401 and reject requests without a valid auth token", async () => {
    const request = await getTestRequest().set(
      "x-trade-company-id",
      TEST_COMPANY_ID,
    );
    const response = await request.get("/job/reject-reasons");

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      detail: "Missing authorization header",
      instance: "/job/reject-reasons",
      status: 401,
      title: "Unauthorized",
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/401",
    });
  });

  describe("when the request has a valid auth token", () => {
    beforeEach(async () => {
      const companyId = 1234;
      request = getTestRequestFromJWTPayload(
        mockValidTokenJWT({ companyId }),
        companyId,
      );
      mockJobsSdkTrade();

      getRejectReasonsSpy = jobsSdkTradeMock.getRejectReasons;
      getRejectReasonsSpy.mockResolvedValue(expectedResponse);
    });

    it("should return reject reasons", async () => {
      const response = await request.get("/job/reject-reasons");

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(
        expect.arrayContaining([
          {
            id: expect.any(String),
            reason: expect.any(String),
            customReason: expect.any(Boolean),
          },
        ]),
      );
    });
  });
});
