import TestAgent from "supertest/lib/agent";
import Test from "supertest/lib/test";

import { firebase } from "../../../src/services/firebase";
import { getTestRequest } from "../../helpers";

describe("Readiness healthcheck", () => {
  let request: TestAgent<Test>;

  const mockFirebaseIsNotAccessible = () => {
    jest
      .spyOn(firebase.utils, "isAccessible")
      .mockRejectedValue(new Error("Firebase is down"));
  };

  beforeEach(() => {
    request = getTestRequest();
  });

  it("should return 200 if both the database and firebase are accessible", async () => {
    firebase.utils.isAccessible = jest.fn().mockResolvedValue(true);

    const response = await request.get("/_internal/health/readiness");

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      uptime: expect.any(Number),
      message: "OK",
      timestamp: expect.any(Number),
    });
  });

  it("should return 503, with errors, if the firebase is not accessible", () => {
    mockFirebaseIsNotAccessible();

    return request.get("/_internal/health/readiness").expect(503, {
      type: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/503",
      status: 503,
      title: "Service Unavailable",
      detail: "Services inaccessible: Firebase",
      instance: "/_internal/health/readiness",
    });
  });
});
