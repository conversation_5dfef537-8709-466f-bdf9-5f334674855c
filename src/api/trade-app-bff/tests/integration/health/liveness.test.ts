import { getTestRequest } from "../../helpers";

describe("Liveness healthcheck", () => {
  it("should return 200, if the server is up", async () => {
    const response = await getTestRequest().get("/_internal/health/liveness");

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      uptime: expect.any(Number),
      message: "OK",
      timestamp: expect.any(Number),
    });
  });
});
