import { ContactRole } from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as GetCompany from "../../../src/services/firebase/firestore/get-company";
import { CompanyType } from "../../../src/services/firebase/firestore/schemas/company";
import { NOT_FOUND_ERROR_MESSAGE } from "../../../src/services/member-details/member-details";
import { TEST_USER_EMAIL, getTestRequestWithValidToken } from "../../helpers";

const teamCompanyId = 123456;

describe("Member Details API", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return 404, if company doc is not found", async () => {
    jest.spyOn(GetCompany, "getCompanyDoc").mockResolvedValueOnce(undefined);

    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request.get(`/member-details`);

    expect(response.statusCode).toBe(404);
    expect(response.body.detail).toEqual(NOT_FOUND_ERROR_MESSAGE);
  });

  it("should return 200 with member details, if token is valid, and user IS the account Owner", async () => {
    const mockCompanyDoc: CompanyType = {
      id: 1234,
      tradeId: 1000,
      name: "Company 1234",
      accountEmail: faker.internet.email(),
      accountPhone: faker.phone.number(),
      contacts: [
        {
          roleId: 1,
          email: TEST_USER_EMAIL,
          dateOfBirth: { _seconds: **********, _nanoseconds: 0 },
        },
      ],
      companyAdminAddress: {
        street: "456 Admin St",
        city: "Adminville",
        county: "Adminshire",
        postcode: "AD1 1AA",
        country: "Adminland",
      },
      companyPrimaryPostalAddress: {
        street: "123 Main St",
        city: "Anytown",
        county: "Anycounty",
        postcode: "PO1 1AA",
        country: "Anyland",
      },
    };

    const expectedContacts = [
      {
        email: TEST_USER_EMAIL,
        roleId: 1,
        role: ContactRole[ContactRole.Owner],
        dateOfBirth: "2021-01-01T00:00:00.000Z", // Jan 1, 2021
      },
    ];

    jest
      .spyOn(GetCompany, "getCompanyDoc")
      .mockResolvedValueOnce(mockCompanyDoc);

    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request.get(`/member-details`);

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      accountEmail: mockCompanyDoc.accountEmail,
      accountPhone: mockCompanyDoc.accountPhone,
      companyAdminAddress: mockCompanyDoc.companyAdminAddress,
      contacts: expectedContacts,
      companyPrimaryPostalAddress: mockCompanyDoc.companyPrimaryPostalAddress,
      isAccountOwner: true,
    });
  });

  it("should return 200 with member details, if token is valid, and user is NOT the account Owner", async () => {
    const mockCompanyDoc: CompanyType = {
      id: 1234,
      tradeId: 1000,
      name: "Company 1234",
      accountEmail: faker.internet.email(),
      accountPhone: faker.phone.number(),
      contacts: [
        {
          roleId: 3,
          email: TEST_USER_EMAIL,
        },
      ],
    };

    const expectedContacts = [
      {
        email: TEST_USER_EMAIL,
        roleId: 3,
        role: ContactRole[ContactRole.Director],
      },
    ];

    jest
      .spyOn(GetCompany, "getCompanyDoc")
      .mockResolvedValueOnce(mockCompanyDoc);

    const request = getTestRequestWithValidToken(teamCompanyId);
    const response = await request.get(`/member-details`);

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      accountEmail: mockCompanyDoc.accountEmail,
      accountPhone: mockCompanyDoc.accountPhone,
      contacts: expectedContacts,
      isAccountOwner: false,
    });
  });
});
