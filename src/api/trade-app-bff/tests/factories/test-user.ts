import { consumerSDK } from "@checkatrade/consumer-sdk";
import { faker } from "@faker-js/faker";
import { Static } from "@sinclair/typebox";

type ConsumerResponse = Static<
  typeof consumerSDK.schemas.api.profile.getProfile.response
>;

export type TestUser = {
  id?: string;
  email?: string;
};

export const getTestUser = (): TestUser => ({
  id: faker.string.uuid(),
  email: faker.internet.email(),
});

export const getTestConsumerResponse = (
  user?: TestUser,
  overrides?: Partial<ConsumerResponse>,
): ConsumerResponse => {
  const consumer = {
    id: user?.id || faker.string.uuid(),
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    phone: "07955650121",
    postcode: "NW8 7RG",
    addresses: [],
    ...overrides,
  };

  return consumer;
};
