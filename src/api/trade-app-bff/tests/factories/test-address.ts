import { AddressType, consumerSDK } from "@checkatrade/consumer-sdk";
import { faker } from "@faker-js/faker";
import { Static } from "@sinclair/typebox";

type AddressResponse = Static<
  typeof consumerSDK.schemas.api.profile.address.postAddress.response
>;

export type TestAddress = {
  id: string;
};

export const getTestConsumerAddressResponse = (
  address?: TestAddress,
  overrides?: Partial<AddressResponse>,
): AddressResponse => {
  return {
    id: address?.id || faker.string.uuid(),
    line1: "Street",
    line2: null,
    line3: null,
    city: "City",
    postcode: "NW8 7RG",
    type: AddressType.JOB,
    ...overrides,
  };
};
