import {
  type ConsumerJobResponse,
  FormattedJobStatus,
  FormattedOpportunityStatus,
} from "@checkatrade/jobs-sdk";
import { faker } from "@faker-js/faker";

export type MockJobParams = Partial<ConsumerJobResponse> & {
  id: ConsumerJobResponse["id"];
  tradeIds?: number[];
};

export const getConsumerJobResponseMock = (
  params?: Partial<Omit<MockJobParams, "id">> & {
    id?: string;
  },
): ConsumerJobResponse => {
  const {
    id = faker.string.uuid(),
    tradeIds = [1000001],
    trades,
    categoryId = 1,
    postcode = "NW8 7RG",
    description = "leaky sink",
    createdAt = new Date().toISOString(),
    preferredStart = { id: "", title: "" },
    status = FormattedJobStatus.CANCELLED,
  } = params || {};

  return {
    id,
    description,
    categoryId,
    postcode,
    trades:
      trades ||
      tradeIds.map((companyId) => ({
        id: faker.string.uuid(),
        companyId,
        status: FormattedOpportunityStatus.REQUESTED,
      })),
    createdAt,
    preferredStart,
    address: {
      postcode,
      line1: "Some address",
      city: "Correct city",
    },
    status,
    jobRefinementForm: {
      formId: "",
      questions: [],
    },
  };
};
