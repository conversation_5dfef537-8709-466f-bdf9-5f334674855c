import { PaymentRequestStatus } from "@checkatrade/payment-sdk";
import { faker } from "@faker-js/faker";

export const generateMockPaymentRequest = (
  countPaymentDetailsPerRequest: number = 2,
) => {
  const currency = "GBP";
  const tradeAmount = faker.number.int({ max: 10000 });
  const commissionAmount = faker.number.int({ max: 10000 });
  const amount = tradeAmount + commissionAmount;
  return {
    id: faker.string.uuid(),
    status: PaymentRequestStatus.Paid,
    reference: faker.string.alphanumeric(8),
    paymentLinkId: `PL${faker.string.alphanumeric({ casing: "upper", length: 17 })}`,
    totalAmount: amount,
    commissionAmount,
    tradeAmount,
    currency,
    consumerId: faker.string.uuid(),
    companyId: faker.string.uuid(),
    jobId: faker.string.uuid(),
    opportunityId: faker.string.uuid(),
    consumerName: faker.person.fullName(),
    consumerEmail: faker.internet.email(),
    paymentUrl: faker.internet.url(),
    expiryDate: faker.date.recent().toISOString(),
    dueDate: faker.date.recent().toISOString(),
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    paymentDetail: new Array(countPaymentDetailsPerRequest)
      .fill(null)
      ?.map(() => {
        return {
          id: faker.string.uuid(),
          paymentLinkId: `PL${faker.string.alphanumeric({ casing: "upper", length: 17 })}`,
          pspReference: faker.string.alphanumeric({
            casing: "upper",
            length: 16,
          }),
          paymentMethod: faker.finance.creditCardIssuer(),
          cardSummary: faker.finance.maskedNumber({
            parens: false,
            ellipsis: false,
          }),
          amount,
          currency,
          reason: "reason",
          eventCode: faker.number.int({ min: 0, max: 37 }),
          eventDate: faker.date.recent(),
          isSuccess: faker.datatype.boolean(),
          createdAt: faker.date.recent().toISOString(),
          updatedAt: faker.date.recent().toISOString(),
        };
      }),
  };
};
