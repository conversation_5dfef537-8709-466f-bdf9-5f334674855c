import { faker } from "@faker-js/faker";

import { ReviewRequestDoc } from "../../src/services/firebase/firestore/schemas/review-request";
import { getFakeMobileNumber } from "./test-mobile";

export const mockFirestoreReviewRequest = (
  dateCreated?: Date,
): ReviewRequestDoc => ({
  fullName: faker.person.fullName(),
  email: faker.internet.email(),
  phone: getFakeMobileNumber(),
  sent: false,
  dateCreated: dateCreated || new Date(),
});
