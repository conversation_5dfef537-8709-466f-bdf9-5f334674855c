import { consumerSDK } from "@checkatrade/consumer-sdk";
import {
  FormattedOpportunityStatus,
  FulfilmentType,
  TradeArchivedJobResponse,
  TradeJobResponse,
} from "@checkatrade/jobs-sdk";
import { faker } from "@faker-js/faker";
import { Static } from "@sinclair/typebox";
import nock from "nock";

import { JobDetailsResponse } from "../../src/controllers/jobs/job.schema";
import { PropertyFacts } from "../../src/services/property-facts/get-property-facts-for-consumer";
import { TEST_CONSUMER_ID, TEST_JOB_ID } from "./test-constants";

const baseURL = process.env.JOBS_API_URL;

export const mockDetail = {
  questionId: 1,
  question: "q",
  answerId: 1,
  answer: "a",
};
export const mockJob: TradeJobResponse = {
  id: TEST_JOB_ID,
  description: "leaky sink",
  categoryId: 1,
  postcode: "NW8 7RG",
  opportunityId: "channelId",
  status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
  consumerId: TEST_CONSUMER_ID,
  createdAt: new Date().toISOString(),
  address: {
    postcode: "NW8 7RG",
    line1: "Some address",
    city: "Correct city",
  },
  preferredStart: {
    id: faker.string.uuid(),
    title: "Preferred start",
  },
  details: [mockDetail],
  tradeViewed: false,
  mediaAttachmentIds: [],
  fulfilmentType: FulfilmentType.ENQUIRY,
  jobRefinementForm: {
    formId: "f1",
    experimentId: "e1",
    variant: "v1",
    questions: [
      {
        title: "foo",
        questionId: "1",
        answers: [
          {
            key: "a",
            value: "b",
          },
        ],
        format: "TEXT",
      },
    ],
  },
};

export const createMockJob = (
  fulfilmentType: FulfilmentType,
  bookingData?: { id: string; createdAt: string },
): TradeJobResponse => {
  const baseMockJob: TradeJobResponse = {
    id: TEST_JOB_ID,
    description: "leaky sink",
    categoryId: 1,
    postcode: "NW8 7RG",
    opportunityId: "channelId",
    status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
    consumerId: TEST_CONSUMER_ID,
    createdAt: new Date().toISOString(),
    address: {
      postcode: "NW8 7RG",
      line1: "Some address",
      city: "Correct city",
    },
    preferredStart: {
      id: faker.string.uuid(),
      title: "Preferred start",
    },
    details: [mockDetail],
    tradeViewed: false,
    mediaAttachmentIds: [],
    fulfilmentType: fulfilmentType,
    jobRefinementForm: {
      formId: "f1",
      experimentId: "e1",
      variant: "v1",
      questions: [
        {
          title: "foo",
          questionId: "1",
          answers: [
            {
              key: "a",
              value: "b",
            },
          ],
          format: "TEXT",
        },
      ],
    },
  };

  if (
    fulfilmentType === FulfilmentType.BOOKING_ENQUIRY ||
    fulfilmentType === FulfilmentType.EMERGENCY_ENQUIRY
  ) {
    return {
      ...baseMockJob,
      booking: {
        id: bookingData?.id || faker.string.uuid(),
        createdAt: bookingData?.createdAt || new Date().toISOString(),
      },
    };
  }

  return baseMockJob;
};

export const mockArchivedJob: TradeArchivedJobResponse = {
  jobId: TEST_JOB_ID,
  companyId: 0,
  dateCreated: new Date().toISOString(),
  archivedAt: new Date().toISOString(),
};

export const testPartialJobResponseBody: Partial<JobDetailsResponse> = {
  category: {
    id: 1,
    label: "",
  },
  description: mockJob.description,
  postcode: mockJob.postcode,
  channelId: mockJob.opportunityId,
  id: TEST_JOB_ID,
  jobRefinementForm: mockJob.jobRefinementForm,
  mediaAttachments: [],
};

export const mockConsumer: Static<
  typeof consumerSDK.schemas.api.consumers.getConsumer.response
> = {
  id: TEST_CONSUMER_ID,
  firstName: "test",
  lastName: "testerson",
  email: "<EMAIL>",
  phone: "12345678901",
};

export const mockPropertyFacts: PropertyFacts = {
  energyRating: faker.helpers.arrayElement(["A", "B", "C", "D", "E", "F", "G"]),
  primaryHeatingSource: faker.helpers.arrayElement([
    "Boiler, mains gas",
    "Electric",
    "Oil",
    "Wood",
    "Solar",
  ]),
  roofType: faker.helpers.arrayElement([
    "pitched",
    "flat",
    "thatched",
    "other premises above",
  ]),
  propertyType: faker.helpers.arrayElement([
    "Apartment",
    "Maisonette",
    "Detached",
    "Semi-detached",
    "Terraced",
    "Other",
  ]),
  propertySubtype: faker.helpers.arrayElement([
    "Apartment",
    "Basement apartment",
    "Ground floor apartment",
    "Basement maisonette",
    "Ground floor maisonette",
    "Maisonette",
    "Detached",
    "Detached bungalow",
    "Semi-detached",
    "Semi-detached bungalow",
    "End-terrace",
    "Mid-terrace",
    "Terraced",
    "Caravan",
    "House Boat",
    "Other",
  ]),
  longitude: faker.number.float({ min: -180, max: 180 }),
  latitude: faker.number.float({ min: -90, max: 90 }),
  groundsArea: faker.number.float({ min: 100, max: 10000 }),
  bedrooms: faker.number.int({ min: 1, max: 7 }),
  bathrooms: faker.number.int({ min: 1, max: 5 }),
  floorArea: faker.number.float({ min: 50, max: 500 }),
  mainsGas: faker.datatype.boolean(),
  garage: faker.datatype.boolean(),
  garden: faker.datatype.boolean(),
  driveway: faker.datatype.boolean(),
  periodOfConstruction: faker.helpers.arrayElement([
    "1990-1994",
    "1960-1969",
    "1925-1935",
  ]),
  dateOfConstruction: faker.date.past().getFullYear(),
};

export const nockTradeJob = (
  overrides: Partial<TradeJobResponse> & { jobId: TradeJobResponse["id"] },
) => {
  const { jobId, ...rest } = overrides;
  const url = new URL(`${baseURL}/trade/jobs/${jobId}`);

  return nock(url.origin)
    .get(url.pathname)
    .reply(200, {
      ...rest,
      id: jobId,
    });
};
