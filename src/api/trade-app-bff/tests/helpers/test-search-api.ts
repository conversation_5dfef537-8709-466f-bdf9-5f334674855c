import { httpClient } from "@checkatrade/axios";
import { merge } from "lodash";
import nock from "nock";

import {
  Category,
  TradeCard,
  TradeCardSearch,
  TradeProfile,
  TradeSearchResponse,
  searchApi,
} from "../../src/lib/api-common";
import tradeCardCompanyIdSuccess from "./nock/trade-card-companyId-success.json";
import tradeCardSearchSuccessResponse from "./nock/trade-card-search-success.json";
import nockTradeProfileUniqueName from "./nock/trade-profile-uniquename-success.json";
import tradeSearchSuccess from "./nock/trade-search-success.json";

export const TEST_TRADE_COMPANY_ID = 1067292;
export const TEST_TRADE_COMPANY_2_ID = 1067293;

const validTrades: Partial<TradeCard>[] = [
  {
    companyId: TEST_TRADE_COMPANY_ID,
    name: "Absolute World Ltd",
    uniqueName: "absoluteworld1023739",
    logoUrl: "",
    tradeApp: true,
    contact: {
      primary: {
        calloutable24hr: false,
        label: "02045 487413",
        number: "050505050505",
        secure: true,
      },
      contacts: [],
    },
    reviewsSummary: {
      recentMeanScore: 4.5,
      totalRecentReviews: 10,
      totalReviews: 100,
    },
  },
  {
    companyId: TEST_TRADE_COMPANY_2_ID,
    name: "New World Ltd",
    uniqueName: "newworld1023733",
    logoUrl: "",
    tradeApp: false,
    contact: {
      primary: {
        calloutable24hr: false,
        label: "02045 487413",
        number: "050505050505",
        secure: true,
      },
      contacts: [],
    },
    reviewsSummary: {
      recentMeanScore: 4.5,
      totalRecentReviews: 10,
      totalReviews: 100,
    },
  },
];

const validGetTrade: Partial<TradeCard> = {
  companyId: TEST_TRADE_COMPANY_ID,
  name: "Absolute World Ltd",
  uniqueName: "absoluteworld1023739",
  logoUrl: "",
};

export const mockSearchMultipleTradesApi = (
  trades?: TradeCard[],
  tradeCard = validGetTrade as TradeCard,
) => {
  const pickedTrades = trades ?? validTrades;

  jest
    .spyOn(searchApi, "findTrades")
    .mockResolvedValue({ items: pickedTrades } as TradeCardSearch);
  jest.spyOn(searchApi, "getTrade").mockResolvedValue(tradeCard);
  jest.spyOn(searchApi, "getTrades").mockResolvedValue(trades || []);
  jest
    .spyOn(searchApi, "getCategoryName")
    .mockResolvedValue("categoryLabelFromSearchApi");

  jest.spyOn(searchApi, "getCategoryNames").mockResolvedValue({
    1: "categoryLabelFromSearchApi1",
    2: "categoryLabelFromSearchApi2",
  });
};

export const mockSearchTradesApi = (trade?: TradeCard) => {
  const pickedTrades = trade ? [trade] : [validTrades[0] as TradeCard];

  mockSearchMultipleTradesApi(pickedTrades, pickedTrades[0]);
};

export const mockGetTradeWithTradeProfile404 = () => {
  jest.spyOn(searchApi, "getTrade").mockRestore();
  jest
    .spyOn(httpClient, "get")
    .mockResolvedValueOnce({
      data: validGetTrade, // First call for trade returns valid trade
    })
    .mockRejectedValueOnce({
      response: { status: 404 }, // Second call for trade-profile returns 404
    });
};

export const nockTradeCardSearchSuccess = (
  {
    categoryId,
    postcode,
    page = 1,
    size = 10,
    excludeCompanyIds = [],
  }: {
    categoryId: number;
    postcode: string;
    page?: number;
    size?: number;
    excludeCompanyIds?: number[];
  },
  itemOverrides: (index: number) => Partial<TradeCard> = () => ({}),
  responseBody?: Partial<TradeCardSearch>,
) => {
  const response: TradeCardSearch = merge(
    {},
    tradeCardSearchSuccessResponse,
    responseBody,
  );
  const limit = size;
  let companyIdCounter = (page - 1) * size + 1000001;
  let i = 0;
  while (i < limit) {
    const companyId = companyIdCounter++;
    if (excludeCompanyIds.includes(companyId)) {
      continue;
    }

    const placeholderItemId = i % tradeCardSearchSuccessResponse.items.length;
    response.items[i] = {
      ...tradeCardSearchSuccessResponse.items[placeholderItemId],
      displayState: {
        ...tradeCardSearchSuccessResponse.items[placeholderItemId].displayState,
        modalContent:
          tradeCardSearchSuccessResponse.items[placeholderItemId].displayState
            .modalContent ?? "",
      },
    };
    response.items[i] = {
      ...response.items[placeholderItemId],
      ...itemOverrides(placeholderItemId),
      companyId,
      name: `Test company ${companyId}`,
      uniqueName: `testcompany${companyId}`,
    };
    i++;
  }

  response.items = response.items.slice(0, size);

  response.meta.totalResults =
    response.meta.totalResults - excludeCompanyIds.length;
  response.meta.totalPages = Math.ceil(response.meta.totalResults / size);

  const urlString = searchApi.getUrl(`/trade-card/search`);
  const url = new URL(urlString);

  const excludeCompanyIdsFilter =
    excludeCompanyIds.length > 0 ?
      `;companyId[nin]:${excludeCompanyIds.join(",")}`
    : "";

  const filter = `categoryId[eq]:${categoryId};postcode[eq]:${postcode}${excludeCompanyIdsFilter}`;

  return nock(url.origin)
    .get(url.pathname)
    .query({ filter, page, size })
    .reply(200, response);
};

export const nockTradeNameSearchSuccess = (
  {
    name,
    page = 1,
    size = 10,
  }: {
    name: string;
    page?: number;
    size?: number;
  },
  itemOverrides: (index: number) => Partial<TradeCard> = () => ({}),
  responseBody?: Partial<TradeSearchResponse>,
) => {
  const response: TradeSearchResponse = merge(
    { isSponsored: undefined },
    tradeSearchSuccess,
    responseBody,
  );
  const limit = size;
  let companyIdCounter = (page - 1) * size + 1000001;
  let i = 0;
  while (i < limit) {
    const companyId = companyIdCounter++;

    const placeholderItemId = i % tradeSearchSuccess.items.length;
    response.items[i] = tradeSearchSuccess.items[placeholderItemId];
    response.items[i] = {
      ...response.items[placeholderItemId],
      ...itemOverrides(placeholderItemId),
      companyId,
      name: `Test company ${companyId}`,
      uniqueName: `testcompany${companyId}`,
      tradeProfileUrl: `/api/v1/trade-profile/testcompany${companyId}`,
    };
    i++;
  }

  response.meta.totalPages = Math.ceil(response.meta.totalResults / size);

  const urlString = searchApi.getUrl(`/trade/search`);

  const url = new URL(urlString);
  const filter = `name[like]:${name};view[eq]:profile`;

  return nock(url.origin)
    .get(url.pathname)
    .query({ filter, page, size })
    .reply(200, response);
};

export const nockFindTradesError = ({
  statusCode = 500,
  errorMessage = "Some error message",
}: {
  statusCode?: number;
  errorMessage?: string;
}) => {
  const urlString = searchApi.getUrl(`/trade-card/search`);

  const url = new URL(urlString);

  return nock(url.origin).get(url.pathname).query(true).reply(statusCode, {
    type: "/trade-card/validation-error",
    title: "Validation Error",
    detail: errorMessage,
    status: statusCode,
  });
};

export const nockGetTradeSuccess = ({
  companyId,
  heroBannerUrl,
  override = {},
}: {
  companyId: number;
  heroBannerUrl?: string | null;
  override?: Partial<TradeCard>;
}) => {
  const urlString = searchApi.getUrl(`/trade-card/${companyId}`);

  const url = new URL(urlString);
  const {
    companyId: id,
    name,
    uniqueName,
    tradeApp,
  } = tradeCardCompanyIdSuccess;

  return nock(url.origin)
    .get(url.pathname)
    .reply(200, {
      ...tradeCardCompanyIdSuccess,
      companyId,
      tradeApp,
      name: name.replace(`${id}`, `${companyId}`),
      uniqueName: uniqueName.replace(`${id}`, `${companyId}`),
      heroBannerUrl:
        typeof heroBannerUrl === "undefined" ?
          tradeCardCompanyIdSuccess.heroBannerUrl
        : heroBannerUrl,
      ...override,
    });
};

export const nockGetTradeProfileSuccess = ({
  uniqueName,
  override = {},
}: {
  uniqueName: string;
  override?: Partial<TradeProfile>;
}) => {
  const urlString = searchApi.getUrl(`/trade-profile/${uniqueName}`);
  const url = new URL(urlString);

  return nock(url.origin)
    .get(url.pathname)
    .reply(200, {
      ...nockTradeProfileUniqueName,
      ...override,
    });
};

export const nockGetTradeError = ({ companyId }: { companyId: number }) => {
  const urlString = searchApi.getUrl(`/trade-card/${companyId}`);
  const url = new URL(urlString);

  return nock(url.origin)
    .get(url.pathname)
    .reply(400, {
      type: "https://tools.ietf.org/html/rfc9110#section-15.5.1",
      title: "One or more validation errors occurred.",
      status: 400,
      errors: { companyId: [`The value '${companyId}' is not valid.`] },
      traceId: "00-35fca24578e8a6c7da5f7a6c3cae0a14-13b00a4898da1fe6-00",
    });
};

export const nockCategorySuccess = (
  data: Partial<Category> & { id: number },
) => {
  const urlString = searchApi.getUrl(`/category/${data.id}`);
  const url = new URL(urlString);

  return nock(url.origin)
    .get(url.pathname)
    .reply(200, {
      label: "Test category",
      name: "Test category",
      subcategories: [],
      noindex: false,
      ...data,
    });
};

export const nockCategory404Failure = (
  data: Partial<Category> & { id: number },
) => {
  const urlString = searchApi.getUrl(`/category/${data.id}`);
  const url = new URL(urlString);

  return nock(url.origin).get(url.pathname).reply(404);
};
