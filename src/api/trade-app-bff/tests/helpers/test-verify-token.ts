import { JwtPayload } from "jsonwebtoken";

import {
  TEST_COMPANY_ID,
  TEST_USER_EMAIL,
  TEST_USER_ID,
} from "./test-constants";

export type MockTokenJWTParams = {
  companyId: number;
  sub?: string; // slash user id
};

export function mockValidTokenJWT({
  companyId,
  sub,
}: MockTokenJWTParams): Partial<JwtPayload> {
  return {
    sub: sub ?? TEST_USER_ID,
    email: TEST_USER_EMAIL,
    iss: "test",
    accounts: [
      {
        user_id: TEST_USER_ID,
        company_id: `${companyId}`,
        vetting_status: 0,
        sf_vetting_status: "active",
      },
    ],
  };
}

export const mockInvalidTokenJWT = ({
  companyId,
  sub,
}: MockTokenJWTParams): Partial<JwtPayload> => {
  return {
    sub: sub ?? TEST_USER_ID,
    email: "<EMAIL>",
    accounts: [
      {
        user_id: TEST_USER_ID,
        company_id: `${companyId}`,
        vetting_status: 1,
        sf_vetting_status: "active",
      },
    ],
  };
};

export const mockValidPendingMemberTokenJWT = ({
  companyId = TEST_COMPANY_ID,
  sub = TEST_USER_ID,
}: {
  companyId: number;
  sub: string; // slash user id
}): Partial<JwtPayload> => {
  return {
    sub: sub ?? TEST_USER_ID,
    email: TEST_USER_EMAIL,
    iss: "test",
    accounts: [
      {
        user_id: TEST_USER_ID,
        company_id: `${companyId}`,
        vetting_status: 1,
        sf_vetting_status: "pending",
      },
    ],
  };
};

export const mockValidNonMemberTokenJWT = ({
  companyId = TEST_COMPANY_ID,
  sub = TEST_USER_ID,
}: {
  companyId: number;
  sub: string; // slash user id
}): Partial<JwtPayload> => {
  return {
    sub,
    email: TEST_USER_EMAIL,
    iss: "test",
    accounts: [
      {
        user_id: TEST_USER_ID,
        company_id: `${companyId}`,
        vetting_status: 1,
        sf_vetting_status: "suspend",
      },
    ],
  };
};
