{"companyId": 1118918, "name": "Test company 1118918", "uniqueName": "testcompany1118918", "summary": "This is the test company summary", "operatingLocationText": "Operates in your area", "logoUrl": "https://lh3.googleusercontent.com/placeholder", "logoUrlTemplate": "https://lh3.googleusercontent.com/placeholder", "distanceFromSearch": null, "trackingBadges": ["guaranteed"], "displayState": {"text": "<PERSON><PERSON><PERSON><PERSON>", "useModal": true, "warning": false, "catIcon": true, "modalContent": null, "modalKey": "guaranteed"}, "address": {"locality": "London", "region": null}, "reviewsSummary": {"recentMeanScore": 8.96, "totalRecentReviews": 28, "totalReviews": 28}, "contact": {"primary": {"calloutable24hr": false, "secure": true, "label": "02045 487413", "number": "050505050505"}, "contacts": [{"calloutable24hr": false, "secure": true, "label": "02045 487413", "number": "050505050505"}]}, "view": {"showSummary": true, "showLogo": true, "showMessage": true, "showShortlist": true, "showNewToCheckatrade": false, "showClaim": false}, "skills": [{"id": 10, "iconName": "central-heating", "label": "Central Heating", "subSkills": [{"id": 134, "iconName": "gas-central-heating-installation", "label": "Gas Central Heating Installation"}, {"id": 1933, "iconName": "emergency-central-heating-repair", "label": "Emergency Central Heating Repair"}, {"id": 1335, "iconName": "gas-boiler-servicing-repair", "label": "Gas Boiler Servicing / Repair"}, {"id": 88, "iconName": "gas-boiler-installation", "label": "Gas Boiler Installation"}, {"id": 1093, "iconName": "gas-cooker-installation", "label": "Gas Cooker Installation"}, {"id": 1567, "iconName": "smart-thermostats", "label": "Smart Thermostats"}, {"id": 187, "iconName": "gas-safety-checks-cp12", "label": "Gas Safety Checks - CP12"}]}, {"id": 12, "iconName": "electrician", "label": "Electrician", "subSkills": [{"id": 1376, "iconName": "electric-stoves", "label": "Electric Stoves"}, {"id": 1665, "iconName": "led-lighting", "label": "LED Lighting"}, {"id": 205, "iconName": "electrical-installation-condition-report-safety-checks-eicr", "label": "Electrical Installation Condition Report - Safety Checks - EICR"}, {"id": 2031, "iconName": "electric-sockets", "label": "Electric Sockets"}, {"id": 1438, "iconName": "electric-radiators", "label": "Electric Radiators"}, {"id": 1377, "iconName": "electric-boiler-installation", "label": "Electric Boiler Installation"}, {"id": 1475, "iconName": "energy-efficient-lighting", "label": "Energy Efficient Lighting"}, {"id": 1583, "iconName": "electric-smart-thermostats", "label": "Electric Smart Thermostats"}, {"id": 114, "iconName": "electric-cookers", "label": "Electric Cookers"}, {"id": 1495, "iconName": "electrical-heater-repair", "label": "Electrical Heater Repair"}, {"id": 1436, "iconName": "fault-finding", "label": "Fault Finding"}, {"id": 1378, "iconName": "electric-boiler-servicing-repair", "label": "Electric Boiler Servicing / Repair"}, {"id": 78, "iconName": "electrical-contractors", "label": "Electrical Contractors"}, {"id": 2065, "iconName": "extractor-fans", "label": "Extractor Fans"}]}], "reviews": [{"body": "They booked the job within 2 days, <PERSON><PERSON> came on time, performed the job timely and explained the features of the timer. Payment was easy and convenient, would use the company again", "reviewId": "cL9JGKciDoCbwSPGNGsE", "location": "EN3", "openOnLoad": false, "reviewedDate": "April 2024", "score": 10, "scorePercent": 100, "showBreakdowns": true, "title": "Timer for immersion boiler ", "tradeResponse": "", "verified": true, "breakdowns": [{"label": "<PERSON><PERSON><PERSON>", "value": 10}, {"label": "Tidiness", "value": 10}, {"label": "Timekeeping", "value": 10}, {"label": "Workmanship", "value": 10}]}, {"body": "Electrician came out to find the fault. Said it was a two person job because they’d likely need to pull up floorboards to find issue. Electricians didn’t turn up on two consecutive days and no communication as to why. Call centre help incredibly poor.\nHired an alternative electrician who found the fault by himself in 15 min and took 15 min to fix. The original electrician should have found the issue as well if he had investigated properly.\nUtterly terrible service and would not recommend this company. \nCompany is aware of my concerns and has not addressed them.", "reviewId": "kuNuA93fvH4W4Guo2pE9", "location": "SE7", "openOnLoad": false, "reviewedDate": "May 2024", "score": 1, "scorePercent": 10, "showBreakdowns": true, "title": "Finding fault with electrical sockets", "tradeResponse": "", "verified": true, "breakdowns": [{"label": "Tidiness", "value": 1}, {"label": "Timekeeping", "value": 1}, {"label": "Workmanship", "value": 1}]}, {"body": "The company replied to my search within 10 mins and 4 hours later the job was done to a great standard.  Very friendly team from the phone call to the technician.  Would definitely recommend.", "reviewId": "AvwxxATr9y5sBFdRcTQM", "location": "SE7", "openOnLoad": false, "reviewedDate": "April 2024", "score": 10, "scorePercent": 100, "showBreakdowns": true, "title": "Light fitted, switch and double plug sockets", "tradeResponse": "", "verified": true, "breakdowns": [{"label": "<PERSON><PERSON><PERSON>", "value": 10}, {"label": "Timekeeping", "value": 10}, {"label": "Workmanship", "value": 10}]}, {"body": "Effective communication and efficient work", "reviewId": "yv8l1LRCWpCzyeg0Athd", "location": "SW19", "openOnLoad": false, "reviewedDate": "April 2024", "score": 10, "scorePercent": 100, "showBreakdowns": true, "title": "Electrical safety", "tradeResponse": "", "verified": true, "breakdowns": [{"label": "<PERSON><PERSON><PERSON>", "value": 10}, {"label": "Tidiness", "value": 10}, {"label": "Timekeeping", "value": 10}, {"label": "Workmanship", "value": 10}]}, {"body": "Had a non-urgent small electrical job (fitting a bathroom string-switch) and company (Lidia) was great to deal with, and the electrician (<PERSON>), was competent and friendly.  0 complaints.", "reviewId": "9Xt8rPgzG9AOo3q2cTRr", "location": "W12", "openOnLoad": false, "reviewedDate": "April 2024", "score": 10, "scorePercent": 100, "showBreakdowns": true, "title": "Had a bathroom string-switch fitted", "tradeResponse": "", "verified": true, "breakdowns": [{"label": "<PERSON><PERSON><PERSON>", "value": 10}, {"label": "Tidiness", "value": 10}, {"label": "Timekeeping", "value": 10}, {"label": "Workmanship", "value": 10}]}, {"body": "Quick efficient communicative friendly", "reviewId": "Gxh45Ycq2P7y1dYn2W0m", "location": "SE18", "openOnLoad": false, "reviewedDate": "April 2024", "score": 8.75, "scorePercent": 88, "showBreakdowns": true, "title": "Electrical fix", "tradeResponse": "", "verified": false, "breakdowns": [{"label": "<PERSON><PERSON><PERSON>", "value": 9}, {"label": "Tidiness", "value": 9}, {"label": "Timekeeping", "value": 8}, {"label": "Workmanship", "value": 9}]}], "galleries": [{"id": "WJmcX38GcfDtC2LJ6blb", "title": "The company", "items": [{"id": "M1KOyVVn9Kv2mvAX6TvF", "albumId": "WJmcX38GcfDtC2LJ6blb", "dateFormatted": "February 2024", "description": null, "label": null, "type": "PHOTO", "title": null, "url": "https://lh3.googleusercontent.com/placeholder", "urlTemplate": "https://lh3.googleusercontent.com/placeholder", "youTubeId": null}, {"id": "4U8vG5yoCvuz3aZr6UBj", "albumId": "WJmcX38GcfDtC2LJ6blb", "dateFormatted": "February 2024", "description": null, "label": null, "type": "PHOTO", "title": null, "url": "https://lh3.googleusercontent.com/placeholder", "urlTemplate": "https://lh3.googleusercontent.com/placeholder", "youTubeId": null}, {"id": "r0tzM7jwSxhXVp3U4FZ5", "albumId": "WJmcX38GcfDtC2LJ6blb", "dateFormatted": "February 2024", "description": null, "label": null, "type": "PHOTO", "title": null, "url": "https://lh3.googleusercontent.com/placeholder", "urlTemplate": "https://lh3.googleusercontent.com/placeholder", "youTubeId": null}]}], "badges": [], "isSponsored": false, "memberDetails": {"acquiredBadge": "None", "memberSince": ""}, "tradeApp": {"messaging": false}, "heroBannerUrl": "https://lh3.googleusercontent.com/single-company-hero-banner-url-placeholder"}