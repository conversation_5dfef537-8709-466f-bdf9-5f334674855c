{"results": [{"message": {"id": "cdbf2dda-78cf-4217-883f-35be4887c2ce", "text": "", "html": "", "type": "regular", "user": {"id": "Test_System_User", "role": "user", "created_at": "2024-05-17T08:50:52.746922Z", "updated_at": "2024-05-17T14:09:41.857961Z", "banned": false, "online": false, "type": "SYSTEM", "name": "Test System User"}, "attachments": [{"type": "image", "asset_url": "https://stream-io-cdn.com/attachment-1"}, {"type": "image", "image_url": "https://stream-io-cdn.com/attachment-2"}], "latest_reactions": [], "own_reactions": [], "reaction_counts": {}, "reaction_scores": {}, "reaction_groups": null, "reply_count": 0, "deleted_reply_count": 0, "cid": "messaging:testJobOpportunity", "created_at": "2024-05-20T09:43:24.232592Z", "updated_at": "2024-05-20T09:52:23.729522Z", "shadowed": false, "mentioned_users": [], "silent": false, "pinned": false, "pinned_at": null, "pinned_by": null, "pin_expires": null, "message_text_updated_at": "2024-05-20T09:52:12.947355Z", "channel": {"id": "testJobOpportunity", "type": "messaging", "cid": "messaging:testJobOpportunity", "last_message_at": "2024-05-20T09:50:41.156098Z", "created_at": "2024-05-17T14:11:05.263083Z", "updated_at": "2024-05-17T14:11:05.263083Z", "created_by": {"id": "Test_System_User", "role": "user", "created_at": "2024-05-17T08:50:52.746922Z", "updated_at": "2024-05-17T14:09:41.857961Z", "banned": false, "online": false, "type": "SYSTEM", "name": "Test System User"}, "frozen": false, "disabled": false, "member_count": 1, "config": {"created_at": "2024-02-19T16:19:25.418896Z", "updated_at": "2024-03-21T18:44:14.27987Z", "name": "messaging", "typing_events": true, "read_events": true, "connect_events": true, "search": true, "reactions": false, "replies": false, "quotes": true, "mutes": true, "uploads": true, "url_enrichment": true, "custom_events": true, "push_notifications": true, "reminders": false, "mark_messages_pending": false, "polls": false, "message_retention": "infinite", "max_message_length": 5000, "automod": "disabled", "automod_behavior": "flag", "blocklist_behavior": "flag", "blocklists": [{"blocklist": "checkatrade_profanity_v1", "behavior": "block"}], "commands": []}, "own_capabilities": ["ban-channel-members", "cast-poll-vote", "connect-events", "create-call", "delete-any-message", "delete-channel", "delete-own-message", "flag-message", "freeze-channel", "join-call", "join-channel", "leave-channel", "mute-channel", "pin-message", "query-poll-votes", "quote-message", "read-events", "search-messages", "send-custom-events", "send-links", "send-message", "send-poll", "send-typing-events", "set-channel-cooldown", "skip-slow-mode", "typing-events", "update-any-message", "update-channel", "update-channel-members", "update-own-message", "update-thread", "upload-file"], "name": "testJobOpportunity"}, "test": "", "smartType": "JOB_REQUEST"}}]}