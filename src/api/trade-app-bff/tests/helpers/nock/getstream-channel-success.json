{"channels": [{"channel": {"id": "channelId", "type": "messaging", "cid": "messaging:channelId", "created_at": "2024-06-10T16:20:47.901641Z", "updated_at": "2024-06-10T16:20:47.901641Z", "created_by": {"id": "Test_user", "role": "user", "created_at": "2024-05-28T08:24:36.210627Z", "updated_at": "2024-06-10T16:20:47.660879Z", "banned": false, "online": false, "name": "TestUser", "type": "SYSTEM"}, "frozen": false, "disabled": false, "member_count": 2, "config": {"created_at": "2024-02-19T16:19:25.418896Z", "updated_at": "2024-03-21T18:44:14.27987Z", "name": "messaging", "typing_events": true, "read_events": true, "connect_events": true, "search": true, "reactions": false, "replies": false, "quotes": true, "mutes": true, "uploads": true, "url_enrichment": true, "custom_events": true, "push_notifications": true, "reminders": false, "mark_messages_pending": false, "polls": false, "message_retention": "infinite", "max_message_length": 5000, "automod": "disabled", "automod_behavior": "flag", "blocklist_behavior": "flag", "blocklists": [{"blocklist": "checkatrade_profanity_v1", "behavior": "block"}], "commands": []}, "own_capabilities": ["cast-poll-vote", "connect-events", "create-call", "delete-channel", "delete-own-message", "flag-message", "join-call", "join-channel", "leave-channel", "mute-channel", "pin-message", "query-poll-votes", "quote-message", "read-events", "search-messages", "send-custom-events", "send-links", "send-message", "send-poll", "send-typing-events", "typing-events", "update-channel", "update-channel-members", "update-own-message", "update-thread", "upload-file"], "hidden": false, "name": "channelId"}, "messages": [], "pinned_messages": [], "read": [{"user": {"id": "Test_user", "name": "TestUser", "language": "", "role": "user", "teams": [], "created_at": "2024-05-28T08:24:36.210627Z", "updated_at": "2024-06-10T16:20:47.660879Z", "banned": false, "online": false, "type": "SYSTEM"}, "last_read": "2024-06-10T16:20:47.911935074Z", "unread_messages": 0}, {"user": {"id": "<PERSON><PERSON><PERSON>_Delete_Test_System_User_2", "name": "Andriyan Delete Test System User_2", "language": "", "role": "user", "teams": [], "created_at": "2024-05-28T08:41:50.69639Z", "updated_at": "2024-06-10T16:20:47.660879Z", "banned": false, "online": false, "type": "SYSTEM"}, "last_read": "2024-06-10T16:20:47.911935074Z", "unread_messages": 0}], "members": [{"user_id": "Test_user", "user": {"id": "Test_user", "role": "user", "created_at": "2024-05-28T08:24:36.210627Z", "updated_at": "2024-06-10T16:20:47.660879Z", "banned": false, "online": false, "name": "TestUser", "type": "SYSTEM"}, "status": "member", "created_at": "2024-06-10T16:20:47.904303Z", "updated_at": "2024-06-10T16:20:47.904303Z", "banned": false, "shadow_banned": false, "role": "owner", "channel_role": "channel_member", "notifications_muted": false}, {"user_id": "<PERSON><PERSON><PERSON>_Delete_Test_System_User_2", "user": {"id": "<PERSON><PERSON><PERSON>_Delete_Test_System_User_2", "role": "user", "created_at": "2024-05-28T08:41:50.69639Z", "updated_at": "2024-06-10T16:20:47.660879Z", "banned": false, "online": false, "name": "Andriyan Delete Test System User_2", "type": "SYSTEM"}, "status": "member", "created_at": "2024-06-10T16:20:47.904303Z", "updated_at": "2024-06-10T16:20:47.904303Z", "banned": false, "shadow_banned": false, "role": "member", "channel_role": "channel_member", "notifications_muted": false}], "membership": {"user": {"id": "Test_user", "role": "user", "created_at": "2024-05-28T08:24:36.210627Z", "updated_at": "2024-06-10T16:20:47.660879Z", "banned": false, "online": false, "name": "TestUser", "type": "SYSTEM"}, "status": "member", "created_at": "2024-06-10T16:20:47.904303Z", "updated_at": "2024-06-10T16:20:47.904303Z", "banned": false, "shadow_banned": false, "channel_role": "channel_member", "notifications_muted": false}, "threads": [], "data": {}}], "duration": "8.64ms"}