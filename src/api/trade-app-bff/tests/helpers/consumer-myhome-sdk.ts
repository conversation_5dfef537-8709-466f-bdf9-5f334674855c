import { consumerMyhomeSDK } from "@checkatrade/consumer-myhome-sdk";

export const consumerMyhomeSdkTradeMock = {
  propertyFactsForConsumer: {
    get: jest.fn(),
  },
};

export const mockConsumerMyhomeSdkTrade = () => {
  jest
    .spyOn(consumerMyhomeSDK, "trade")
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    .mockImplementation((_token) => consumerMyhomeSdkTradeMock as any);
};
