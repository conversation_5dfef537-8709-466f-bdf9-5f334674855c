import jwt, { JwtPayload } from "jsonwebtoken";
import supertest, { Test } from "supertest";
import TestAgent from "supertest/lib/agent";

import { app } from "../jest.setup";
import { TEST_COMPANY_ID, TEST_USER_ID } from "./test-constants";
import {
  mockInvalidTokenJWT,
  mockValidNonMemberTokenJWT,
  mockValidPendingMemberTokenJWT,
  mockValidTokenJWT,
} from "./test-verify-token";

export type TestRequest = TestAgent<Test> & {
  token: string;
};

export const getTestRequest = (): TestRequest => {
  const agent = supertest.agent(app.server) as TestRequest;
  agent.token = "";

  return agent;
};

/** Wrapper for getTestRequestFromJWTPayload */
export const getTestRequestWithValidToken = (
  companyId = TEST_COMPANY_ID,
  sub = TEST_USER_ID,
) => {
  return getTestRequestFromJWTPayload(
    mockValidTokenJWT({ companyId, sub }),
    companyId,
  );
};

export const getTestRequestWithValidPendingMemberToken = (
  companyId = TEST_COMPANY_ID,
  sub = TEST_USER_ID,
) => {
  return getTestRequestFromJWTPayload(
    mockValidPendingMemberTokenJWT({ companyId, sub }),
    companyId,
  );
};

export const getTestRequestWithValidNonMemberToken = (
  companyId = TEST_COMPANY_ID,
  sub = TEST_USER_ID,
) => {
  return getTestRequestFromJWTPayload(
    mockValidNonMemberTokenJWT({ companyId, sub }),
    companyId,
  );
};

export const getTestRequestWithInvalidToken = (
  companyId = TEST_COMPANY_ID,
  sub = TEST_USER_ID,
) => {
  return getTestRequestFromJWTPayload(
    mockInvalidTokenJWT({ companyId, sub }),
    companyId,
  );
};

export const getTestRequestFromJWTPayload = (
  payload: Partial<JwtPayload>,
  companyId?: number,
): TestRequest => {
  const request = getTestRequest();

  request.token = jwt.sign(payload, "secret");

  request.auth(request.token, { type: "bearer" });
  if (companyId) {
    request.set("x-trade-company-id", companyId);
  }

  return request;
};
