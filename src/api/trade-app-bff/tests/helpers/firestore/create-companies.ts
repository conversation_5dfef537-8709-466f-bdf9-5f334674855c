import { Static } from "@sinclair/typebox";

import { tradeApp } from "../../../src/services/firebase/app";
import { COMPANIES_COLLECTION } from "../../../src/services/firebase/firestore/collections";
import { CompaniesSchema } from "../../../src/services/firebase/firestore/schemas/companies";

const MOCK_COMPANY: Static<typeof CompaniesSchema> = {
  id: 0,
};

export const createCompanies = async (companyId: number) => {
  const collection = tradeApp.firestore().collection(COMPANIES_COLLECTION);
  await collection.doc(companyId.toString()).set({
    ...MOCK_COMPANY,
    id: companyId,
    companyId,
    dateCreated: new Date().toISOString(),
    lastFetchedAt: new Date().toISOString(),
    lastModifiedAt: new Date().toISOString(),
  });
};
