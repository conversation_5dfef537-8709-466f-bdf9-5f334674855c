import { tradeApp } from "../../../src/services/firebase/app";
import {
  COMPANIES_COLLECTION,
  PERSON_ACCREDITATIONS_COLLECTION,
} from "../../../src/services/firebase/firestore/collections";
import {
  AccreditationMutationType,
  AccreditationPlatform,
  AccreditationStatusType,
  PersonAccreditation,
} from "../../../src/services/firebase/firestore/schemas/person-accreditations";
import { createCompanies } from "./create-companies";

export const createPersonAccreditation = async (
  companyId: number,
  personId: string,
  accreditationId: number,
  overrides: Partial<PersonAccreditation> = {},
) => {
  await createCompanies(companyId);

  const companyRef = tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(PERSON_ACCREDITATIONS_COLLECTION);

  const id = companyRef.doc().id;

  const document = {
    personId,
    companyId,
    accreditationId,
    isDeleted: false,
    modifiedDate: new Date(),
    status: AccreditationStatusType.Pending,
    registrationNumber: "registration-number",
    history: [
      {
        platform: AccreditationPlatform.TradeApp,
        type: AccreditationMutationType.Submitted,
        updatedDate: new Date(),
      },
    ],
    ...overrides,
  };

  await companyRef.doc(id).set({ ...document });

  return {
    id,
    ...document,
  };
};
