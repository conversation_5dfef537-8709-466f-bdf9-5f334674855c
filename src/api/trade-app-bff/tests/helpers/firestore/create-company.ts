import { faker } from "@faker-js/faker";

import { tradeApp } from "../../../src/services/firebase/app";
import { CompanyType } from "../../../src/services/firebase/firestore/schemas/company";

const companyCollection = "company";

export const createCompany = async (companyId: number) => {
  const companyDoc: CompanyType = {
    id: companyId,
    tradeId: companyId + 1000,
    uniqueName: `company-${companyId}`,
    name: `Company ${companyId}`,
    accountEmail: faker.internet.email(),
  };

  const collection = tradeApp.firestore().collection(companyCollection);
  await collection.doc(companyId.toString()).set(companyDoc);
};
