import { tradeApp } from "../../../src/services/firebase/app";
import { Accreditation } from "../../../src/services/firebase/firestore/schemas/accreditation";

export const TestAccreditations: Accreditation[] = [
  {
    accreditationId: 10,
    name: "Test accreditation 10",
    canExpire: true,
    requiresApproval: true,
    logoFilename: "logo10/path",
    isDeleted: false,
  },
  {
    accreditationId: 20,
    name: "Test accreditation 20",
    canExpire: false,
    requiresApproval: false,
    logoFilename: "logo20/path",
    isDeleted: false,
  },
  {
    accreditationId: 30,
    name: "Test accreditation 30",
    canExpire: false,
    requiresApproval: true,
    logoFilename: "logo30/path",
    isDeleted: false,
  },
  {
    accreditationId: 40,
    name: "Test accreditation 40",
    canExpire: true,
    requiresApproval: false,
    logoFilename: "logo40/path",
    isDeleted: false,
  },
  {
    accreditationId: 50,
    name: "Test accreditation 50",
    canExpire: true,
    requiresApproval: true,
    isDeleted: true,
  },
];

export const createAccreditations = async () => {
  for (const accreditation of TestAccreditations) {
    const docId = accreditation.accreditationId;
    const docRef = tradeApp
      .firestore()
      .collection("accreditations")
      .doc(docId.toString());

    await docRef.set(accreditation);
  }
};
