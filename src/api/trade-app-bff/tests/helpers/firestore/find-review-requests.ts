import { firestore } from "firebase-admin";

import { tradeApp } from "../../../src/services/firebase/app";
import { ReviewRequestDoc } from "../../../src/services/firebase/firestore/schemas/review-request";

import Filter = firestore.Filter;

const matchingReviewRequestFilter = ({
  phone,
  email,
  fullName,
}: Partial<ReviewRequestDoc>): Filter => {
  const fullNameFilter = Filter.where("fullName", "==", fullName);
  if (phone && email) {
    return Filter.and(
      fullNameFilter,
      Filter.where("email", "==", email),
      Filter.where("phone", "==", phone),
    );
  }

  if (phone) {
    return Filter.and(fullNameFilter, Filter.where("phone", "==", phone));
  }

  if (email) {
    return Filter.and(fullNameFilter, Filter.where("email", "==", email));
  }

  throw new Error("At least one of phone, email or fullName must be provided");
};

export const findReviewRequest = async (
  companyId: number,
  fullName: string,
  email?: string,
  phone?: string,
): Promise<ReviewRequestDoc | null> => {
  const query = tradeApp
    .firestore()
    .collection("companies")
    .doc(companyId.toString())
    .collection("reviewRequests")
    .where(matchingReviewRequestFilter({ fullName, email, phone }));

  const snapshot = await query.get();

  if (snapshot.empty) {
    return null;
  }

  return snapshot.docs[0].data() as ReviewRequestDoc;
};
