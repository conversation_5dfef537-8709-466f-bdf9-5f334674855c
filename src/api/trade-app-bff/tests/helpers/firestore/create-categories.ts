import { tradeApp } from "../../../src/services/firebase/app";
import { CATEGORIES_COLLECTION } from "../../../src/services/firebase/firestore/collections";
import { Category } from "../../../src/services/firebase/firestore/schemas/categories";

export const createCategories = async (
  categoryId: number,
  category: Partial<Category>,
) => {
  const collection = tradeApp.firestore().collection(CATEGORIES_COLLECTION);
  await collection.doc(categoryId.toString()).set({
    ...category,
  });
};
