import { consumerSDK } from "@checkatrade/consumer-sdk";
import { Static } from "@sinclair/typebox";
import nock from "nock";

import { TestUser, getTestConsumerResponse } from "../factories";

export const nockTradeConsumerProfileSuccess = (
  user: TestUser,
  overrides?: Partial<
    Static<typeof consumerSDK.schemas.api.profile.getProfile.response>
  >,
) => {
  const baseURL = consumerSDK.trade("test").connector.defaults.baseURL;
  const url = new URL(`${baseURL}/consumers/${user?.id}`);
  const response = getTestConsumerResponse(user, overrides);

  return nock(url.origin).get(url.pathname).reply(200, response);
};
