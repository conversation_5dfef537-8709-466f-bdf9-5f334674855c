import { gcp } from "@checkatrade/gcp";
import nock from "nock";

import { CommsReportUserData } from "../../src/controllers/consumers/post-report-types";
import { type CommsReviewRequestData } from "../../src/lib/api-common";

const baseURL = String(process.env.COMMS_API_URL);

export const nockCommsReviewRequest = (data: CommsReviewRequestData) => {
  const url = new URL(`${baseURL}/reviews/request`);

  jest
    .spyOn(gcp, "generateBearerToken")
    .mockImplementation((audience: string) => {
      return Promise.resolve(audience === url.href ? "comms-review-token" : "");
    });

  return nock(url.origin)
    .post(url.pathname, data)
    .matchHeader("Authorization", "Bearer comms-review-token")
    .reply(200);
};

export const nockCommsReportConsumerV1 = (payload: CommsReportUserData) => {
  const url = new URL(`${baseURL}/chat/block`);

  jest
    .spyOn(gcp, "generateBearerToken")
    .mockImplementation((audience: string) => {
      return Promise.resolve(
        audience === url.href ? "comms-chat-block-token-v1" : "",
      );
    });

  return nock(url.origin)
    .post(url.pathname, (body) => {
      const { attributes, data } = body;
      const timestamp = new Date(attributes.timestamp);

      return (
        attributes.schemaVersion === "1.0" &&
        timestamp.getTime() > 0 &&
        timestamp.toISOString() === attributes.timestamp &&
        payload.channelId === data.channelId &&
        payload.reporter.id === data.senderId &&
        payload.reporter.id === data.members[0].id &&
        payload.reporter.type === data.members[0].type &&
        payload.user.id === data.members[1].id &&
        payload.user.type === data.members[1].type
      );
    })
    .matchHeader("Authorization", "Bearer comms-chat-block-token-v1")
    .reply(200);
};

export const nockCommsReportConsumerV2 = (payload: CommsReportUserData) => {
  const url = new URL(`${baseURL}/report-user`);

  jest
    .spyOn(gcp, "generateBearerToken")
    .mockImplementation((audience: string) => {
      return Promise.resolve(
        audience === url.href ? "comms-chat-block-token-v2" : "",
      );
    });

  return nock(url.origin)
    .post(url.pathname, (body) => {
      return (
        payload.jobId === body.jobId &&
        payload.reporter.id === body.reporter.id &&
        payload.reporter.type === body.reporter.type &&
        payload.user.id === body.user.id &&
        payload.user.type === body.user.type
      );
    })
    .matchHeader("Authorization", "Bearer comms-chat-block-token-v2")
    .reply(200);
};
