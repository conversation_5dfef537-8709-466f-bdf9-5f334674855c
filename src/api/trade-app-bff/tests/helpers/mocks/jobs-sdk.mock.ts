import { jobsSDK } from "@checkatrade/jobs-sdk";

export const jobsSdkTradeMock = {
  getJob: jest.fn(),
  getJobs: jest.fn(),
  getOpportunity: jest.fn(),
  getArchivedJob: jest.fn(),
  getArchivedJobs: jest.fn(),
  acceptJob: jest.fn(),
  rejectJob: jest.fn(),
  completeJob: jest.fn(),
  cancelJob: jest.fn(),
  bookJob: jest.fn(),
  markJobAsRead: jest.fn(),
  getUnreadJobsCount: jest.fn(),
  updateJobNote: jest.fn(),
  getRejectReasons: jest.fn(),
  getCancelReasons: jest.fn(),
  cancelAppointment: jest.fn(),
  config: {},
  token: "",
};

export const jobsSdkMock = {
  getPreferredStarts: jest.fn(),
};

export const mockJobsSdkTrade = () => {
  jest
    .spyOn(jobsSDK, "trade")
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    .mockImplementation((_token) => jobsSdkTradeMock as any);
};

export const jobsSdkServiceMock = {
  getJob: jest.fn(),
  acceptOpportunity: jest.fn(),
  trades: {
    redirect: jest.fn(),
  },
  config: {},
};
export const mockJobsSdkService = () => {
  jest
    .spyOn(jobsSDK, "service")
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    .mockImplementation(() => jobsSdkServiceMock as any);
};
