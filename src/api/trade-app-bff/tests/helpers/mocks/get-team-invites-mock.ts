export const getTeamInvitesMock = {
  data: [
    {
      invite: {
        id: "cb665513-e4ac-46f4-ba7e-8d884bb351eb",
        relationshipStatus: "Pending",
        expiryDate: "2025-03-16T13:48:26.119Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-03-03T04:08:11.909Z",
        dateUpdated: "2025-03-05T18:34:59.287Z",
        parentMemberId: "12958f24-0873-4c6b-b3fe-0d884e2bc12a",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 2,
        traderId: 2,
        name: "Company 2",
        uniqueName: "company-2",
        id: "12958f24-0873-4c6b-b3fe-0d884e2bc12a",
      },
    },
    {
      invite: {
        id: "cdd54321-421f-4bea-b1db-2cc110290fe4",
        relationshipStatus: null,
        expiryDate: "2025-03-10T20:44:31.370Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-03-02T01:37:30.091Z",
        dateUpdated: "2025-03-04T15:18:46.270Z",
        parentMemberId: "f313c405-e2bd-4fcc-a003-9aaa94f7731c",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 3,
        traderId: 3,
        name: "Company 3",
        uniqueName: "company-3",
        id: "f313c405-e2bd-4fcc-a003-9aaa94f7731c",
      },
    },
    {
      invite: {
        id: "0798752e-9f7e-4cb0-b67c-029bce741abb",
        relationshipStatus: null,
        expiryDate: "2025-03-10T05:38:09.335Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-03-01T01:45:28.617Z",
        dateUpdated: "2025-03-04T05:32:53.356Z",
        parentMemberId: "16edead4-d926-4279-a511-45b29522e886",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 4,
        traderId: 4,
        name: "Company 4",
        uniqueName: "company-4",
        id: "16edead4-d926-4279-a511-45b29522e886",
      },
    },
    {
      invite: {
        id: "5a815ee8-0f74-4012-8a79-d20cb9560911",
        relationshipStatus: "Pending",
        expiryDate: "2025-03-04T19:49:00.617Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-03-04T11:22:25.082Z",
        dateUpdated: "2025-03-02T14:44:44.845Z",
        parentMemberId: "1433b20a-7582-40d7-8fa1-644b6f1df611",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 5,
        traderId: 5,
        name: "Company 5",
        uniqueName: "company-5",
        id: "1433b20a-7582-40d7-8fa1-644b6f1df611",
      },
    },
    {
      invite: {
        id: "147cec76-0680-45d1-9b95-b0a94a87ad11",
        relationshipStatus: "Pending",
        expiryDate: "2025-03-10T02:24:52.879Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-03-03T04:44:29.620Z",
        dateUpdated: "2025-03-02T17:07:48.677Z",
        parentMemberId: "d5a7a3e2-8939-43d6-b849-39c225903f3b",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 6,
        traderId: 6,
        name: "Company 6",
        uniqueName: "company-6",
        id: "d5a7a3e2-8939-43d6-b849-39c225903f3b",
      },
    },
    {
      invite: {
        id: "51076099-909a-4cd1-a95c-9fde59debd77",
        relationshipStatus: "Accepted",
        expiryDate: "2025-03-10T13:18:05.384Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-03-01T16:51:56.230Z",
        dateUpdated: "2025-03-01T02:29:16.854Z",
        parentMemberId: "beca4c3f-875c-4061-80f4-a7789c909681",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 7,
        traderId: 7,
        name: "Company 7",
        uniqueName: "company-7",
        id: "beca4c3f-875c-4061-80f4-a7789c909681",
      },
    },
    {
      invite: {
        id: "8388d1a3-b243-4a17-b684-2ce1f474eba9",
        relationshipStatus: "Pending",
        expiryDate: "2025-03-07T12:26:24.846Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-03-01T09:08:11.050Z",
        dateUpdated: "2025-02-28T05:56:14.936Z",
        parentMemberId: "e50e366f-8b97-4992-8c1b-c022e8f8e13b",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 8,
        traderId: 8,
        name: "Company 8",
        uniqueName: "company-8",
        id: "e50e366f-8b97-4992-8c1b-c022e8f8e13b",
      },
    },
    {
      invite: {
        id: "2ab6fe74-74f9-4473-9d1e-70543bad3d92",
        relationshipStatus: "Pending",
        expiryDate: "2025-02-28T21:25:56.937Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-25T14:28:44.278Z",
        dateUpdated: "2025-02-28T00:53:08.633Z",
        parentMemberId: "1bef3978-9592-485a-9650-03460da7e4a2",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 9,
        traderId: 9,
        name: "Company 9",
        uniqueName: "company-9",
        id: "1bef3978-9592-485a-9650-03460da7e4a2",
      },
    },
    {
      invite: {
        id: "5a461bca-5c3e-4707-93a7-a86d8e808b7f",
        relationshipStatus: "Pending",
        expiryDate: "2025-02-27T20:28:01.384Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-25T18:39:56.712Z",
        dateUpdated: "2025-02-25T22:38:49.439Z",
        parentMemberId: "c984556a-f221-40f0-af3f-991a60526bbe",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 10,
        traderId: 10,
        name: "Company 10",
        uniqueName: "company-10",
        id: "c984556a-f221-40f0-af3f-991a60526bbe",
      },
    },
    {
      invite: {
        id: "e9bc2a79-b7d2-4a04-9ba9-8ea4f99d2c0c",
        relationshipStatus: "Accepted",
        expiryDate: "2025-02-27T12:01:52.332Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-21T19:01:12.025Z",
        dateUpdated: "2025-02-25T14:58:40.711Z",
        parentMemberId: "a27529e1-d474-4d26-bd16-9385a4865f6a",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 11,
        traderId: 11,
        name: "Company 11",
        uniqueName: "company-11",
        id: "a27529e1-d474-4d26-bd16-9385a4865f6a",
      },
    },
    {
      invite: {
        id: "536b9bb0-965e-4b4b-bdb6-7de3d5bf27fd",
        relationshipStatus: "Accepted",
        expiryDate: "2025-03-05T08:33:44.021Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-20T20:48:13.091Z",
        dateUpdated: "2025-02-23T12:24:30.402Z",
        parentMemberId: "cdadd3b3-cb94-4ac5-b96b-cc745e81821b",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 12,
        traderId: 12,
        name: "Company 12",
        uniqueName: "company-12",
        id: "cdadd3b3-cb94-4ac5-b96b-cc745e81821b",
      },
    },
    {
      invite: {
        id: "0af9cc9e-ab23-40ce-9f7d-1cf2e61d9179",
        relationshipStatus: "Pending",
        expiryDate: "2025-03-03T00:28:52.089Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-22T07:06:52.472Z",
        dateUpdated: "2025-02-22T12:38:25.466Z",
        parentMemberId: "91ca9a3a-b236-4598-a572-955afdd77451",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 13,
        traderId: 13,
        name: "Company 13",
        uniqueName: "company-13",
        id: "91ca9a3a-b236-4598-a572-955afdd77451",
      },
    },
    {
      invite: {
        id: "a77c7506-5e7b-4df9-810c-cd068ba5ede0",
        relationshipStatus: "Pending",
        expiryDate: "2025-02-27T02:28:20.951Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-21T21:22:24.826Z",
        dateUpdated: "2025-02-22T08:20:59.466Z",
        parentMemberId: "42a8fa12-7e1a-4770-b3e1-74c8d4d38c4f",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 14,
        traderId: 14,
        name: "Company 14",
        uniqueName: "company-14",
        id: "42a8fa12-7e1a-4770-b3e1-74c8d4d38c4f",
      },
    },
    {
      invite: {
        id: "2fe6a7a1-0645-49ac-90f8-c0d648745ef1",
        relationshipStatus: "Accepted",
        expiryDate: "2025-02-26T11:14:53.335Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-21T10:43:38.660Z",
        dateUpdated: "2025-02-22T06:34:46.436Z",
        parentMemberId: "5925095a-a552-49dd-8597-fbf14779ba38",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 15,
        traderId: 15,
        name: "Company 15",
        uniqueName: "company-15",
        id: "5925095a-a552-49dd-8597-fbf14779ba38",
      },
    },
    {
      invite: {
        id: "ab95d0d1-fcb0-451a-84e8-552071281347",
        relationshipStatus: "Accepted",
        expiryDate: "2025-02-24T19:26:37.340Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-21T08:09:10.090Z",
        dateUpdated: "2025-02-21T11:05:02.731Z",
        parentMemberId: "a0737ef0-c49b-4f63-bda7-635de69c272d",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 16,
        traderId: 16,
        name: "Company 16",
        uniqueName: "company-16",
        id: "a0737ef0-c49b-4f63-bda7-635de69c272d",
      },
    },
    {
      invite: {
        id: "e8ecf085-5772-4d33-83b3-b0c3c5633b62",
        relationshipStatus: null,
        expiryDate: "2025-02-24T13:06:26.576Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-17T08:07:12.032Z",
        dateUpdated: "2025-02-19T16:00:16.783Z",
        parentMemberId: "7076dc53-cc31-4c8e-81ef-d4ae4c322101",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 17,
        traderId: 17,
        name: "Company 17",
        uniqueName: "company-17",
        id: "7076dc53-cc31-4c8e-81ef-d4ae4c322101",
      },
    },
    {
      invite: {
        id: "e1ef3c89-b48a-427f-a08b-525dc12e7d47",
        relationshipStatus: "Pending",
        expiryDate: "2025-02-20T01:10:24.740Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-18T11:40:37.991Z",
        dateUpdated: "2025-02-19T06:59:52.996Z",
        parentMemberId: "0b44d661-198a-4738-971d-52528029e974",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 18,
        traderId: 18,
        name: "Company 18",
        uniqueName: "company-18",
        id: "0b44d661-198a-4738-971d-52528029e974",
      },
    },
    {
      invite: {
        id: "8ef8056d-0207-4aad-b626-02de2a4ee4d8",
        relationshipStatus: "Accepted",
        expiryDate: "2025-02-19T02:14:05.759Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-13T16:48:06.148Z",
        dateUpdated: "2025-02-16T23:44:26.122Z",
        parentMemberId: "f2fdd07b-4386-4026-9c9c-e4b26c5f9703",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 19,
        traderId: 19,
        name: "Company 19",
        uniqueName: "company-19",
        id: "f2fdd07b-4386-4026-9c9c-e4b26c5f9703",
      },
    },
    {
      invite: {
        id: "754e7d26-fd8f-4426-b999-495de6eb6333",
        relationshipStatus: "Accepted",
        expiryDate: "2025-02-24T20:03:10.985Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-17T08:11:28.617Z",
        dateUpdated: "2025-02-15T22:33:39.726Z",
        parentMemberId: "5e855284-9103-452a-be57-20549d20613b",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 20,
        traderId: 20,
        name: "Company 20",
        uniqueName: "company-20",
        id: "5e855284-9103-452a-be57-20549d20613b",
      },
    },
    {
      invite: {
        id: "26a5f4b6-a938-4a2f-b055-7ef4fad5d3bf",
        relationshipStatus: "Pending",
        expiryDate: "2025-02-18T03:45:12.131Z",
        relationshipType: "Subcontractor",
        emailAddress: "<EMAIL>",
        dateCreated: "2025-02-16T11:44:01.838Z",
        dateUpdated: "2025-02-15T13:46:18.609Z",
        parentMemberId: "a3010d5e-22cf-417d-85a2-d0bf7100b9ea",
        childMemberId: "01961ecd-d2cd-7f27-b79f-200caea5cd87",
        vettingStatus: "ACTIVE",
      },
      childCompany: {
        companyId: 21,
        traderId: 21,
        name: "Company 21",
        uniqueName: "company-21",
        id: "a3010d5e-22cf-417d-85a2-d0bf7100b9ea",
      },
    },
  ],
  pagination: {
    currentPage: 1,
    pageSize: 10,
    totalRecordsCount: 20,
    totalPagesCount: 2,
  },
};
