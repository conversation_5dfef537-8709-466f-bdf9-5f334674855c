import { quotingSDK } from "@checkatrade/quoting-sdk";

export const quotingSdkTradeMock = {
  getQuote: jest.fn(),
  getQuotesByJobId: jest.fn(),
  getQuotesByOpportunityId: jest.fn(),
  deleteQuote: jest.fn(),
  config: {},
  token: "",
};

export const mockQuotingSdkTrade = () => {
  jest
    .spyOn(quotingSDK, "trade")
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    .mockImplementation((_token) => quotingSdkTradeMock as any);
};
