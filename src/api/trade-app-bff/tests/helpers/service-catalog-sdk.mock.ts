import { serviceCatalogSDK } from "@checkatrade/service-catalog-sdk";

export const serviceCatalogSdkTradeMock = {
  serviceVersions: {
    createServiceVersion: jest.fn(),
    updateServiceVersion: jest.fn(),
    getServiceHistory: jest.fn(),
  },
};

export const mockConsumerSdkTrade = () => {
  jest
    .spyOn(serviceCatalogSDK, "trade", "get")
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    .mockImplementation(() => serviceCatalogSdkTradeMock as any);
};
