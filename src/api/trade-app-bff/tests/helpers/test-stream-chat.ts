import { chatSD<PERSON> } from "@checkatrade/chat-sdk";
import nock from "nock";

import getstreamChannelSuccess from "./nock/getstream-channel-success.json";
import getstreamMessageById from "./nock/getstream-message-by-id.json";

const apiKey = process.env.STREAM_CHAT_API_KEY;
const baseUrl = "https://chat.stream-io-api.com";

export const nockFindChannel = (
  channelId: string,
  smartMessages?: Awaited<ReturnType<typeof chatSDK.getChannelSmartMessages>>,
) => {
  const url = new URL(`${baseUrl}/channels`);

  return nock(url.origin)
    .post(url.pathname, {
      filter_conditions: {
        cid: `messaging:${channelId}`,
      },
      sort: [],
      state: true,
      watch: false,
      presence: false,
    })
    .query({ api_key: apiKey })
    .reply(200, {
      channels: [
        {
          ...getstreamChannelSuccess.channels[0],
          channel: {
            ...getstreamChannelSuccess.channels[0].channel,
            id: channelId,
            cid: `messaging:${channelId}`,
            ...(smartMessages ? { smartMessages } : {}),
          },
        },
      ],
    });
};

export const nockGetMessageById = ({
  messageId,
  createdAt,
}: {
  messageId: string;
  createdAt?: Date | string;
}) => {
  const url = new URL(`${baseUrl}/messages/${messageId}`);

  return nock(url.origin)
    .get(url.pathname)
    .query({ api_key: apiKey })
    .reply(200, {
      message: {
        ...getstreamMessageById.message,
        id: messageId,
        created_at:
          createdAt ?
            new Date(createdAt).toISOString()
          : getstreamMessageById.message.created_at,
      },
    });
};

export const nockGetChannels = () => {
  return nock(baseUrl)
    .post("/channels", (body) => !!body)
    .query({ api_key: apiKey })
    .reply(200);
};
