import { FastifyInstance } from "fastify";
import "jest-extended";

import { server } from "../src/server";

export let app: FastifyInstance;

jest.mock("@google-cloud/pubsub", () => {
  const publishMessage = jest.fn();
  class PubSub {
    topic = () => ({ publishMessage });
  }
  return { PubSub };
});

jest.mock("@checkatrade/payment-sdk", () =>
  jest.requireActual("@checkatrade/payment-sdk/dist/mock"),
);
jest.mock("@checkatrade/quoting-sdk", () =>
  jest.requireActual("@checkatrade/quoting-sdk"),
);

beforeAll(async () => {
  app = await server.buildApp();
});

afterAll(server.stop);
