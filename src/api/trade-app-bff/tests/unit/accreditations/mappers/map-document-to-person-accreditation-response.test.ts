import dayjs from "dayjs";

import { AccreditationStatus } from "../../../../src/controllers/accreditations/accreditations.schema";
import { mapDocumentToPersonAccreditationResponse } from "../../../../src/controllers/accreditations/mappers";
import { Accreditation } from "../../../../src/services/firebase/firestore/schemas/accreditation";
import {
  AccreditationMutationType,
  AccreditationPlatform,
  AccreditationStatusType,
  PersonAccreditation,
} from "../../../../src/services/firebase/firestore/schemas/person-accreditations";

const PersonAccreditationMock = {
  id: "docId",
  withFurtherAction: false,
  accreditationId: 12,
  companyId: 123,
  isDeleted: false,
  personId: "person-id",
};

describe("mapDocumentToPersonAccreditationResponse mapper", () => {
  describe("Does not expire", () => {
    const updatedDate = dayjs("2025-03-03 12:21:32").toDate();
    const modifiedDate = dayjs("2025-03-02 12:21:32").toDate();
    const approvedDate = dayjs("2025-03-01 12:21:32").toDate();

    const accreditation: Accreditation = {
      accreditationId: 12,
      canExpire: false,
      isDeleted: false,
      name: "name",
      requiresApproval: false,
    };

    const expectedResponse = {
      accreditationId: 12,
      companyId: 123,
      id: "docId",
      personId: "person-id",
      canExpire: true,
      proof: [],
    };

    it("APPROVED - no verification needed", () => {
      const document: PersonAccreditation = {
        ...PersonAccreditationMock,
        approvedDate,
        history: [
          {
            platform: AccreditationPlatform.TradeApp,
            type: AccreditationMutationType.Approved,
            updatedDate,
          },
        ],
        modifiedDate,
        status: AccreditationStatusType.Approved,
      };

      const response = mapDocumentToPersonAccreditationResponse(
        document,
        accreditation,
      );

      expect(response).toEqual({
        ...expectedResponse,
        canExpire: false,
        accreditationName: accreditation.name,
        accreditationLogo: accreditation.logoFilename,
        status: AccreditationStatus.Active,
        statusDate: approvedDate.toISOString(),
        statusText: "Active from 1 Mar 2025",
      });
    });

    describe("Expires", () => {
      const updatedDate = dayjs("2025-03-03 12:21:32").toDate();
      const updatedMostRecentDate = dayjs("2025-03-04 12:21:32").toDate();
      const modifiedDate = dayjs("2025-03-02 12:21:32").toDate();
      const approvedDate = dayjs("2025-03-01 12:21:32").toDate();

      const accreditation: Accreditation = {
        accreditationId: 12,
        requiresApproval: true,
        name: "test accreditation",
        logoFilename: "logo.png",
        canExpire: true,
        isDeleted: false,
      };

      it("PENDING approval", () => {
        const document: PersonAccreditation = {
          ...PersonAccreditationMock,
          approvedDate,
          history: [
            {
              platform: AccreditationPlatform.TradeApp,
              type: AccreditationMutationType.Submitted,
              updatedDate,
            },
          ],
          modifiedDate,
          status: AccreditationStatusType.Pending,
        };

        const response = mapDocumentToPersonAccreditationResponse(
          document,
          accreditation,
        );

        expect(response).toEqual({
          ...expectedResponse,
          accreditationName: accreditation.name,
          accreditationLogo: accreditation.logoFilename,
          status: AccreditationStatus.PendingReview,
          statusDate: updatedDate.toISOString(),
          statusText: "Added 3 Mar 2025",
        });
      });

      it("APPROVED", () => {
        const document: PersonAccreditation = {
          ...PersonAccreditationMock,
          approvedDate,
          history: [
            {
              platform: AccreditationPlatform.TradeApp,
              type: AccreditationMutationType.Submitted,
              updatedDate,
            },
            {
              platform: AccreditationPlatform.Salesforce,
              type: AccreditationMutationType.Approved,
              updatedDate,
            },
          ],
          modifiedDate,
          status: AccreditationStatusType.Approved,
        };

        const response = mapDocumentToPersonAccreditationResponse(
          document,
          accreditation,
        );

        expect(response).toEqual({
          ...expectedResponse,
          accreditationName: accreditation.name,
          accreditationLogo: accreditation.logoFilename,
          status: AccreditationStatus.Active,
          statusDate: approvedDate.toISOString(),
          statusText: "Active from 1 Mar 2025",
        });
      });

      it("REJECTED", () => {
        const document: PersonAccreditation = {
          ...PersonAccreditationMock,
          approvedDate,
          history: [
            {
              platform: AccreditationPlatform.TradeApp,
              type: AccreditationMutationType.Submitted,
              updatedDate,
            },
            {
              platform: AccreditationPlatform.Salesforce,
              type: AccreditationMutationType.Rejected,
              updatedDate: updatedMostRecentDate,
            },
          ],
          modifiedDate,
          status: AccreditationStatusType.Rejected,
        };

        const response = mapDocumentToPersonAccreditationResponse(
          document,
          accreditation,
        );

        expect(response).toEqual({
          ...expectedResponse,
          accreditationName: accreditation.name,
          accreditationLogo: accreditation.logoFilename,
          status: AccreditationStatus.Rejected,
          statusDate: updatedMostRecentDate.toISOString(),
          statusText: "Rejected 4 Mar 2025",
        });
      });

      it("EXPIRED", () => {
        const expiryInThePastDate = dayjs("2024-02-03 12:21:32").toDate();

        const document: PersonAccreditation = {
          ...PersonAccreditationMock,
          approvedDate,
          expiryDate: expiryInThePastDate,
          history: [
            {
              platform: AccreditationPlatform.TradeApp,
              type: AccreditationMutationType.Submitted,
              updatedDate,
            },
            {
              platform: AccreditationPlatform.Salesforce,
              type: AccreditationMutationType.Approved,
              updatedDate,
            },
          ],
          modifiedDate,
          status: AccreditationStatusType.Approved,
        };

        const response = mapDocumentToPersonAccreditationResponse(
          document,
          accreditation,
        );

        expect(response).toEqual({
          ...expectedResponse,
          accreditationName: accreditation.name,
          accreditationLogo: accreditation.logoFilename,
          status: AccreditationStatus.Expired,
          statusDate: expiryInThePastDate.toISOString(),
          statusText: "Expired 3 Feb 2024",
        });
      });

      it("APPROVED - expires soon", () => {
        const expiryDate = dayjs().add(2, "days").toDate();

        const document: PersonAccreditation = {
          ...PersonAccreditationMock,
          approvedDate,
          expiryDate,
          history: [
            {
              platform: AccreditationPlatform.TradeApp,
              type: AccreditationMutationType.Submitted,
              updatedDate,
            },
            {
              platform: AccreditationPlatform.Salesforce,
              type: AccreditationMutationType.Approved,
              updatedDate,
            },
          ],
          modifiedDate,
          status: AccreditationStatusType.Approved,
        };

        const response = mapDocumentToPersonAccreditationResponse(
          document,
          accreditation,
        );

        expect(response).toEqual({
          ...expectedResponse,
          accreditationName: accreditation.name,
          accreditationLogo: accreditation.logoFilename,
          status: AccreditationStatus.ExpiresSoon,
          statusDate: expiryDate.toISOString(),
          statusText: `Expires on ${dayjs(expiryDate).format("D MMM YYYY")}`,
        });
      });
    });
  });
});
