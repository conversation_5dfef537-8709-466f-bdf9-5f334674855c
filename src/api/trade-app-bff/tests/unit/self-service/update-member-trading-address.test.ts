import { faker } from "@faker-js/faker";
import { v7 as UUID7, v4 as UUIDv4 } from "uuid";

import { address } from "../../../src/services/address";
import * as memberDetails from "../../../src/services/member-details/member-details";
import { selfService } from "../../../src/services/self-service";
import { TEST_USER_EMAIL } from "../../helpers";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("updateMemberTradingAddress", () => {
  let request: TestRequest;
  const mockCompanyId = 123;
  const mockValidCorrelationId = UUID7();

  const mockMemberAddressMinimumFields = {
    street: faker.location.street(),
    city: faker.location.city(),
    postcode: "NW7 1LD",
  };

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("When user is the account owner", () => {
    beforeEach(() => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(true);

      jest
        .spyOn(selfService, "memberTradingAddressRequest")
        .mockResolvedValueOnce(undefined);

      jest.spyOn(address, "validateAddress").mockResolvedValueOnce(true);
    });

    it("should update member trading address when a correlation id is provided", async () => {
      const response = await request
        .post("/self-service/member-trading-address")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockMemberAddressMinimumFields);

      expect(selfService.memberTradingAddressRequest).toHaveBeenCalledWith({
        correlationId: mockValidCorrelationId,
        data: {
          companyId: mockCompanyId.toString(),
          address: mockMemberAddressMinimumFields,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    it("should update member trading address when a correlation id is NOT provided", async () => {
      const response = await request
        .post("/self-service/member-trading-address")
        .send(mockMemberAddressMinimumFields);

      expect(selfService.memberTradingAddressRequest).toHaveBeenCalledWith({
        correlationId: expect.any(String),
        data: {
          companyId: mockCompanyId.toString(),
          address: mockMemberAddressMinimumFields,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    it("should update member trading address when optional fields are provided", async () => {
      const mockMemberAddressWithOptionalFields = {
        ...mockMemberAddressMinimumFields,
        county: faker.location.county(),
      };

      const response = await request
        .post("/self-service/member-trading-address")
        .send(mockMemberAddressWithOptionalFields);

      expect(selfService.memberTradingAddressRequest).toHaveBeenCalledWith({
        correlationId: expect.any(String),
        data: {
          companyId: mockCompanyId.toString(),
          address: mockMemberAddressWithOptionalFields,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    describe("should fail if correlation ID is not a valid UUIDv7", () => {
      it("should fail if correlation ID is not a valid UUID", async () => {
        const response = await request
          .post("/self-service/member-trading-address")
          .set("x-correlation-id", "invalid uuid")
          .send(mockMemberAddressMinimumFields);

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.memberTradingAddressRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });

      it("should fail if correlation ID is not a valid UUIDv7", async () => {
        const v4UUID = UUIDv4();

        const response = await request
          .post("/self-service/member-trading-address")
          .set("x-correlation-id", v4UUID)
          .send(mockMemberAddressMinimumFields);

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.memberTradingAddressRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });
    });

    it("should validate request body", async () => {
      const response = await request
        .post("/self-service/member-trading-address")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({});

      expect(selfService.memberTradingAddressRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(400);
    });

    it("should return 400 status when providing an invalid postcode", async () => {
      const response = await request
        .post("/self-service/member-trading-address")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({
          ...mockMemberAddressMinimumFields,
          postcode: "invalid postcode",
        });

      expect(selfService.memberTradingAddressRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(400);
    });
  });

  describe("When user is NOT the account owner", () => {
    it("should return 403", async () => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(false);

      jest
        .spyOn(selfService, "memberTradingAddressRequest")
        .mockResolvedValueOnce(undefined);

      const response = await request
        .post("/self-service/member-trading-address")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockMemberAddressMinimumFields);

      expect(selfService.memberTradingAddressRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(403);
    });
  });

  describe("When user address is not valid", () => {
    it("should return 422", async () => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(true);

      jest
        .spyOn(selfService, "memberTradingAddressRequest")
        .mockResolvedValueOnce(undefined);

      jest.spyOn(address, "validateAddress").mockResolvedValueOnce(false);

      const response = await request
        .post("/self-service/member-trading-address")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockMemberAddressMinimumFields);

      expect(selfService.memberTradingAddressRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(422);
    });
  });
});
