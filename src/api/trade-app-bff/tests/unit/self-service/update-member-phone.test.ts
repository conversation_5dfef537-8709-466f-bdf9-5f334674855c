import { faker } from "@faker-js/faker";
import { v7 as UUID7, v4 as UUIDv4 } from "uuid";

import * as memberDetails from "../../../src/services/member-details/member-details";
import { selfService } from "../../../src/services/self-service";
import { TEST_USER_EMAIL } from "../../helpers";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("updateMemberPhone", () => {
  let request: TestRequest;
  const mockCompanyId = 123;
  const mockValidCorrelationId = UUID7();
  const mockMemberPhone = faker.phone.number();

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("When user is the account owner", () => {
    beforeEach(() => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(true);

      jest
        .spyOn(selfService, "memberPhoneRequest")
        .mockResolvedValueOnce(undefined);
    });

    it("should update member phone when a correlation id is provided", async () => {
      const response = await request
        .post("/self-service/member-phone")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({
          memberPhone: mockMemberPhone,
        });

      expect(selfService.memberPhoneRequest).toHaveBeenCalledWith({
        correlationId: mockValidCorrelationId,
        data: {
          companyId: mockCompanyId.toString(),
          phone: mockMemberPhone,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    it("should update member phone when a correlation id is NOT provided", async () => {
      const response = await request.post("/self-service/member-phone").send({
        memberPhone: mockMemberPhone,
      });

      expect(selfService.memberPhoneRequest).toHaveBeenCalledWith({
        correlationId: expect.any(String),
        data: {
          companyId: mockCompanyId.toString(),
          phone: mockMemberPhone,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    describe("should fail if correlation ID is not a valid UUIDv7", () => {
      it("should fail if correlation ID is not a valid UUID", async () => {
        const response = await request
          .post("/self-service/member-phone")
          .set("x-correlation-id", "invalid uuid")
          .send({
            memberPhone: mockMemberPhone,
          });

        expect(selfService.memberPhoneRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });

      it("should fail if correlation ID is not a valid UUIDv7", async () => {
        const v4UUID = UUIDv4();

        const response = await request
          .post("/self-service/member-phone")
          .set("x-correlation-id", v4UUID)
          .send({
            memberPhone: mockMemberPhone,
          });

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.memberPhoneRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });
    });

    it("should validate request body", async () => {
      const response = await request
        .post("/self-service/member-phone")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({});

      expect(selfService.memberPhoneRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(400);
    });
  });

  describe("When user is NOT the account owner", () => {
    it("should return 403", async () => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(false);

      jest
        .spyOn(selfService, "memberPhoneRequest")
        .mockResolvedValueOnce(undefined);

      const response = await request
        .post("/self-service/member-phone")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({
          memberPhone: mockMemberPhone,
        });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(selfService.memberPhoneRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(403);
    });
  });
});
