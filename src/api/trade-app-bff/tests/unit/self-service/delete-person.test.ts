import {
  DeletePersonType,
  PersonCompanyRoleDto,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";
import { v7 as UUID7, v4 as UUIDv4 } from "uuid";

import { address } from "../../../src/services/address";
import * as memberDetails from "../../../src/services/member-details/member-details";
import { selfService } from "../../../src/services/self-service";
import { TEST_USER_EMAIL } from "../../helpers";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("deletePerson", () => {
  let request: TestRequest;
  const mockCompanyId = 123;
  const mockValidCorrelationId = UUID7();
  const mockPerson: DeletePersonType = {
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    contactId: faker.string.uuid(),
    companyRole: PersonCompanyRoleDto.ADMIN_CONTACT,
  };

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("When user is the account owner", () => {
    beforeEach(() => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(true);

      jest
        .spyOn(selfService, "deletePersonRequest")
        .mockResolvedValueOnce(undefined);

      jest.spyOn(address, "validateAddress").mockResolvedValueOnce(true);
    });

    it("should delete person when a correlation id is provided", async () => {
      const response = await request
        .post("/self-service/delete-person")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockPerson);

      expect(selfService.deletePersonRequest).toHaveBeenCalledWith({
        correlationId: mockValidCorrelationId,
        data: {
          companyId: mockCompanyId.toString(),
          personId: mockPerson.contactId,
          firstName: mockPerson.firstName,
          lastName: mockPerson.lastName,
          email: mockPerson.email,
          companyRole: mockPerson.companyRole,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    it("should delete person when a correlation id is NOT provided", async () => {
      const response = await request
        .post("/self-service/delete-person")
        .send(mockPerson);

      expect(selfService.deletePersonRequest).toHaveBeenCalledWith({
        correlationId: expect.any(String),
        data: {
          companyId: mockCompanyId.toString(),
          personId: mockPerson.contactId,
          firstName: mockPerson.firstName,
          lastName: mockPerson.lastName,
          email: mockPerson.email,
          companyRole: mockPerson.companyRole,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    describe("should fail if correlation ID is not a valid UUIDv7", () => {
      it("should fail if correlation ID is not a valid UUID", async () => {
        const response = await request
          .post("/self-service/delete-person")
          .set("x-correlation-id", "invalid uuid")
          .send(mockPerson);

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.deletePersonRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });

      it("should fail if correlation ID is not a valid UUIDv7", async () => {
        const v4UUID = UUIDv4();

        const response = await request
          .post("/self-service/delete-person")
          .set("x-correlation-id", v4UUID)
          .send(mockPerson);

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.deletePersonRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });
    });

    it("should validate request body", async () => {
      const response = await request
        .post("/self-service/delete-person")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({});

      expect(selfService.deletePersonRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(400);
    });
  });

  describe("When user is NOT the account owner", () => {
    it("should return 403", async () => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(false);

      jest
        .spyOn(selfService, "deletePersonRequest")
        .mockResolvedValueOnce(undefined);

      const response = await request
        .post("/self-service/delete-person")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockPerson);

      expect(selfService.deletePersonRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(403);
    });
  });
});
