import { faker } from "@faker-js/faker";
import { v7 as UUID7, v4 as UUIDv4 } from "uuid";

import * as memberDetails from "../../../src/services/member-details/member-details";
import { selfService } from "../../../src/services/self-service";
import { TEST_USER_EMAIL } from "../../helpers";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("updateMemberEmail", () => {
  let request: TestRequest;
  const mockCompanyId = 123;
  const mockValidCorrelationId = UUID7();
  const mockMemberEmail = faker.internet.email();

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("When user is the account owner", () => {
    beforeEach(() => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(true);

      jest
        .spyOn(selfService, "memberEmailRequest")
        .mockResolvedValueOnce(undefined);
    });

    it("should update member email when a correlation id is provided", async () => {
      const response = await request
        .post("/self-service/member-email")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({
          memberEmail: mockMemberEmail,
        });

      expect(selfService.memberEmailRequest).toHaveBeenCalledWith({
        correlationId: mockValidCorrelationId,
        data: {
          companyId: mockCompanyId.toString(),
          email: mockMemberEmail,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    it("should update member email when a correlation id is NOT provided", async () => {
      const response = await request.post("/self-service/member-email").send({
        memberEmail: mockMemberEmail,
      });

      expect(selfService.memberEmailRequest).toHaveBeenCalledWith({
        correlationId: expect.any(String),
        data: {
          companyId: mockCompanyId.toString(),
          email: mockMemberEmail,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    describe("should fail if correlation ID is not a valid UUIDv7", () => {
      it("should fail if correlation ID is not a valid UUID", async () => {
        const response = await request
          .post("/self-service/member-email")
          .set("x-correlation-id", "invalid uuid")
          .send({
            memberEmail: mockMemberEmail,
          });

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.memberEmailRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });

      it("should fail if correlation ID is not a valid UUIDv7", async () => {
        const v4UUID = UUIDv4();

        const response = await request
          .post("/self-service/member-email")
          .set("x-correlation-id", v4UUID)
          .send({
            memberEmail: mockMemberEmail,
          });

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.memberEmailRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });
    });

    it("should validate request body", async () => {
      const response = await request
        .post("/self-service/member-email")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({});

      expect(selfService.memberEmailRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(400);
    });
  });

  describe("When user is NOT the account owner", () => {
    it("should return 403", async () => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(false);

      jest
        .spyOn(selfService, "memberEmailRequest")
        .mockResolvedValueOnce(undefined);

      const response = await request
        .post("/self-service/member-email")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({
          memberEmail: mockMemberEmail,
        });

      expect(selfService.memberEmailRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(403);
    });
  });
});
