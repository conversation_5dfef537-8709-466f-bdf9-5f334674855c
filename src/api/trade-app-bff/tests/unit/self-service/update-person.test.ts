import {
  PersonCompanyRoleDto,
  UpdatePersonType,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";
import { v7 as UUID7, v4 as UUIDv4 } from "uuid";

import { address } from "../../../src/services/address";
import * as memberDetails from "../../../src/services/member-details/member-details";
import { selfService } from "../../../src/services/self-service";
import { TEST_USER_EMAIL } from "../../helpers";
import {
  TestRequest,
  getTestRequestWithValidToken,
} from "../../helpers/test-request";

describe("updatePerson", () => {
  let request: TestRequest;
  const mockCompanyId = 123;
  const mockValidCorrelationId = UUID7();
  const mockMemberAddressMinimumFields = {
    street: faker.location.street(),
    city: faker.location.city(),
    postcode: "NW7 1LD",
  };
  const mockPerson: UpdatePersonType = {
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    mobilePhone: faker.phone.number(),
    contactId: faker.string.uuid(),
    dateOfBirth: faker.date.birthdate().toISOString(),
    mailingAddress: mockMemberAddressMinimumFields,
    companyRole: PersonCompanyRoleDto.ADMIN_CONTACT,
  };
  const { contactId, ...rest } = mockPerson;
  const expectedPerson = {
    personId: contactId,
    ...rest,
  };

  beforeEach(() => {
    request = getTestRequestWithValidToken(mockCompanyId);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("When user is the account owner", () => {
    beforeEach(() => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(true);

      jest
        .spyOn(selfService, "updatePersonRequest")
        .mockResolvedValueOnce(undefined);

      jest.spyOn(address, "validateAddress").mockResolvedValueOnce(true);
    });

    it("should update person when a correlation id is provided", async () => {
      const response = await request
        .post("/self-service/update-person")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockPerson);

      expect(selfService.updatePersonRequest).toHaveBeenCalledWith({
        correlationId: mockValidCorrelationId,
        data: {
          companyId: mockCompanyId.toString(),
          ...expectedPerson,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    it("should update person when a correlation id is NOT provided", async () => {
      const response = await request
        .post("/self-service/update-person")
        .send(mockPerson);

      expect(selfService.updatePersonRequest).toHaveBeenCalledWith({
        correlationId: expect.any(String),
        data: {
          companyId: mockCompanyId.toString(),
          ...expectedPerson,
          requesterEmail: TEST_USER_EMAIL,
        },
      });

      expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
        companyId: mockCompanyId,
        userEmail: TEST_USER_EMAIL,
      });

      expect(response.statusCode).toBe(200);
    });

    describe("should fail if correlation ID is not a valid UUIDv7", () => {
      it("should fail if correlation ID is not a valid UUID", async () => {
        const response = await request
          .post("/self-service/update-person")
          .set("x-correlation-id", "invalid uuid")
          .send(mockPerson);

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.updatePersonRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });

      it("should fail if correlation ID is not a valid UUIDv7", async () => {
        const v4UUID = UUIDv4();

        const response = await request
          .post("/self-service/update-person")
          .set("x-correlation-id", v4UUID)
          .send(mockPerson);

        expect(memberDetails.checkAccountOwner).toHaveBeenCalledWith({
          companyId: mockCompanyId,
          userEmail: TEST_USER_EMAIL,
        });

        expect(selfService.updatePersonRequest).not.toHaveBeenCalled();

        expect(response.statusCode).toBe(400);
      });
    });

    it("should validate request body", async () => {
      const response = await request
        .post("/self-service/update-person")
        .set("x-correlation-id", mockValidCorrelationId)
        .send({});

      expect(selfService.updatePersonRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(400);
    });
  });

  describe("When user is NOT the account owner", () => {
    it("should return 403", async () => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(false);

      jest
        .spyOn(selfService, "updatePersonRequest")
        .mockResolvedValueOnce(undefined);

      const response = await request
        .post("/self-service/update-person")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockPerson);

      expect(selfService.updatePersonRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(403);
    });
  });

  describe("When user address is not valid", () => {
    it("should return 422", async () => {
      jest
        .spyOn(memberDetails, "checkAccountOwner")
        .mockResolvedValueOnce(true);

      jest
        .spyOn(selfService, "updatePersonRequest")
        .mockResolvedValueOnce(undefined);

      jest.spyOn(address, "validateAddress").mockResolvedValueOnce(false);

      const response = await request
        .post("/self-service/update-person")
        .set("x-correlation-id", mockValidCorrelationId)
        .send(mockPerson);

      expect(selfService.updatePersonRequest).not.toHaveBeenCalled();

      expect(response.statusCode).toBe(422);
    });
  });
});
