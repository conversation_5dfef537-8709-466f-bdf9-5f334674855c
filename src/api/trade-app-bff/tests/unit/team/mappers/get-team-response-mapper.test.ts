import {
  CompanyRole,
  GetTeamTradeDataResponse,
  GetWorkersVettingStatusResponse,
  MembershipType,
  VettingConsentStatus,
  VettingStatus,
  VettingStatusTVS,
  vettingStatusTVSMapper,
} from "@checkatrade/trade-bff-types";

import { mapToGetTeamResponse } from "../../../../src/controllers/team/mappers/get-team-response-mapper";

describe("mapToGetTeamResponse", () => {
  // Mock data setup
  const mockTeamPerson = {
    id: "person-id-1",
    firstName: "John",
    lastName: "Doe",
    fullName: "<PERSON>",
    dateOfBirth: "1990-01-01",
    lastUpdated: "2023-01-01",
    email: "<EMAIL>",
    mobilePhone: "1234567890",
    phone: "0987654321",
    role: CompanyRole.Employee,
    address: {
      line1: "123 Test Street",
      line2: "",
      city: "Test City",
      county: "Test County",
      postalCode: "TE1 1ST",
    },
  };

  const mockOwnerPerson = {
    ...mockTeamPerson,
    id: "person-id-2",
    role: CompanyRole.Owner,
    membershipType: MembershipType.FullMember,
  };

  const mockOwnerEssentials = {
    ...mockTeamPerson,
    id: "person-id-3",
    role: CompanyRole.Owner,
    membershipType: MembershipType.EssentialsMember,
  };

  const mockDirector = {
    ...mockTeamPerson,
    id: "person-id-4",
    role: CompanyRole.Director,
    membershipType: MembershipType.FullMember,
  };

  const mockTeamPersons: GetTeamTradeDataResponse = {
    data: [mockTeamPerson, mockOwnerPerson, mockOwnerEssentials, mockDirector],
    pagination: {
      currentPage: 1,
      pageSize: 10,
      totalPagesCount: 1,
      totalRecordsCount: 4,
    },
  };

  const mockVettingWorker = {
    vettingStatus: "InFlight" as const,
    consentStatus: "Granted" as const,
    tradePersonId: "vetting-id-1",
    externalId: "person-id-1",
  };

  const mockTeamVettingWorkers: GetWorkersVettingStatusResponse["workers"] = [
    mockVettingWorker,
  ];

  const mockCompanyVettingStatus = VettingStatus.Active;

  it("should map team persons data correctly", () => {
    const result = mapToGetTeamResponse(
      mockTeamPersons,
      mockTeamVettingWorkers,
      mockCompanyVettingStatus,
    );

    expect(result).toEqual({
      data: [
        {
          id: "person-id-1",
          firstName: "John",
          lastName: "Doe",
          fullName: "John Doe",
          dateOfBirth: "1990-01-01",
          lastUpdated: "2023-01-01",
          email: "<EMAIL>",
          mobilePhone: "1234567890",
          phone: "0987654321",
          role: "Employee",
          address: {
            line1: "123 Test Street",
            line2: "",
            city: "Test City",
            county: "Test County",
            postalCode: "TE1 1ST",
          },
          vettingStatus: "IN_PROGRESS",
          consentStatus: "Granted",
        },
        {
          id: "person-id-2",
          firstName: "John",
          lastName: "Doe",
          fullName: "John Doe",
          dateOfBirth: "1990-01-01",
          lastUpdated: "2023-01-01",
          email: "<EMAIL>",
          mobilePhone: "1234567890",
          phone: "0987654321",
          role: "Owner",
          address: {
            line1: "123 Test Street",
            line2: "",
            city: "Test City",
            county: "Test County",
            postalCode: "TE1 1ST",
          },
          membershipType: MembershipType.FullMember,
          vettingStatus: "ACTIVE",
          consentStatus: "NotCreated",
        },
        {
          id: "person-id-3",
          firstName: "John",
          lastName: "Doe",
          fullName: "John Doe",
          dateOfBirth: "1990-01-01",
          lastUpdated: "2023-01-01",
          email: "<EMAIL>",
          mobilePhone: "1234567890",
          phone: "0987654321",
          role: "Owner",
          address: {
            line1: "123 Test Street",
            line2: "",
            city: "Test City",
            county: "Test County",
            postalCode: "TE1 1ST",
          },
          membershipType: MembershipType.EssentialsMember,
          vettingStatus: "ACTIVE",
          consentStatus: "Granted",
        },
        {
          id: "person-id-4",
          firstName: "John",
          lastName: "Doe",
          fullName: "John Doe",
          dateOfBirth: "1990-01-01",
          lastUpdated: "2023-01-01",
          email: "<EMAIL>",
          mobilePhone: "1234567890",
          phone: "0987654321",
          role: "Director",
          address: {
            line1: "123 Test Street",
            line2: "",
            city: "Test City",
            county: "Test County",
            postalCode: "TE1 1ST",
          },
          membershipType: MembershipType.FullMember,
          vettingStatus: "ACTIVE",
          consentStatus: "NotCreated",
        },
      ],
      pages: mockTeamPersons.pagination.totalPagesCount,
      total: mockTeamPersons.pagination.totalRecordsCount,
    });

    expect(result.data).toHaveLength(4);
  });

  it("an employee with their own vetting record should always have vetting status from that record", () => {
    const result = mapToGetTeamResponse(
      {
        data: [mockTeamPerson],
        pagination: mockTeamPersons.pagination,
      },
      mockTeamVettingWorkers,
      mockCompanyVettingStatus,
    );

    expect(result.data[0]).toEqual({
      ...mockTeamPerson,
      email: mockTeamPerson.email,
      vettingStatus: "IN_PROGRESS",
      consentStatus: mockVettingWorker.consentStatus,
    });
  });

  it("should handle employee without vetting record correctly", () => {
    const employeeWithoutVetting = {
      ...mockTeamPerson,
      id: "person-id-not-vetted",
    };

    const result = mapToGetTeamResponse(
      {
        data: [employeeWithoutVetting],
        pagination: mockTeamPersons.pagination,
      },
      mockTeamVettingWorkers,
      mockCompanyVettingStatus,
    );

    expect(result.data[0]).toEqual({
      ...employeeWithoutVetting,
      email: employeeWithoutVetting.email,
      vettingStatus: mockCompanyVettingStatus,
      consentStatus: VettingConsentStatus.Granted,
    });
  });

  it("should handle employee without vetting record and without company vetting status correctly", () => {
    const employeeWithoutVetting = {
      ...mockTeamPerson,
      id: "person-id-not-vetted",
    };

    const result = mapToGetTeamResponse(
      {
        data: [employeeWithoutVetting],
        pagination: mockTeamPersons.pagination,
      },
      mockTeamVettingWorkers,
      undefined,
    );

    expect(result.data[0]).toEqual({
      ...employeeWithoutVetting,
      email: employeeWithoutVetting.email,
      vettingStatus: VettingStatus.InProgress,
      consentStatus: VettingConsentStatus.Granted,
    });
  });

  it("should handle Owner/Director with non-Essentials membership correctly", () => {
    const result = mapToGetTeamResponse(
      {
        data: [mockOwnerPerson],
        pagination: mockTeamPersons.pagination,
      },
      mockTeamVettingWorkers,
      mockCompanyVettingStatus,
    );

    expect(result.data[0]).toEqual({
      ...mockOwnerPerson,
      email: mockOwnerPerson.email,
      vettingStatus: mockCompanyVettingStatus,
      consentStatus: VettingConsentStatus.NotCreated,
    });
  });

  it("should handle Director with non-Essentials membership correctly", () => {
    const result = mapToGetTeamResponse(
      {
        data: [mockDirector],
        pagination: mockTeamPersons.pagination,
      },
      mockTeamVettingWorkers,
      mockCompanyVettingStatus,
    );

    expect(result.data[0]).toEqual({
      ...mockDirector,
      email: mockDirector.email,
      vettingStatus: mockCompanyVettingStatus,
      consentStatus: VettingConsentStatus.NotCreated,
    });
  });

  it("should handle Owner with Essentials membership as a normal employee", () => {
    const result = mapToGetTeamResponse(
      {
        data: [mockOwnerEssentials],
        pagination: mockTeamPersons.pagination,
      },
      mockTeamVettingWorkers,
      mockCompanyVettingStatus,
    );

    // Since this person is an Owner but with Essentials membership,
    // they should be treated as an employee without vetting record
    expect(result.data[0]).toEqual({
      ...mockOwnerEssentials,
      email: mockOwnerEssentials.email,
      vettingStatus: mockCompanyVettingStatus,
      consentStatus: VettingConsentStatus.Granted,
    });
  });

  it("should handle employee with vetting record but no vetting status", () => {
    const workerWithoutStatus = {
      ...mockVettingWorker,
      vettingStatus: VettingStatusTVS.NotStarted,
    };

    const result = mapToGetTeamResponse(
      {
        data: [mockTeamPerson],
        pagination: mockTeamPersons.pagination,
      },
      [workerWithoutStatus],
      mockCompanyVettingStatus,
    );

    expect(result.data[0]).toEqual({
      ...mockTeamPerson,
      email: mockTeamPerson.email,
      vettingStatus: VettingStatus.NotStarted,
      consentStatus: VettingConsentStatus.Granted,
    });
  });

  it("should handle employee with vetting record but no consent status", () => {
    const workerWithoutConsent = {
      ...mockVettingWorker,
      consentStatus: VettingConsentStatus.NotCreated,
    };

    const result = mapToGetTeamResponse(
      {
        data: [mockTeamPerson],
        pagination: mockTeamPersons.pagination,
      },
      [workerWithoutConsent],
      mockCompanyVettingStatus,
    );

    expect(result.data[0]).toEqual({
      ...mockTeamPerson,
      email: mockTeamPerson.email,
      vettingStatus: vettingStatusTVSMapper(
        mockVettingWorker.vettingStatus as VettingStatusTVS,
      ),
      consentStatus: VettingConsentStatus.NotCreated,
    });
  });

  it("should handle missing email with empty string", () => {
    const personWithoutEmail = {
      ...mockTeamPerson,
      email: null,
    };

    const result = mapToGetTeamResponse(
      {
        data: [personWithoutEmail],
        pagination: mockTeamPersons.pagination,
      },
      mockTeamVettingWorkers,
      mockCompanyVettingStatus,
    );

    expect(result.data[0].email).toBe("");
  });
});
