import { BadRequestError } from "@checkatrade/errors";
import nock from "nock";

import { findTrades } from "../../../../../../src/lib/api-common/services/search-api/trade-card/find-trades";
import {
  nockFindTradesError,
  nockTradeCardSearchSuccess,
} from "../../../../../helpers/test-search-api";

describe("findTrades", () => {
  const validCategoryId = 1234;
  const validPostcode = "N19EB";
  let searchApiGetSpy: jest.SpyInstance;

  beforeEach(async () => {
    nock.cleanAll();
    searchApiGetSpy = jest.spyOn(
      await import(
        "../../../../../../src/lib/api-common/services/search-api/request"
      ),
      "searchApiGet",
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    nock.restore();
  });

  it("should call searchApiGet with correct params", async () => {
    nockTradeCardSearchSuccess({
      categoryId: validCategoryId,
      postcode: validPostcode,
    });

    await findTrades({
      categoryId: validCategoryId,
      postcode: validPostcode,
    });

    expect(searchApiGetSpy).toHaveBeenCalledWith("/trade-card/search", {
      params: {
        filter: `categoryId[eq]:${validCategoryId};postcode[eq]:${validPostcode}`,
        page: 1,
        size: 10,
      },
      url: "https://search-dev.checkatrade.com/api/v1/trade-card/search",
    });
    expect(nock.isDone()).toBe(true);
  });

  it("should include excludeCompanyIds in filter when provided", async () => {
    const excludeCompanyIds = [456, 789];
    nockTradeCardSearchSuccess({
      categoryId: validCategoryId,
      postcode: validPostcode,
      excludeCompanyIds,
    });

    await findTrades({
      categoryId: validCategoryId,
      postcode: validPostcode,
      excludeCompanyIds,
    });

    expect(searchApiGetSpy).toHaveBeenCalledWith("/trade-card/search", {
      params: {
        filter: `categoryId[eq]:${validCategoryId};postcode[eq]:${validPostcode};companyId[nin]:${excludeCompanyIds.join(",")}`,
        page: 1,
        size: 10,
      },
      url: "https://search-dev.checkatrade.com/api/v1/trade-card/search",
    });
    expect(nock.isDone()).toBe(true);
  });

  it("should handle custom page and size parameters", async () => {
    const customPage = 2;
    const customSize = 20;
    nockTradeCardSearchSuccess({
      categoryId: validCategoryId,
      postcode: validPostcode,
      page: customPage,
      size: customSize,
    });

    await findTrades({
      categoryId: validCategoryId,
      postcode: validPostcode,
      page: customPage,
      size: customSize,
    });

    expect(searchApiGetSpy).toHaveBeenCalledWith("/trade-card/search", {
      params: {
        filter: `categoryId[eq]:${validCategoryId};postcode[eq]:${validPostcode}`,
        page: customPage,
        size: customSize,
      },
      url: "https://search-dev.checkatrade.com/api/v1/trade-card/search",
    });
    expect(nock.isDone()).toBe(true);
  });

  it("should throw BadRequestError on 400 response", async () => {
    const errorDetail = "An unexpected error occurred";
    nockFindTradesError({
      statusCode: 400,
      errorMessage: errorDetail,
    });

    await expect(
      findTrades({
        categoryId: validCategoryId,
        postcode: validPostcode,
      }),
    ).rejects.toThrow(BadRequestError);

    await expect(
      findTrades({
        categoryId: validCategoryId,
        postcode: validPostcode,
      }),
    ).rejects.toThrow(errorDetail);
  });

  it("should use default error message when no detail is provided in 400 response", async () => {
    nockFindTradesError({
      statusCode: 400,
      errorMessage: undefined,
    });

    await expect(
      findTrades({
        categoryId: validCategoryId,
        postcode: validPostcode,
      }),
    ).rejects.toThrow("Some error message");
  });

  it("should throw original error for non-400 errors", async () => {
    nockFindTradesError({
      statusCode: 500,
      errorMessage: "Network error",
    });

    await expect(
      findTrades({
        categoryId: validCategoryId,
        postcode: validPostcode,
      }),
    ).rejects.toThrow();
  });
});
