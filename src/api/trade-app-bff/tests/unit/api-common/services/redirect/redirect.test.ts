import {
  FormattedJobStatus,
  FormattedOpportunityStatus,
  OpportunityType,
  ServiceJobResponse,
} from "@checkatrade/jobs-sdk";
import { Logger } from "@checkatrade/logging";

import { config } from "../../../../../src/services/redirect/config";
import * as findTradesModule from "../../../../../src/services/redirect/find-trades";
import {
  URGENT_PREFERRED_START,
  canJobBeRedirected,
  canTradeBeRedirected,
  redirectOnReject,
} from "../../../../../src/services/redirect/redirect";
import {
  jobsSdkServiceMock,
  mockJobsSdkService,
} from "../../../../helpers/mocks/jobs-sdk.mock";

// Use jest.spyOn instead of jest.mock for findTrades
jest.mock("../../../../../src/lib/api-common/formatters/format-trade-channels");
jest.mock("@checkatrade/chat-sdk", () => ({
  chatSDK: {
    upsertTradeUsers: jest.fn(),
    createChannels: jest.fn(),
    getImagesFromJobRequestMessage: jest.fn(),
  },
}));
jest.mock("@checkatrade/consumer-sdk", () => ({
  consumerSDK: {
    trade: jest.fn().mockReturnValue({
      getConsumer: jest.fn(),
    }),
  },
}));

jest.mock("../../../../../src/lib/api-common/services", () => ({
  nationalAccounts: {
    isNationalAccount: jest.fn(),
    serviceAutoAccept: jest.fn(),
    forward: jest.fn(),
  },
  search: {
    findInitialChannelId: jest.fn(),
  },
  searchApi: {
    getCategoryName: jest.fn(),
    findTrades: jest.fn(),
  },
}));

describe("redirect", () => {
  describe("canJobBeRedirected", () => {
    const baseDate = new Date("2024-01-01T12:00:00Z");
    const baseJob: ServiceJobResponse = {
      id: "test-job",
      createdAt: baseDate.toISOString(),
      categoryId: 1,
      description: "Test job",
      status: FormattedJobStatus.REQUESTED,
      consumerId: "consumer-123",
      postcode: "N19EB",
      address: {
        postcode: "N19EB",
        line1: "123 Test St",
        city: "London",
      },
      jobRefinementForm: {
        formId: "f1",
        experimentId: "e1",
        variant: "v1",
        questions: [
          {
            title: "test",
            questionId: "1",
            answers: [{ key: "a", value: "b" }],
            format: "TEXT",
          },
        ],
      },
      trades: [
        {
          id: "trade-1",
          companyId: 123,
          type: OpportunityType.REQUEST_A_QUOTE,
          status: FormattedOpportunityStatus.REQUEST_REJECTED,
        },
      ],
      preferredStart: {
        id: "regular-start",
        title: "Regular",
      },
    };

    it("should return true when all conditions are met", () => {
      const redirectedAt = new Date(baseDate.getTime() + 1000); // 1 second after creation
      const result = canJobBeRedirected(baseJob, redirectedAt);
      expect(result).toBe(true);
    });

    it("should return false if job status is not acceptable", () => {
      const job = {
        ...baseJob,
        status: FormattedJobStatus.CANCELLED,
      };
      const redirectedAt = new Date(baseDate.getTime() + 1000);
      const result = canJobBeRedirected(job, redirectedAt);
      expect(result).toBe(false);
    });

    it("should return false if job has expired (regular job)", () => {
      const tempConfig = config.redirection;
      config.redirection.jobExpiryHours = 24;
      const redirectedAt = new Date(baseDate.getTime() + 170 * 60 * 60 * 1000); // 170 hours after creation
      const result = canJobBeRedirected(baseJob, redirectedAt);
      expect(result).toBe(false);
      config.redirection = tempConfig;
    });

    it("should return false if urgent job has expired", () => {
      const urgentJob = {
        ...baseJob,
        preferredStart: {
          id: URGENT_PREFERRED_START,
          title: "Urgent",
        },
      };
      const redirectedAt = new Date(baseDate.getTime() + 5 * 60 * 60 * 1000); // 5 hours after creation
      const result = canJobBeRedirected(urgentJob, redirectedAt);
      expect(result).toBe(false);
    });

    it("should return false if job has non-REQUEST_A_QUOTE trades", () => {
      const job = {
        ...baseJob,
        trades: [
          {
            id: "trade-2",
            companyId: 456,
            type: OpportunityType.DIRECT_MESSAGE,
            status: FormattedOpportunityStatus.REQUEST_REJECTED,
          },
        ],
      };
      const redirectedAt = new Date(baseDate.getTime() + 1000);
      const result = canJobBeRedirected(job, redirectedAt);
      expect(result).toBe(false);
    });

    it("should return false if accepted trades count exceeds maxAccepts", () => {
      const job = {
        ...baseJob,
        trades: [
          {
            id: "trade-1",
            companyId: 123,
            type: OpportunityType.REQUEST_A_QUOTE,
            status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          },
          {
            id: "trade-2",
            companyId: 456,
            type: OpportunityType.REQUEST_A_QUOTE,
            status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          },
          {
            id: "trade-3",
            companyId: 789,
            type: OpportunityType.REQUEST_A_QUOTE,
            status: FormattedOpportunityStatus.REQUEST_REJECTED,
          },
        ],
      };
      const redirectedAt = new Date(baseDate.getTime() + 1000);
      const result = canJobBeRedirected(job, redirectedAt);
      expect(result).toBe(false);
    });

    it("should return false if total trades count exceeds maxOpportunities", () => {
      const job = {
        ...baseJob,
        trades: Array(6)
          .fill(null)
          .map((_, i) => ({
            id: `trade-${i + 1}`,
            companyId: 100 + i,
            type: OpportunityType.REQUEST_A_QUOTE,
            status: FormattedOpportunityStatus.REQUEST_REJECTED,
          })),
      };
      const redirectedAt = new Date(baseDate.getTime() + 1000);
      const result = canJobBeRedirected(job, redirectedAt);
      expect(result).toBe(false);
    });
  });

  describe("canTradeBeRedirected", () => {
    it("should return true for REQUEST_A_QUOTE trade", () => {
      const trade = {
        id: "trade-1",
        companyId: 123,
        type: OpportunityType.REQUEST_A_QUOTE,
        status: FormattedOpportunityStatus.REQUEST_REJECTED,
      };
      expect(canTradeBeRedirected(trade)).toBe(true);
    });

    it("should return false for non-REQUEST_A_QUOTE trade", () => {
      const trade = {
        id: "trade-1",
        companyId: 123,
        type: OpportunityType.DIRECT_MESSAGE,
        status: FormattedOpportunityStatus.REQUEST_REJECTED,
      };
      expect(canTradeBeRedirected(trade)).toBe(false);
    });
  });

  describe("redirectOnReject", () => {
    const loggerMock = {
      info: jest.fn(),
      warn: jest.fn(),
    } as unknown as Logger;

    beforeEach(async () => {
      mockJobsSdkService();
      jest.clearAllMocks();
      // Directly spy on the findTrades function
      jest.spyOn(findTradesModule, "findTrades");
    });

    it("should log a warning if trade is not found in job", async () => {
      const jobId = "test-job";
      const companyId = 123;
      const redirectedAt = new Date();
      const tradeToken = "token";
      const job = {
        id: jobId,
        trades: [],
      };
      jobsSdkServiceMock.getJob.mockResolvedValueOnce(job);
      await redirectOnReject(
        jobId,
        companyId,
        redirectedAt,
        tradeToken,
        loggerMock,
      );
      expect(loggerMock.warn).toHaveBeenCalledWith(
        `Trade ${companyId} not found in job ${jobId}`,
      );
    });

    it("should log an info if trade cannot be redirected", async () => {
      const jobId = "test-job";
      const companyId = 123;
      const redirectedAt = new Date();
      const tradeToken = "token";
      const job = {
        id: jobId,
        trades: [
          {
            id: "trade-1",
            companyId: companyId,
            type: OpportunityType.DIRECT_MESSAGE,
            status: FormattedOpportunityStatus.REQUEST_REJECTED,
          },
        ],
        preferredStart: {
          id: "regular-start",
          title: "Regular",
        },
      };
      jobsSdkServiceMock.getJob.mockResolvedValueOnce(job);
      await redirectOnReject(
        jobId,
        companyId,
        redirectedAt,
        tradeToken,
        loggerMock,
      );
      expect(loggerMock.info).toHaveBeenCalledWith(
        `Trade ${companyId} cannot be redirected`,
      );
    });

    it("should log an info if job cannot be redirected", async () => {
      const jobId = "test-job";
      const companyId = 123;
      const redirectedAt = new Date();
      const tradeToken = "token";
      const job = {
        id: jobId,
        trades: [
          {
            id: "trade-1",
            companyId: companyId,
            type: OpportunityType.REQUEST_A_QUOTE,
            status: FormattedOpportunityStatus.REQUEST_REJECTED,
          },
        ],
        status: FormattedJobStatus.CANCELLED,
        preferredStart: {
          id: "regular-start",
          title: "Regular",
        },
      };
      jobsSdkServiceMock.getJob.mockResolvedValueOnce(job);
      await redirectOnReject(
        jobId,
        companyId,
        redirectedAt,
        tradeToken,
        loggerMock,
      );
      expect(loggerMock.info).toHaveBeenCalledWith(
        `Job ${jobId} cannot be redirected`,
      );
    });

    // This test is skipped because it is failing due to the fact that it makes the request and it's not mocked properly
    it.skip("should log a warning if new trade is not found", async () => {
      const jobId = "test-job";
      const companyId = 123;
      const redirectedAt = new Date();
      const tradeToken = "token";
      const job = {
        id: jobId,
        status: FormattedJobStatus.REQUESTED,
        categoryId: 123,
        description: "Test job",
        consumerId: "consumer-123",
        trades: [
          {
            id: "trade-1",
            companyId: companyId,
            type: OpportunityType.REQUEST_A_QUOTE,
            status: FormattedOpportunityStatus.REQUEST_REJECTED,
          },
        ],
        preferredStart: {
          id: "regular-start",
          title: "Regular",
        },
        createdAt: new Date().toISOString(),
        postcode: "12345",
        address: {
          postcode: "12345",
          line1: "123 Test St",
          city: "Test City",
        },
      };

      jobsSdkServiceMock.getJob.mockResolvedValueOnce(job);

      (findTradesModule.findTrades as jest.Mock).mockResolvedValueOnce([]);

      await redirectOnReject(
        jobId,
        companyId,
        redirectedAt,
        tradeToken,
        loggerMock,
      );

      expect(loggerMock.warn).toHaveBeenCalledWith(
        `New trade not found for job ${jobId}`,
      );
    });
  });
});
