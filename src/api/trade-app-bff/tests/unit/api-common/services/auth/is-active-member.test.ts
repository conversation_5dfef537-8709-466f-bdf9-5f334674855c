import { AuthTradeCapiAccount } from "@checkatrade/auth-trade";

import { isActiveMember } from "../../../../../src/lib/api-common";

describe("isMember", () => {
  it.each([
    [1, [{ companyId: 1, vettingStatus: 1 }], false],
    [0, [{ companyId: 1, vettingStatus: 1 }], false],
    [1, [{ companyId: 1, vettingStatus: 0 }], true],
    [1, [{ companyId: 1, vettingStatus: 1 }], false],
    [1, [{ companyId: 1, vettingStatus: 2 }], true],
    [1, [{ companyId: 1, vettingStatus: 3 }], false],
  ])("isMember(%s, %s) = %s", (id, accounts, result) => {
    expect(isActiveMember(id, accounts as AuthTradeCapiAccount[])).toBe(result);
  });
});
