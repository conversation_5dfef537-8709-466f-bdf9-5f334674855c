import { hasAccessToCompany } from "../../../src/helpers/auth-helpers";

describe("Auth Helpers", () => {
  describe("hasAccessToCompany", () => {
    it("should return true when user has access to the company", () => {
      // Arrange
      const companyId = 123;
      const authToken = {
        accounts: [
          { companyId: 123, vettingStatus: 0 },
          { companyId: 456, vettingStatus: 1 },
        ],
      };

      // Act
      const result = hasAccessToCompany(authToken, companyId);

      // Assert
      expect(result).toBe(true);
    });

    it("should return false when user does not have access to the company", () => {
      // Arrange
      const companyId = 789;
      const authToken = {
        accounts: [
          { companyId: 123, vettingStatus: 0 },
          { companyId: 456, vettingStatus: 1 },
        ],
      };

      // Act
      const result = hasAccessToCompany(authToken, companyId);

      // Assert
      expect(result).toBe(false);
    });

    it("should return false when accounts array is empty", () => {
      // Arrange
      const companyId = 123;
      const authToken = {
        accounts: [],
      };

      // Act
      const result = hasAccessToCompany(authToken, companyId);

      // Assert
      expect(result).toBe(false);
    });

    it("should return false when accounts array is undefined", () => {
      // Arrange
      const companyId = 123;
      const authToken = {};

      // Act
      const result = hasAccessToCompany(authToken, companyId);

      // Assert
      expect(result).toBe(false);
    });
  });
});
