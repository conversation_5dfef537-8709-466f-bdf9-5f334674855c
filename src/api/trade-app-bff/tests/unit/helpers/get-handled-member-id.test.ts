import { NotFoundError, UnauthorizedError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";

import { getHandledMemberId } from "../../../src/helpers/get-handled-member-id";
import * as getTeamMemberIdModule from "../../../src/services/team/get-team-memberId";

describe("getHandledMemberId", () => {
  const loggerMock = {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
  } as unknown as Logger;

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it("should return memberId when getTeamMemberId returns valid data", async () => {
    const companyId = 123;
    const expectedMemberId = "member-123";

    jest.spyOn(getTeamMemberIdModule, "getTeamMemberId").mockResolvedValueOnce({
      memberId: expectedMemberId,
    });

    const result = await getHandledMemberId(companyId, loggerMock);

    expect(result).toBe(expectedMemberId);
  });

  it("should throw NotFoundError when memberId is empty", async () => {
    const companyId = 456;

    jest.spyOn(getTeamMemberIdModule, "getTeamMemberId").mockResolvedValueOnce({
      memberId: "",
    });

    await expect(getHandledMemberId(companyId, loggerMock)).rejects.toThrow(
      new NotFoundError(`Failed to get member id for company ${companyId}`),
    );
  });

  it("should throw NotFoundError when getTeamMemberId returns undefined", async () => {
    const companyId = 789;

    jest
      .spyOn(getTeamMemberIdModule, "getTeamMemberId")
      .mockResolvedValueOnce(undefined);

    await expect(getHandledMemberId(companyId, loggerMock)).rejects.toThrow(
      new NotFoundError(`Failed to get member id for company ${companyId}`),
    );
  });

  it("should propagate errors from getTeamMemberId", async () => {
    const companyId = 999;
    const errorMessage = "API error";
    const expectedError = new UnauthorizedError(errorMessage);

    jest
      .spyOn(getTeamMemberIdModule, "getTeamMemberId")
      .mockRejectedValueOnce(expectedError);

    await expect(getHandledMemberId(companyId, loggerMock)).rejects.toThrow(
      expectedError,
    );
  });
});
