import { FormattedOpportunityStatus } from "@checkatrade/jobs-sdk";

import {
  jobsFilterCorrection,
  transformJobStatus,
} from "../../../src/helpers/transform-job-status";
import { TabEnum } from "../../../src/lib/schemas/jobsSchema";
import { mockJob } from "../../helpers";

describe("transformJobStatus", () => {
  it.each([
    {
      status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
      tradeMarkedBooked: true,
      expected: FormattedOpportunityStatus.BOOKED,
    },
    {
      status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
      tradeMarkedBooked: false,
      expected: FormattedOpportunityStatus.REQUEST_ACCEPTED,
    },
    {
      status: FormattedOpportunityStatus.REQUEST_REJECTED,
      tradeMarkedBooked: true,
      expected: FormattedOpportunityStatus.REQUEST_REJECTED,
    },
    {
      status: FormattedOpportunityStatus.COMPLETED,
      tradeMarkedBooked: true,
      expected: FormattedOpportunityStatus.COMPLETED,
    },
  ])(
    "transformJobStatus returns correct status for the condition(%s, %s)",
    ({ status, tradeMarkedBooked, expected }) => {
      expect(transformJobStatus(status, tradeMarkedBooked)).toBe(expected);
    },
  );
});

describe("jobsFilterCorrection", () => {
  const tabTestData = [
    {
      tab: TabEnum.Booked,
      job: [{ ...mockJob, status: FormattedOpportunityStatus.BOOKED }],
      expected: [{ ...mockJob, status: FormattedOpportunityStatus.BOOKED }],
    },
    {
      tab: TabEnum.Booked,
      job: [
        {
          ...mockJob,
          status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          tradeMarkedBooked: true,
        },
      ],
      expected: [
        {
          ...mockJob,
          status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          tradeMarkedBooked: true,
        },
      ],
    },
    {
      tab: TabEnum.Booked,
      job: [
        {
          ...mockJob,
          status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          tradeMarkedBooked: false,
        },
      ],
      expected: [], // Data does not have booked job
    },
    {
      tab: TabEnum.Interested,
      job: [
        {
          ...mockJob,
          status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          tradeMarkedBooked: false,
        },
      ],
      expected: [
        {
          ...mockJob,
          status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          tradeMarkedBooked: false,
        },
      ],
    },
    {
      tab: TabEnum.Interested,
      job: [
        {
          ...mockJob,
          status: FormattedOpportunityStatus.REQUEST_ACCEPTED,
          tradeMarkedBooked: true,
        },
      ],
      expected: [], // Filtered out as job is marked as booked
    },
  ];

  it.each(tabTestData)(
    "jobsFilterCorrection returns correct jobs for the condition(%s)",
    ({ tab, job, expected }) => {
      expect(jobsFilterCorrection(tab, job)).toEqual(expected);
    },
  );
});
