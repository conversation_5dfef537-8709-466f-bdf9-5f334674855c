import { consumerSDK } from "@checkatrade/consumer-sdk";
import { TradeJobListResponse, TradeJobResponse } from "@checkatrade/jobs-sdk";
import { faker } from "@faker-js/faker";
import { Static } from "@sinclair/typebox";

import { formatJobReviewRequestResponse } from "../../../src/controllers/reviews/get-job-review-request-list";
import { JobReviewRequestStatus } from "../../../src/controllers/reviews/review-request.schema";

type ConsumerResponse = Static<
  typeof consumerSDK.schemas.api.consumers.getConsumer.response
>;

describe("formatJobReviewRequestResponse", () => {
  const mockJob: TradeJobResponse | TradeJobListResponse = {
    id: faker.string.uuid(),
    opportunityId: faker.string.uuid(),
    postcode: "N1 9EB",
    consumerId: faker.string.uuid(),
  } as TradeJobResponse;

  const mockConsumer: Partial<ConsumerResponse> & {
    id: ConsumerResponse["id"];
  } = {
    id: mockJob.consumerId,
    firstName: "John",
    lastName: "Doe",
  };

  const mockReviewRequestStatus: JobReviewRequestStatus = {
    canRequestReview: true,
    hasSentReviewRequest: false,
    hasSentReviewRequestReminder: false,
  };

  it("should format job review request response correctly", () => {
    const result = formatJobReviewRequestResponse(
      mockJob,
      mockConsumer,
      mockReviewRequestStatus,
    );

    expect(result).toEqual({
      id: mockJob.id,
      channelId: mockJob.opportunityId,
      postcode: mockJob.postcode,
      consumer: {
        id: mockConsumer.id,
        firstName: mockConsumer.firstName,
        lastName: mockConsumer.lastName,
      },
      reviewRequestStatus: mockReviewRequestStatus,
    });
  });

  it("should handle missing consumer first/last name", () => {
    const consumerWithoutNames = {
      id: mockJob.consumerId,
    };

    const result = formatJobReviewRequestResponse(
      mockJob,
      consumerWithoutNames,
      mockReviewRequestStatus,
    );

    expect(result).toEqual({
      id: mockJob.id,
      channelId: mockJob.opportunityId,
      postcode: mockJob.postcode,
      consumer: {
        id: consumerWithoutNames.id,
        firstName: undefined,
        lastName: undefined,
      },
      reviewRequestStatus: mockReviewRequestStatus,
    });
  });

  it("should handle different review request status combinations", () => {
    const statuses: JobReviewRequestStatus[] = [
      {
        canRequestReview: true,
        hasSentReviewRequest: true,
        hasSentReviewRequestReminder: false,
      },
      {
        canRequestReview: false,
        hasSentReviewRequest: true,
        hasSentReviewRequestReminder: true,
      },
      {
        canRequestReview: false,
        hasSentReviewRequest: false,
        hasSentReviewRequestReminder: false,
      },
    ];

    statuses.forEach((status) => {
      const result = formatJobReviewRequestResponse(
        mockJob,
        mockConsumer,
        status,
      );

      expect(result.reviewRequestStatus).toEqual(status);
    });
  });
});
