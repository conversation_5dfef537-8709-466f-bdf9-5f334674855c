import { AlternativeStatus } from "@checkatrade/scheduling-sdk";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

import { formatAppointmentAlternatives } from "../../../src/controllers/appointments/appointment.formatter";

dayjs.extend(utc);
dayjs.extend(timezone);

describe("formatAppointmentAlternatives", () => {
  beforeAll(() => {
    dayjs.tz.setDefault("UTC");
  });

  it("should format alternatives with start time only", () => {
    const alternatives = [
      {
        id: "1",
        start: "2024-01-15T10:00:00Z",
        status: AlternativeStatus.CREATED,
      },
      {
        id: "2",
        start: "2024-01-16T14:30:00Z",
        status: AlternativeStatus.CREATED,
      },
    ];

    const result = formatAppointmentAlternatives(alternatives);
    expect(result).toBe("15 January 2024 10:00, 16 January 2024 14:30");
  });

  it("should format alternatives with start time and duration", () => {
    const alternatives = [
      {
        id: "1",
        start: "2024-01-15T10:00:00Z",
        end: "2024-01-15T12:00:00Z",
        status: AlternativeStatus.CREATED,
      },
      {
        id: "2",
        start: "2024-01-16T14:30:00Z",
        end: "2024-01-16T15:45:00Z",
        status: AlternativeStatus.CREATED,
      },
    ];

    const result = formatAppointmentAlternatives(alternatives);
    expect(result).toBe(
      "15 January 2024 10:00 (2h), 16 January 2024 14:30 (1h 15min)",
    );
  });

  it("should format single alternative with start time and duration", () => {
    const alternatives = [
      {
        id: "1",
        start: "2024-01-15T10:00:00Z",
        end: "2024-01-15T11:30:00Z",
        status: AlternativeStatus.CREATED,
      },
    ];

    const result = formatAppointmentAlternatives(alternatives);
    expect(result).toBe("15 January 2024 10:00 (1h 30min)");
  });

  it("should handle empty alternatives array", () => {
    const alternatives: {
      id: string;
      start: string;
      end?: string;
      status: AlternativeStatus;
    }[] = [];

    const result = formatAppointmentAlternatives(alternatives);
    expect(result).toBe("");
  });

  afterAll(() => {
    // Reset timezone setting
    dayjs.tz.setDefault();
  });
});
