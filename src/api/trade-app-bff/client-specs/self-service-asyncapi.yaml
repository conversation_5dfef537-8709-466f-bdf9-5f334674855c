asyncapi: "2.6.0"
info:
  title: Content.Api.Contracts - Self-Service
  version: 8.40.0
  description: Self Service Requests
defaultContentType: application/json
channels:
  self-service-requests:
    subscribe:
      message:
        oneOf:
          - $ref: "#/components/messages/SelfServiceMemberEmail"
          - $ref: "#/components/messages/SelfServiceMemberPhone"
          - $ref: "#/components/messages/SelfServiceMemberAdminAddress"
          - $ref: "#/components/messages/SelfServiceMemberTradingAddress"
          - $ref: "#/components/messages/SelfServiceCompanyName"
          - $ref: "#/components/messages/SelfServiceAddPerson"
          - $ref: "#/components/messages/SelfServiceRemovePerson"
          - $ref: "#/components/messages/SelfServiceUpdatePerson"
    bindings:
      googlepubsub:
        topic: self-service-requests
components:
  schemas:
    MemberEmailPayload:
      type: object
      properties:
        companyId:
          type:
            - "null"
            - string
        requesterEmail:
          type:
            - "null"
            - string
        email:
          type:
            - "null"
            - string
      additionalProperties: false
    SelfServiceRequestType:
      type: string
      description: ""
      enum:
        - CompanyName
        - MemberEmail
        - MemberPhone
        - MemberTradingAddress
        - MemberAdminAddress
        - AddPerson
        - UpdatePerson
        - RemovePerson
      x-enumNames:
        - CompanyName
        - MemberEmail
        - MemberPhone
        - MemberTradingAddress
        - MemberAdminAddress
        - AddPerson
        - UpdatePerson
        - RemovePerson
    SelfServiceMemberEmail:
      title: SelfServiceMemberEmail
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/MemberEmailPayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
    MemberPhonePayload:
      type: object
      properties:
        companyId:
          type:
            - "null"
            - string
        requesterEmail:
          type:
            - "null"
            - string
        phone:
          type:
            - "null"
            - string
      additionalProperties: false
    SelfServiceMemberPhone:
      title: SelfServiceMemberPhone
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/MemberPhonePayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
    MemberAddressPayload:
      type: object
      properties:
        companyId:
          type:
            - "null"
            - string
        requesterEmail:
          type:
            - "null"
            - string
        address:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/PostalAddressDto"
      additionalProperties: false
    PostalAddressDto:
      type: object
      properties:
        postcode:
          type:
            - "null"
            - string
        city:
          type:
            - "null"
            - string
        town:
          type:
            - "null"
            - string
        county:
          type:
            - "null"
            - string
        country:
          type:
            - "null"
            - string
        street:
          type:
            - "null"
            - string
      additionalProperties: false
    SelfServiceMemberAdminAddress:
      title: SelfServiceMemberAdminAddress
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/MemberAddressPayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
    SelfServiceMemberTradingAddress:
      title: SelfServiceMemberTradingAddress
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/MemberAddressPayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
    CompanyNamePayload:
      type: object
      properties:
        companyId:
          type:
            - "null"
            - string
        requesterEmail:
          type:
            - "null"
            - string
        name:
          type:
            - "null"
            - string
      additionalProperties: false
    SelfServiceCompanyName:
      title: SelfServiceCompanyName
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/CompanyNamePayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
    AddPersonPayload:
      type: object
      properties:
        companyId:
          type:
            - "null"
            - string
        requesterEmail:
          type:
            - "null"
            - string
        firstName:
          type:
            - "null"
            - string
        lastName:
          type: string
        email:
          type:
            - "null"
            - string
        dateOfBirth:
          type: string
          format: date-time
        mobilePhone:
          type:
            - "null"
            - string
        phone:
          type:
            - "null"
            - string
        companyRole:
          $ref: "#/components/schemas/PersonCompanyRoleDto"
        mailingAddress:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/PostalAddressDto"
      additionalProperties: false
    PersonCompanyRoleDto:
      type: string
      description: ""
      enum:
        - Owner
        - AdminContact
        - Director
        - Employee
      x-enumNames:
        - Owner
        - AdminContact
        - Director
        - Employee
    SelfServiceAddPerson:
      title: SelfServiceAddPerson
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/AddPersonPayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
    RemovePersonPayload:
      type: object
      properties:
        companyId:
          type:
            - "null"
            - string
        requesterEmail:
          type:
            - "null"
            - string
        personId:
          type: string
        firstName:
          type:
            - "null"
            - string
        lastName:
          type: string
        email:
          type:
            - "null"
            - string
        companyRole:
          $ref: "#/components/schemas/PersonCompanyRoleDto"
      additionalProperties: false
    SelfServiceRemovePerson:
      title: SelfServiceRemovePerson
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/RemovePersonPayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
    UpdatePersonPayload:
      type: object
      properties:
        companyId:
          type:
            - "null"
            - string
        requesterEmail:
          type:
            - "null"
            - string
        personId:
          type: string
        firstName:
          type:
            - "null"
            - string
        lastName:
          type:
            - "null"
            - string
        email:
          type:
            - "null"
            - string
        dateOfBirth:
          type:
            - "null"
            - string
          format: date-time
        mobilePhone:
          type:
            - "null"
            - string
        phone:
          type:
            - "null"
            - string
        companyRole:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/PersonCompanyRoleDto"
        mailingAddress:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/PostalAddressDto"
      additionalProperties: false
    SelfServiceUpdatePerson:
      title: SelfServiceUpdatePerson
      type: object
      properties:
        correlationId:
          type:
            - "null"
            - string
        originator:
          type:
            - "null"
            - string
        payload:
          oneOf:
            - type: "null"
            - $ref: "#/components/schemas/UpdatePersonPayload"
        selfServiceRequestType:
          $ref: "#/components/schemas/SelfServiceRequestType"
      additionalProperties: false
  messages:
    SelfServiceMemberEmail:
      payload:
        $ref: "#/components/schemas/SelfServiceMemberEmail"
      name: SelfServiceMemberEmail
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceMemberEmail
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
    SelfServiceMemberPhone:
      payload:
        $ref: "#/components/schemas/SelfServiceMemberPhone"
      name: SelfServiceMemberPhone
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceMemberPhone
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
    SelfServiceMemberAdminAddress:
      payload:
        $ref: "#/components/schemas/SelfServiceMemberAdminAddress"
      name: SelfServiceMemberAdminAddress
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceMemberAdminAddress
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
    SelfServiceMemberTradingAddress:
      payload:
        $ref: "#/components/schemas/SelfServiceMemberTradingAddress"
      name: SelfServiceMemberTradingAddress
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceMemberTradingAddress
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
    SelfServiceCompanyName:
      payload:
        $ref: "#/components/schemas/SelfServiceCompanyName"
      name: SelfServiceCompanyName
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceCompanyName
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
    SelfServiceAddPerson:
      payload:
        $ref: "#/components/schemas/SelfServiceAddPerson"
      name: SelfServiceAddPerson
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceAddPerson
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
    SelfServiceRemovePerson:
      payload:
        $ref: "#/components/schemas/SelfServiceRemovePerson"
      name: SelfServiceRemovePerson
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceRemovePerson
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
    SelfServiceUpdatePerson:
      payload:
        $ref: "#/components/schemas/SelfServiceUpdatePerson"
      name: SelfServiceUpdatePerson
      bindings:
        googlepubsub:
          attributes:
            properties:
              messageType:
                const: SelfServiceUpdatePerson
              messageVersion:
                const: v1
      traits:
        - $ref: "#/components/messageTraits/GooglePubSubCheckatradeEvent"
  messageTraits:
    GooglePubSubCheckatradeEvent:
      bindings:
        googlepubsub:
          contentType: application/json
          attributes:
            type: object
            required:
              - messageType
              - messageVersion
            properties:
              messageType:
                type: string
              messageVersion:
                type: string
            additionalProperties: false
