{"name": "trade-app-bff", "version": "0.0.1", "private": true, "description": "Trade App BFF", "scripts": {"prebuild": "rimraf ./dist", "build": "tsc", "dotenv": "dotenvx run -f ../../../.env", "dotenv-sample": "dotenvx run -f ../../../.env.sample", "start": "node --enable-source-maps dist", "start:dev": "pnpm run dotenv -- tsx --inspect --watch --env-file=../../../.env -- src/index.ts", "test": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --runInBand --colors", "typecheck": "tsc -noEmit", "lint": "eslint .", "lint:fix": "eslint --fix .", "generate-openapi": "pnpm run dotenv-sample -- tsx scripts/generate-openapi.ts && prettier --write ./spec/*.yaml", "generate-asyncapi": "./scripts/generate-asyncapi-types.sh"}, "repository": {"type": "git", "url": "git+https://github.com/checkatrade/core-trade-bff.git"}, "keywords": ["checkatrade", "capi", "trade", "bff", "service"], "author": "Checkatrade.com", "license": "ISC", "bugs": {"url": "https://github.com/checkatrade/core-trade-bff/issues"}, "homepage": "https://github.com/checkatrade/core-trade-bff#readme", "dependencies": {"@checkatrade/address-sdk": "3.5.15", "@checkatrade/auth-trade": "^2.3.4", "@checkatrade/axios": "^0.9.1", "@checkatrade/chat-sdk": "0.20.2", "@checkatrade/chat-types": "0.9.1", "@checkatrade/consumer-myhome-sdk": "0.12.0", "@checkatrade/consumer-sdk": "^8.18.0", "@checkatrade/datadog": "^0.4.0", "@checkatrade/env": "^0.3.0", "@checkatrade/errors": "^0.8.1", "@checkatrade/fastify-five": "0.2.3", "@checkatrade/gcp": "^0.2.1", "@checkatrade/jobs-sdk": "6.61.3", "@checkatrade/logging": "^0.7.0", "@checkatrade/media-service-sdk": "^1.20.1", "@checkatrade/metrics-sdk": "1.43.0", "@checkatrade/payment-sdk": "^0.92.4", "@checkatrade/payment-types": "^1.119.2", "@checkatrade/project-sdk": "1.4.2", "@checkatrade/quoting-sdk": "^2.9.11", "@checkatrade/review-sdk": "1.47.1", "@checkatrade/reviews-summary-sdk": "^0.2.0", "@checkatrade/scheduling-sdk": "^2.12.8", "@checkatrade/schemas": "^0.6.0", "@checkatrade/service-catalog-sdk": "^2.0.21", "@checkatrade/service-catalog-types": "^2.0.21", "@checkatrade/trade-bff-types": "workspace:^", "@google-cloud/pubsub": "^4.9.0", "@sinclair/typebox": "^0.33.16", "dayjs": "^1.11.10", "dd-trace": "^5.23.1", "fastify": "5.3.2", "firebase-admin": "13.0.2", "google-auth-library": "^9.15.0", "json-2-csv": "^5.5.9", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "stream-chat": "8.56.1", "uuid": "^11.1.0", "zod": "4.0.0-beta.20250424T163858"}, "devDependencies": {"@asyncapi/cli": "^3.1.1", "@checkatrade/jest": "^0.5.0", "@faker-js/faker": "^9.3.0", "@types/jest": "^29.5.13", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.15", "@types/node": "^22.9.0", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nock": "14.0.0-beta.6", "supertest": "^6.3.4", "tsx": "^4.19.3", "typescript": "^5.5.4"}}