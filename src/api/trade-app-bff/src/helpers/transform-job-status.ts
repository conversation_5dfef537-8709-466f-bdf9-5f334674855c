import {
  FormattedOpportunityStatus,
  TradePagedJobResponse,
} from "@checkatrade/jobs-sdk";

import { TabEnum } from "../lib/schemas/jobsSchema";

export const transformJobStatus = (
  status: FormattedOpportunityStatus,
  tradeMarkedBooked: boolean | undefined,
): FormattedOpportunityStatus => {
  if (
    status === FormattedOpportunityStatus.REQUEST_ACCEPTED &&
    tradeMarkedBooked
  ) {
    return FormattedOpportunityStatus.BOOKED;
  }

  return status;
};

export const jobsFilterCorrection = (
  tab: TabEnum | undefined,
  jobsData: TradePagedJobResponse["data"],
): TradePagedJobResponse["data"] => {
  switch (tab) {
    case TabEnum.Booked: {
      return jobsData.filter(
        (job) =>
          job.status === FormattedOpportunityStatus.BOOKED ||
          (job.status === FormattedOpportunityStatus.REQUEST_ACCEPTED &&
            job.tradeMarkedBooked),
      );
    }

    case TabEnum.Interested: {
      return jobsData.filter((job) => job.tradeMarkedBooked !== true);
    }

    default: {
      return jobsData;
    }
  }
};
