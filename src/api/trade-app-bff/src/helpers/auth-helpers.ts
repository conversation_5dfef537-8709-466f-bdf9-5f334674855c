/**
 * Checks if the authenticated user has access to the specified company
 * @param authToken The authentication token from authTradeCapi
 * @param companyId The company ID to check access for
 * @returns True if the user has access to the company, false otherwise
 */
export const hasAccessToCompany = (
  authToken: { accounts?: Array<{ companyId: number }> },
  companyId: number,
): boolean => {
  return (authToken.accounts || []).some(
    (account) => account.companyId === companyId,
  );
};
