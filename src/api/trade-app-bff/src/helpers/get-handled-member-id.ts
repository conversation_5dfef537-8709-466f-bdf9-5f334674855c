import { NotFoundError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";

import { getTeamMemberId } from "../services/team/get-team-memberId";

export async function getHandledMemberId(
  companyId: number,
  logger: Logger,
): Promise<string> {
  const member = await getTeamMemberId(companyId, logger);

  if (!member?.memberId) {
    throw new NotFoundError(`Failed to get member id for company ${companyId}`);
  }
  return member.memberId;
}
