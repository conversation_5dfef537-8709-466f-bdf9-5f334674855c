import { authTradeCapi } from "@checkatrade/auth-trade";
import { ForbiddenError } from "@checkatrade/errors";
import { FastifyRequest } from "fastify";

import { checkAccountOwner } from "../services/member-details/member-details";

export const verifyAccountOwner = async (
  req: FastifyRequest,
  companyId: number,
) => {
  const authToken = authTradeCapi(req);

  const isAccountOwner = await checkAccountOwner({
    companyId,
    userEmail: authToken.email,
  });

  if (!isAccountOwner) {
    throw new ForbiddenError("Only the account owner can perform this action");
  }
};
