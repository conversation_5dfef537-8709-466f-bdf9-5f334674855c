import { JobReviewRequestStatus } from "../../controllers/reviews/review-request.schema";
import { getJobReviewRequestByJobId } from "../firebase/firestore/job-review-request";
import { oneDayHasPassed } from "./utils";

type JobReviewRequestStatusFirestore = JobReviewRequestStatus & {
  jobReviewRequestId: string;
  dateCreated: Date;
};

export const getJobReviewRequestStatusFirestore = async (
  companyId: number,
  jobId: string,
): Promise<JobReviewRequestStatusFirestore | undefined> => {
  const jobReviewRequest = await getJobReviewRequestByJobId(companyId, jobId);

  if (jobReviewRequest) {
    if (jobReviewRequest.sent) {
      return {
        canRequestReview: false,
        hasSentReviewRequest: true,
        hasSentReviewRequestReminder: true,
        jobReviewRequestId: jobReviewRequest.id!,
        dateCreated: jobReviewRequest.dateCreated!,
      };
    }

    return {
      canRequestReview: oneDayHasPassed(jobReviewRequest.dateCreated),
      hasSentReviewRequest: true,
      hasSentReviewRequestReminder: false,
      jobReviewRequestId: jobReviewRequest.id!,
      dateCreated: jobReviewRequest.dateCreated!,
    };
  }

  return undefined;
};
