import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";

export async function fetchReviewRequestMessages(opportunityId: string) {
  const smartMessages = await chatSDK.getChannelSmartMessages({
    id: opportunityId,
  });
  return [
    smartMessages.type[SmartMessageType.REVIEW_REQUESTED],
    smartMessages.type[SmartMessageType.REVIEW_REQUEST_REMINDER],
  ];
}
