import { FastifyBaseLogger } from "fastify";

import { JobReviewRequestStatus } from "../../controllers/reviews/review-request.schema";
import { getJobReviewRequestStatusChat } from "./get-job-review-request-status-chat";
import { getJobReviewRequestStatusFirestore } from "./get-job-review-request-status-firestore";

type GetJobReviewRequestStatusProps = {
  companyId: number;
  jobId: string;
  opportunityId: string;
  logger?: FastifyBaseLogger;
};

export const getJobReviewRequestStatus = async ({
  companyId,
  jobId,
  opportunityId,
  logger,
}: GetJobReviewRequestStatusProps): Promise<JobReviewRequestStatus> => {
  const firestoreStatus = await getJobReviewRequestStatusFirestore(
    companyId,
    jobId,
  );

  if (firestoreStatus) return firestoreStatus;

  const chatStatus = await getJobReviewRequestStatusChat(opportunityId, logger);

  if (chatStatus) return chatStatus;

  return {
    canRequestReview: true,
    hasSentReviewRequest: false,
    hasSentReviewRequestReminder: false,
  };
};
