import { chatSDK } from "@checkatrade/chat-sdk";
import dayjs from "dayjs";
import { FastifyBaseLogger } from "fastify";

import { JobReviewRequestStatus } from "../../controllers/reviews/review-request.schema";
import { fetchReviewRequestMessages } from "./fetch-review-requests-smart-messages";
import { oneDayHasPassed } from "./utils";

type JobReviewRequestStatusChat = JobReviewRequestStatus & {
  dateCreated: Date;
};

export const getJobReviewRequestStatusChat = async (
  opportunityId: string,
  logger?: FastifyBaseLogger,
): Promise<JobReviewRequestStatusChat | undefined> => {
  try {
    const [reviewMessageId, reminderMessageId] =
      await fetchReviewRequestMessages(opportunityId);

    if (reviewMessageId) {
      const {
        message: { created_at },
      } = await chatSDK.streamChat.getMessage(reviewMessageId);

      if (reminderMessageId) {
        return {
          canRequestReview: false,
          hasSentReviewRequest: true,
          hasSentReviewRequestReminder: true,
          dateCreated: dayjs(created_at).toDate(),
        };
      }

      return {
        canRequestReview: oneDayHasPassed(created_at),
        hasSentReviewRequest: true,
        hasSentReviewRequestReminder: false,
        dateCreated: dayjs(created_at).toDate(),
      };
    }
    return undefined;
  } catch (error) {
    logger?.warn(
      { opportunityId },
      "Error fetching jobReviewRequests messages from chat",
      error,
    );
    return undefined;
  }
};
