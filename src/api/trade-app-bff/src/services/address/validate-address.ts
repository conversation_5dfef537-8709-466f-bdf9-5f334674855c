import { addressSD<PERSON> } from "@checkatrade/address-sdk";
import type { Logger } from "@checkatrade/logging";
import type { Address } from "@checkatrade/trade-bff-types";

export async function validateAddress(
  address: Address,
  logger: Logger,
): Promise<boolean> {
  try {
    const response = await addressSDK.validate({
      ...address,
    });
    return response;
  } catch (error) {
    logger.error(error, "Error validating address");
    return false;
  }
}
