import jwt from "jsonwebtoken";

import { config } from "./config";

export const generateToken = (quoteId: string) => {
  if (!quoteId || quoteId.trim() == "") {
    throw new Error("QuoteId cannot be null for generating quoting token");
  }
  const payload = {
    quoteId: quoteId,
    exp: Math.floor(Date.now() / 1000) + config.tokenExpiration,
  };

  return jwt.sign(payload, config.tokenSigningKey);
};
