import { UnauthorizedError } from "@checkatrade/errors";
import jwt from "jsonwebtoken";

import { config } from "./config";

export interface DecodedToken {
  quoteId: string;
}

export const verifyToken = (token: string): DecodedToken => {
  try {
    const result = jwt.verify(token, config.tokenSigningKey) as {
      quoteId: string;
    };

    const quoteId = result.quoteId;

    const decoded: DecodedToken = {
      quoteId,
    };

    return decoded;
  } catch (err) {
    throw new UnauthorizedError("Quoting time limited token is invalid");
  }
};
