import { consumerMyhomeSDK } from "@checkatrade/consumer-myhome-sdk";
import { Logger } from "@checkatrade/fastify-five";
import { TradeJobResponse } from "@checkatrade/jobs-sdk";
import { Static } from "@sinclair/typebox";

export type PropertyFacts = Static<
  typeof consumerMyhomeSDK.schemas.api.propertyFacts.getPropertyFactsForConsumer.response
>;

const timeout = (ms: number) =>
  new Promise<never>((_, reject) =>
    setTimeout(() => reject(new Error("Request timed out")), ms),
  );

export const getPropertyFactsForConsumer = async (
  token: string,
  job: TradeJobResponse,
  logger: Logger,
) => {
  let propertyFacts: PropertyFacts | undefined;
  if (job.address.uprn) {
    try {
      propertyFacts = await Promise.race([
        consumerMyhomeSDK.trade(token).propertyFactsForConsumer.get({
          uprn: job.address.uprn,
          consumerId: job.consumerId,
        }),
        timeout(1000),
      ]);
    } catch (error) {
      logger.error(
        {
          error,
          uprn: job.address.uprn,
        },
        "Error getting property data",
      );
    }
  }
  return propertyFacts;
};
