import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  SelfServiceRequestType,
  UpdatePersonPayload,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface UpdatePersonRequest {
  data: UpdatePersonPayload;
  correlationId: string;
}

export const updatePersonRequest = async ({
  data,
  correlationId,
}: UpdatePersonRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.UPDATE_PERSON,
        }),
      ),
      attributes: {
        messageType: "SelfServiceUpdatePerson",
        messageVersion: "v1",
      },
    });
};
