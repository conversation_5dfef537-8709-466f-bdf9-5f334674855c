import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  MemberAddressPayload,
  SelfServiceRequestType,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface MemberAdminAddressRequest {
  data: MemberAddressPayload;
  correlationId: string;
}

export const memberAdminAddressRequest = async ({
  data,
  correlationId,
}: MemberAdminAddressRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.MEMBER_ADMIN_ADDRESS,
        }),
      ),
      attributes: {
        messageType: "SelfServiceMemberAdminAddress",
        messageVersion: "v1",
      },
    });
};
