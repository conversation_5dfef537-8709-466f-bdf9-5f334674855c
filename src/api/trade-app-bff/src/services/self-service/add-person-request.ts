import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  AddPersonPayload,
  SelfServiceRequestType,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface AddPersonRequest {
  data: AddPersonPayload;
  correlationId: string;
}

export const addPersonRequest = async ({
  data,
  correlationId,
}: AddPersonRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.ADD_PERSON,
        }),
      ),
      attributes: {
        messageType: "SelfServiceAddPerson",
        messageVersion: "v1",
      },
    });
};
