import { addPersonRequest } from "./add-person-request";
import { companyNameRequest } from "./company-name-request";
import { deletePersonRequest } from "./delete-person-request";
import { memberAdminAddressRequest } from "./member-admin-address-request";
import { memberEmailRequest } from "./member-email-request";
import { memberPhoneRequest } from "./member-phone-request";
import { memberTradingAddressRequest } from "./member-trading-address-request";
import { updatePersonRequest } from "./update-person-request";

type SelfService = {
  companyNameRequest: typeof companyNameRequest;
  memberEmailRequest: typeof memberEmailRequest;
  memberPhoneRequest: typeof memberPhoneRequest;
  memberTradingAddressRequest: typeof memberTradingAddressRequest;
  memberAdminAddressRequest: typeof memberAdminAddressRequest;
  updatePersonRequest: typeof updatePersonRequest;
  addPersonRequest: typeof addPersonRequest;
  deletePersonRequest: typeof deletePersonRequest;
};

export const selfService: SelfService = {
  companyNameRequest,
  memberEmailRequest,
  memberPhoneRequest,
  memberTradingAddressRequest,
  memberAdminAddressRequest,
  updatePersonRequest: updatePersonRequest,
  addPersonRequest: addPersonRequest,
  deletePersonRequest: deletePersonRequest,
};
