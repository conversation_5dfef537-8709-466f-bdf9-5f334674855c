import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  MemberEmailPayload,
  SelfServiceRequestType,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface MemberEmailRequest {
  data: MemberEmailPayload;
  correlationId: string;
}

export const memberEmailRequest = async ({
  data,
  correlationId,
}: MemberEmailRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.MEMBER_EMAIL,
        }),
      ),
      attributes: {
        messageType: "SelfServiceMemberEmail",
        messageVersion: "v1",
      },
    });
};
