import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  CompanyNamePayload,
  SelfServiceRequestType,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface CompanyNameRequest {
  data: CompanyNamePayload;
  correlationId: string;
}

export const companyNameRequest = async ({
  data,
  correlationId,
}: CompanyNameRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.COMPANY_NAME,
        }),
      ),
      attributes: {
        messageType: "SelfServiceCompanyName",
        messageVersion: "v1",
      },
    });
};
