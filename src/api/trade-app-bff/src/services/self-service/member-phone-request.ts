import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  MemberPhonePayload,
  SelfServiceRequestType,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface MemberPhoneRequest {
  data: MemberPhonePayload;
  correlationId: string;
}

export const memberPhoneRequest = async ({
  data,
  correlationId,
}: MemberPhoneRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.MEMBER_PHONE,
        }),
      ),
      attributes: {
        messageType: "SelfServiceMemberPhone",
        messageVersion: "v1",
      },
    });
};
