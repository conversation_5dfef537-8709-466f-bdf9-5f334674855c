import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  RemovePersonPayload,
  SelfServiceRequestType,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface DeletePersonRequest {
  data: RemovePersonPayload;
  correlationId: string;
}

export const deletePersonRequest = async ({
  data,
  correlationId,
}: DeletePersonRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.REMOVE_PERSON,
        }),
      ),
      attributes: {
        messageType: "SelfServiceRemovePerson",
        messageVersion: "v1",
      },
    });
};
