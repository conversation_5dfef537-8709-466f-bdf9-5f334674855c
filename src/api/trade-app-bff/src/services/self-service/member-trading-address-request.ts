import { capiPubsubClient } from "../../lib/api-common/services/pubsub/capi-pubsub-client";
import {
  MemberAddressPayload,
  SelfServiceRequestType,
} from "../../types/asyncapi-types";
import { config } from "./config";

interface MemberTradingAddressRequest {
  data: MemberAddressPayload;
  correlationId: string;
}

export const memberTradingAddressRequest = async ({
  data,
  correlationId,
}: MemberTradingAddressRequest) => {
  await capiPubsubClient
    .topic(config.gcp.selfServiceRequestsTopic)
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          payload: data,
          correlationId,
          originator: "core-trade-bff",
          selfServiceRequestType: SelfServiceRequestType.MEMBER_TRADING_ADDRESS,
        }),
      ),
      attributes: {
        messageType: "SelfServiceMemberTradingAddress",
        messageVersion: "v1",
      },
    });
};
