import { ServiceJobResponse } from "@checkatrade/jobs-sdk";

import { TradeCard, searchApi } from "../../lib/api-common/services";

type FindTradesProps = Pick<
  ServiceJobResponse,
  "categoryId" | "address" | "postcode" | "trades"
>;

export const findTrades = async (
  job: FindTradesProps,
  count: number,
): Promise<TradeCard[]> => {
  return (
    (
      await searchApi.findTrades({
        categoryId: job.categoryId,
        postcode: job.address?.postcode || job.postcode,
        excludeCompanyIds: job.trades.map((t) => t.companyId),
        size: count,
      })
    ).items || []
  );
};
