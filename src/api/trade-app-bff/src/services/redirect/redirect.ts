import { chatSDK } from "@checkatrade/chat-sdk";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { Logger } from "@checkatrade/fastify-five";
import {
  JobStatusUtils,
  OpportunityStatusUtils,
  OpportunityType,
  ServiceJobResponse,
  jobsSDK,
} from "@checkatrade/jobs-sdk";

import { formatTradeChannels } from "../../lib/api-common/formatters/format-trade-channels";
import {
  nationalAccounts,
  search,
  searchApi,
} from "../../lib/api-common/services";
import { config } from "./config";
import { findTrades } from "./find-trades";

type JobTrade = ServiceJobResponse["trades"][number];

export const URGENT_PREFERRED_START = "018f809e-5354-74c8-85e7-9aa6dead0de6";

export const canJobBeRedirected = (
  job: ServiceJobResponse,
  redirectedAt: Date,
) => {
  const allTradesCount = job.trades.length;
  const acceptedTradesCount = job.trades.filter((t) =>
    OpportunityStatusUtils.isAccepted(t.status),
  ).length;

  const jobExpiryInMs = config.redirection.jobExpiryHours * 60 * 60 * 1000;
  const jobUrgentExpiryInMs =
    config.redirection.jobExpiryUrgentHours * 60 * 60 * 1000;
  const isUrgent = job.preferredStart.id === URGENT_PREFERRED_START;
  const expiryTimeInMs = isUrgent ? jobUrgentExpiryInMs : jobExpiryInMs;

  const jobRedirectionDeadline =
    new Date(job.createdAt).getTime() + expiryTimeInMs;

  return (
    JobStatusUtils.isAcceptable(job.status) &&
    redirectedAt.getTime() <= jobRedirectionDeadline &&
    job.trades.every((t) => t.type === OpportunityType.REQUEST_A_QUOTE) &&
    acceptedTradesCount < config.redirection.maxAccepts &&
    allTradesCount < config.redirection.maxOpportunities
  );
};

export const canTradeBeRedirected = ({ type }: JobTrade) => {
  return type === OpportunityType.REQUEST_A_QUOTE;
};

export const redirectOnReject = async (
  jobId: string,
  companyId: number,
  redirectedAt: Date,
  tradeToken: string,
  logger: Logger,
) => {
  const job = await jobsSDK.service().getJob(jobId);

  const tradeToRedirect = job.trades.find((t) => t.companyId === companyId);
  if (!tradeToRedirect) {
    logger.warn(`Trade ${companyId} not found in job ${jobId}`);
    return;
  }
  if (!canTradeBeRedirected(tradeToRedirect)) {
    logger.info(`Trade ${companyId} cannot be redirected`);
    return;
  }

  if (!canJobBeRedirected(job, redirectedAt)) {
    logger.info(`Job ${jobId} cannot be redirected`);
    return;
  }

  const initialChannelId = await search.findInitialChannelId(
    job.trades,
    logger,
  );

  const newTrades = await findTrades(job, 1);

  if (newTrades.length !== 1) {
    logger.warn(`New trade not found for job ${jobId}`);
    return;
  }

  const tradeToAdd = newTrades[0];

  const updatedJob = await jobsSDK
    .service()
    .trades.redirect(jobId, { trades: [tradeToAdd.companyId] });

  if (nationalAccounts.isNationalAccount(tradeToAdd)) {
    await nationalAccounts.serviceAutoAccept(updatedJob, [tradeToAdd]);
  }

  const newChannels = formatTradeChannels({
    jobId,
    jobTrades: updatedJob.trades.filter(
      (t) => t.companyId === tradeToAdd.companyId,
    ),
    searchTrades: [tradeToAdd],
  });

  let images: string[] = [];

  try {
    if (initialChannelId) {
      images =
        (await chatSDK.getImagesFromJobRequestMessage(initialChannelId)) || [];
    }
  } catch (e) {
    logger.info(
      `Failed fetching images for opportunity: '${initialChannelId}'`,
    );
  }

  await chatSDK.upsertTradeUsers(newChannels);

  await chatSDK.createChannels({
    channels: newChannels,
    consumerMessage: job.description,
    consumerId: job.consumerId,
    images,
    logger,
  });

  if (newTrades.length > 0) {
    const consumer = await consumerSDK
      .trade(tradeToken)
      .getConsumer(job.consumerId);

    const categoryName = await searchApi.getCategoryName(job.categoryId);

    await nationalAccounts.forward(
      {
        ...job,
        category: {
          id: job.categoryId,
          label: categoryName,
        },
      },
      consumer,
      OpportunityType.REQUEST_A_QUOTE,
      newTrades,
    );
  }
};
