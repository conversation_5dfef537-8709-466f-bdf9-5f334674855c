import { ServiceUnavailableError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";
import {
  CreateCampaignsRequest,
  CreateCampaignsResponse,
  createCampaignsResponseSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { adManagerHttpClient } from "./adManagerHttpClient";

/**
 * Creates a base64-encoded user context header for ad-manager API calls
 * @param email - User email
 * @param userId - User ID
 * @returns Base64-encoded user context header
 */
const createUserContextHeader = (email: string, userId: string): string => {
  const data = {
    email,
    uid: userId,
    realm: "trade",
  };

  return btoa(JSON.stringify(data));
};

/**
 * Creates Sponsored Listings campaigns in the ad-manager service
 * @param logger - Logger instance
 * @param companyId - Company ID
 * @param campaigns - Campaign data
 * @param userEmail - User email
 * @param userId - User ID
 * @returns Array of created campaign IDs
 * @throws BadRequestError if the request is invalid
 * @throws ServiceUnavailableError if the ad-manager service is unavailable
 * @throws Error for other unexpected errors
 */
export const createCampaigns = async (
  logger: Logger,
  companyId: number,
  campaigns: CreateCampaignsRequest,
  userEmail: string,
  userId: string,
): Promise<CreateCampaignsResponse> => {
  const url = `/advertisers/${companyId}/campaigns`;

  try {
    const requestPayload = {
      payload: campaigns,
    };

    const userContextHeader = createUserContextHeader(userEmail, userId);

    logger.info(
      {
        url,
        companyId,
        userEmail,
        userId,
        userContextHeader,
        campaignCount: campaigns.length,
        campaignTypes: campaigns.map((c) => c.type),
      },
      "Creating campaigns in ad-manager service",
    );

    // Add timeout to prevent hanging requests
    const response = await adManagerHttpClient.post(url, requestPayload, {
      headers: {
        "x-caller-identity": userContextHeader,
      },
    });

    logger.info(
      {
        status: response.status,
        campaignIds: response.data,
      },
      "Successfully created campaigns in ad-manager service",
    );

    return Value.Parse(createCampaignsResponseSchema, response.data);
  } catch (error) {
    // Handle error based on its properties
    const err = error as Error & {
      response?: { status: number; data: unknown };
      request?: unknown;
    };

    if (err.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      logger.error(
        {
          error: err,
          url,
          companyId,
          status: err.response.status,
          data: err.response.data,
        },
        `Failed to create campaigns: ${err.message}`,
      );

      // Pass through the error from the ad-manager service
      throw error;
    } else if (err.request) {
      // The request was made but no response was received
      logger.error(
        { error: err, url, companyId },
        `No response received from ad-manager service: ${err.message}`,
      );
      throw new ServiceUnavailableError("Ad-manager service is unavailable");
    }

    // Generic error handling
    logger.error(
      { error: err, url, companyId },
      `Failed to create campaigns: ${err.message || "Unknown error"}`,
    );
    throw error;
  }
};
