import { Logger } from "@checkatrade/fastify-five";
import {
  Dimensions,
  GetCampaignStatsResponse,
  getCampaignStatsResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { adManagerHttpClient } from "./adManagerHttpClient";

export const getCampaignStats = async (
  logger: Logger,
  from?: string,
  to?: string,
  dimensions?: Dimensions[] | Dimensions,
  companyId?: number,
): Promise<GetCampaignStatsResponse | undefined> => {
  try {
    const url = `/campaigns/stats`;

    const response = await adManagerHttpClient.get(url, {
      params: {
        from,
        to,
        dimensions,
        companyId,
      },
      paramsSerializer: {
        indexes: null,
      },
    });

    return Value.Parse(getCampaignStatsResponse, response.data);
  } catch (error) {
    logger.error(error, "Failed to get campaign stats");
    throw error;
  }
};
