import { Logger } from "@checkatrade/logging";
import {
  GetExperiencesResponse,
  getExperiencesResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { adManagerHttpClient } from "./adManagerHttpClient";

export const getExperiences = async (
  logger: Logger,
): Promise<GetExperiencesResponse | undefined> => {
  try {
    const url = `/experiences`;

    logger.info({ url }, "Fetching experiences from ad-manager service");
    const response = await adManagerHttpClient.get(url);

    logger.info(
      {
        status: response.status,
        experiencesCount: response.data?.experiences?.length,
      },
      "Successfully fetched experiences from ad-manager service",
    );

    return Value.Parse(getExperiencesResponse, response.data);
  } catch (error) {
    logger.error(
      { error, url: "/experiences" },
      "Failed to get experiences from ad-manager service",
    );
    throw error;
  }
};
