import { consumerSDK } from "@checkatrade/consumer-sdk";

/**
 * Retrieves a map of consumers by their IDs.
 *
 * @param consumerIds - Array or Set of consumer IDs to fetch
 * @param token - Authentication token for the consumer SDK
 * @returns A promise that resolves to a record mapping consumer IDs to their corresponding consumer objects
 *
 * @example
 * const consumerMap = await getConsumerMapByIds(['id1', 'id2'], 'auth-token');
 * // Returns: { 'id1': ConsumerObject1, 'id2': ConsumerObject2 }
 */
export async function getConsumerMapByIds(
  consumerIds: string[] | Set<string>,
  token: string,
) {
  const dedupedConsumerIds =
    Array.isArray(consumerIds) ? [...new Set(consumerIds)] : [...consumerIds];
  const consumers = await consumerSDK
    .trade(token)
    .getConsumers(dedupedConsumerIds);
  const consumerMap: Record<string, (typeof consumers)[0]> = {};

  for (const consumer of consumers) {
    consumerMap[consumer.id] = consumer;
  }

  return consumerMap;
}
