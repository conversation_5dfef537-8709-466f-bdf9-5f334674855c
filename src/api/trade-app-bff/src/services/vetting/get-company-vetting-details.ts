import { ApiError } from "@checkatrade/errors";
import {
  VettingStatus,
  VettingStatusSF,
  vettingStatusSFMapper,
} from "@checkatrade/trade-bff-types";

import { vettingHttpClient } from "./vettingHttpClient";

export async function getCompanyVettingDetails(
  memberId: string,
): Promise<VettingStatus> {
  try {
    const { data } = await vettingHttpClient.get<VettingStatusSF>(
      `/company-vetting/status/${memberId}`,
    );
    return vettingStatusSFMapper(data);
  } catch (error) {
    if (error instanceof ApiError && error.status === 404) {
      // At the moment, if the vetting service returns a 404, we assume that the vetting has not started
      return VettingStatus.NotStarted;
    }
    throw error;
  }
}
