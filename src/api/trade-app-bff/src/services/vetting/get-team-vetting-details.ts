import {
  ContractingType,
  GetWorkersVettingStatusResponse,
  getWorkersVettingStatusResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { vettingHttpClient } from "./vettingHttpClient";

export const getTeamVettingDetails = async (
  legacyCompanyId: string | number,
  relationship: ContractingType,
): Promise<GetWorkersVettingStatusResponse> => {
  const response = await vettingHttpClient.get<GetWorkersVettingStatusResponse>(
    "/company-workers",
    {
      params: {
        companyId: legacyCompanyId,
        relationship,
      },
    },
  );

  return Value.Parse(getWorkersVettingStatusResponse, response.data);
};
