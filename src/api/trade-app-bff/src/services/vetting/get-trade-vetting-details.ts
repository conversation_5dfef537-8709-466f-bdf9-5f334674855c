import {
  GetVettingStatusResponse,
  getVettingStatusResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { vettingHttpClient } from "./vettingHttpClient";

export const getTradeVettingDetails = async (
  memberId: string | number,
  externalId: string,
): Promise<GetVettingStatusResponse> => {
  const response = await vettingHttpClient.get<GetVettingStatusResponse>(
    `/company-workers/person/${externalId}`,
    {
      params: {
        memberId,
      },
    },
  );

  return Value.Parse(getVettingStatusResponse, response.data);
};
