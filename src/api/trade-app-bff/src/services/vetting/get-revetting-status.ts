import {
  Member,
  MemberProfileStatus,
  RevettingStatus,
} from "@checkatrade/trade-bff-types";

import { REVETTING_COMPANY_IDS } from "./constants/revetting-company-ids";

export const getRevettingStatus = async (
  member: Member,
): Promise<RevettingStatus> => {
  const needsRevetting = REVETTING_COMPANY_IDS.includes(member.companyId);
  const isBlind = member.profileStatus === MemberProfileStatus.Blind;

  return needsRevetting && isBlind ?
      RevettingStatus.REQUIRED
    : RevettingStatus.NOT_REQUIRED;
};
