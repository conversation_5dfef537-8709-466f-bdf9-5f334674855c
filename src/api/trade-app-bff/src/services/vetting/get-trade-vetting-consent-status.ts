import { ApiError } from "@checkatrade/errors";
import {
  VettingConsentStatus,
  VettingTradePerson,
  vettingTradePersonSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { vettingHttpClient } from "./vettingHttpClient";

export const getTradeVettingConsentStatus = async (
  personId: string,
): Promise<Pick<
  VettingTradePerson,
  "consent" | "externalId" | "id"
> | null> => {
  try {
    const response = await vettingHttpClient.get<VettingTradePerson>(
      "/trade-persons",
      {
        params: {
          externalId: personId,
        },
      },
    );

    const { id, externalId, consent } = Value.Parse(
      vettingTradePersonSchema,
      response.data,
    );
    return { id, externalId, consent };
  } catch (error) {
    // If the person is not found, return not created status
    if (error instanceof ApiError && error.status === 404) {
      return {
        id: personId,
        externalId: personId,
        consent: VettingConsentStatus.NotCreated,
      };
    }
    throw error;
  }
};
