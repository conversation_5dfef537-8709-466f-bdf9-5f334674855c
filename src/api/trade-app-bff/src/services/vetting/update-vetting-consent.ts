import {
  VettingConsentStatus,
  VettingTradePerson,
  vettingTradePersonSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { vettingHttpClient } from "./vettingHttpClient";

type UpdateTradeVettingProps = {
  companyId: string | number;
  personId: string;
  consent: VettingConsentStatus;
};

export const updateTradeVetting = async ({
  personId,
  ...data
}: UpdateTradeVettingProps) => {
  const response = await vettingHttpClient.put<VettingTradePerson>(
    `/trade-persons/${personId}/consent`,
    { ...data },
  );

  return Value.Parse(vettingTradePersonSchema, response.data);
};
