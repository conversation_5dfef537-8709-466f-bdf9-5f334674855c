import { NotFoundError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";
import {
  CompanyRole,
  CreateMitekResponseSchema,
} from "@checkatrade/trade-bff-types";

import {
  createRevettingEntry,
  getRevettingEntry,
} from "../firebase/firestore/revetting";
import { RevettingRecord } from "../firebase/firestore/schemas/revetting";
import { mitek } from "../mitek";
import { team } from "../team";
import { getMemberByCompanyId } from "../team/get-member-by-company-id";

const getExistingRevettingReference = async (
  companyId: number,
  logger: Logger,
): Promise<RevettingRecord | undefined> => {
  const existingRevettingEntry = await getRevettingEntry(companyId);

  if (existingRevettingEntry?.mitekReferenceId) {
    logger.info(
      { companyId },
      "Using existing revetting reference from Firestore",
    );

    return existingRevettingEntry;
  }

  return undefined;
};

const generateRevettingRequest = async (
  companyId: number,
  logger: Logger,
): Promise<CreateMitekResponseSchema> => {
  const { memberId } = (await getMemberByCompanyId(companyId, logger)) ?? {};
  const teamPersonData =
    memberId ?
      await team.getTeamPerson(
        memberId,
        { relationshipType: [CompanyRole.Owner] },
        logger,
      )
    : undefined;
  const person = teamPersonData?.data?.find((person) => person.phone);

  if (!person) {
    throw new NotFoundError("Member not found");
  }

  const revettingRequest: CreateMitekResponseSchema =
    await mitek.createMitekRequest(person);

  await createRevettingEntry({
    companyId,
    mitekReferenceId: revettingRequest.reference,
  });

  return revettingRequest;
};

export const getRevettingRequestLink = async (
  companyId: number,
  logger: Logger,
): Promise<string | undefined> => {
  const existingRevettingReference = await getExistingRevettingReference(
    companyId,
    logger,
  );

  if (!existingRevettingReference) {
    const { link } = await generateRevettingRequest(companyId, logger);
    return link;
  }

  const revettingStatusResponse = await mitek.getRequestStatus(
    existingRevettingReference.mitekReferenceId,
    logger,
  );

  if (revettingStatusResponse.status === "EXPIRED") {
    const { link } = await generateRevettingRequest(companyId, logger);
    return link;
  }

  return revettingStatusResponse.link;
};
