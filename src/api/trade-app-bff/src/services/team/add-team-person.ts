import { Logger } from "@checkatrade/fastify-five";
import {
  AddTeamPersonsQuery,
  AddTeamPersonsResponse,
  addTeamPersonsResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";
import { mapToAddTeamPersonTradeDataBody } from "./mappers/add-team-persons-mapper";

export const addTeamPerson = async (
  data: AddTeamPersonsQuery,
  companyId: number,
  logger: Logger,
): Promise<AddTeamPersonsResponse | undefined> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const url = `/members/${memberId}/persons`;
    const tdsPerson = mapToAddTeamPersonTradeDataBody(data);

    const response = await tdsHttpClient.post(url, tdsPerson);

    return Value.Parse(addTeamPersonsResponse, response.data);
  } catch (error) {
    logger.error(error, "Failed to add person to team service");
    throw error;
  }
};
