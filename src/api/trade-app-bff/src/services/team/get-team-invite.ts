import { Logger } from "@checkatrade/logging";
import {
  GetTeamInviteResponseTDS,
  getTeamInviteResponseTDS,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";

export const getTeamInvite = async (
  id: string,
  companyId: number,
  logger: Logger,
  memberId?: string,
): Promise<GetTeamInviteResponseTDS | undefined> => {
  try {
    if (!memberId) {
      memberId = await getHandledMemberId(companyId, logger);
    }

    const response = await tdsHttpClient.get(`/invites/${id}`);

    return Value.Parse(getTeamInviteResponseTDS, {
      ...response.data,
      currentMemberId: memberId,
    });
  } catch (error) {
    logger.error(error, `Failed to get team invite for id ${id}`);
    throw error;
  }
};
