import { Logger } from "@checkatrade/logging";
import {
  DeleteTeamPersonQuery,
  DeleteTeamPersonResponse,
  updateTeamPersonResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";

export const deleteTeamPerson = async (
  data: DeleteTeamPersonQuery,
  companyId: number,
  personId: string,
  logger: Logger,
): Promise<DeleteTeamPersonResponse | undefined> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const url = `/members/${memberId}/persons/${data.type}/${personId}`;

    const response = await tdsHttpClient.delete(url);

    return Value.Parse(updateTeamPersonResponse, response.data);
  } catch (error) {
    logger.error(error, "Failed to delete team person service");
    throw error;
  }
};
