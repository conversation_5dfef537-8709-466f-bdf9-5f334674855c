import { Logger } from "@checkatrade/logging";
import { DeleteSubcontractorResponse } from "@checkatrade/trade-bff-types";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";

export const deleteSubcontractor = async (
  companyId: number,
  subcontractorId: string,
  inviteId: string,
  logger: Logger,
): Promise<DeleteSubcontractorResponse | undefined> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    // Unlink MemberInvite
    await tdsHttpClient.delete(`/invites/${inviteId}`);

    return { subcontractorId, memberId, inviteId };
  } catch (error) {
    logger.error(error, "Failed to delete subcontractor");
    throw error;
  }
};
