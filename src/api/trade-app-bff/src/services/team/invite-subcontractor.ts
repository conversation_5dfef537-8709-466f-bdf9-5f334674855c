import { Logger } from "@checkatrade/logging";
import {
  InviteSubcontractorQuery,
  InviteSubcontractorTradeDataResponse,
  inviteSubcontractorTradeDataResponse,
} from "@checkatrade/trade-bff-types";
import dayjs from "dayjs";
import z from "zod";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";

export const inviteSubcontractor = async (
  data: InviteSubcontractorQuery,
  companyId: number,
  logger: Logger,
): Promise<InviteSubcontractorTradeDataResponse | undefined> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const url = `/members/${memberId}/invites`;
    const expiryDate = dayjs().add(90, "day").toISOString();

    const response = await tdsHttpClient.post(url, {
      relationshipStatus: "Pending",
      relationshipType: "Subcontractor",
      emailAddress: data.email,
      expiryDate,
    });

    return z.parse(inviteSubcontractorTradeDataResponse, response.data);
  } catch (error) {
    logger.error(error, "Failed to create subcontractor invite");
    throw error;
  }
};
