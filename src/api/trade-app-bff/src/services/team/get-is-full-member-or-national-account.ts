import { Logger } from "@checkatrade/logging";
import {
  GetIsFullMemberOrNationalAccountResponse,
  getIsFullMemberOrNationalAccountResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";

export const getIsFullMemberOrNationalAccount = async (
  companyId: number,
  logger: Logger,
): Promise<GetIsFullMemberOrNationalAccountResponse> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const url = `/members/${memberId}/isFullMemberOrNationalAccount`;

    const response = await tdsHttpClient.get(url);

    return Value.Parse(getIsFullMemberOrNationalAccountResponse, response.data);
  } catch (error) {
    logger.error(
      error,
      `Failed to get is full member or national account for company ${companyId}`,
    );
    throw error;
  }
};
