import { addConsentEmailEvent } from "./add-consent-email-event";
import { addTeamPerson } from "./add-team-person";
import { config } from "./config";
import { deleteSubcontractor } from "./delete-subcontractor";
import { deleteTeam<PERSON>erson } from "./delete-team-person";
import { getIsFullMemberOrNationalAccount } from "./get-is-full-member-or-national-account";
import { getTeam } from "./get-team";
import { getTeamInvite } from "./get-team-invite";
import { getTeamInvites } from "./get-team-invites";
import { getTeamMemberId } from "./get-team-memberId";
import { getTeamPendingInvites } from "./get-team-pending-invites";
import { getTeamPerson } from "./get-team-person";
import { getVettingConsentTokenDecode } from "./get-vetting-consent-token-decode";
import { inviteSubcontractor } from "./invite-subcontractor";
import { updateTeamInvite } from "./update-team-invite";
import { updateTeamPerson } from "./update-team-person";

export const team = {
  config,
  addTeamPerson,
  updateTeamPerson,
  deleteTeamPerson,
  getTeam,
  getTeamPerson,
  inviteSubcontractor,
  getTeamInvites,
  getTeamMemberId,
  updateTeamInvite,
  getTeamInvite,
  addConsentEmailEvent,
  getTeamPendingInvites,
  getVettingConsentTokenDecode,
  deleteSubcontractor,
  getIsFullMemberOrNationalAccount,
};
