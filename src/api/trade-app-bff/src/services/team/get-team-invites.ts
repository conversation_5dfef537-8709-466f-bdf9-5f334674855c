import { UnprocessableEntityError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/fastify-five";
import {
  GetTeamInvitesTradeDataResponse,
  VettingStatus,
  getTeamMemberInviteTVSResponse,
} from "@checkatrade/trade-bff-types";
import z from "zod";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";
import { vetting } from "../vetting";

export const getTeamInvites = async (
  companyId: number,
  logger: Logger,
  page?: number,
  pageSize?: number,
): Promise<GetTeamInvitesTradeDataResponse | undefined> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const response = await tdsHttpClient.get(`/members/${memberId}/invites`, {
      params: {
        page,
        pageSize,
      },
    });

    const { data, pagination } = z.parse(
      getTeamMemberInviteTVSResponse,
      response.data,
    );

    const invitesWithVettingStatusesPromises = data.map(async (res) => {
      let vettingStatus: VettingStatus | null = null;
      if (res.invite.childMemberId !== null) {
        vettingStatus = await vetting.getCompanyVettingDetails(
          res.invite.childMemberId,
        );
      }
      return {
        ...res,
        invite: {
          ...res.invite,
          vettingStatus: vettingStatus ?? VettingStatus.NotStarted,
        },
      };
    });

    const resolvedInvites = await Promise.all(
      invitesWithVettingStatusesPromises,
    );

    return {
      data: resolvedInvites,
      pagination,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      logger.error(error.message, "Failed to parse team invites response");
      throw new UnprocessableEntityError(
        "Failed to parse team invites response",
      );
    }
    logger.error(error, "Failed to get team invites");
    throw error;
  }
};
