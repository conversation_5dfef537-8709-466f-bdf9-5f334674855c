import { Logger } from "@checkatrade/fastify-five";
import {
  GetVettingConsentTokenDecodeResponse,
  getVettingConsentTokenDecodeResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { decodeMagicLink } from "../comms/decode-magic-link";
import { config } from "./config";

export const getVettingConsentTokenDecode = async (
  token: string,
  logger: Logger,
): Promise<GetVettingConsentTokenDecodeResponse | undefined> => {
  try {
    const payload = await decodeMagicLink(
      getVettingConsentTokenDecodeResponse,
      token,
      config.consentEmailTokenSigningKey,
      "checkatrade-trade-bff",
      "checkatrade-trade-signup",
    );
    return Value.Parse(getVettingConsentTokenDecodeResponse, payload);
  } catch (error) {
    logger.error(error, "Error decoding vetting consent token");
    throw error;
  }
};
