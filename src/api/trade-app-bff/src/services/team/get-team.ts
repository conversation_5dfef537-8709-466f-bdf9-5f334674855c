import {
  ApiError,
  InternalServerError,
  UnprocessableEntityError,
} from "@checkatrade/errors";
import { Logger } from "@checkatrade/fastify-five";
import {
  GetTeamTradeDataResponse,
  getTeamTradeDataResponse,
} from "@checkatrade/trade-bff-types";
import z, { ZodError } from "zod";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";

export const getTeam = async ({
  companyId,
  relationshipType,
  logger,
  searchText,
  page,
  pageSize,
  sortBy,
  sortOrder,
  status,
}: {
  companyId: number;
  relationshipType: Array<string>;
  logger: Logger;
  searchText?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: string;
  status?: string;
}): Promise<GetTeamTradeDataResponse | undefined> => {
  let resData: GetTeamTradeDataResponse | undefined;
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const url = `/members/${memberId}/persons`;

    const response = await tdsHttpClient.get(url, {
      params: {
        relationshipType,
        searchText,
        page,
        pageSize,
        sortBy,
        sortOrder,
        status,
      },
    });

    resData = response.data;

    return z.parse(getTeamTradeDataResponse, resData);
  } catch (error) {
    if (error instanceof ApiError) {
      logger.error(error, "Failed to fetch team person data");
      throw error;
    }
    if (error instanceof ZodError) {
      logger.warn(resData, "Failed to parse team persons response");
      throw new UnprocessableEntityError(error.message);
    }
    logger.error(error, "Failed to get team persons");
    // Should (hopefully) never trigger
    throw new InternalServerError("Error getting team persons");
  }
};
