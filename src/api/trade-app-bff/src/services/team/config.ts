import { env } from "@checkatrade/env";

type TeamsConfig = {
  consentEmailTokenSigningKey: string;
  consentEmailTokenExpiration: number;
  consentEmailBaseUrl: string;
};

export const config: TeamsConfig = {
  consentEmailTokenSigningKey: env.get("CONSENT_EMAIL_TOKEN_SIGNING_KEY"),
  consentEmailTokenExpiration: env.getNumber("CONSENT_EMAIL_TOKEN_EXPIRATION"),
  consentEmailBaseUrl: `${env.get("TRADE_SIGNUP_WEB_URL")}/employee-consent`,
};
