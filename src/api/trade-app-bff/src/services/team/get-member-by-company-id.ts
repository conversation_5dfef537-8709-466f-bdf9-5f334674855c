import { UnprocessableEntityError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/fastify-five";
import {
  GetMemberByLegacyCompanyIdResponse,
  getMemberByLegacyCompanyIdResponse,
} from "@checkatrade/trade-bff-types";
import z from "zod";

import { tdsHttpClient } from "../trade-data-service";

export const getMemberByCompanyId = async (
  legacyCompanyId: number,
  logger: Logger,
): Promise<GetMemberByLegacyCompanyIdResponse | undefined> => {
  try {
    const response = await tdsHttpClient.get("/members", {
      params: {
        legacyCompanyId,
      },
    });

    return z.parse(getMemberByLegacyCompanyIdResponse, response.data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      logger.error(error.message, "Validation failed for member data");
      throw new UnprocessableEntityError("Validation failed for member data");
    }
    logger.error(error, "Failed to get member");
    throw error;
  }
};
