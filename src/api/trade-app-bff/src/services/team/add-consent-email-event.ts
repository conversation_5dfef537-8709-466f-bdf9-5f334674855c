import { Logger } from "@checkatrade/fastify-five";
import {
  AddConsentEmailBody,
  AddConsentEmailResponse,
  addConsentEmailResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { comms as emailComms } from "../../lib/api-common";
import { comms } from "../comms";
import { config } from "./config";

export const addConsentEmailEvent = async (
  data: AddConsentEmailBody,
  companyId: number,
  personId: string,
  memberId: string,
  logger: Logger,
): Promise<AddConsentEmailResponse | undefined> => {
  try {
    logger.info({ personId, memberId, data }, "Creating consent email event");
    const magicLink = await comms.createMagicLink(
      { memberId, personId, companyId },
      config.consentEmailBaseUrl,
      config.consentEmailTokenSigningKey,
      config.consentEmailTokenExpiration,
      "checkatrade-trade-bff",
      "checkatrade-trade-signup",
    );

    await emailComms.vettingConsentRequest({
      companyId,
      personId,
      ...data,
      url: magicLink,
    });

    return Value.Parse(addConsentEmailResponse, { Email: data.email });
  } catch (error) {
    logger.error(error, "Error creating consent email event");
    throw error;
  }
};
