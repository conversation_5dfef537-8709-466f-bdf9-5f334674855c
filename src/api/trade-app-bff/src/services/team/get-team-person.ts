import { Logger } from "@checkatrade/fastify-five";
import {
  CompanyRole,
  GetTeamTradeDataResponse,
  getTeamTradeDataResponse,
} from "@checkatrade/trade-bff-types";
import z from "zod";

import { tdsHttpClient } from "../trade-data-service";

// TODO: Import these from @checkatrade/core-trade-shared-types when available in future SDK implementation - https://checkatrade.atlassian.net/browse/DSU-3326
export type GetTeamPersonQueryParams = {
  page?: number;
  pageSize?: number;
  sortBy?: "name";
  sortOrder?: "asc" | "desc";
  personId?: string;
  searchText?: string;
  status?: "active" | "inactive";
  relationshipType?: CompanyRole[];
  nickname?: string;
  email?: string;
};

export const getTeamPerson = async (
  memberId: string,
  params: GetTeamPersonQueryParams,
  logger: Logger,
): Promise<GetTeamTradeDataResponse | undefined> => {
  try {
    const url = `/members/${memberId}/persons`;

    const response = await tdsHttpClient.get(url, {
      params,
    });

    return z.parse(getTeamTradeDataResponse, response.data);
  } catch (error) {
    logger.error(error, "Failed to get team person");
    throw error;
  }
};
