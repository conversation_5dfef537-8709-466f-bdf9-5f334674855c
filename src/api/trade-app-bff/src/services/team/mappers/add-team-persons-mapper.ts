import {
  AddTeamPersonTradeDataBody,
  AddTeamPersonsQuery,
  CompanyRole,
  ContractingType,
} from "@checkatrade/trade-bff-types";

/**
 * Maps a contracting type to the appropriate company role
 * @param contractingType - The contracting type to be mapped
 * @returns The corresponding company role
 */
function contractingTypeToRole(contractingType: ContractingType): CompanyRole {
  return contractingType === "Employee" ?
      CompanyRole.Employee
    : CompanyRole.Owner;
}

export function mapToAddTeamPersonTradeDataBody(
  query: AddTeamPersonsQuery,
): AddTeamPersonTradeDataBody {
  return {
    firstName: query.firstName,
    lastName: query.lastName,
    dateOfBirth: query.dateOfBirth,
    email: query.email,
    mobilePhone: query.phoneNumber,
    nickname: query.nickname,
    address: query.address,
    workCategories: query.workCategories,
    fullName: `${query.firstName} ${query.lastName}`,
    lastUpdated: new Date().toISOString(),
    role: contractingTypeToRole(query.contractingType),
  };
}
