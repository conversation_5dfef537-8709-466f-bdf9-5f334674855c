import { UnprocessableEntityError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/fastify-five";
import {
  GetTeamPendingInvitesQuery,
  GetTeamPendingInvitesTradeDataResponse,
  TeamPendingInvitesRelationshipStatus,
  getTeamPendingInvitesTradeDataResponse,
} from "@checkatrade/trade-bff-types";
import z from "zod";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";

export const getTeamPendingInvites = async (
  companyId: number,
  logger: Logger,
  page?: number,
  pageSize?: number,
  relationshipStatus?: TeamPendingInvitesRelationshipStatus,
): Promise<GetTeamPendingInvitesTradeDataResponse | undefined> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const url = `/members/${memberId}/pending-invites`;

    const params: Partial<GetTeamPendingInvitesQuery> = {
      page,
      pageSize,
    };

    if (relationshipStatus) {
      params.relationshipStatus = relationshipStatus;
    }

    const response = await tdsHttpClient.get(url, {
      params,
    });

    return z.parse(getTeamPendingInvitesTradeDataResponse, response.data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      logger.error(error, "Error getting team pending invites");
      throw new UnprocessableEntityError("Error getting team pending invites");
    }
    logger.error(error, "Failed to get team pending invites");
    throw error;
  }
};
