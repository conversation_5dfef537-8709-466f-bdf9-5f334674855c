import { NotFoundError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";
import {
  UpdateTeamInviteBody,
  UpdateTeamInviteResponse,
  updateTeamInviteResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { tdsHttpClient } from "../trade-data-service";
import { getTeamInvite } from "./get-team-invite";

// parentMemberId - the member that sent the invite
// childMemberId - the memberId of the person that is being invited fetched by companyId
// inviteId - the invite that is being updated

export const updateTeamInvite = async (
  data: UpdateTeamInviteBody,
  inviteId: string,
  companyId: number,
  logger: Logger,
): Promise<UpdateTeamInviteResponse | undefined> => {
  try {
    const memberId = await getHandledMemberId(companyId, logger);

    const invite = await getTeamInvite(inviteId, companyId, logger, memberId);
    if (!invite?.parentMemberId) {
      // this is covering an edge case as a parentMemberId should be always returned
      throw new NotFoundError(`Failed to get invite for id ${inviteId}`);
    }

    const url = `/members/${invite.parentMemberId}/invites`;
    const response = await tdsHttpClient.put(url, {
      childMemberId: memberId,
      inviteId,
      ...data,
    });

    return Value.Parse(updateTeamInviteResponse, response.data);
  } catch (error) {
    logger.error(
      {
        error,
        data,
        inviteId,
      },
      "Failed to update team invite service",
    );
    throw error;
  }
};
