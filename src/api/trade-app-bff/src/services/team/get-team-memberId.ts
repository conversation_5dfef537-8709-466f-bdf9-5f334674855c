import { Logger } from "@checkatrade/logging";
import {
  GetTeamMemberIdResponse,
  getTeamMemberIdResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { tdsHttpClient } from "../trade-data-service";

export const getTeamMemberId = async (
  companyId: number,
  logger: Logger,
): Promise<GetTeamMemberIdResponse | undefined> => {
  try {
    const url = `/members?legacyCompanyId=${companyId}`;

    const response = await tdsHttpClient.get(url);

    return Value.Parse(getTeamMemberIdResponse, response.data);
  } catch (error) {
    logger.error(
      error,
      `Failed to get team member id for companyId ${companyId}`,
    );
    throw error;
  }
};
