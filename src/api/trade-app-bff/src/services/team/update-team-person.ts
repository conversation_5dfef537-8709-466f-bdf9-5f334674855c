import { Logger } from "@checkatrade/logging";
import {
  UpdateTeamPersonResponse,
  UpdateTeamPersonsQuery,
  updateTeamPersonResponse,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { tdsHttpClient } from "../trade-data-service";
import { mapToUpdateTeamPersonTradeDataBody } from "./mappers/update-team-person-mapper";

export const updateTeamPerson = async (
  data: UpdateTeamPersonsQuery,
  personId: string,
  logger: Logger,
): Promise<UpdateTeamPersonResponse | undefined> => {
  try {
    const url = `/persons/${personId}`;
    const tdsPerson = mapToUpdateTeamPersonTradeDataBody(data);

    const response = await tdsHttpClient.put(url, tdsPerson);

    return Value.Parse(updateTeamPersonResponse, response.data);
  } catch (error) {
    logger.error(error, "Failed to update team person service");
    throw error;
  }
};
