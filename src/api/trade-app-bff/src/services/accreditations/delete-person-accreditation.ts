import admin from "firebase-admin";

import { tradeApp } from "../firebase/app";
import {
  COMPANIES_COLLECTION,
  PERSON_ACCREDITATIONS_COLLECTION,
} from "../firebase/firestore/collections";
import {
  AccreditationMutationType,
  AccreditationPlatform,
  PersonAccreditation,
} from "../firebase/firestore/schemas/person-accreditations";

export const deletePersonAccreditation = async (
  personAccreditation: PersonAccreditation,
): Promise<void> => {
  const modifiedPersonAccreditation = {
    ...personAccreditation,
    history: [
      ...personAccreditation.history,
      {
        platform: AccreditationPlatform.TradeApp,
        type: AccreditationMutationType.Deleted, //firebase lib doesn't allow setting serverTimestamp for the new array entry in history
        updatedDate: new Date(),
      },
    ],
    modifiedDate: admin.firestore.FieldValue.serverTimestamp(),
    isDeleted: true,
  };

  await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(personAccreditation.companyId.toString())
    .collection(PERSON_ACCREDITATIONS_COLLECTION)
    .doc(personAccreditation.id!)
    .update({
      history: modifiedPersonAccreditation.history,
      modifiedDate: modifiedPersonAccreditation.modifiedDate,
      isDeleted: modifiedPersonAccreditation.isDeleted,
    });
};
