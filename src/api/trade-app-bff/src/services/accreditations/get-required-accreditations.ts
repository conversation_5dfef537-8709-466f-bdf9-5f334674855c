import { WorkCategoryMinimal } from "@checkatrade/trade-bff-types";
import { FastifyBaseLogger } from "fastify";

import { tradeApp } from "../firebase/app";
import { CATEGORIES_COLLECTION } from "../firebase/firestore/collections";
import { categorySchema } from "../firebase/firestore/schemas/categories";
import { validateSchema } from "../firebase/utils/validate-schema";

export const getRequiredAccreditations = async (
  workCategories: WorkCategoryMinimal,
  logger?: FastifyBaseLogger,
): Promise<number[] | undefined> => {
  const requiredIds = new Set<number>();
  for (const personCategory of workCategories) {
    // grab the category document from firestore
    const query = await tradeApp
      .firestore()
      .collection(CATEGORIES_COLLECTION)
      .doc(personCategory.id)
      .get();
    if (!query.exists) {
      return;
    }
    // validate the results
    const validationResults = validateSchema(categorySchema, query.data());
    if (!validationResults.valid || !validationResults.value) {
      if (logger) {
        logger.error(
          {
            validationErrors: validationResults.errors,
            personCategory,
          },
          "Invalid category document",
        );
      }
      throw new Error("Invalid category document");
    }
    const category = validationResults.value;

    // create a lookup map of subCategoryId to accreditationIds if they have require accreditations
    const subCategoryMap: Record<number, number[]> =
      category.subCategories.reduce(
        (map, subCategory) => {
          if (subCategory.accreditationsNeeded.length > 0) {
            map[subCategory.webCatId] = subCategory.accreditationsNeeded
              .filter((accreditaion) => !accreditaion.deleted)
              .map((accreditation) => accreditation.id);
          }
          return map;
        },
        {} as Record<number, number[]>,
      );

    // check map for subcategory that was passed in with person workCategories
    for (const subCategory of personCategory.subCategories) {
      const subCatId = parseInt(subCategory.id, 10);
      const accreditationIds = subCategoryMap[subCatId];

      if (accreditationIds) {
        accreditationIds.forEach((id) => requiredIds.add(id));
      }
    }
  }
  return Array.from(requiredIds);
};
