import { Static, Type } from "@sinclair/typebox";
import { FastifyBaseLogger } from "fastify";

import { tradeApp } from "../firebase/app";
import {
  COMPANIES_COLLECTION,
  PERSON_ACCREDITATIONS_COLLECTION,
} from "../firebase/firestore/collections";
import { createDateFirestoreConverter } from "../firebase/firestore/firestore-date-converter";
import { personAccreditationsSchema } from "../firebase/firestore/schemas/person-accreditations";
import { validateSchema } from "../firebase/utils/validate-schema";

const PersonAccreditationsArraySchema = Type.Array(personAccreditationsSchema);
type PersonAccreditationsArray = Static<typeof PersonAccreditationsArraySchema>;

export const getPersonAccreditations = async (
  companyId: number,
  personId: string,
  logger?: FastifyBaseLogger,
): Promise<PersonAccreditationsArray> => {
  const query = await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(PERSON_ACCREDITATIONS_COLLECTION)
    .withConverter(createDateFirestoreConverter<PersonAccreditationsArray>())
    .where("personId", "==", personId)
    .get();

  if (query.empty) {
    return [] as PersonAccreditationsArray;
  }

  const accreditations = query.docs.map((doc) => ({
    ...doc.data(),
  }));

  const validationResults = validateSchema(
    PersonAccreditationsArraySchema,
    accreditations,
  );

  if (!validationResults.valid) {
    if (logger) {
      logger.error(
        {
          validationErrors: validationResults.errors,
          personId,
          companyId,
        },
        "Invalid personAccreditation document",
      );
    }
    throw new Error("Invalid personAccreditation document");
  }

  return validationResults.value ?? ([] as PersonAccreditationsArray);
};
