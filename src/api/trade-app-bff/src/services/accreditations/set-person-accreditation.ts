import { tradeApp } from "../firebase/app";
import {
  COMPANIES_COLLECTION,
  PERSON_ACCREDITATIONS_COLLECTION,
} from "../firebase/firestore/collections";
import { PersonAccreditation } from "../firebase/firestore/schemas/person-accreditations";

export const setPersonAccreditation = async (
  personAccreditation: PersonAccreditation,
  documentId?: string,
): Promise<PersonAccreditation> => {
  const personAccreditationRef = tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(personAccreditation.companyId.toString())
    .collection(PERSON_ACCREDITATIONS_COLLECTION);

  const id = documentId ?? personAccreditationRef.doc().id;

  const personAccreditationWithId = {
    ...personAccreditation,
    id,
    modifiedDate: new Date(),
  };
  await personAccreditationRef.doc(id).set(personAccreditationWithId);

  return personAccreditationWithId;
};
