import { tradeApp } from "../firebase/app";
import { ACCREDITATIONS_COLLECTION } from "../firebase/firestore/collections";
import { Accreditation } from "../firebase/firestore/schemas/accreditation";

export const getAccreditationById = async (
  id: number,
): Promise<Accreditation | undefined> => {
  const accreditationRef = tradeApp
    .firestore()
    .collection(ACCREDITATIONS_COLLECTION)
    .doc(id.toString());

  const accreditationDoc = await accreditationRef.get();

  if (!accreditationDoc.exists) {
    return undefined;
  }

  return accreditationDoc.data() as Accreditation;
};
