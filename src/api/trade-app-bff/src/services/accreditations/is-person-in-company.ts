import { FastifyBaseLogger } from "fastify";

import { getTeamMemberId } from "../team/get-team-memberId";
import { getTeamPerson } from "../team/get-team-person";

export const isPersonInCompany = async (
  companyId: number,
  personId: string,
  logger: FastifyBaseLogger,
): Promise<boolean> => {
  const { memberId } = (await getTeamMemberId(companyId, logger)) ?? {};

  if (!memberId) {
    logger.error(`Failed to get memberId for company ${companyId}`);
    return false;
  }

  const person = await getTeamPerson(memberId, { personId }, logger);

  if (!person) {
    logger.warn(`Failed to get person ${personId} for member ${memberId}`);
    return false;
  }

  return person.data.find((p) => p.id === personId) !== undefined;
};
