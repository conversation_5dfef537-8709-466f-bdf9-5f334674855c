import { FastifyBaseLogger } from "fastify";

import { tradeApp } from "../firebase/app";
import {
  COMPANIES_COLLECTION,
  PERSON_ACCREDITATIONS_COLLECTION,
} from "../firebase/firestore/collections";
import { createDateFirestoreConverter } from "../firebase/firestore/firestore-date-converter";
import {
  PersonAccreditation,
  personAccreditationsSchema,
} from "../firebase/firestore/schemas/person-accreditations";
import { validateSchema } from "../firebase/utils/validate-schema";

export const getPersonAccreditation = async (
  companyId: number,
  personId: string,
  accreditationId: number,
  logger?: FastifyBaseLogger,
): Promise<PersonAccreditation | undefined> => {
  const query = await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(PERSON_ACCREDITATIONS_COLLECTION)
    .withConverter(createDateFirestoreConverter<PersonAccreditation>())
    .where("accreditationId", "==", accreditationId)
    .where("personId", "==", personId)
    .get();

  if (query.empty) {
    return undefined;
  }

  if (query.docs.length > 1) {
    if (logger) {
      logger.error(
        { personId, companyId, accreditationId },
        `More than one personAccreditation document found`,
      );
    }
    throw new Error("More than one personAccreditation document found");
  }

  const validationResults = validateSchema(personAccreditationsSchema, {
    ...query.docs[0].data(),
    id: query.docs[0].id,
  });

  if (!validationResults.valid) {
    if (logger) {
      logger.error(
        {
          validationErrors: validationResults.errors,
          personId,
          companyId,
          accreditationId,
        },
        "Invalid personAccreditation document",
      );
    }
    throw new Error("Invalid personAccreditation document");
  }

  return validationResults.value;
};
