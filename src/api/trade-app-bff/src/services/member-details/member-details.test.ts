import { NotFoundError } from "@checkatrade/errors";
import {
  ContactRole,
  GetMemberDetailsResponse,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import * as MemberUtils from "../../utilities/member";
import * as GetCompany from "../firebase/firestore/get-company";
import { CompanyType } from "../firebase/firestore/schemas/company";
import {
  NOT_FOUND_ERROR_MESSAGE,
  checkAccountOwner,
  memberDetails,
} from "./member-details";

describe("member-details", () => {
  const mockCompanyId = 123;
  const mockUserEmail = "<EMAIL>";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("memberDetails", () => {
    it("should return member details when optional fields are present", async () => {
      const MOCK_EMAIL = faker.internet.email();
      const MOCK_PHONE = faker.phone.number();
      const MOCK_COMPANY_PRIMARY_POSTAL_ADDRESS = {
        street: "123 Main St",
        city: "Anytown",
        county: "Anycounty",
        postcode: "PO1 1AA",
        country: "Anyland",
      };

      const MOCK_COMPANY_ADMIN_ADDRESS = {
        street: "456 Admin St",
        city: "Adminville",
        county: "Adminshire",
        postcode: "AD1 1AA",
        country: "Adminland",
      };

      const MOCK_CONTACTS = [
        {
          email: mockUserEmail,
          roleId: 1,
          dateOfBirth: { _seconds: **********, _nanoseconds: 0 }, // Jan 1, 2021
        },
      ];

      // it should transform the Firestore timestamp to an ISO string
      // should add the role using the corresponding ContactRole key
      const expectedContacts = [
        {
          email: mockUserEmail,
          roleId: 1,
          role: ContactRole[ContactRole.Owner],
          dateOfBirth: "2021-01-01T00:00:00.000Z", // Jan 1, 2021
        },
      ];

      const mockCompanyDoc: CompanyType = {
        id: 1234,
        tradeId: 1000,
        name: "Company 1234",
        contacts: MOCK_CONTACTS,
        accountEmail: MOCK_EMAIL,
        accountPhone: MOCK_PHONE,
        companyAdminAddress: MOCK_COMPANY_ADMIN_ADDRESS,
        companyPrimaryPostalAddress: MOCK_COMPANY_PRIMARY_POSTAL_ADDRESS,
      };

      const expectedResult: GetMemberDetailsResponse = {
        isAccountOwner: true,
        accountEmail: MOCK_EMAIL,
        accountPhone: MOCK_PHONE,
        companyAdminAddress: MOCK_COMPANY_ADMIN_ADDRESS,
        contacts: expectedContacts,
        companyPrimaryPostalAddress: MOCK_COMPANY_PRIMARY_POSTAL_ADDRESS,
      };

      jest
        .spyOn(GetCompany, "getCompanyDoc")
        .mockResolvedValueOnce(mockCompanyDoc);

      jest.spyOn(MemberUtils, "isAccountOwner").mockReturnValueOnce(true);

      const result = await memberDetails({
        companyId: mockCompanyId,
        userEmail: mockUserEmail,
      });

      expect(GetCompany.getCompanyDoc).toHaveBeenCalledWith(
        mockCompanyId.toString(),
      );

      expect(MemberUtils.isAccountOwner).toHaveBeenCalledWith(
        mockUserEmail,
        expectedContacts,
      );

      expect(result).toEqual(expectedResult);
    });

    it("should return member details when optional fields ARE NOT present", async () => {
      const mockCompanyDoc: CompanyType = {
        id: 1234,
        tradeId: 1000,
        name: "Company 1234",
      };

      const expectedResult: GetMemberDetailsResponse = {
        isAccountOwner: false,
      };

      jest
        .spyOn(GetCompany, "getCompanyDoc")
        .mockResolvedValueOnce(mockCompanyDoc);

      jest.spyOn(MemberUtils, "isAccountOwner").mockReturnValueOnce(true);

      const result = await memberDetails({
        companyId: mockCompanyId,
        userEmail: mockUserEmail,
      });

      expect(GetCompany.getCompanyDoc).toHaveBeenCalledWith(
        mockCompanyId.toString(),
      );

      expect(MemberUtils.isAccountOwner).not.toHaveBeenCalled();

      expect(result).toEqual(expectedResult);
    });

    it("should throw NotFoundError when no company is found", async () => {
      jest.spyOn(GetCompany, "getCompanyDoc").mockResolvedValueOnce(undefined);

      await expect(
        memberDetails({
          companyId: mockCompanyId,
          userEmail: mockUserEmail,
        }),
      ).rejects.toThrow(new NotFoundError(NOT_FOUND_ERROR_MESSAGE));
    });
  });

  describe("checkAccountOwner", () => {
    it("should return true when the user is an account owner", async () => {
      const mockCompanyDoc: CompanyType = {
        id: 1234,
        tradeId: 1000,
        name: "Company 1234",
        contacts: [{ email: mockUserEmail, roleId: 1 }],
      };

      jest
        .spyOn(GetCompany, "getCompanyDoc")
        .mockResolvedValueOnce(mockCompanyDoc);

      jest.spyOn(MemberUtils, "isAccountOwner").mockReturnValueOnce(true);

      const result = await checkAccountOwner({
        companyId: mockCompanyId,
        userEmail: mockUserEmail,
      });

      expect(GetCompany.getCompanyDoc).toHaveBeenCalledWith(
        mockCompanyId.toString(),
      );

      expect(MemberUtils.isAccountOwner).toHaveBeenCalledWith(
        mockUserEmail,
        mockCompanyDoc.contacts,
      );
      expect(result).toBe(true);
    });

    it("should return false when the user is not an account owner", async () => {
      const mockCompanyDoc = {
        id: 1234,
        tradeId: 1000,
        name: "Company 1234",
        contacts: [{ email: "<EMAIL>", roleId: 3 }],
      };

      jest
        .spyOn(GetCompany, "getCompanyDoc")
        .mockResolvedValueOnce(mockCompanyDoc);

      jest.spyOn(MemberUtils, "isAccountOwner").mockReturnValueOnce(false);

      const result = await checkAccountOwner({
        companyId: mockCompanyId,
        userEmail: mockUserEmail,
      });

      expect(MemberUtils.isAccountOwner).toHaveBeenCalledWith(
        mockUserEmail,
        mockCompanyDoc.contacts,
      );
      expect(result).toBe(false);
    });

    it("should return false when company has no contacts", async () => {
      const mockCompanyDoc = {
        contacts: null,
        id: 1234,
        tradeId: 1000,
        name: "Company 1234",
      };

      jest
        .spyOn(GetCompany, "getCompanyDoc")
        .mockResolvedValueOnce(mockCompanyDoc);

      jest.spyOn(MemberUtils, "isAccountOwner");

      const result = await checkAccountOwner({
        companyId: mockCompanyId,
        userEmail: mockUserEmail,
      });

      expect(MemberUtils.isAccountOwner).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it("should throw NotFoundError when no company is found", async () => {
      jest.spyOn(GetCompany, "getCompanyDoc").mockResolvedValueOnce(undefined);

      await expect(
        checkAccountOwner({
          companyId: mockCompanyId,
          userEmail: mockUserEmail,
        }),
      ).rejects.toThrow(new NotFoundError(NOT_FOUND_ERROR_MESSAGE));
    });
  });
});
