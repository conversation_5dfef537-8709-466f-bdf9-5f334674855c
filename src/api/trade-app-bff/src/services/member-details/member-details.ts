import { NotFoundError } from "@checkatrade/errors";
import {
  ContactRole,
  GetMemberDetailsResponse,
  GetMemberDetailsResponseSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { transformFirestoreTimestamps } from "../../utilities/firestore";
import { isAccountOwner } from "../../utilities/member";
import { getCompanyDoc } from "../firebase/firestore/get-company";
import {
  CompanyType,
  ContactsType,
} from "../firebase/firestore/schemas/company";

interface MemberDetailsContactType extends ContactsType {
  role: string | null;
}

export const NOT_FOUND_ERROR_MESSAGE = "No member data found";

export const memberDetails = async ({
  companyId,
  userEmail,
}: {
  companyId: number;
  userEmail: string;
}): Promise<GetMemberDetailsResponse> => {
  const company = await getCompanyDoc(companyId.toString());

  if (!company) {
    throw new NotFoundError(NOT_FOUND_ERROR_MESSAGE);
  }

  const transformedCompany = applyTransformations(company);

  return Value.Parse(GetMemberDetailsResponseSchema, {
    ...transformedCompany,
    isAccountOwner:
      transformedCompany?.contacts ?
        isAccountOwner(userEmail, transformedCompany.contacts)
      : false,
  });
};

const applyTransformations = (company: CompanyType) => {
  // add role key using the corresponding ContactRole key
  if (company.contacts) {
    company.contacts = company.contacts?.map(mapContactRole);
  }

  // Transform Firestore timestamps to ISO date strings
  return transformFirestoreTimestamps(company);
};

const mapContactRole = (contact: ContactsType): MemberDetailsContactType => ({
  ...contact,
  role: contact.roleId != null ? ContactRole[contact.roleId] : null,
});

export const checkAccountOwner = async ({
  companyId,
  userEmail,
}: {
  companyId: number;
  userEmail: string;
}): Promise<boolean> => {
  const company = await getCompanyDoc(companyId.toString());

  if (!company) {
    throw new NotFoundError(NOT_FOUND_ERROR_MESSAGE);
  }

  return company.contacts ? isAccountOwner(userEmail, company.contacts) : false;
};
