import { httpClient } from "@checkatrade/axios";
import { Logger } from "@checkatrade/fastify-five";
import { gcp } from "@checkatrade/gcp";
import { Account, accountSchema } from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { config } from "./config";

export const getAccount = async (
  companyId: number,
  logger: Logger,
): Promise<Account | undefined> => {
  const token = await gcp.generateBearerToken(config.financeServiceHost);

  let account: Account | undefined;
  try {
    const response = await httpClient.get(
      `${config.financeServiceHost}/accounts/${companyId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          idType: "companyId",
        },
      },
    );
    account = Value.Parse(accountSchema, response.data);
  } catch (error) {
    logger.error(
      {
        error,
        companyId,
      },
      "Error getting account information",
    );
    throw error;
  }
  return account;
};
