import { httpClient } from "@checkatrade/axios";
import { Logger } from "@checkatrade/fastify-five";
import { gcp } from "@checkatrade/gcp";
import { Account, accountSchema } from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { config } from "./config";

export const updateAccount = async (
  accountId: string,
  paymentMethodId: string,
  logger: Logger,
): Promise<Account | undefined> => {
  const token = await gcp.generateBearerToken(config.financeServiceHost);

  let account: Account | undefined;
  try {
    const response = await httpClient.patch(
      `${config.financeServiceHost}/accounts/${accountId}`,
      {
        default_payment_method_id: paymentMethodId,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );
    account = Value.Parse(accountSchema, response.data);
  } catch (error) {
    logger.error(
      {
        error,
        accountId,
        paymentMethodId,
      },
      "Error updating account",
    );
    throw error;
  }
  return account;
};
