import { httpClient } from "@checkatrade/axios";
import { gcp } from "@checkatrade/gcp";
import {
  PaymentMethod,
  paymentMethodSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { config } from "./config";

export const getPaymentMethod = async (
  payment_method_id: string,
): Promise<PaymentMethod | undefined> => {
  const token = await gcp.generateBearerToken(config.financeServiceHost);

  const response = await httpClient.get(
    `${config.financeServiceHost}/payment-methods/${payment_method_id}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return Value.Parse(paymentMethodSchema, response.data);
};
