import { Value } from "@sinclair/typebox/value";

import { ContactsSchema } from "./company";

describe("Company Schema", () => {
  it("Should not error for a valid contacts schema", () => {
    const validContact = {
      firstName: "<PERSON>",
      dateOfBirth: {
        _seconds: -*********,
        _nanoseconds: 0,
      },
      lastName: "<PERSON>",
      email: null,
      phone: null,
      roleId: 3,
      contactId: "003Pt00000KNnvpIAD",
      mobilePhone: null,
      mailingAddress: {
        postcode: "SW12 0LT",
        country: "United Kingdom",
        street: "1 Weir Road",
        county: null,
        town: null,
        city: "London",
      },
    };

    expect(() => Value.Parse(ContactsSchema, validContact)).not.toThrow();
  });
});
