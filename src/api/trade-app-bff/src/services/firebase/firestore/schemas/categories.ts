import { Static, Type } from "@sinclair/typebox";

export const accreditationNeededSchema = Type.Object({
  id: Type.Number(),
  deleted: Type.Boolean(),
  name: Type.String(),
});

export const subCategorySchema = Type.Object({
  webCatId: Type.Number(),
  name: Type.String(),
  primary: Type.Boolean(),
  accreditationsNeeded: Type.Array(Type.Optional(accreditationNeededSchema)),
});

export const categorySchema = Type.Object({
  name: Type.String(),
  subCategories: Type.Array(subCategorySchema),
});

export type Category = Static<typeof categorySchema>;
export type SubCategory = Static<typeof subCategorySchema>;
export type AccreditationNeeded = Static<typeof accreditationNeededSchema>;
