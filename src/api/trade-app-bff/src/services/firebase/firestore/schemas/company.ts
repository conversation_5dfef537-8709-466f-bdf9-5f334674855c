import { Nullish } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

const FirestoreTimestampSchema = Type.Object({
  _nanoseconds: Type.Number(),
  _seconds: Type.Number(),
});

const MailingAddressSchema = Type.Object({
  city: Nullish(Type.String()),
  country: Nullish(Type.String()),
  county: Nullish(Type.String()),
  postcode: Nullish(Type.String()),
  street: Nullish(Type.String()),
  town: Nullish(Type.String()),
});

export const ContactsSchema = Type.Object({
  contactId: Nullish(Type.String()),
  dateOfBirth: Nullish(FirestoreTimestampSchema),
  email: Nullish(Type.String()),
  firstName: Nullish(Type.String()),
  lastName: Nullish(Type.String()),
  mailingAddress: Nullish(MailingAddressSchema),
  mobilePhone: Nullish(Type.String()),
  phone: Nullish(Type.String()),
  roleId: Nullish(Type.Number()),
});

export const CompanySchema = Type.Object({
  id: Type.Number(),
  tradeId: Type.Number(),
  name: Nullish(Type.String()),
  uniqueName: Nullish(Type.String()),
  membershipType: Nullish(Type.String()),
  membershipLevel: Nullish(Type.String()),
  membershipFlexible: Nullish(Type.Boolean()),
  accountBalance: Nullish(Type.Number()),
  accountEmail: Nullish(Type.String()),
  accountPhone: Nullish(Type.String()),
  contacts: Nullish(Type.Array(ContactsSchema)),
  companyAdminAddress: Nullish(MailingAddressSchema),
  companyPrimaryPostalAddress: Nullish(MailingAddressSchema),
});

export type ContactsType = Static<typeof ContactsSchema>;
export type CompanyType = Static<typeof CompanySchema>;
