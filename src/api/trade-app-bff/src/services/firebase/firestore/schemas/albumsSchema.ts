import { firestore } from "firebase-admin";
import { z } from "zod";

export enum ImageTag {
  NONE = "NONE",
  BEFORE = "BEFORE",
  DURING = "DURING",
  AFTER = "AFTER",
}

export enum IMAGE_STATUS {
  SUBMITTED = "SUBMITTED",
  LIVE = "LIVE",
  AI_FLAGGED = "AI_FLAGGED",
  HUMAN_FLAGGED = "HUMAN_FLAGGED",
  UPLOAD_FAILED = "UPLOAD_FAILED",
}

export enum IMAGE_TYPE {
  PHOTO = "PHOTO",
  VIDEO = "VIDEO",
}

export type Timestamp = { toDate: () => Date };

export const timestampToDate = z
  .custom<Date>((data) => data instanceof firestore.Timestamp)
  .transform<Date>((data) => (data as unknown as Timestamp)?.toDate());

export const ImageMetadata = z.object({
  width: z.number().int().nonnegative().nullish(),
  height: z.number().int().nonnegative().nullish(),
  rotation: z
    .union([
      z.null(),
      z.literal(0),
      z.literal(90),
      z.literal(180),
      z.literal(270),
    ])
    .optional(),
});

export const Image = z.object({
  dateCreated: timestampToDate,
  dateModified: timestampToDate.nullish(),
  description: z.string().nullish(),
  id: z.string(),
  imageId: z.string().nullish(),
  label: z.enum(ImageTag),
  metadata: ImageMetadata.nullish(),
  migrationId: z.string().nullish(),
  status: z.enum(IMAGE_STATUS),
  title: z.string().nullish(),
  type: z.enum(IMAGE_TYPE),
  url: z.string().nullish(),
  youTubeId: z.string().nullish(),
});

export const AlbumSchema = z.object({
  dateCreated: timestampToDate,
  dateModified: timestampToDate,
  description: z.string().nullish(),
  featuredProjectJobId: z.string().nullish(),
  id: z.string(),
  items: z.array(Image),
  itemsOrder: z.array(z.string()),
  legacyAlbumId: z.number().int().positive().nullish(),
  lastPhotoUploadedDate: timestampToDate.nullish(),
  title: z.string(),
  thumbnail: z.string().optional(),
  url: z.string().optional(),
});

export type AlbumServerType = Omit<
  AlbumType,
  "dateCreated" | "dateModified" | "lastPhotoUploadedDate"
> & {
  dateCreated: Timestamp;
  dateModified: Timestamp;
  lastPhotoUploadedDate: Timestamp;
};

export type AlbumType = z.infer<typeof AlbumSchema>;
export type ImageType = z.infer<typeof Image>;
