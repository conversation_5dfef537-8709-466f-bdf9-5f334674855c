import { Static, Type } from "@sinclair/typebox";

export const accreditationSchema = Type.Object({
  accreditationId: Type.Number(),
  name: Type.String(),
  canExpire: Type.Boolean(),
  requiresApproval: Type.Boolean(),
  logoFilename: Type.Optional(Type.String()),
  isDeleted: Type.Boolean(),
  visibleOnlyToAgents: Type.Optional(Type.Boolean()),
});

export type Accreditation = Static<typeof accreditationSchema>;
