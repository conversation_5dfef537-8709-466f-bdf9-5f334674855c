import { Static, Type } from "@sinclair/typebox";

export const AttachmentSchema = Type.Object({
  fileName: Type.String(),
  fileSize: Type.Number({ minimum: 1, maximum: 1024 * 1024 * 20 }),
  reference: Type.String(),
  uploadedDate: Type.Date(),
  mimeType: Type.String(),
});

export enum AccreditationApprovalType {
  NotRequired = "NOT_REQUIRED",
  Agent = "AGENT",
}

export enum AccreditationPlatform {
  Salesforce = "SALESFORCE",
  TradeApp = "TRADE_APP",
}

export enum AccreditationMutationType {
  Submitted = "SUBMITTED",
  Rejected = "REJECTED",
  Approved = "APPROVED",
  Deleted = "DELETED",
  ExpiryDateUpdated = "EXPIRY_DATE_UPDATED",
  RegistrationNumberUpdated = "REGISTRATION_NUMBER_UPDATED",
  Migrated = "MIGRATED",
  Restored = "RESTORED",
  ProofAdded = "PROOF_ADDED",
  ProofRemoved = "PROOF_REMOVED",
  WithFurtherAction = "WITH_FURTHER_ACTION",
}

export enum AccreditationStatusType {
  Pending = "PENDING",
  Approved = "APPROVED",
  Rejected = "REJECTED",
}

export const HistoryItemSchema = Type.Object({
  platform: Type.Enum(AccreditationPlatform),
  type: Type.Enum(AccreditationMutationType),
  reason: Type.Optional(Type.Union([Type.String(), Type.Null()])), //can be set to null by AdvisorUI
  updatedBy: Type.Optional(Type.Union([Type.String(), Type.Null()])), //salesforce user can be set to null by AdvisorUI
  updatedDate: Type.Date(),
});

export const personAccreditationsSchema = Type.Object({
  id: Type.Optional(Type.String()),
  accreditationId: Type.Number(),
  personId: Type.String(),
  companyId: Type.Number(),
  status: Type.Enum(AccreditationStatusType),
  expiryDate: Type.Optional(Type.Union([Type.Date(), Type.Null()])),
  approvedDate: Type.Optional(Type.Union([Type.Date(), Type.Null()])),
  modifiedDate: Type.Optional(Type.Date()),
  isDeleted: Type.Boolean({ default: false }),
  withFurtherAction: Type.Optional(Type.Boolean({ default: false })),
  registrationNumber: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  approvalType: Type.Optional(
    Type.Union([Type.Enum(AccreditationApprovalType), Type.Null()]),
  ),
  proof: Type.Optional(Type.Array(AttachmentSchema)),
  history: Type.Array(HistoryItemSchema),
});

export type PersonAccreditation = Static<typeof personAccreditationsSchema>;

export type Attachment = Static<typeof AttachmentSchema>;
export type HistoryItem = Static<typeof HistoryItemSchema>;
