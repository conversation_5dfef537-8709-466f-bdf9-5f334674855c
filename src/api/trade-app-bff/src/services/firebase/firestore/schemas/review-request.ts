import { Static, Type } from "@sinclair/typebox";

export const ReviewRequestDocSchema = Type.Object({
  id: Type.Optional(Type.String()),
  fullName: Type.String(),
  phone: Type.Optional(Type.String()), //core-review uses `mobile` instead
  email: Type.Optional(Type.String()),
  sent: Type.Boolean({ default: false }),
  dateCreated: Type.Optional(Type.Date()),
});

export type ReviewRequestDoc = Static<typeof ReviewRequestDocSchema>;
