import { Nullish } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

export enum IMAGE_STATUS {
  SUBMITTED = "SUBMITTED",
  LIVE = "LIVE",
  AI_FLAGGED = "AI_FLAGGED",
  HUMAN_FLAGGED = "HUMAN_FLAGGED",
  UPLOAD_FAILED = "UPLOAD_FAILED",
}

export const ImageRotation = Type.Union([
  Type.Null(),
  Type.Literal(0),
  Type.Literal(90),
  Type.Literal(180),
  Type.Literal(270),
]);

export const ImageMetadata = Type.Object({
  width: Nullish(Type.Number()),
  height: Nullish(Type.Number()),
  rotation: Type.Optional(ImageRotation),
});

export const CompanyLogo = Type.Object({
  imageId: Nullish(Type.String()),
  metadata: Nullish(ImageMetadata),
  status: Type.Optional(Type.Enum(IMAGE_STATUS)),
  url: Type.Optional(Type.String()),
});

export const CoverPhoto = Type.Object({
  imageId: Type.Optional(Type.String()),
  metadata: Nullish(ImageMetadata),
  status: Type.Optional(Type.Enum(IMAGE_STATUS)),
  url: Type.Optional(Type.String()),
});

const Details = Type.Object({
  logo: Nullish(CompanyLogo),
  heroBanner: Nullish(CoverPhoto),
});

const EssentialsTerms = Type.Object({
  accepted: Type.Boolean(),
  acceptedByCompanyUserId: Type.String(),
  acceptedByUserId: Type.String(),
});

const TermsAndConditions = Type.Object({
  essentialsTerms: Type.Optional(EssentialsTerms),
});

// This is not the full object, just a snippet
export const CompaniesSchema = Type.Object({
  id: Type.Number(),
  companyId: Nullish(Type.Number()),
  details: Type.Optional(Details),
  termsAndConditions: Type.Optional(TermsAndConditions),
});

export type CompaniesType = Static<typeof CompaniesSchema>;
