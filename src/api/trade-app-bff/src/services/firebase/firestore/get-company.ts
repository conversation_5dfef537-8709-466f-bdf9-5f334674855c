import { Value } from "@sinclair/typebox/value";
import { FieldPath } from "firebase-admin/firestore";

import { tradeApp } from "../app";
import { firestoreNumericIdCollectionConverter } from "./firestore-converter";
import { CompaniesSchema, CompaniesType } from "./schemas/companies";
import { CompanySchema, CompanyType } from "./schemas/company";

export const getCompanyDoc = async (
  companyId: string,
): Promise<CompanyType | undefined> => {
  const company = await tradeApp
    .firestore()
    .collection("company")
    .doc(companyId)
    .withConverter(firestoreNumericIdCollectionConverter())
    .get();

  if (company.exists) {
    return Value.Parse(CompanySchema, company.data());
  }

  return undefined;
};

export const getCompanyByIds = async (
  companyIds: number[],
): Promise<CompanyType[]> => {
  const companies = await tradeApp
    .firestore()
    .collection("company")
    .where(
      FieldPath.documentId(),
      "in",
      companyIds.map((c) => c.toString()),
    )
    .withConverter(firestoreNumericIdCollectionConverter())
    .get();

  return companies.docs.map((d) => Value.Parse(CompanySchema, d.data()));
};

export const getCompaniesDoc = async (companyId: string) => {
  const company = await tradeApp
    .firestore()
    .collection("companies")
    .doc(companyId)
    .withConverter(firestoreNumericIdCollectionConverter<CompaniesType>())
    .get();

  if (company.exists) {
    return Value.Parse(CompaniesSchema, company.data());
  }

  return undefined;
};

export const getCompaniesByIds = async (
  companyIds: number[],
): Promise<CompaniesType[]> => {
  const companies = await tradeApp
    .firestore()
    .collection("companies")
    .where(
      FieldPath.documentId(),
      "in",
      companyIds.map((c) => c.toString()),
    )
    .withConverter(firestoreNumericIdCollectionConverter<CompaniesType>())
    .get();

  return companies.docs.map((d) => Value.Parse(CompaniesSchema, d.data()));
};
