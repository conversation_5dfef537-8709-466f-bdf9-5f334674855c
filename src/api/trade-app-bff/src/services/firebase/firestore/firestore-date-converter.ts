import { firestore } from "firebase-admin";
import { Timestamp } from "firebase-admin/firestore";

export function convertTimestampsToDate(data: Timestamp): Date;
export function convertTimestampsToDate(
  data: firestore.DocumentData,
): firestore.DocumentData;

export function convertTimestampsToDate(
  data: firestore.DocumentData | firestore.Timestamp,
): firestore.DocumentData | Date {
  if (!data) {
    return data;
  }

  if (data instanceof firestore.Timestamp) {
    return data.toDate();
  }

  if (Array.isArray(data)) {
    return data.map((item) => convertTimestampsToDate(item));
  }

  if (typeof data === "object" && data !== null) {
    return Object.entries(data).reduce((result, [key, value]) => {
      result[key] = convertTimestampsToDate(value);
      return result;
    }, {} as firestore.DocumentData);
  }

  return data;
}

export function createDateFirestoreConverter<
  T extends firestore.DocumentData,
>(): firestore.FirestoreDataConverter<T> {
  return {
    toFirestore(modelObject: firestore.DocumentData): T {
      return Object.entries(modelObject).reduce((result, [key, value]) => {
        if (value instanceof Date) {
          result[key] = firestore.Timestamp.fromDate(value);
        } else {
          result[key] = value;
        }
        return result;
      }, {} as firestore.DocumentData) as T;
    },
    fromFirestore(snapshot: firestore.QueryDocumentSnapshot): T {
      const data = snapshot.data();
      return convertTimestampsToDate({
        ...data,
        id: snapshot.id,
      }) as T;
    },
  };
}
