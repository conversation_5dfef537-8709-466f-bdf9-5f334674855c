import dayjs from "dayjs";
import { firestore } from "firebase-admin";

import { tradeApp } from "../app";
import {
  COMPANIES_COLLECTION,
  REVIEW_REQUESTS_COLLECTION,
} from "./collections";
import { ReviewRequestDoc } from "./schemas/review-request";

import Filter = firestore.Filter;

export const createReviewRequest = async (
  companyId: number,
  reviewRequest: ReviewRequestDoc,
): Promise<ReviewRequestDoc> => {
  const companyRef = tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString());

  const company = await companyRef.get();
  if (!company.exists) {
    throw new Error(`Company with id ${companyId} does not exist.`);
  }
  const collectionRef = companyRef.collection(REVIEW_REQUESTS_COLLECTION);
  const id = collectionRef.doc().id;

  const document = {
    ...reviewRequest,
    id,
    dateCreated:
      reviewRequest.dateCreated ?? firestore.FieldValue.serverTimestamp(),
  };
  const res = await collectionRef.doc(id).set({ ...document });

  return {
    ...document,
    dateCreated: res.writeTime.toDate(),
  };
};

export const updateReviewRequest = async (
  companyId: number,
  reviewRequestId: string,
  data: Partial<ReviewRequestDoc>,
): Promise<void> => {
  await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(REVIEW_REQUESTS_COLLECTION)
    .doc(reviewRequestId)
    .set(data, { merge: true });
};

export const getReviewRequest = async (
  companyId: number,
  reviewRequestId: string,
): Promise<ReviewRequestDoc | undefined> => {
  const reviewRequest = await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(REVIEW_REQUESTS_COLLECTION)
    .doc(reviewRequestId)
    .get();

  const reviewRequestData = reviewRequest.data();
  return {
    ...reviewRequestData,
    dateCreated: reviewRequestData?.dateCreated?.toDate(),
  } as ReviewRequestDoc;
};

export type GetReviewRequestListResults = {
  data: ReviewRequestDoc[];
  lastReviewRequestId: string | undefined;
};

export const getReviewRequestList = async (
  companyId: number,
  size: number,
  lastReviewRequestId: string | undefined = undefined,
): Promise<GetReviewRequestListResults> => {
  const reviewRequestRef = tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(REVIEW_REQUESTS_COLLECTION);

  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

  let query = reviewRequestRef
    .where("dateCreated", ">=", threeMonthsAgo)
    .orderBy("dateCreated", "desc") // Ensure date sorting
    .limit(size);

  // If a lastDocId is provided, start the query after the last document
  if (lastReviewRequestId) {
    const lastDocSnapshot = await reviewRequestRef
      .doc(lastReviewRequestId)
      .get();
    if (lastDocSnapshot.exists) {
      query = query.startAfter(lastDocSnapshot);
    }
  }

  const snapshot = await query.get();
  const results = snapshot.docs.map((doc) => {
    const reviewRequestDocument = doc.data();
    return {
      ...reviewRequestDocument,
      dateCreated: reviewRequestDocument.dateCreated.toDate(),
    } as ReviewRequestDoc;
  });

  const lastDocId =
    results.length > 0 ? results[results.length - 1].id : undefined;

  return {
    data: results,
    lastReviewRequestId: lastDocId,
  };
};

const matchingReviewRequestFilter = ({
  phone,
  email,
}: Partial<ReviewRequestDoc>): Filter => {
  if (phone && email) {
    return Filter.or(
      Filter.where("email", "==", email),
      Filter.where("phone", "==", phone),
    );
  }

  if (phone) {
    return Filter.where("phone", "==", phone);
  }

  if (email) {
    return Filter.where("email", "==", email);
  }

  return {};
};

export const canCreateReviewRequest = async (
  companyId: number,
  reviewRequest: Partial<ReviewRequestDoc>,
): Promise<boolean> => {
  const query = await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(REVIEW_REQUESTS_COLLECTION)
    .where(matchingReviewRequestFilter(reviewRequest))
    .get();

  const sevenDaysAgo = dayjs().subtract(7, "day");

  const docs = query.docs
    .map((d) => {
      const reviewRequest = d.data();
      return {
        ...reviewRequest,
        dateCreated: reviewRequest.dateCreated.toDate(),
      } as ReviewRequestDoc;
    })
    .filter((doc) => {
      return dayjs(doc.dateCreated).isAfter(sevenDaysAgo);
    });

  return docs.length === 0;
};
