import { NotFoundError } from "@checkatrade/errors";
import { firestore } from "firebase-admin";

import { tradeApp } from "../app";
import {
  COMPANIES_COLLECTION,
  JOB_REVIEW_REQUESTS_COLLECTION,
} from "./collections";
import { JobReviewRequestDoc } from "./schemas/job-review-request";

export const createJobReviewRequest = async (
  companyId: number,
  jobReviewRequest: JobReviewRequestDoc,
): Promise<JobReviewRequestDoc> => {
  const companyRef = tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString());

  const company = await companyRef.get();

  if (!company.exists) {
    throw new NotFoundError(`Company with id ${companyId} does not exist.`);
  }

  const collectionRef = companyRef.collection(JOB_REVIEW_REQUESTS_COLLECTION);
  const id = collectionRef.doc().id;

  const document = {
    ...jobReviewRequest,
    id,
    dateCreated:
      jobReviewRequest.dateCreated ?? firestore.FieldValue.serverTimestamp(),
  };

  const writeResult = await collectionRef.doc(id).set({ ...document });

  return {
    ...document,
    dateCreated: writeResult.writeTime.toDate(),
  };
};

export const getJobReviewRequestByJobId = async (
  companyId: number,
  jobId: string,
): Promise<JobReviewRequestDoc | undefined> => {
  const query = await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(JOB_REVIEW_REQUESTS_COLLECTION)
    .where("jobId", "==", jobId)
    .get();

  const docs = query.docs.map((doc) => {
    const jobReviewRequest = doc.data();
    return {
      ...jobReviewRequest,
      dateCreated: jobReviewRequest.dateCreated?.toDate(),
    } as JobReviewRequestDoc;
  });

  if (docs.length > 1) {
    //TODO: add logger.error() and log companyId and jobId
    throw new Error(`Trade cannot have more than one jobReviewRequest per job`);
  }
  return docs?.length > 0 ? docs[0] : undefined;
};

export const updateJobReviewRequest = async (
  companyId: number,
  id: string,
  updates: Partial<JobReviewRequestDoc>,
): Promise<void> => {
  await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(JOB_REVIEW_REQUESTS_COLLECTION)
    .doc(id)
    .set(updates, { merge: true });
};
