// Creates a Firestore converter for a single document fetch.
// The result may be undefined.
export const firestoreDocumentConverter = <
  TDocWithStringId extends { id: string },
>(): FirebaseFirestore.FirestoreDataConverter<TDocWithStringId | undefined> => {
  return {
    toFirestore(): FirebaseFirestore.DocumentData {
      // we won't be storing data in Firestore
      throw new Error("toFirestore is not supported");
    },
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot) {
      if (!snapshot.exists) {
        return undefined;
      }

      return {
        ...(snapshot.data() as TDocWithStringId),
        id: snapshot.id,
      };
    },
  };
};

// Creates a Firestore converter for multiple documents query.
// Each document in the array response is always defined.
export const firestoreCollectionConverter = <
  TDocWithStringId extends { id: string },
>(): FirebaseFirestore.FirestoreDataConverter<
  NonNullable<TDocWithStringId>
> => {
  return {
    toFirestore(): FirebaseFirestore.DocumentData {
      // we won't be storing data in Firestore
      throw new Error("toFirestore is not supported");
    },
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot) {
      return {
        ...(snapshot.data() as TDocWithStringId),
        id: snapshot.id,
      };
    },
  };
};

// Creates a Firestore converter for a single document fetch where document ID is convertable to a number.
// The result may be undefined.
export const firestoreNumericIdDocumentConverter = <
  TDocWithNumericId extends { id: number },
>(): FirebaseFirestore.FirestoreDataConverter<
  TDocWithNumericId | undefined
> => {
  return {
    toFirestore(): FirebaseFirestore.DocumentData {
      // we won't be storing data in Firestore
      throw new Error("toFirestore is not supported");
    },
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot) {
      if (!snapshot.exists) {
        return undefined;
      }

      return {
        ...(snapshot.data() as TDocWithNumericId),
        id: Number(snapshot.id),
      };
    },
  };
};

// Creates a Firestore converter for a multiple documents query of records where document ID is convertable to a number.
// Each document in the array response is always defined.
export const firestoreNumericIdCollectionConverter = <
  TDocWithNumericId extends { id: number },
>(): FirebaseFirestore.FirestoreDataConverter<
  NonNullable<TDocWithNumericId>
> => {
  return {
    toFirestore(): FirebaseFirestore.DocumentData {
      // we won't be storing data in Firestore
      throw new Error("toFirestore is not supported");
    },
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot) {
      return {
        ...(snapshot.data() as TDocWithNumericId),
        id: Number(snapshot.id),
      };
    },
  };
};
