import { tradeApp } from "../app";
import { ALBUMS_COLLECTION, COMPANIES_COLLECTION } from "./collections";
import {
  // AlbumSchema,
  AlbumType,
} from "./schemas/albumsSchema";

export const getAlbumDocByCompanyId = async (
  companyId: number | string,
  albumId: string,
): Promise<AlbumType | undefined> => {
  const album = await tradeApp
    .firestore()
    .collection(COMPANIES_COLLECTION)
    .doc(companyId.toString())
    .collection(ALBUMS_COLLECTION)
    .doc(albumId)
    .get();

  if (album.exists) {
    // return AlbumSchema.parse(album.data());
    return album.data() as AlbumType;
  }

  return undefined;
};
