import { tradeApp } from "../app";
import { REVETTING_COLLECTION } from "./collections";
import { RevettingRecord } from "./schemas/revetting";

export const createRevettingEntry = async ({
  companyId,
  mitekReferenceId,
}: RevettingRecord): Promise<RevettingRecord> => {
  const collectionRef = tradeApp.firestore().collection(REVETTING_COLLECTION);

  const res = await collectionRef
    .doc(companyId.toString())
    .set({ mitekReferenceId });

  if (!res.writeTime) {
    throw new Error("Failed to write revetting entry");
  }

  return { companyId, mitekReferenceId };
};

export const updateRevettingEntry = async (
  companyId: number,
  updates: Partial<RevettingRecord>,
): Promise<void> => {
  await tradeApp
    .firestore()
    .collection(REVETTING_COLLECTION)
    .doc(companyId.toString())
    .set(updates, { merge: true });
};

export const getRevettingEntry = async (
  companyId: number,
): Promise<RevettingRecord | undefined> => {
  const revettingDoc = await tradeApp
    .firestore()
    .collection(REVETTING_COLLECTION)
    .doc(companyId.toString())
    .get();

  if (!revettingDoc.exists) {
    return undefined;
  }

  return revettingDoc.data() as RevettingRecord;
};
