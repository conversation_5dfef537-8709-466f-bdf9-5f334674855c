import { St<PERSON>, TSchema } from "@sinclair/typebox";
import { Value } from "@sinclair/typebox/value";

export function validateSchema<T extends TSchema>(
  schema: T,
  data: unknown,
):
  | {
      valid: false;
      value?: null;
      errors: string[];
    }
  | {
      valid: true;
      value: Static<T>;
      errors?: null;
    } {
  const valid = Value.Check(schema, data);
  if (!valid) {
    const errors = [...Value.Errors(schema, data)].map(
      (error) => `${error.path} - ${error.message}`,
    );
    return { valid: false, errors };
  }

  return { valid: true, value: data };
}
