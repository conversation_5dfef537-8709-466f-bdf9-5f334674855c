import { initializeApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";

import { config } from "../../../config";

const firebaseForAuth = initializeApp(
  {
    projectId: config.tradeFirebaseApp.projectId,
    // Set the service account id, so that the token is valid for the trade-exp gcp project
    // https://firebase.google.com/docs/auth/admin/create-custom-tokens#using_a_service_account_id
    serviceAccountId: config.tradeFirebaseApp.firebaseAuthServiceAccountId,
  },
  "trade-app-bff-auth",
);

export const createCustomToken = (
  id: string,
  additionalClaims?: Record<string, unknown>,
): Promise<string> => {
  return getAuth(firebaseForAuth).createCustomToken(id, additionalClaims);
};
