import { Logger } from "@checkatrade/logging";
import { type Member, MemberSchema } from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { tdsHttpClient } from "./tdsHttpClient";

export const getMember = async (
  memberId: string,
  logger: Logger,
): Promise<Member | undefined> => {
  try {
    const response = await tdsHttpClient.get(`/members/${memberId}`);

    return Value.Parse(MemberSchema, response.data);
  } catch (error) {
    logger.error(
      error,
      `Failed to get team member data for memberId: ${memberId}`,
    );
    throw error;
  }
};
