import { Static, TSchema } from "@sinclair/typebox";
import jwt from "jsonwebtoken";

export const createMagicLink = async <TRequest extends TSchema>(
  tokenData: Static<TRequest>,
  baseUrl: string,
  tokenSigningKey: string,
  tokenExpiryHours: number,
  issuer: string = "checkatrade-trade-bff",
  audience?: string,
): Promise<string> => {
  const payload = tokenData as object;

  // Encrypt JWT token (default of HMAC SHA-256 encryption)
  const token = jwt.sign(payload, tokenSigningKey, {
    expiresIn: `${tokenExpiryHours}h`,
    issuer,
    audience,
  });

  return `${baseUrl}?token=${token}`;
};
