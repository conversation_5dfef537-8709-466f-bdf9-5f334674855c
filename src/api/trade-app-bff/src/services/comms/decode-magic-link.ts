import { TSchema } from "@sinclair/typebox";
import { Value } from "@sinclair/typebox/value";
import jwt from "jsonwebtoken";

export const decodeMagicLink = async <TResponse extends TSchema>(
  schema: TResponse,
  token: string,
  tokenSigningKey: string,
  issuer: string = "checkatrade-trade-bff",
  audience?: string,
): Promise<TResponse> => {
  const payload = jwt.verify(token, tokenSigningKey, {
    issuer,
    audience,
  });

  return Value.Parse(schema, payload);
};
