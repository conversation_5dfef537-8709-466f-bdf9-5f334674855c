import {
  CreateMitekRequestSchema,
  CreateMitekResponseSchema,
  TeamPerson,
  createMitekRequestResponseSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { config } from "./config";
import { mitekHttpClient } from "./mitek-http-client";

export const createMitekRequest = async (
  member: TeamPerson,
): Promise<CreateMitekResponseSchema> => {
  try {
    const phone = member.mobilePhone || member.phone;

    if (!phone) {
      throw new Error("Phone number is required for Mitek request");
    }

    const requestBody: CreateMitekRequestSchema = {
      name: member.fullName,
      phone: phone ? `+44${phone}` : undefined,
      email: member.email ?? undefined,
      address: {
        address1: member.address?.line1 ?? undefined,
        address2: member.address?.line2 ?? undefined,
        town: member.address?.city ?? undefined,
        region: member.address?.county ?? undefined,
        postcode: member.address?.postalCode ?? undefined,
      },
      scope: "selfie,documents(passport,driving,address)",
      environment: config.mitekEnvironment,
    };

    const mitekResponse = await mitekHttpClient.post(
      "/1.0/request/create",
      requestBody,
    );

    return Value.Parse(createMitekRequestResponseSchema, mitekResponse.data);
  } catch (error) {
    console.error("Error sending Mitek request:", error);
    throw error;
  }
};
