import { Logger } from "@checkatrade/logging";
import {
  MitekRequestStatusResponse,
  mitekRequestStatusResponseSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

import { mitekHttpClient } from "./mitek-http-client";

export const getRequestStatus = async (
  reference: string,
  logger: Logger,
): Promise<MitekRequestStatusResponse> => {
  try {
    const mitekResponse = await mitekHttpClient.get(
      `/1.0/request/status/${reference}`,
    );

    return Value.Parse(mitekRequestStatusResponseSchema, mitekResponse.data);
  } catch (error) {
    logger.error(error, "Failed to get mitek request status");
    throw error;
  }
};
