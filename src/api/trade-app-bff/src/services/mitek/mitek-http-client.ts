import { type AxiosInstance, axios } from "@checkatrade/axios";

import { config } from "./config";

// Create Base64 encoded credentials string (clientId:secret)
const credentials = Buffer.from(
  `${config.mitekClientId}:${config.mitekClientSecret}`,
).toString("base64");

export const mitekHttpClient: AxiosInstance = axios.getInstance({
  baseURL: config.mitekHost,
  timeout: 5000,
  headers: {
    "Authorization": `Basic ${credentials}`,
    "Content-Type": "application/json",
  },
});
