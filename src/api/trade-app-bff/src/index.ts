// datadog has to be the first import in the application
import "@checkatrade/datadog";

import { server } from "./server";

const stop = async (signal: NodeJS.Signals) => {
  server.logger.info(signal, "Shutting down");

  try {
    await server.stop();
  } catch (error) {
    server.logger.error(error, "Close failed");
    process.exitCode = 1;
  }
};

process.on("unhandledRejection", (reason) => {
  server.logger.error(reason, "Unhandled rejection");
});

process.on("uncaughtException", (error) => {
  server.logger.error(error, "Unhandled exception");
});

process.on("SIGINT", stop);
process.on("SIGTERM", stop);

server.start().catch((error) => {
  server.logger.error(error, "Startup failed");
  process.exitCode = 1;
});
