import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { NotFoundError, apiErrorSchema } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  type GetMemberInfoResponse,
  GetMemberInfoResponseSchema,
  Member,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import {
  getCompaniesDoc,
  getCompanyDoc,
} from "../../services/firebase/firestore/get-company";
import { team } from "../../services/team";
import { tradeData } from "../../services/trade-data-service";
import { isAccountOwner as checkAccountOwner } from "../../utilities/member";

export const getMemberInfo: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: GetMemberInfoResponse | Static<typeof apiErrorSchema>;
}> = {
  method: "GET",
  url: "/member-info",
  schema: {
    summary: "Fetch member info",
    description: "Returns the member information for the authenticated user.",
    operationId: "getMemberInfo",
    tags: ["MemberInfo"],
    headers: companyIdHeader,
    response: {
      200: GetMemberInfoResponseSchema,
      404: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const authToken = authTradeCapi(req);

    const companyData = await getCompanyDoc(companyId.toString());
    const companiesData = await getCompaniesDoc(companyId.toString()).catch(
      (error) => logger.warn(error, "Error fetching companies collection data"),
    );

    let memberId: string | null = null;
    let memberData: Member | null = null;

    try {
      // Only fetch TDS data if we have no firestore data or the company is an Essentials Member
      if (
        !companyData ||
        companyData?.membershipLevel === "Essentials Member"
      ) {
        memberId =
          (await team.getTeamMemberId(companyId, logger))?.memberId ?? null;
        // only fetch if firestore data isn't available
        memberData =
          memberId && !companyData ?
            ((await tradeData.getMember(memberId, logger)) ?? null)
          : null;
      }
    } catch (error) {
      logger.error(error, "Error fetching member data from TDS");
    }

    if (!companyData && !memberData) {
      throw new NotFoundError("No member data found");
    }

    return res.send({
      data: {
        memberId,
        companyId,
        name: companyData?.name ?? memberData?.name,
        isMembershipFlexible:
          companyData?.membershipFlexible ??
          memberData?.isMembershipFlexible ??
          false,
        joinedDate: memberData?.joinedDate,
        legacyProductType:
          companyData?.membershipType ?? memberData?.legacyProductType,
        membershipType:
          companyData?.membershipLevel ?? memberData?.membershipType,
        traderId: companyData?.tradeId ?? memberData?.traderId,
        uniqueName: companyData?.uniqueName ?? memberData?.uniqueName,
        accountBalance: companyData?.accountBalance ?? 0,
        hasAcceptedEssentialsTerms:
          Boolean(memberData?.acceptedEssentialsTermsDate) ||
          companiesData?.termsAndConditions?.essentialsTerms?.accepted === true,
        isAccountOwner:
          companyData?.contacts ?
            checkAccountOwner(authToken.email, companyData.contacts)
          : false,
      },
    });
  },
};
