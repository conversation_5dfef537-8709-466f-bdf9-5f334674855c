import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  Account,
  CompanyIdHeader,
  accountSchema,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

import { finance } from "../../services/finance";

export const getZuoraAccount: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Reply: Account;
}> = {
  method: "GET",
  url: "/zuora/account",
  schema: {
    summary: "Get Zuora account information",
    description: "Fetches Zuora account information for a company",
    operationId: "getZuoraAccount",
    tags: ["Finance"],
    headers: companyIdHeader,
    response: {
      200: accountSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);

    const account = await finance.getAccount(companyId, logger);
    return res.send(account);
  },
};
