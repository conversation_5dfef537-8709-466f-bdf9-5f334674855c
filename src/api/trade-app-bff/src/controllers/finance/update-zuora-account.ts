import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  Account,
  CompanyIdHeader,
  UpdateAccountBody,
  UpdateAccountParams,
  accountSchema,
  companyIdHeader,
  updateAccountBodySchema,
  updateAccountParamsSchema,
} from "@checkatrade/trade-bff-types";

import { finance } from "../../services/finance";

export const updateZuoraAccount: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: UpdateAccountParams;
  Body: UpdateAccountBody;
  Reply: Account;
}> = {
  method: "PATCH",
  url: "/zuora/account/:accountId",
  schema: {
    summary: "update Zuora account information with new defaultPaymentMethodId",
    description: "Updates Zuora account information for an account",
    operationId: "patchZuoraAccount",
    tags: ["Finance"],
    headers: companyIdHeader,
    params: updateAccountParamsSchema,
    body: updateAccountBodySchema,
    response: {
      200: accountSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { accountId } = req.params;
    const { paymentMethodId } = req.body;
    await authTrade(req);

    const account = await finance.updateAccount(
      accountId,
      paymentMethodId,
      logger,
    );

    return res.send(account);
  },
};
