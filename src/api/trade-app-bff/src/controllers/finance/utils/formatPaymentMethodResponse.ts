import { PaymentMethod } from "@checkatrade/trade-bff-types";

export const formatPaymentMethodResponse = (
  paymentMethod: PaymentMethod,
): PaymentMethod => ({
  ...paymentMethod,
  billing_details:
    paymentMethod?.billing_details ?
      {
        ...paymentMethod.billing_details,
        name:
          paymentMethod.billing_details.name ?
            paymentMethod.billing_details.name.slice(0, 4)
          : undefined,
      }
    : undefined,
});
