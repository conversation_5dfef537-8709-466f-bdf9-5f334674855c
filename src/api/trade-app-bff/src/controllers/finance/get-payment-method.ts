import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  PaymentMethod,
  PaymentMethodParams,
  companyIdHeader,
  paymentMethodParamsSchema,
  paymentMethodSchema,
} from "@checkatrade/trade-bff-types";

import { finance } from "../../services/finance";
import { formatPaymentMethodResponse } from "./utils/formatPaymentMethodResponse";

export const getPaymentMethod: FastifyRouteOptions<{
  Params: PaymentMethodParams;
  Header: CompanyIdHeader;
  Reply: PaymentMethod;
}> = {
  method: "GET",
  url: "/zuora/payment-methods/:paymentMethodId",
  schema: {
    summary: "Get payment method information from Zuora",
    description: "Fetches payment method information for an id from Zuora",
    operationId: "getZuoraPaymentMethod",
    tags: ["Finance"],
    headers: companyIdHeader,
    params: paymentMethodParamsSchema,
    response: {
      200: paymentMethodSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { paymentMethodId } = req.params;
    await authTrade(req);
    let paymentMethod: PaymentMethod | undefined;

    try {
      paymentMethod = await finance.getPaymentMethod(paymentMethodId);
    } catch (error) {
      logger.error(
        {
          error,
          paymentMethodId,
        },
        "Error getting payment method",
      );
      throw error;
    }

    if (paymentMethod === undefined) {
      throw new NotFoundError("Payment method not found");
    }

    const slicedPaymentMethod = formatPaymentMethodResponse(paymentMethod);

    return res.send(slicedPaymentMethod);
  },
};
