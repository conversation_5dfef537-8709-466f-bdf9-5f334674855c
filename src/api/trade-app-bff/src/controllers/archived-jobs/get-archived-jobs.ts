import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export const getArchivedJobs: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Querystring: Static<typeof jobsSDK.schemas.trade.getArchivedJobs.query>;
  Reply: Static<typeof jobsSDK.schemas.trade.getArchivedJobs.response>;
}> = {
  method: "GET",
  url: "/archived-jobs",
  schema: {
    summary: "Get archived jobs",
    description: "Get paginated consumer archived jobs",
    operationId: "getArchivedJobs",
    tags: ["Job"],
    headers: companyIdHeader,
    querystring: jobsSDK.schemas.trade.getArchivedJobs.query,
    response: {
      200: jobsSDK.schemas.trade.getArchivedJobs.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { page, size, filter } = req.query;

    const jobs = await jobsSDK
      .trade(token, companyId)
      .getArchivedJobs({ page, size, filter });

    return res.send(jobs);
  },
};
