import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export const getArchivedJob: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof jobsSDK.schemas.trade.getArchivedJob.params>;
  Reply: Static<typeof jobsSDK.schemas.trade.getArchivedJob.response>;
}> = {
  method: "GET",
  url: "/archived-jobs/:jobId",
  schema: {
    summary: "Get archived job",
    description: "Get archived job by id",
    operationId: "getArchivedJob",
    tags: ["Job"],
    headers: companyIdHeader,
    params: jobsSDK.schemas.trade.getArchivedJob.params,
    response: {
      200: jobsSDK.schemas.trade.getArchivedJob.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { jobId } = req.params;

    const job = await jobsSDK.trade(token, companyId).getArchivedJob(jobId);

    return res.send(job);
  },
};
