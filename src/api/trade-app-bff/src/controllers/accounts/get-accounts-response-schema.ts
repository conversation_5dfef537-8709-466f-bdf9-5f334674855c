import { Type } from "@sinclair/typebox";

export const getAccountsResponseSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    companyId: Type.Number(),
    traderId: Type.Optional(Type.Integer()),
    companyName: Type.Optional(Type.Union([Type.Null(), Type.String()])),
    companyLogo: Type.Optional(Type.Union([Type.Null(), Type.String()])),
    vettingStatus: Type.Number(),
    salesforceVettingStatus: Type.String(),
  }),
);
