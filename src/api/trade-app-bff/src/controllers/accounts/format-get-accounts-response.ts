import { AuthTradeCapiAccount } from "@checkatrade/auth-trade";
import { Static } from "@sinclair/typebox";

import type { CompaniesType } from "../../services/firebase/firestore/schemas/companies";
import type { CompanyType } from "../../services/firebase/firestore/schemas/company";
import { getAccountsResponseSchema } from "./get-accounts-response-schema";

export const formatGetAccountsResponse = (
  accounts: AuthTradeCapiAccount[],
  companys: CompanyType[],
  companies: CompaniesType[],
): Static<typeof getAccountsResponseSchema> => {
  const companysMap = companys.reduce(
    (acc, item) => {
      acc[item.id] = item;
      return acc;
    },
    {} as Record<number, CompanyType | undefined>,
  );

  const companiesMap = companies.reduce(
    (acc, item) => {
      acc[item.id] = item;
      return acc;
    },
    {} as Record<number, CompaniesType | undefined>,
  );

  return accounts.map((acc) => {
    const company = companysMap[acc.companyId];
    const companies = companiesMap[acc.companyId];

    return {
      id: acc.id,
      companyId: acc.companyId,
      traderId: company?.tradeId,
      companyName: company?.name ?? company?.uniqueName,
      companyLogo: companies?.details?.logo?.url,
      vettingStatus: acc.vettingStatus,
      salesforceVettingStatus: acc.salesforceVettingStatus,
    };
  });
};
