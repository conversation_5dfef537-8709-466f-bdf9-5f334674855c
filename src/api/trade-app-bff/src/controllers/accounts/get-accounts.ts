import { authTradeCapi } from "@checkatrade/auth-trade";
import { apiErrorSchema } from "@checkatrade/errors";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { Static } from "@sinclair/typebox";

import {
  getCompaniesByIds,
  getCompanyByIds,
} from "../../services/firebase/firestore/get-company";
import { formatGetAccountsResponse } from "./format-get-accounts-response";
import { getAccountsResponseSchema } from "./get-accounts-response-schema";

export const getAccounts: FastifyRouteOptions<{
  Reply:
    | Static<typeof getAccountsResponseSchema>
    | Static<typeof apiErrorSchema>;
}> = {
  method: "GET",
  url: "/accounts",
  schema: {
    summary: "Fetch available trade accounts",
    description:
      "Returns all available accounts as pairs of a company and a trade to switched between",
    operationId: "getAccounts",
    tags: ["Accounts"],
    response: {
      200: getAccountsResponseSchema,
      401: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const authToken = authTradeCapi(req);

    const companyIds = authToken.accounts.map((acc) => acc.companyId);
    const companys = await getCompanyByIds(companyIds);
    const companies = await getCompaniesByIds(companyIds);

    const result = formatGetAccountsResponse(
      authToken.accounts,
      companys,
      companies,
    );

    return res.send(result);
  },
};
