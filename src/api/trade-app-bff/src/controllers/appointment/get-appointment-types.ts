import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export const getAppointmentTypes: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Reply: Static<
    typeof schedulingSDK.schemas.api.trade.getAppointmentTypes.response
  >;
}> = {
  method: "GET",
  url: "/appointment/types",
  schema: {
    summary: "Get available appointment types",
    description: "Provides list of all available appointment types",
    operationId: "getAppointmentTypes",
    tags: ["Appointments"],
    headers: companyIdHeader,
    response: {
      200: schedulingSDK.schemas.api.trade.getAppointmentTypes.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const appointmentTypes = await schedulingSDK
      .trade(token, companyId)
      .types.get();

    return res.status(200).send(appointmentTypes);
  },
};
