import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export const getTradeCancellationReasons: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Reply: Static<
    typeof schedulingSDK.schemas.api.trade.getCancellationReasons.response
  >;
}> = {
  method: "GET",
  url: "/appointment/cancellation-reasons",
  schema: {
    summary: "Get appointments cancellation reasons",
    description: "Get trade appointments cancellation reasons list",
    operationId: "getTradeCancellationReasons",
    tags: ["Appointments"],
    headers: companyIdHeader,
    response: {
      200: schedulingSDK.schemas.api.consumer.getCancellationReasons.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const cancellationReasons = await schedulingSDK
      .trade(token, companyId)
      .getCancellationReasons();

    return res.send(cancellationReasons);
  },
};
