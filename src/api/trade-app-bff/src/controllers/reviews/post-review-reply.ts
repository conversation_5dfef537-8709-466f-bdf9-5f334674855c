import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Type } from "@sinclair/typebox";

export const postReviewReply: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ReviewTypes.Api.Public.CreateReply.Params;
  Body: Omit<ReviewTypes.Api.Public.CreateReply.Body, "companyId">;
  Reply: ReviewTypes.Api.Public.CreateReply.Response;
}> = {
  method: "POST",
  url: "/reviews/:reviewId/reply",
  schema: {
    summary: "Post review reply",
    description: "Create a review reply",
    operationId: "postReviewReply",
    tags: ["Reviews"],
    headers: companyIdHeader,
    params: reviewSDK.schemas.review.api.public.review.createReply.params,
    body: Type.Omit(
      reviewSDK.schemas.review.api.public.review.createReply.body,
      ["companyId"],
    ),
    response: {
      201: reviewSDK.schemas.review.api.public.review.createReply.response,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const { reply } = req.body;
    const { reviewId } = req.params;
    const reviewReply = await reviewSDK.public().postReviewReply(reviewId, {
      reply,
      companyId,
    });

    return res.status(201).send(reviewReply);
  },
};
