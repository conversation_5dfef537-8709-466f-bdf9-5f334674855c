import { authTrade } from "@checkatrade/auth-trade";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

const reviewer = Type.Object({
  firstName: Type.String(),
  lastName: Type.String(),
  postcode: Type.String(),
});

const reviewListDataUpdatedSchema = Type.Composite([
  reviewSDK.schemas.review.api.public.review.list.response.properties.data
    .items,
  Type.Object({ reviewer }),
]);

const getReviewsResponseSchema = Type.Object({
  ...reviewSDK.schemas.review.api.public.review.list.response.properties,
  data: Type.Array(reviewListDataUpdatedSchema),
});

export const getReviewsList: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Omit<ReviewTypes.Api.Public.List.Query, "companyId">;
  Reply: Static<typeof getReviewsResponseSchema>;
}> = {
  method: "GET",
  url: "/reviews",
  schema: {
    summary: "Gets reviews list",
    description: "Fetches a list of reviews for a companyId",
    operationId: "getReviewsList",
    tags: ["Reviews"],
    headers: companyIdHeader,
    querystring: Type.Omit(
      reviewSDK.schemas.review.api.public.review.list.querystring,
      ["companyId"],
    ),
    response: {
      201: getReviewsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);

    const reviewsListResponse = await reviewSDK.public().getReviews({
      ...req.query,
      companyId,
      contextCompanyId: companyId,
    });

    // TODO: @andrew - refactor when adding tests
    const reviewerAddedData = await Promise.all(
      reviewsListResponse.data.map(async (review) => {
        if (review.consumerId) {
          try {
            const consumerInfo = await consumerSDK
              .service()
              .getConsumer(review.consumerId);

            return {
              ...review,
              reviewer: {
                firstName: consumerInfo.firstName ?? "",
                lastName: consumerInfo.lastName ?? "",
                postcode: consumerInfo.primaryAddress?.postcode ?? "",
              },
            };
          } catch (error) {
            logger.info(
              `Failed to get consumer info: ${(error as Error)?.message}`,
            );
          }
        }

        if (review.reviewer) {
          return {
            ...review,
            reviewer: {
              firstName: review.reviewer.firstName ?? "",
              lastName: review.reviewer.lastName ?? "",
              postcode: review.reviewer.postcode ?? "",
            },
          };
        }

        return {
          ...review,
          reviewer: {
            firstName: "",
            lastName: "",
            postcode: "",
          },
        };
      }),
    );

    return res.status(200).send({
      ...reviewsListResponse,
      data: reviewerAddedData,
    });
  },
};
