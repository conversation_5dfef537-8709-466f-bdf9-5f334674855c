import { authTrade } from "@checkatrade/auth-trade";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  OpportunityStatus,
  TradeJobListResponse,
  TradeJobResponse,
  TradeJobs,
  jobsSDK,
} from "@checkatrade/jobs-sdk";
import { schemas } from "@checkatrade/schemas";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { getConsumerMapByIds } from "../../services/consumer";
import { jobReviewRequestStatus } from "../../services/reviews";
import {
  JobReviewRequestResponse,
  JobReviewRequestStatus,
  getJobReviewRequestListResponse,
} from "./review-request.schema";

const reviewableFilter: Exclude<
  Parameters<TradeJobs["getJobs"]>[0],
  undefined
> = {
  "filter[in]": [
    OpportunityStatus.REQUEST_ACCEPTED,
    OpportunityStatus.SCHEDULED,
    OpportunityStatus.SCHEDULE_CANCELLED,
    OpportunityStatus.V2_INTERESTED,
    OpportunityStatus.COMPLETED,
    OpportunityStatus.V2_WORK_FINISHED,
  ],
};

type ConsumerResponse = Static<
  typeof consumerSDK.schemas.api.consumers.getConsumer.response
>;

export const formatJobReviewRequestResponse = (
  job: TradeJobResponse | TradeJobListResponse,
  consumer: Partial<ConsumerResponse> & { id: ConsumerResponse["id"] },
  reviewRequestStatus: JobReviewRequestStatus,
): JobReviewRequestResponse => {
  return {
    id: job.id,
    channelId: job.opportunityId,
    postcode: job.postcode,
    consumer: {
      id: consumer.id,
      firstName: consumer.firstName,
      lastName: consumer.lastName,
    },
    reviewRequestStatus,
  };
};

export const getJobReviewRequestList: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof schemas.pagination.query>;
  Reply: Static<typeof getJobReviewRequestListResponse>;
}> = {
  method: "GET",
  url: "/reviews/job-requests",
  schema: {
    summary: "Get reviewable jobs with review request status",
    description: "Get paginated reviewable jobs with review request status",
    operationId: "getJobReviewRequestList",
    tags: ["Reviews"],
    querystring: schemas.pagination.query,
    headers: companyIdHeader,
    response: {
      200: getJobReviewRequestListResponse,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { page, size } = req.query;
    const { log: logger } = req;

    const jobsResponse = await jobsSDK
      .trade(token, companyId)
      .getJobs({ page, size, ...reviewableFilter });

    const jobs = jobsResponse.data;
    if (jobs.length === 0) {
      return res.send({
        data: [],
        page: jobsResponse.page,
        size: jobsResponse.size,
        total: jobsResponse.total,
      });
    }

    const consumerIds = jobs.map((job) => job.consumerId);
    const consumerMap = await getConsumerMapByIds(consumerIds, token);

    const reviewRequestStatuses: JobReviewRequestStatus[] = [];
    for (const job of jobs) {
      // TODO: setup batch getter for review request status
      const status = await jobReviewRequestStatus.get({
        companyId,
        jobId: job.id,
        opportunityId: job.opportunityId,
        logger,
      });
      reviewRequestStatuses.push(status);
    }

    const data = jobs.map((job, index) => {
      const consumer = consumerMap[job.consumerId];
      const reviewRequestStatus = reviewRequestStatuses[index];

      return formatJobReviewRequestResponse(
        job,
        consumer ?? { id: job.consumerId },
        reviewRequestStatus,
      );
    });

    return res.send({
      data,
      page: jobsResponse.page,
      size: jobsResponse.size,
      total: jobsResponse.total,
    });
  },
};
