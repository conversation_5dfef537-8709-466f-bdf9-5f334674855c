import dayjs from "dayjs";

import { GetReviewRequestListResults } from "../../../services/firebase/firestore/review-request";
import { ReviewRequestDoc } from "../../../services/firebase/firestore/schemas/review-request";
import { ManualReviewRequestListResponse } from "../review-request.schema";

export const canSendReminder = ({
  dateCreated,
  sent,
}: ReviewRequestDoc): boolean => {
  if (sent) {
    return false;
  }
  return dayjs(new Date()).diff(dayjs(dateCreated), "hours") > 24;
};

export const mapReviewRequestDocToResponse = (
  reviewRequest: ReviewRequestDoc,
) => {
  const { phone, fullName, dateCreated, ...rest } = reviewRequest;
  return {
    ...rest,
    consumerName: fullName,
    mobile: phone,
    dateCreated: dateCreated!.toISOString(),
    canSendReminder: canSendReminder(reviewRequest),
  };
};

export const mapReviewRequestDocListToResponse = (
  reviewRequestsDocs: GetReviewRequestListResults,
): ManualReviewRequestListResponse => ({
  data: reviewRequestsDocs.data.map((reviewRequest: ReviewRequestDoc) =>
    mapReviewRequestDocToResponse(reviewRequest),
  ),
  lastReviewRequestId: reviewRequestsDocs.lastReviewRequestId,
});
