import { consumerSDK } from "@checkatrade/consumer-sdk";
import { schemas } from "@checkatrade/schemas";
import { Static, Type } from "@sinclair/typebox";

export const ReviewRequestResponseSchema = Type.Object({
  id: Type.Optional(Type.String()),
  consumerName: Type.String(),
  mobile: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  sent: Type.Boolean({ default: false }),
  dateCreated: Type.String({ format: "date-time" }),
  canSendReminder: Type.Boolean({ default: false }),
});

export type ReviewRequestResponse = Static<typeof ReviewRequestResponseSchema>;

export const ManualReviewRequestListResponseSchema = Type.Object({
  data: Type.Array(
    Type.Intersect([
      ReviewRequestResponseSchema,
      Type.Object({
        canSendReminder: Type.Boolean(),
      }),
    ]),
  ),
  lastReviewRequestId: Type.Optional(Type.String()),
});

export type ManualReviewRequestListResponse = Static<
  typeof ManualReviewRequestListResponseSchema
>;

export const jobReviewRequestStatusSchema = Type.Object({
  canRequestReview: Type.Boolean(),
  hasSentReviewRequest: Type.Boolean(),
  hasSentReviewRequestReminder: Type.Boolean(),
});

export const jobReviewRequestResponseSchema = Type.Object({
  id: Type.String(),
  channelId: Type.String(),
  consumer: Type.Pick(consumerSDK.schemas.api.consumers.getConsumer.response, [
    "id",
    "firstName",
    "lastName",
  ]),
  postcode: Type.Optional(Type.String()),
  reviewRequestStatus: jobReviewRequestStatusSchema,
});

export type JobReviewRequestResponse = Static<
  typeof jobReviewRequestResponseSchema
>;

export type JobReviewRequestStatus = Static<
  typeof jobReviewRequestStatusSchema
>;

export const getJobReviewRequestListResponse = Type.Composite([
  schemas.pagination.response,
  Type.Object(
    {
      data: Type.Array(jobReviewRequestResponseSchema),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);
