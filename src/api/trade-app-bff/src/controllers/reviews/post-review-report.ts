import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { ForbiddenError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Type } from "@sinclair/typebox";

import { isActiveMember } from "../../lib/api-common";

export const postReviewReport: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ReviewTypes.Api.Public.Report.Params;
  Body: Omit<ReviewTypes.Api.Public.Report.Body, "companyId">;
  Reply: ReviewTypes.Api.Public.Report.Response;
}> = {
  method: "POST",
  url: "/reviews/:reviewId/report",
  schema: {
    summary: "Post review report",
    description: "Create a review report",
    operationId: "postReviewReport",
    tags: ["Reviews"],
    headers: companyIdHeader,
    params: reviewSDK.schemas.review.api.public.review.report.params,
    body: Type.Omit(reviewSDK.schemas.review.api.public.review.report.body, [
      "companyId",
    ]),
    response: {
      201: reviewSDK.schemas.review.api.public.review.report.response,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { accounts } = authTradeCapi(req);

    if (!isActiveMember(companyId, accounts)) {
      throw new ForbiddenError(`Company '${companyId}' is a non-member`);
    }

    const { reviewId } = req.params;
    const reviewReport = await reviewSDK.public().postReportReview(reviewId, {
      ...req.body,
      companyId,
    });

    return res.status(201).send(reviewReport);
  },
};
