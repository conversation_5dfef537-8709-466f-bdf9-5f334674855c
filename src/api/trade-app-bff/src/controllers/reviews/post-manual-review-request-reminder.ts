import { authTrade } from "@checkatrade/auth-trade";
import { ConflictError, NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { getCompanyDoc } from "../../services/firebase/firestore/get-company";
import {
  getReviewRequest,
  updateReviewRequest,
} from "../../services/firebase/firestore/review-request";
import {
  canSendReminder,
  mapReviewRequestDocToResponse,
} from "./mappers/map-review-request-doc-to-response";
import { ReviewRequestResponseSchema } from "./review-request.schema";

export const postManualReviewRequestReminderParamsSchema = Type.Object({
  id: Type.String(),
});

export const postReviewRequestManualReminder: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<typeof postManualReviewRequestReminderParamsSchema>;
}> = {
  method: "POST",
  url: "/manual-review-requests/:id/reminder",

  schema: {
    summary: "Post manual review request",
    description: "Post a manual review request reminder",
    operationId: "postManualReviewRequestReminder",
    tags: ["Reviews"],
    headers: companyIdHeader,
    params: postManualReviewRequestReminderParamsSchema,
    response: {
      200: ReviewRequestResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { id } = req.params;

    const reviewRequest = await getReviewRequest(companyId, id);

    if (!reviewRequest) {
      throw new NotFoundError("ReviewRequest not found");
    }

    if (reviewRequest.sent) {
      throw new ConflictError("Reminder already sent");
    }

    if (!canSendReminder(reviewRequest)) {
      throw new ConflictError("Reminder cannot be sent util 24h has passed");
    }

    const company = await getCompanyDoc(companyId.toString());
    if (!company?.name || !company?.uniqueName) {
      throw new NotFoundError("Company not found");
    }

    const payload: ReviewTypes.Api.Public.ManualReviewRequest.Create.Body = {
      companyId,
      companyName: company.name,
      companyUniqueName: company.uniqueName,
      consumerName: reviewRequest.fullName,
      mobile: reviewRequest.phone ?? undefined,
      email: reviewRequest.email ?? undefined,
    };

    await reviewSDK.public().postManualReviewRequest(payload);

    await updateReviewRequest(companyId, id, {
      sent: true,
    });

    return res.status(200).send(
      mapReviewRequestDocToResponse({
        ...reviewRequest,
        sent: true,
      }),
    );
  },
};
