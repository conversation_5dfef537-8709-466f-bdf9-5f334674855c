import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { metricsSDK } from "@checkatrade/metrics-sdk";
import {
  CompanyIdHeader,
  ExternalMetricResponse,
  companyIdHeader,
  externalMetricResponseSchema,
} from "@checkatrade/trade-bff-types";
import { Value } from "@sinclair/typebox/value";

export const getReviewsMetrics: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: ExternalMetricResponse;
}> = {
  method: "GET",
  url: "/reviews/metrics",
  schema: {
    summary: "Get review stat summary",
    description: "Fetches a review stat summary",
    operationId: "getReviewsMetrics",
    tags: ["Reviews"],
    headers: companyIdHeader,
    response: {
      201: externalMetricResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const metrics = await metricsSDK.metrics().getExternal(companyId);

    const strippedMetricsResponse = Value.Parse(
      externalMetricResponseSchema,
      metrics,
    );

    return res.status(200).send(strippedMetricsResponse);
  },
};
