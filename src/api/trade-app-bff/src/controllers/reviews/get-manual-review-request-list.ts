import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { getReviewRequestList } from "../../services/firebase/firestore/review-request";
import { mapReviewRequestDocListToResponse } from "./mappers/map-review-request-doc-to-response";
import { ManualReviewRequestListResponseSchema } from "./review-request.schema";

const queryStringSchema = Type.Object({
  size: Type.Number({ minimum: 1, maximum: 50 }),
  lastReviewRequestId: Type.Optional(Type.String()),
});

type QueryString = Static<typeof queryStringSchema>;

export const getManualReviewRequestList: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: QueryString;
}> = {
  method: "GET",
  url: "/manual-review-requests",
  schema: {
    summary: "Gets manual review requests list",
    description: "Fetches a list of review requests for companyId",
    operationId: "getManualReviewRequestList",
    tags: ["Reviews"],
    headers: companyIdHeader,
    querystring: queryStringSchema,
    response: {
      200: ManualReviewRequestListResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { size, lastReviewRequestId } = req.query;

    const manualReviews = await getReviewRequestList(
      companyId,
      size,
      lastReviewRequestId,
    );
    const response = mapReviewRequestDocListToResponse(manualReviews);

    return res.status(200).send(response);
  },
};
