import { authTrade } from "@checkatrade/auth-trade";
import { ConflictError, NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import {
  getCompaniesDoc,
  getCompanyDoc,
} from "../../services/firebase/firestore/get-company";
import {
  canCreateReviewRequest,
  createReviewRequest,
} from "../../services/firebase/firestore/review-request";
import { formatPhoneNumber } from "./format-phone-number";
import { mapReviewRequestDocToResponse } from "./mappers/map-review-request-doc-to-response";
import { ReviewRequestResponseSchema } from "./review-request.schema";

const postManualReviewRequestBody = Type.Omit(
  reviewSDK.schemas.review.api.public.manualReviewRequest.create.body,
  ["companyId", "companyName", "companyUniqueName"],
);

type PostManualReviewRequestBody = Static<typeof postManualReviewRequestBody>;

export const postManualReviewRequestV2: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ReviewTypes.Api.Public.Report.Params;
  Body: PostManualReviewRequestBody;
}> = {
  method: "POST",
  url: "/manual-review-requests",
  schema: {
    summary: "Post manual review request",
    description: "record that manual review request was made",
    operationId: "postManualReviewRequestV2",
    tags: ["Reviews"],
    headers: companyIdHeader,
    body: postManualReviewRequestBody,
    response: {
      201: ReviewRequestResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { mobile, email, consumerName } = req.body;
    const { companyId } = await authTrade(req);

    const companiesDoc = await getCompaniesDoc(companyId.toString());
    if (!companiesDoc) {
      throw new NotFoundError("Company not found in Companies collection");
    }

    const canCreate = await canCreateReviewRequest(companyId, {
      phone: mobile,
      email,
    });
    if (!canCreate) {
      throw new ConflictError("Similar review request already exists");
    }

    const company = await getCompanyDoc(companyId.toString());
    if (!company?.name || !company?.uniqueName) {
      throw new NotFoundError("Company not found");
    }

    const payload: ReviewTypes.Api.Public.ManualReviewRequest.Create.Body = {
      ...req.body,
      companyId,
      mobile: formatPhoneNumber(mobile),
      companyName: company.name,
      companyUniqueName: company.uniqueName,
    };

    await reviewSDK.public().postManualReviewRequest(payload);

    const reviewRequestDoc = await createReviewRequest(companyId, {
      fullName: consumerName,
      ...(mobile ? { phone: formatPhoneNumber(mobile) } : {}),
      ...(email ? { email } : {}),
      sent: false,
    });

    return res
      .status(201)
      .send(mapReviewRequestDocToResponse(reviewRequestDoc));
  },
};
