import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Type } from "@sinclair/typebox";

import { getCompanyDoc } from "../../services/firebase/firestore/get-company";
import { formatPhoneNumber } from "./format-phone-number";

export const postReviewRequestManual: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ReviewTypes.Api.Public.Report.Params;
  Body: Omit<
    ReviewTypes.Api.Public.ManualReviewRequest.Create.Body,
    "companyId" | "companyName" | "companyUniqueName"
  >;
}> = {
  method: "POST",
  url: "/review-request/manual",
  schema: {
    summary: "Post manual review request",
    description: "record that manual review request was made",
    operationId: "postReviewRequestManual",
    tags: ["Reviews"],
    headers: companyIdHeader,
    body: Type.Omit(
      reviewSDK.schemas.review.api.public.manualReviewRequest.create.body,
      ["companyId", "companyName", "companyUniqueName"],
    ),
    response: {
      201: reviewSDK.schemas.review.api.public.manualReviewRequest.create
        .response,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const company = await getCompanyDoc(companyId.toString());
    if (!company?.name || !company?.uniqueName) {
      throw new NotFoundError("Company not found");
    }

    const payload: ReviewTypes.Api.Public.ManualReviewRequest.Create.Body = {
      ...req.body,
      companyId,
      mobile: formatPhoneNumber(req.body.mobile),
      companyName: company.name,
      companyUniqueName: company.uniqueName,
    };

    const response = await reviewSDK.public().postManualReviewRequest(payload);

    return res.status(201).send(response);
  },
};
