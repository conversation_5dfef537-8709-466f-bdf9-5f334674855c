//code used in legacy reviews before sending comms message
export const formatPhoneNumber = (phoneNumber?: string): string | undefined => {
  if (phoneNumber === null || phoneNumber === undefined || phoneNumber === "") {
    return undefined;
  }

  if (phoneNumber.startsWith("0")) {
    return `44${phoneNumber.substring(1)}`;
  }

  if (phoneNumber.startsWith("+")) {
    return phoneNumber.substring(1);
  }

  return phoneNumber;
};
