import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

export const postReviewReplyUnpublish: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ReviewTypes.Api.Public.Reply.Unpublish.Params;
  Reply: ReviewTypes.Api.Public.Reply.Unpublish.Response;
}> = {
  method: "POST",
  url: "/review-reply/:replyId/unpublish",
  schema: {
    summary: "Post review reply unpublish",
    description: "Unpublish review reply",
    operationId: "postReviewReplyUnpublish",
    tags: ["Reviews"],
    headers: companyIdHeader,
    params: reviewSDK.schemas.review.api.public.reply.unpublish.params,
    response: {
      200: reviewSDK.schemas.review.api.public.review.createReply.response,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const { replyId } = req.params;
    const reviewReply = await reviewSDK.public().postUnpublishReply(replyId, {
      companyId,
    });

    return res.status(200).send(reviewReply);
  },
};
