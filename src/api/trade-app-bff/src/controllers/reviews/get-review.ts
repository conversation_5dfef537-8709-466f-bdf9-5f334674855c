import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ReviewTypes, reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Type } from "@sinclair/typebox";

export const getReview: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ReviewTypes.Api.Public.Get.Params;
  Querystring: Omit<ReviewTypes.Api.Public.Get.Query, "companyId">;
  Reply: ReviewTypes.Api.Public.Get.Response;
}> = {
  method: "GET",
  url: "/reviews/:reviewId",
  schema: {
    summary: "Get a review",
    description: "Fetches a single review by id",
    operationId: "getReview",
    tags: ["Reviews"],
    params: reviewSDK.schemas.review.api.public.review.get.params,
    headers: companyIdHeader,
    querystring: Type.Omit(
      reviewSDK.schemas.review.api.public.review.get.querystring,
      ["companyId"],
    ),
    response: {
      201: reviewSDK.schemas.review.api.public.review.get.response,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const { reviewId } = req.params;
    const review = await reviewSDK.public().getReview(reviewId, { companyId });

    return res.status(200).send(review);
  },
};
