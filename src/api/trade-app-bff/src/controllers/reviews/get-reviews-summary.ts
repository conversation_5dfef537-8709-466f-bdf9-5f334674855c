import { authTrade } from "@checkatrade/auth-trade";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { reviewsSummarySDK } from "@checkatrade/reviews-summary-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

type TradeSummaryResponse = Static<
  typeof reviewsSummarySDK.schemas.summary.api.getSummary.response
>;

export const getReviewsSummary: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<
    typeof reviewsSummarySDK.schemas.summary.api.getSummary.params
  >;
  Reply: Static<
    typeof reviewsSummarySDK.schemas.summary.api.getSummary.response
  >;
}> = {
  method: "GET",
  url: "/reviews/summary/:companyId/:summaryType",
  schema: {
    headers: companyIdHeader,
    params: reviewsSummarySDK.schemas.summary.api.getSummary.params,
    response: {
      200: reviewsSummarySDK.schemas.summary.api.getSummary.response,
    },
    summary: "Get reviews summary",
    description:
      "Retrieves the summary of reviews for a specific company and summary type",
  },
  handler: async (request, reply) => {
    await authTrade(request);
    const { companyId, summaryType } = request.params;
    const data = await reviewsSummarySDK
      .summary()
      .getSummary(companyId, summaryType);
    return reply.send(data as TradeSummaryResponse);
  },
};
