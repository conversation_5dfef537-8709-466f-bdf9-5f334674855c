import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export type QuotesByJobIdResponse = Static<
  typeof quotingSDK.schemas.api.trade.getQuotesByJobId.response
>;

export const getQuotesByJobId: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof quotingSDK.schemas.api.trade.getQuotesByJobId.params>;
  Querystring: Static<
    typeof quotingSDK.schemas.api.trade.getQuotesByJobId.querystring
  >;
  Reply: QuotesByJobIdResponse;
}> = {
  method: "GET",
  url: "/quotes/job/:jobId",
  schema: {
    summary: "Gets all quotes associated with a job ID",
    headers: companyIdHeader,
    params: quotingSDK.schemas.api.trade.getQuotesByJobId.params,
    querystring: quotingSDK.schemas.api.trade.getQuotesByJobId.querystring,
    response: {
      200: quotingSDK.schemas.api.trade.getQuotesByJobId.response,
    },
    description: "Gets quotes associated with a job ID",
    operationId: "GetQuotesByJobId",
    tags: ["Quote"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const { jobId } = req.params;

    const quotes = await quotingSDK
      .trade(token, String(companyId))
      .getQuotesByJobId(jobId, req.query);

    return res.send(quotes);
  },
};
