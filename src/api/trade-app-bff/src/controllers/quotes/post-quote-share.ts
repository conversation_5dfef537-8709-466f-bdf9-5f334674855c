import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { BadRequestError } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

export const postQuoteShare: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof quotingSDK.schemas.api.trade.shareQuote.params>;
  Reply: void;
}> = {
  method: "POST",
  url: "/quotes/:quoteId/share",
  schema: {
    summary: "Shares the quote to consumer, renders update to trader",
    params: quotingSDK.schemas.api.trade.shareQuote.params,
    description:
      "Shares a Quote with a consumer, decorating it with Opportunity data before sending a message.",
    operationId: "postQuoteShare",
    tags: ["Quote"],
    headers: companyIdHeader,
    response: {
      200: Type.Object({}),
    },
  },
  handler: async (req, res) => {
    const { quoteId } = req.params;
    const { log: logger } = req;
    const { companyId, token } = await authTrade(req);

    const quote = await quotingSDK.trade(token, companyId).getQuote(quoteId);
    const { totalAmount, title, type, status, jobIdV2, pdfFileUrl } = quote;

    if (!jobIdV2) {
      throw new BadRequestError("The quote has no relation to a job");
    }

    const { opportunityId: channelId } = await jobsSDK
      .trade(token, companyId)
      .getJob(jobIdV2);

    await chatSDK.sendSmartMessage({
      channelId,
      senderId: companyId,
      smartType: SmartMessageType.QUOTE,
      text: `The trade has offered a quote: "${title}" for £${totalAmount}. [View Quote](${pdfFileUrl})`,
      quoteId,
      quote: {
        id: quoteId,
        title,
        cardType: type,
        total: totalAmount,
        pdfFileUrl,
        status,
      },
      logger,
    });

    return res.send({});
  },
};
