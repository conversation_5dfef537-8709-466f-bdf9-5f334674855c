import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export const deleteQuote: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof quotingSDK.schemas.api.trade.deleteQuote.params>;
  Reply: Static<typeof quotingSDK.schemas.api.trade.deleteQuote.response>;
}> = {
  method: "DELETE",
  url: "/quotes/:quoteId",
  schema: {
    summary: "Delete quote. This is a hard delete.",
    headers: companyId<PERSON>eader,
    params: quotingSDK.schemas.api.trade.deleteQuote.params,
    response: {
      200: quotingSDK.schemas.api.trade.deleteQuote.response,
    },
    description:
      "Delete the quote with the given id, cancel any associated payments and send a chat message notifying consumer and trade that the quote is deleted.",
    operationId: "deleteQuote",
    tags: ["Quote"],
  },
  handler: async (req, res) => {
    const { quoteId } = req.params;
    const { log: logger } = req;
    const { companyId, token } = await authTrade(req);

    const { jobIdV2, title, totalAmount } = await quotingSDK
      .trade(token, companyId)
      .getQuote(quoteId);

    // Delete the quote
    const deleted = await quotingSDK
      .trade(token, companyId)
      .deleteQuote(quoteId);

    // Send chat message
    if (jobIdV2) {
      try {
        const job = await jobsSDK.trade(token, companyId).getJob(jobIdV2);
        const { opportunityId: channelId } = job;

        await chatSDK.sendSmartMessage({
          channelId: channelId,
          senderId: companyId,
          // eslint-disable-next-line  @typescript-eslint/no-explicit-any
          smartType: "QUOTE_DELETE" as any,
          text: "The Trade has withdrawn the quote",
          quoteId,
          quote: {
            id: quoteId,
            total: totalAmount,
            title: title,
          },
          logger,
        });
      } catch (err) {
        //Graceful error handling for errors around fetching job details or sending smart message.
        logger.error(
          err,
          "Failed fetching job or sending smart message upon deleting a quote",
        );
      }
    }

    // Cancel associated payments
    let hasMorePayments = true;
    let currentPage = 1;
    const recordsPerPage = 10;

    while (hasMorePayments) {
      try {
        const payments =
          await paymentSDK.trade.paymentRequests.getPaymentRequestsForQuote(
            quoteId,
            {
              pageNumber: currentPage,
              pageSize: recordsPerPage,
            },
          );
        currentPage++;

        if (payments) {
          hasMorePayments =
            payments.pagination.hasNext ??
            payments.data.length === recordsPerPage;

          await Promise.all(
            payments.data.map(async (payment) => {
              try {
                await paymentSDK.trade.paymentRequest.patchPaymentRequestCancel(
                  payment.paymentLinkId,
                  "Quote deleted",
                );
              } catch (error) {
                logger.warn(error);
              }
            }),
          );
        } else {
          hasMorePayments = false;
        }
      } catch (error) {
        hasMorePayments = false;
        logger.warn(error);
      }
    }

    return res.send(deleted);
  },
};
