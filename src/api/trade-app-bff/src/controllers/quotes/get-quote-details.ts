import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export type QuoteDetailsResponse = Static<
  typeof quotingSDK.schemas.api.trade.getQuoteDetails.response
>;

export const getQuoteDetails: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof quotingSDK.schemas.api.trade.getQuoteDetails.params>;
  Reply: QuoteDetailsResponse;
}> = {
  method: "GET",
  url: "/quotes/:quoteId/details",
  schema: {
    summary: "Gets a quote's details",
    headers: companyIdHeader,
    params: quotingSDK.schemas.api.trade.getQuoteDetails.params,
    response: {
      200: quotingSDK.schemas.api.trade.getQuoteDetails.response,
    },
    description: "Gets a quote's details",
    operationId: "GetsQuoteDetails",
    tags: ["Quote"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const { quoteId } = req.params;

    const quote = await quotingSDK
      .trade(token, companyId)
      .getQuoteDetails(quoteId);

    return res.send(quote);
  },
};
