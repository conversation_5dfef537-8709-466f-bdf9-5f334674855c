import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { formatCreateQuoteResponse } from "./quote.response";

export const createQuote: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Body: Static<typeof quotingSDK.schemas.api.trade.createQuote.body>;
  Reply: Static<typeof quotingSDK.schemas.api.trade.createQuote.response>;
}> = {
  method: "POST",
  url: "/quotes",
  schema: {
    summary: "Creates a quote",
    headers: companyIdHeader,
    body: quotingSDK.schemas.api.trade.createQuote.body,
    response: {
      201: quotingSDK.schemas.api.trade.createQuote.response,
    },
    description: "Creates a quote",
    operationId: "createQuote",
    tags: ["Quote"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const { quoteId } = await quotingSDK
      .trade(token, companyId)
      .createQuote(req.body);

    return res.status(201).send(formatCreateQuoteResponse(quoteId));
  },
};
