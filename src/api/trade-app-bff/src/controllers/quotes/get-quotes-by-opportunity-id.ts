import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export type QuotesByOpportunityIdResponse = Static<
  typeof quotingSDK.schemas.api.trade.getQuotesByOpportunityId.response
>;

export const getQuotesByOpportunityId: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<
    typeof quotingSDK.schemas.api.trade.getQuotesByOpportunityId.params
  >;
  Querystring: Static<
    typeof quotingSDK.schemas.api.trade.getQuotesByOpportunityId.querystring
  >;
  Reply: QuotesByOpportunityIdResponse;
}> = {
  method: "GET",
  url: "/quotes/opportunity/:opportunityId",
  schema: {
    summary: "Gets all quotes associated with an opportunity ID",
    headers: companyIdHeader,
    params: quotingSDK.schemas.api.trade.getQuotesByOpportunityId.params,
    querystring:
      quotingSDK.schemas.api.trade.getQuotesByOpportunityId.querystring,
    response: {
      200: quotingSDK.schemas.api.trade.getQuotesByOpportunityId.response,
    },
    description: "Gets quotes associated with an opportunity ID",
    operationId: "getQuotesByOpportunityId",
    tags: ["Quote"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const { opportunityId } = req.params;

    const quotes = await quotingSDK
      .trade(token, String(companyId))
      .getQuotesByOpportunityId(opportunityId, req.query);

    return res.send(quotes);
  },
};
