import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { quoting } from "../../services/quoting";
import { formatQuoteTokenResponse } from "./quote.response";
import {
  QuoteTokenParams,
  QuoteTokenResponse,
  quoteTokenParams,
  quoteTokenResponse,
} from "./quote.schema";

export const postQuoteToken: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: QuoteTokenParams;
  Reply: QuoteTokenResponse;
}> = {
  method: "POST",
  url: "/quotes/:quoteId/token",
  schema: {
    summary: "Create a quoting time limited token",
    description:
      "Creates a time limited token for allowing consumers without a CAT account to view their quote",
    operationId: "postQuoteToken",
    tags: ["Quote"],
    headers: companyIdHeader,
    params: quoteTokenParams,
    response: {
      200: quoteTokenResponse,
    },
  },
  handler: async (req, res) => {
    const { quoteId } = req.params;

    await authTrade(req);

    const token = quoting.generateToken(quoteId);
    return res.send(formatQuoteTokenResponse(token));
  },
};
