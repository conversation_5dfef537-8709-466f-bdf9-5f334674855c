import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { BadRequestError } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { QuoteType, quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

export const postQuoteDraft: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof quotingSDK.schemas.api.trade.shareQuote.params>;
}> = {
  method: "POST",
  url: "/quotes/:quoteId/draft",
  schema: {
    summary: "Shares a draft quote/invoice",
    params: quotingSDK.schemas.api.trade.shareQuote.params,
    description:
      "Initial quote/invoice quote created for trader rendering purposes only",
    operationId: "postQuoteDraft",
    tags: ["Quote"],
    headers: companyIdHeader,
    response: {
      200: Type.Object({}),
    },
  },
  handler: async (req, res) => {
    const { quoteId } = req.params;
    const { log: logger } = req;
    const { companyId, token } = await authTrade(req);

    const quote = await quotingSDK.trade(token, companyId).getQuote(quoteId);
    const { totalAmount, title, type, status, jobIdV2 } = quote;

    if (!jobIdV2) {
      throw new BadRequestError("The quote has no relation to a job");
    }

    const { opportunityId: channelId } = await jobsSDK
      .trade(token, companyId)
      .getJob(jobIdV2);

    await chatSDK
      .sendSmartMessage({
        channelId,
        senderId: companyId,
        // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        smartType: "QUOTE_DRAFT" as any,
        text: `${type === QuoteType.QUOTE ? "A quote" : "An invoice"} is being created for this job`,
        quoteId,
        quote: {
          id: quoteId,
          title,
          cardType: type,
          total: totalAmount,
          status,
        },
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.send({});
  },
};
