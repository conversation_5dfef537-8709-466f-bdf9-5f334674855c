import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { JobSource, paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { PostCreatePayByPhonePaymentRequestBodySchema } from "./schemas/PostCreatePayByPhonePaymentRequest.types";
import { getJobReference } from "./utils/get-job-reference";

export const postPayByPhonePaymentRequest: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: Static<typeof PostCreatePayByPhonePaymentRequestBodySchema>;
  Reply: Static<
    typeof paymentSDK.schemas.api.payment.payByPhone.paymentRequest.response
  >;
}> = {
  method: "POST",
  url: `/payments/pay-by-phone/payment-request`,
  schema: {
    summary: "Create Payment Request for pay by phone",
    body: PostCreatePayByPhonePaymentRequestBodySchema,
    headers: companyIdHeader,
    response: {
      201: paymentSDK.schemas.api.payment.payByPhone.paymentRequest.response,
    },
    description: "Creates a new payment request for pay by phone",
    operationId: "postCreatePayByPhonePaymentRequest",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId, token } = await authTrade(req);
    const { paymentRequest, vendorData, job: jobBody, vendor } = req.body;

    const existingJob =
      paymentRequest.jobId ?
        await jobsSDK.trade(token, companyId).getJob(paymentRequest.jobId)
      : undefined;

    const job = existingJob ?? jobBody;

    let jobReference: string | undefined;

    if (job?.categoryId && job?.postcode) {
      jobReference = await getJobReference(
        Number(job.categoryId),
        job.postcode,
      );
    }

    const jobSource =
      existingJob ? JobSource.JobService : JobSource.OffPlatform;

    const createdPaymentRequestData =
      await paymentSDK.trade.payByPhone.postCreatePaymentRequest({
        vendor,
        vendorData,
        paymentRequest: {
          ...paymentRequest,
          jobReference,
          jobSource,
        },
      });

    return res.status(201).send(createdPaymentRequestData);
  },
};
