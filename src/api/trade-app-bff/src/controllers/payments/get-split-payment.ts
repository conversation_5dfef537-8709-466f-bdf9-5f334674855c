import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static } from "@sinclair/typebox";

export const getSplitPayment: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<typeof paymentSDK.schemas.api.payment.splitPayment.request>;
  Reply: Static<typeof paymentSDK.schemas.api.payment.splitPayment.response>;
}> = {
  method: "GET",
  url: `/payments/split-payment/:penceAmount`,
  schema: {
    summary:
      "Gets the payment service charge and total for the provided amount in pennies",
    params: paymentSDK.schemas.api.payment.splitPayment.request,
    headers: companyIdHeader,
    response: {
      200: paymentSDK.schemas.api.payment.splitPayment.response,
    },
    description:
      "Gets the payment service charge and total the consumer will pay (penceAmount + service charge) for the provided amount in pennies",
    operationId: "getSplitPayment",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    await authTrade(req);

    const { penceAmount } = req.params;
    const data =
      await paymentSDK.trade.splitPayment.getSplitPayment(penceAmount);

    return res.send(data);
  },
};
