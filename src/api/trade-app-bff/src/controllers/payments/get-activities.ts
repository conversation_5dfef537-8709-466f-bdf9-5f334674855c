import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static } from "@sinclair/typebox";

export const getActivities: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof paymentSDK.schemas.api.helpers.pagination.query>;
  Reply: Static<
    typeof paymentSDK.schemas.api.payment.activities.getActivities.response
  >;
}> = {
  method: "GET",
  url: "/payments/activities",
  schema: {
    summary: "Gets latest payment activities from core payment",
    querystring: paymentSDK.schemas.api.payment.activities.getActivities.query,
    headers: companyIdHeader,
    response: {
      200: paymentSDK.schemas.api.payment.activities.getActivities.response,
    },
    description: "Gets the authenticated user's activities from core-payments",
    operationId: "getActivities",
    tags: ["Payments"],
  },
  handler: async (req) => {
    const { companyId } = await authTrade(req);

    return paymentSDK.trade.activities.getActivities(
      String(companyId),
      req.query,
    );
  },
};
