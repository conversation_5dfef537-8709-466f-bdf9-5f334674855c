import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { schemas } from "@checkatrade/payment-types";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static, Type } from "@sinclair/typebox";
import { json2csv } from "json-2-csv";
import { logger } from "nx/src/utils/logger";

import { config } from "./config";
import { formatStatementReportData } from "./utils/transformers";

export const getActivitiesStatementReport: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<
    typeof schemas.api.payment.activities.getActivitiesStatement.query
  >;
}> = {
  method: "GET",
  url: "/payments/report/activities-statement",
  schema: {
    summary: "Get payment activities csv report",
    querystring: schemas.api.payment.activities.getActivitiesStatement.query,
    headers: companyIdHeader,
    response: {
      200: Type.Never(),
    },
    description: "Get payment activities csv report for the given time period",
    operationId: "getActivitiesStatementReport",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { fromDate, toDate } = req.query;

    let hasMoreActivities = true;
    let currentPage = 1;
    let csv = "";

    while (hasMoreActivities) {
      try {
        const activitiesResult =
          await paymentSDK.trade.activities.getActivitiesStatement(
            companyId.toString(),
            fromDate as Date,
            toDate as Date,
            { pageNumber: currentPage, pageSize: config.reportRecordsPerPage },
          );

        if (activitiesResult.data) {
          const formattedActivitiesData =
            formatStatementReportData(activitiesResult);

          const csvResult = json2csv(formattedActivitiesData, {
            prependHeader: currentPage === 1, // Include header only for the first page
            excelBOM: true, // Add BOM for Excel compatibility
          });

          csv += csvResult;

          hasMoreActivities =
            activitiesResult.data.length == config.reportRecordsPerPage;
          currentPage++;
        } else {
          hasMoreActivities = false;
        }
      } catch (error) {
        hasMoreActivities = false;
        logger.warn(error as Error);
      }
    }

    return res
      .headers({
        "Content-Type": "text/csv",
        "Content-Disposition": `attachment; filename="checkatrade-payments-${fromDate}-${toDate}.csv"`,
      })
      .send(csv);
  },
};
