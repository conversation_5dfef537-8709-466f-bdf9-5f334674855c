import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { schemas } from "@checkatrade/payment-types";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import type { Static } from "@sinclair/typebox";

export const getPaymentRequestsByQuote: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<typeof schemas.api.payment.paymentRequests.quoteParams>;
  Querystring: Static<typeof schemas.api.payment.paymentRequests.queryString>;
  Reply?: Static<typeof schemas.api.payment.paymentRequests.response>;
}> = {
  method: "GET",
  url: `/payments/payment-requests/quote/:quoteId`,
  schema: {
    summary: "payment requests for the specified quote",
    description:
      "returns an array of payment requests that are associated with the given quoteId",
    headers: companyIdHeader,
    response: {
      200: schemas.api.payment.paymentRequests.response,
    },
    params: schemas.api.payment.paymentRequests.quoteParams,
    querystring: schemas.api.payment.paymentRequests.queryString,
    operationId: "getPaymentRequestsByQuote",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { quoteId } = req.params;

    //getting the quote will validate that it belongs to this company - will throw exception if not authorised
    await quotingSDK.trade(token, companyId).getQuote(quoteId);

    const paymentRequests =
      await paymentSDK.trade.paymentRequests.getPaymentRequestsForQuote(
        quoteId,
        req.query,
      );

    if (!paymentRequests) {
      return res.send({
        data: [],
        pagination: {
          page: req.query.pageNumber,
          size: req.query.pageSize,
          total: 0,
        },
      });
    }

    return res.send(paymentRequests);
  },
};
