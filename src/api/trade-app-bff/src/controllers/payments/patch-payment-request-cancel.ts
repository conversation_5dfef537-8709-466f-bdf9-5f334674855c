import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { PaymentRequestStatus, paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

export const patchPaymentRequestCancel: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<
    typeof paymentSDK.schemas.api.payment.paymentRequest.cancel.params
  >;
  Body: Static<
    typeof paymentSDK.schemas.api.payment.paymentRequest.cancel.body
  >;
}> = {
  method: "PATCH",
  url: `/payments/payment-request/:paymentLinkId/cancel`,
  schema: {
    summary: "Create Payment Request",
    params: paymentSDK.schemas.api.payment.paymentRequest.cancel.params,
    body: paymentSDK.schemas.api.payment.paymentRequest.cancel.body,
    headers: companyIdHeader,
    response: { 200: Type.Object({}) },
    description: "Cancel payment request",
    operationId: "cancelPaymentRequest",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { log: logger } = req;
    const { paymentLinkId } = req.params;
    const { reason } = req.body;

    await paymentSDK.trade.paymentRequest.patchPaymentRequestCancel(
      paymentLinkId,
      reason,
    );

    const paymentRequest =
      await paymentSDK.trade.paymentRequest.getPaymentRequest(paymentLinkId);

    if (paymentRequest.status !== PaymentRequestStatus.Cancelled) {
      logger.error(
        `Expected payment request status to be ${PaymentRequestStatus.Cancelled}, but received ${paymentRequest.status} on ${paymentRequest.id}`,
      );
    }

    await chatSDK
      .sendSmartMessage({
        channelId: paymentRequest.opportunityId,
        senderId: companyId,
        smartType: SmartMessageType.PAYMENT_REQUEST_NEW,
        paymentRequestId: paymentRequest.id,
        text: `Payment for ${paymentRequest.reference} has been cancelled.`,
        paymentRequest: {
          id: paymentRequest.id,
          dueDate: new Date(paymentRequest.dueDate),
          reference: paymentRequest.reference,
          jobReference: paymentRequest.jobReference,
          description: paymentRequest.description,
          amount: {
            value: paymentRequest.totalAmount,
            currency: paymentRequest.currency,
          },
          status: PaymentRequestStatus.Cancelled,
          paymentLinkId: paymentRequest.paymentLinkId,
          paymentUrl: paymentRequest.paymentUrl,
          consumerName: paymentRequest.consumerName,
        },
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.send();
  },
};
