import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static } from "@sinclair/typebox";

export const getBalance: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: Static<
    typeof paymentSDK.schemas.api.payment.balance.getBalance.response
  >;
}> = {
  method: "GET",
  url: `/payments/balance`,
  schema: {
    summary: "Gets the balance of the company",
    headers: companyIdHeader,
    response: {
      200: paymentSDK.schemas.api.payment.balance.getBalance.response,
    },
    description: "Gets the balance and outstanding balance of the company",
    operationId: "getBalance",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const companyIdString = String(companyId);

    const balanceData =
      await paymentSDK.trade.balance.getBalance(companyIdString);

    return res.send(balanceData);
  },
};
