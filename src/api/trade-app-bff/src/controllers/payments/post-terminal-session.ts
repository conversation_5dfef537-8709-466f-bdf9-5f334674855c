import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { Static, Type } from "@sinclair/typebox";

const SessionBody = Type.Object({
  setupToken: Type.String(),
});

export const postTerminalSession: FastifyRouteOptions<{
  Body: Static<typeof SessionBody>;
  Reply: Static<typeof paymentSDK.schemas.api.payment.session.response>;
}> = {
  method: "POST",
  url: "/payments/session",
  schema: {
    summary: "Post Session",
    description: "Get a session for running tap to pay capabilities",
    operationId: "postTerminalSession",
    tags: ["Payments"],
    body: SessionBody,
    response: {
      204: paymentSDK.schemas.api.payment.session.response,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { setupToken } = req.body;
    req.log.info("debugging: ", setupToken);

    const data = await paymentSDK.trade.terminalSession.postTerminalSession({
      companyId: String(companyId),
      setupToken,
    });

    return res.send(data);
  },
};
