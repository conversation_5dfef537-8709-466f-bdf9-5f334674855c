import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { schemas } from "@checkatrade/payment-types";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import type { Static } from "@sinclair/typebox";

export const getPaymentRequestsByOpportunity: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<typeof schemas.api.payment.paymentRequests.opportunityParams>;
  Querystring: Static<typeof schemas.api.payment.paymentRequests.queryString>;
  Reply?: Static<typeof schemas.api.payment.paymentRequests.response>;
}> = {
  method: "GET",
  url: `/payments/payment-requests/opportunity/:opportunityId`,
  schema: {
    summary: "payment requests for the specified opportunity",
    description:
      "returns an array of payment requests for the given opportunityId",
    headers: companyIdHeader,
    response: {
      200: schemas.api.payment.paymentRequests.response,
    },
    params: schemas.api.payment.paymentRequests.opportunityParams,
    querystring: schemas.api.payment.paymentRequests.queryString,
    operationId: "getPaymentRequestsByOpportunity",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { opportunityId } = req.params;

    const opportunity = await jobsSDK
      .trade(token, companyId)
      .getOpportunity(opportunityId);
    if (!opportunity) {
      throw new NotFoundError(`No opportunity exists with id=${opportunityId}`);
    }

    const paymentRequests =
      await paymentSDK.trade.paymentRequests.getPaymentRequestsForOpportunity(
        opportunityId,
        req.query,
      );

    if (!paymentRequests) {
      return res.send({
        data: [],
        pagination: {
          page: req.query.pageNumber,
          size: req.query.pageSize,
          total: 0,
        },
      });
    }

    return res.send(paymentRequests);
  },
};
