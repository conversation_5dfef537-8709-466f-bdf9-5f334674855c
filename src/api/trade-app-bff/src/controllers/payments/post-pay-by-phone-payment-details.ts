import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import {
  CompanyIdHeader,
  companyIdHeader,
} from "@checkatrade/trade-bff-types/dist/schemas";
import { Static } from "@sinclair/typebox";

export const postPayByPhonePaymentDetails: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: Static<
    typeof paymentSDK.schemas.api.payment.payByPhone.paymentDetails.body
  >;
  Reply: Static<
    typeof paymentSDK.schemas.api.payment.payByPhone.paymentDetails.response
  >;
}> = {
  method: "POST",
  url: `/payments/pay-by-phone/payment-details`,
  schema: {
    summary: "Create Payment Details for pay by phone",
    body: paymentSDK.schemas.api.payment.payByPhone.paymentDetails.body,
    headers: companyIdHeader,
    response: {
      200: paymentSDK.schemas.api.payment.payByPhone.paymentDetails.response,
    },
    description: "Creates a new payment details for pay by phone",
    operationId: "postPayByPhonePaymentDetails",
    tags: ["Payments"],
  },
  handler: async (request, response) => {
    await authTrade(request);

    const responseData = await paymentSDK.trade.payByPhone.postPaymentDetails(
      request.body,
    );

    return response.status(200).send(responseData);
  },
};
