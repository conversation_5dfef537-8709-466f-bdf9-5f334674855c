import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { JobSource, paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { comms } from "../../lib/api-common";
import { getCompanyDoc } from "../../services/firebase/firestore/get-company";
import { PostCreatePaymentRequestRequestOffPlatformJobSchema } from "./schemas/PostCreatePaymentRequestOffPlatformJob.types";
import { getJobReference } from "./utils/get-job-reference";
import * as offPlatformPubSubTransformer from "./utils/off-platform-job-event.transformer";

export const postPaymentRequestOffPlatformJob: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: Static<typeof PostCreatePaymentRequestRequestOffPlatformJobSchema>;
  Reply: Static<typeof paymentSDK.schemas.api.payment.paymentRequest.response>;
}> = {
  method: "POST",
  url: `/payments/payment-request/off-platform-job`,
  schema: {
    summary: "Create Payment Request for an off platform job",
    body: PostCreatePaymentRequestRequestOffPlatformJobSchema,
    headers: companyIdHeader,
    response: { 201: paymentSDK.schemas.api.payment.paymentRequest.response },
    description:
      "Creates a new payment request and return a link to share with the consumer",
    operationId: "postCreatePaymentRequestOffPlatformJob",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    // TODO: add tests - https://checkatrade.atlassian.net/browse/BAP-142
    const { companyId } = await authTrade(req);
    const { paymentRequest, job, consumer } = req.body;

    paymentRequest.jobSource = JobSource.OffPlatform;

    const companyData = await getCompanyDoc(companyId.toString());
    const tokenData = {
      company: {
        companyId,
        companyName: companyData?.name ?? "",
      },
      job,
      consumer: {
        ...consumer,
        emailAddress: paymentRequest.emailAddress,
      },
    };

    const jobReference = await getJobReference(
      Number(job.categoryId),
      job.postcode,
    );

    const createdPaymentRequestData =
      await paymentSDK.trade.paymentRequest.postCreatePaymentRequest({
        ...paymentRequest,
        jobReference,
        tokenData,
      });

    if (
      !createdPaymentRequestData.link.id ||
      !createdPaymentRequestData.link.url
    ) {
      throw new Error("The payment request was not successfully created");
    }

    //send job data to pub/sub
    await comms.publishOffPlatformJobCreated(
      offPlatformPubSubTransformer.transform(
        createdPaymentRequestData,
        job,
        consumer,
      ),
    );

    //return consumer web url
    return res.status(201).send(createdPaymentRequestData);
  },
};
