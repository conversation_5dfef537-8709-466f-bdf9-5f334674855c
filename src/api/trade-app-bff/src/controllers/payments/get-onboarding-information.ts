import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static } from "@sinclair/typebox";

import { GetOnboardingInformationSchema } from "./schemas/GetOnboarding.types";
import { onboardingAndCapabilitiesTransformer } from "./utils/transformers";

export const getOnboardingInformation: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: Static<typeof GetOnboardingInformationSchema>;
}> = {
  method: "GET",
  url: `/payments/onboarding-information`,
  schema: {
    summary: "Returns a breakdown of current onboarding progress",
    headers: companyIdHeader,
    response: {
      200: GetOnboardingInformationSchema,
    },
    description:
      "Receives onboarding information and percentage alongside the capabilities",
    operationId: "getOnboardingInformation",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const companyIdString = String(companyId);

    const [capabilities, onboardingInfo] = await Promise.all([
      paymentSDK.trade.capabilities.getCapabilities(companyIdString),
      paymentSDK.trade.onboarding.getOnboarding(companyIdString),
    ]);

    const transformedResponse = onboardingAndCapabilitiesTransformer({
      onboardingInfo,
      capabilities,
    });

    return res.send(transformedResponse);
  },
};
