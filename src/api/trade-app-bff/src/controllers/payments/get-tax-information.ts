import { authTrade } from "@checkatrade/auth-trade";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import type { Static } from "@sinclair/typebox";

import ContentAPI from "./contentapi";
import { TaxInformationSchema } from "./schemas/TaxInformation";
import { taxInformationTransformer } from "./utils/transformers";

export const getTaxInformation: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: Static<typeof TaxInformationSchema>;
}> = {
  method: "get",
  url: `/payments/tax-information`,
  schema: {
    summary: "Fetch trade tax information",
    headers: companyIdHeader,
    response: { 200: TaxInformationSchema },
    description: "Retrieves tax information from ContentApi",
    operationId: "getTaxInformation",
    tags: ["Payments"],
  },
  handler: async (req) => {
    const { companyId } = await authTrade(req);

    const traderInfo = await ContentAPI.getProfileData(companyId);

    return taxInformationTransformer(traderInfo);
  },
};
