import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static } from "@sinclair/typebox";

export const getPayByPhonePaymentMethods: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<
    typeof paymentSDK.schemas.api.payment.payByPhone.paymentMethods.params
  >;
  Reply: Static<
    typeof paymentSDK.schemas.api.payment.payByPhone.paymentMethods.response
  >;
}> = {
  method: "GET",
  url: `/payments/pay-by-phone/payment-methods`,
  schema: {
    summary: "Gets available payment methods for the company",
    headers: companyIdHeader,
    response: {
      200: paymentSDK.schemas.api.payment.payByPhone.paymentMethods.response,
    },
    description: "Retrieves list of available payment methods for the company",
    operationId: "getPaymentMethods",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const companyIdString = String(companyId);

    const paymentMethods =
      await paymentSDK.trade.payByPhone.getPaymentMethods(companyIdString);

    return res.send(paymentMethods);
  },
};
