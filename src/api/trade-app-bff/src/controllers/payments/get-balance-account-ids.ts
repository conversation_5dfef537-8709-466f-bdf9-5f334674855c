import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static } from "@sinclair/typebox";

export const getBalanceAccountIds: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: Static<
    typeof paymentSDK.schemas.api.payment.onboarding.getBalanceAccountIdsResponse
  >;
}> = {
  method: "GET",
  url: `/payments/balance-accounts`,
  schema: {
    summary: "Gets the balance account id of the company and merchant",
    headers: companyIdHeader,
    response: {
      200: paymentSDK.schemas.api.payment.onboarding
        .getBalanceAccountIdsResponse,
    },
    description: "Gets the balance account id of the company and merchant",
    operationId: "getBalanceAccountIds",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const balanceAccountIds =
      await paymentSDK.trade.onboarding.getBalanceAccountIds(String(companyId));

    return res.send(balanceAccountIds);
  },
};
