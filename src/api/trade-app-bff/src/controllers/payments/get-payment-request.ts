import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { type Static } from "@sinclair/typebox";

import { searchApi } from "../../lib/api-common";
import { GetPaymentRequestResponseSchema } from "./schemas/GetPaymentRequest.types";

export const getPaymentRequest: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<typeof paymentSDK.schemas.api.payment.paymentRequest.params>;
  Reply: Static<typeof GetPaymentRequestResponseSchema>; // TODO: Switch to ...paymentRequest.paymentRequestWithDetailResponse after 15/03/2025 (after release of BAP-226)
}> = {
  method: "GET",
  url: `/payments/payment-request/:paymentLinkId`,
  schema: {
    summary: "Get Payment Request",
    description: "Receives a payment request for a payment link id",
    headers: companyIdHeader,
    response: {
      200: GetPaymentRequestResponseSchema,
    },
    operationId: "getPaymentRequest",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const { paymentLinkId } = req.params;

    const paymentRequestData =
      await paymentSDK.trade.paymentRequest.getPaymentRequest(paymentLinkId);
    if (!paymentRequestData) {
      throw new Error("Unable to retrieve payment request");
    }

    // Job title defaults to consumer name as only data we have available for Off Platform Jobs
    let formattedJobTitle =
      paymentRequestData.description ?? paymentRequestData.consumerName;
    // For On platform Jobs we use the job category and location
    if (paymentRequestData.jobId) {
      const jobData = await jobsSDK
        .trade(token, companyId)
        .getJob(paymentRequestData.jobId);
      if (!jobData?.categoryId || !jobData?.postcode) {
        throw new Error("Job data not found");
      }

      const categoryName = await searchApi.getCategoryName(jobData.categoryId);

      formattedJobTitle = `${categoryName} - ${jobData.postcode}`;
    }

    return res.send({
      ...paymentRequestData,
      jobTitle: formattedJobTitle,
    });
  },
};
