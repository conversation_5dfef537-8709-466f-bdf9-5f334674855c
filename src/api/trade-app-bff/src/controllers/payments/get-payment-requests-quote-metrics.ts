import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { schemas } from "@checkatrade/payment-types";
import { quotingSDK } from "@checkatrade/quoting-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import type { Static } from "@sinclair/typebox";

export const getPaymentRequestsQuoteMetrics: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<typeof schemas.api.payment.quote.metrics.params>;
  Reply?: Static<typeof schemas.api.payment.quote.metrics.response>;
}> = {
  method: "GET",
  url: `/payments/payment-requests/quote/:quoteId/metrics`,
  schema: {
    summary: "metrics for a quote and associated payments ",
    description:
      "returns the shared metrics between a quote and associated payments",
    headers: companyIdHeader,
    response: {
      200: schemas.api.payment.quote.metrics.response,
    },
    params: schemas.api.payment.quote.metrics.params,

    operationId: "getPaymentRequestsQuoteMetrics",
    tags: ["Payments", "Quotes"],
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { quoteId } = req.params;

    // getting the quote will validate that it belongs to this company - will throw exception if not authorised
    await quotingSDK.trade(token, companyId).getQuote(quoteId);

    const metrics =
      await paymentSDK.trade.paymentRequests.getPaymentRequestMetricsForQuote(
        quoteId,
      );

    return res.send(metrics);
  },
};
