import { gcp } from "@checkatrade/gcp";
import { Static } from "@sinclair/typebox";

import { config } from "../../config";
import { TradeProfileResponse } from "./schemas/TradeProfileSchema";

class ContentAPI {
  public static async getProfileData(
    companyId: number,
  ): Promise<Static<typeof TradeProfileResponse>> {
    const url = `${config.contentApi.url}/api/v2/trade/${companyId}`;

    const gcpToken = await gcp.generateBearerToken(`${config.contentApi.url}`);

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${gcpToken}`,
      },
    });
    if (!response.ok) {
      throw new Error(`Response status: ${response.status}`);
    }

    const json = await response.json();

    // TODO: maybe validate this with Zod/TypeBox
    return json as Static<typeof TradeProfileResponse>;
  }
}

export default ContentAPI;
