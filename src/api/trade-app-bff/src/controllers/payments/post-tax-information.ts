import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { salesforce } from "../../lib/api-common";
import { prepareTaxNumber } from "./utils/transformers";

const postTaxInformationBody = Type.Object({
  vatNumber: Type.Optional(Type.String()),
  uniqueTaxpayerReference: Type.String(),
});

const postTaxInformationResponse = Type.Object({});

export const postTaxInformation: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: Static<typeof postTaxInformationBody>;
  Reply: Static<typeof postTaxInformationResponse>;
}> = {
  method: "POST",
  url: "/payments/tax-information",
  schema: {
    summary: "Post Tax Information",
    description:
      "Any updates to tax information, broadcasts into GCP and consumed by Agent tooling.",
    operationId: "postTaxInformation",
    tags: ["Payments"],
    body: postTaxInformationBody,
    headers: companyIdHeader,
    response: {
      204: postTaxInformationResponse,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { vatNumber, uniqueTaxpayerReference } = req.body;

    salesforce.taxInformation({
      companyId,
      vatNumber: vatNumber ? prepareTaxNumber(vatNumber) : undefined,
      uniqueTaxpayerReference,
    });

    await paymentSDK.trade.onboarding.updateTaxInformation(
      String(companyId),
      true,
    );

    return res.status(204).send();
  },
};
