import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { BadRequestError } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { JobSource, paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { getCompanyDoc } from "../../services/firebase/firestore/get-company";
import { PostCreatePaymentRequestRequestSchema } from "./schemas/PostCreatePaymentRequest.types";
import { getJobReference } from "./utils/get-job-reference";

export const postPaymentRequest: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: Static<typeof PostCreatePaymentRequestRequestSchema>;
  Reply: Static<typeof paymentSDK.schemas.api.payment.paymentRequest.response>;
}> = {
  method: "POST",
  url: `/payments/payment-request`,
  schema: {
    summary: "Create Payment Request",
    body: PostCreatePaymentRequestRequestSchema,
    headers: companyIdHeader,
    response: { 201: paymentSDK.schemas.api.payment.paymentRequest.response },
    description:
      "Creates a new payment request and sends a stream message with the payment link once successfully created",
    operationId: "postCreatePaymentRequest",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId, token } = await authTrade(req);
    const { log: logger } = req;
    const { paymentRequest } = req.body;

    if (!paymentRequest.consumerId) {
      throw new BadRequestError("Consumer ID is required");
    }

    paymentRequest.jobSource = JobSource.JobService;

    const job = await jobsSDK
      .trade(token, companyId)
      .getJob(paymentRequest.jobId);

    let jobReference: string | undefined;

    if (job.postcode) {
      jobReference = await getJobReference(
        Number(job.categoryId),
        job.postcode,
      );
    }

    const channel = await chatSDK.getChannelById(paymentRequest.opportunityId);
    const companyData = await getCompanyDoc(companyId.toString());
    const consumer = await consumerSDK
      .trade(token)
      .getConsumer(paymentRequest.consumerId);

    const createdPaymentRequestData =
      await paymentSDK.trade.paymentRequest.postCreatePaymentRequest(
        {
          ...paymentRequest,
          jobReference,
          tokenData: {
            company: {
              companyId,
              companyName: companyData?.name ?? "",
            },
            job: {
              description: paymentRequest.description ?? "",
              categoryId: String(job.categoryId),
              postcode: job.postcode ?? "",
            },
            consumer: {
              firstName: consumer.firstName ?? paymentRequest.firstName,
              lastName: consumer.lastName ?? paymentRequest.lastName,
              emailAddress: consumer.email,
              phoneNumber: consumer.phone ?? "",
            },
          },
        },
        !!channel,
      );

    if (
      !createdPaymentRequestData.link.id ||
      !createdPaymentRequestData.link.url
    ) {
      throw new Error("The payment request was not successfully created");
    }

    await chatSDK
      .sendSmartMessage({
        channelId: paymentRequest.opportunityId,
        senderId: companyId,
        smartType: SmartMessageType.PAYMENT_REQUEST_NEW,
        paymentRequestId: createdPaymentRequestData.link.id,
        text: `Trader has requested £${(paymentRequest.amount.value / 100).toFixed(2)} from the customer`,
        paymentRequest: {
          id: createdPaymentRequestData.paymentId,
          dueDate: new Date(paymentRequest.dueDate as string),
          reference: createdPaymentRequestData.paymentId,
          amount: paymentRequest.amount,
          status: createdPaymentRequestData.link.status,
          paymentLinkId: createdPaymentRequestData.link.id,
          paymentUrl: createdPaymentRequestData.link.url,
          consumerName: createdPaymentRequestData.paymentRequest.consumerName,
          jobReference: jobReference,
          description: paymentRequest.description,
        },
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.status(201).send(createdPaymentRequestData);
  },
};
