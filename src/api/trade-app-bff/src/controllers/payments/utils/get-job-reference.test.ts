import { faker } from "@faker-js/faker";

import { searchApi } from "../../../lib/api-common";
import { getJobReference } from "./get-job-reference";

const MOCK_JOB = {
  categoryId: "12345",
  description: "Fix tap in AB1 2CD",
  postcode: faker.location.zipCode(),
};

const MOCK_CATEGORY_NAME = "Plumber";

describe("getJobReference", () => {
  beforeEach(() => {
    jest.resetAllMocks();

    jest
      .spyOn(searchApi, "getCategoryName")
      .mockResolvedValue(MOCK_CATEGORY_NAME);
  });

  it("should return job reference when category name is available", async () => {
    const expected = `${MOCK_CATEGORY_NAME} - ${MOCK_JOB.postcode}`;

    const result = await getJobReference(
      Number(MOCK_JOB.categoryId),
      MOCK_JOB.postcode,
    );

    expect(result).toBe(expected);
  });

  it("should return undefined when category name is unavailable", async () => {
    jest.spyOn(searchApi, "getCategoryName").mockResolvedValue("");

    const result = await getJobReference(
      Number(MOCK_JOB.categoryId),
      MOCK_JOB.postcode,
    );

    expect(result).toBeUndefined();
  });
});
