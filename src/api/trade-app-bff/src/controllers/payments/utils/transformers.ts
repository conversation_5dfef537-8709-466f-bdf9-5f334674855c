import { UnprocessableEntityError } from "@checkatrade/errors";
import {
  OnboardingStatus,
  OnboardingType,
  paymentSDK,
} from "@checkatrade/payment-sdk";
import { schemas } from "@checkatrade/payment-types";
import { Static } from "@sinclair/typebox";
import dayjs from "dayjs";

import { GetOnboardingInformationSchema } from "../schemas/GetOnboarding.types";
import { PersonType, TraderType } from "../schemas/Shared.types";
import { TaxInformationSchema } from "../schemas/TaxInformation";
import {
  BusinessOwnershipSchema,
  TradeProfileResponse,
  TradeProfileResponseAddress,
  TradeProfileSchema,
} from "../schemas/TradeProfileSchema";

const getOnboardingSchema =
  paymentSDK.schemas.api.payment.onboarding.getOnboardingResponse;
const getCapabilitiesSchema =
  paymentSDK.schemas.api.payment.capabilities.getCapabilities.response;

export const isValidString = (value: string) => {
  return value.trim().length > 0;
};

export const findByPersonType = (
  businessOwnership: Static<typeof BusinessOwnershipSchema>[],
  personType: PersonType,
) => {
  return businessOwnership.find(
    (contact) =>
      contact.personTypeId === personType &&
      !contact.inactive &&
      !contact.isDeleted &&
      isValidString(contact.firstName) &&
      isValidString(contact.lastName) &&
      contact.birthdate,
  );
};

export const findPrimaryOwner = (
  businessOwnership: Static<typeof BusinessOwnershipSchema>[],
): Static<typeof BusinessOwnershipSchema> | undefined => {
  return (
    findByPersonType(businessOwnership, PersonType.PrimaryOwner) ||
    findByPersonType(businessOwnership, PersonType.SecondaryOwner)
  );
};

export const preparePhoneNumber = (phone: string) => {
  if (phone.startsWith("0")) return `+44${phone.substring(1)}`;
  return phone;
};

export const prepareTaxNumber = (tax: string) => {
  if (!tax.startsWith("GB")) return `GB${tax}`;
  return tax;
};

export const mapProfileAddressToOnboardingAddress = (
  address: Static<typeof TradeProfileResponseAddress>,
) => {
  return {
    city: address.city!,
    country: "GB",
    postalCode: address.postcode!,
    street: address.street!,
  };
};

export const tradeProfileOnboardingTransformer = ({
  traderInfo,
  companyId,
}: {
  traderInfo: Static<typeof TradeProfileResponse>;
  companyId: number;
}): Static<typeof TradeProfileSchema> => {
  let traderTypeExtraDetails;
  const { core, profile } = traderInfo.payload;

  if (
    !core.companyPrimaryPostalAddress.street ||
    !core.companyPrimaryPostalAddress.city ||
    !core.companyPrimaryPostalAddress.postcode
  ) {
    throw new UnprocessableEntityError(
      `Some or all address details (street, city, postcode) are missing for companyId: ${companyId}`,
    );
  }

  const primaryContact = findPrimaryOwner(profile.businessOwnership);

  const getOwnerPhoneNumber = () => {
    const ownerContact = traderInfo.payload.core.contacts?.find(
      (c) => c.roleId === 1,
    );
    const [firstPhone] = traderInfo.payload.core.personalPhoneNumbers;
    const someMatch =
      firstPhone ?? ownerContact?.mobilePhone ?? ownerContact?.phone;

    if (!someMatch) return "";

    return preparePhoneNumber(someMatch);
  };

  switch (core.traderType) {
    case TraderType.LtdCompany:
    case TraderType.Partnership:
    case TraderType.LLP:
      traderTypeExtraDetails = {
        type: OnboardingType.Organisation,
      };
      break;
    case TraderType.SoleTrader:
      traderTypeExtraDetails = {
        type: OnboardingType.SoleProprietorship,
        individual: {
          name: {
            firstName: primaryContact?.firstName ?? "",
            lastName: primaryContact?.lastName ?? "",
          },
          birthData: {
            dateOfBirth:
              primaryContact?.birthdate ?
                dayjs(new Date(primaryContact.birthdate)).format("YYYY-MM-DD")
              : "",
          },
          email: core.personalEmails[0],
          phone: { number: getOwnerPhoneNumber() },
          residentialAddress: mapProfileAddressToOnboardingAddress(
            core.companyPrimaryPostalAddress,
          ),
        },
      };
      break;
    default:
      throw new UnprocessableEntityError(
        `Trader type is not recognised for ${companyId}`,
      );
  }

  return {
    legalName: core.ltdCompanyRegisteredName ?? core.name ?? "",
    vatNumber:
      core.vatRegistrationNo ?
        prepareTaxNumber(core.vatRegistrationNo)
      : undefined,
    registeredAddress: mapProfileAddressToOnboardingAddress(
      core.companyPrimaryPostalAddress,
    ),
    phoneNumber: getOwnerPhoneNumber(),
    ...traderTypeExtraDetails,
  };
};

export const taxInformationTransformer = (
  traderInfo: Static<typeof TradeProfileResponse>,
): Static<typeof TaxInformationSchema> => {
  const { core } = traderInfo.payload;
  const { traderType, vatRegistrationNo } = core;

  return { traderType, vatRegistrationNo };
};

const problemsMessageMap: { [key: string]: string } = {
  "1_300": "Add personal details",
  "1_301": "Upload your ID",
  "1_501": "Complete your company details",
  "2_115": "Connect your bank account",
  "2_123": "Add a company owner",
  "2_124": "Add a signatory",
  "2_125": "Specify your company type",
  "2_151": "Add a controlling person",
  "2_901": "Sign Payment card industry agreement",
  "2_902": "Sign services agreement",
};

const codesRelevantToTraders = new Set(["1_3", "1_5", "2_1", "2_9"]);

/**
 * When onboarding requires further information return list of actions to problems
 * @param capabilities
 * @param onboardingInfo
 */
export const problemsTransformer = ({
  capabilities,
  onboardingInfo,
}: {
  capabilities: Static<typeof getCapabilitiesSchema>;
  onboardingInfo: Static<typeof getOnboardingSchema>;
}): string[] | null => {
  // NOSONAR
  const problemsSet = new Set<string>();

  if (onboardingInfo.status !== OnboardingStatus.RequiresFurtherAction) {
    return null;
  }

  for (const capabilityValue of Object.values(capabilities.data)) {
    if (!capabilityValue?.problems) continue;

    for (const problem of capabilityValue.problems) {
      if (!problem?.verificationErrors) continue;

      for (const verificationError of problem.verificationErrors) {
        if (!verificationError?.remediatingActions) continue;

        for (const remediatingAction of verificationError.remediatingActions) {
          if (
            !remediatingAction.code ||
            !codesRelevantToTraders.has(remediatingAction.code.slice(0, 3))
          )
            continue;

          const code = remediatingAction.code;
          if (!problemsMessageMap[code]) continue;

          problemsSet.add(problemsMessageMap[code]);
        }
      }
    }
  }

  const resultingArray = Array.from(problemsSet);

  return resultingArray.length > 0 ? resultingArray : null;
};

export const onboardingAndCapabilitiesTransformer = ({
  capabilities,
  onboardingInfo,
}: {
  capabilities: Static<typeof getCapabilitiesSchema>;
  onboardingInfo: Static<typeof getOnboardingSchema>;
}): Static<typeof GetOnboardingInformationSchema> => {
  let anyDisabledCapabilities = false;

  const requiredFields = [
    "legalEntityId",
    "accountHolderId",
    "balanceAccountId",
    "businessLineId",
    "storeId",
  ];

  if (onboardingInfo.type === OnboardingType.SoleProprietorship) {
    requiredFields.push("soleProprietorshipId");
  }

  let foundRequiredEntities = 0;

  for (const [key, value] of Object.entries(onboardingInfo)) {
    if (requiredFields.includes(key)) {
      if (value) {
        foundRequiredEntities += 1;
      }
    }
  }

  const reducedProblemsList = problemsTransformer({
    onboardingInfo,
    capabilities,
  });

  const caps = capabilities.data.reduce(
    (capObj, { status, disabled, capabilityKey }) => {
      if (disabled) {
        anyDisabledCapabilities = true;
      }

      return {
        ...capObj,
        [capabilityKey]: {
          status,
          disabled,
        },
      };
    },
    {
      RECEIVE_FROM_PLATFORM_PAYMENTS: null,
      RECEIVE_FROM_BALANCE_ACCOUNT: null,
      SEND_TO_BALANCE_ACCOUNT: null,
      SEND_TO_TRANSFER_INSTRUMENT: null,
      RECEIVE_FROM_TRANSFER_INSTRUMENT: null,
      RECEIVE_PAYMENTS: null,
    },
  );

  const progress = foundRequiredEntities / requiredFields.length;

  return {
    onboarding: {
      progress,
      providedTaxInformation: onboardingInfo.providedTaxInformation,
      status: onboardingInfo.status,
    },
    problems: reducedProblemsList,
    anyDisabledCapabilities,
    capabilities: caps,
  };
};

export const formatStatementReportData = (
  statementResponse: Static<
    typeof schemas.api.payment.activities.getActivitiesStatement.response
  >,
) => {
  const formatDate = (date: Date | null): string => {
    return date === null ? "" : dayjs(date).format("YYYY-MM-DD HH:mm:ss");
  };
  const formatMoney = (amount: number | null): string => {
    return amount === null ? "" : (amount / 100).toFixed(2);
  };
  const formatValue = (value: string | null): string => {
    return value === null ? "" : value;
  };

  return statementResponse.data.map((activity) => {
    return {
      "Payment Requested Date": formatDate(
        activity.paymentRequestedDate as Date,
      ),
      "Paid At": formatDate(activity.paidAt as Date),
      "Customer Name": formatValue(activity.customerName),
      "Customer Email": formatValue(activity.customerEmail),
      "Type": activity.type,
      "Payment Type": formatValue(activity.paymentType),
      "Status": activity.status,
      "Amount (GBP)": formatMoney(activity.amount),
      "Id": activity.id,
      "Job Reference": formatValue(activity.jobReference),
      "Description": formatValue(activity.description),
    };
  });
};
