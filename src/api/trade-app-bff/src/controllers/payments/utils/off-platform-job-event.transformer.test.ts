import { schemas } from "@checkatrade/payment-types";
import { Static } from "@sinclair/typebox";

import { PostCreatePaymentRequestRequestOffPlatformJobSchema } from "../schemas/PostCreatePaymentRequestOffPlatformJob.types";
import * as offPlatformPubSubTransformer from "./off-platform-job-event.transformer";

describe("transform", () => {
  it("should transform the input data correctly", () => {
    const paymentRequest = {
      paymentId: "randomId123456",
      paymentRequest: { companyId: "company123" },
    } as Static<typeof schemas.api.payment.paymentRequest.response>;

    const job = {
      id: "job123",
      description: "Test job",
      categoryId: "cat123",
      postcode: "PO1 1AA",
    } as Static<
      typeof PostCreatePaymentRequestRequestOffPlatformJobSchema
    >["job"];

    const consumer = {
      firstName: "John",
      lastName: "Doe",
      emailAddress: "<EMAIL>",
      phoneNumber: "1234567890",
    };

    const result = offPlatformPubSubTransformer.transform(
      paymentRequest,
      job,
      consumer,
    );

    expect(result).toEqual({
      paymentRequestId: "randomId123456",
      companyId: "company123",
      consumer: {
        firstName: "John",
        lastName: "Doe",
        emailAddress: "<EMAIL>",
        phoneNumber: "1234567890",
      },
      job: {
        id: "job123",
        description: "Test job",
        categoryId: "cat123",
        postcode: "PO1 1AA",
      },
    });
  });

  it("should handle missing optional fields", () => {
    const paymentRequest = {
      paymentId: "randomId123456",
      paymentRequest: { companyId: "company123" },
    } as Static<typeof schemas.api.payment.paymentRequest.response>;

    const job = {
      id: "job123",
      description: "Test job",
      categoryId: "cat123",
      postcode: "PO1 1AA",
    } as Static<
      typeof PostCreatePaymentRequestRequestOffPlatformJobSchema
    >["job"];

    const consumer = {
      firstName: "John",
      lastName: "Doe",
      emailAddress: "",
      phoneNumber: "",
    };

    const result = offPlatformPubSubTransformer.transform(
      paymentRequest,
      job,
      consumer,
    );

    expect(result).toEqual({
      paymentRequestId: "randomId123456",
      companyId: "company123",
      consumer: {
        firstName: "John",
        lastName: "Doe",
        emailAddress: "",
        phoneNumber: "",
      },
      job: {
        id: "job123",
        description: "Test job",
        categoryId: "cat123",
        postcode: "PO1 1AA",
      },
    });
  });
});
