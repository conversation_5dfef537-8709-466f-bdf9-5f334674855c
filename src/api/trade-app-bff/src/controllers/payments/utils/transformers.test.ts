import { UnprocessableEntityError } from "@checkatrade/errors";
import { OnboardingStatus } from "@checkatrade/payment-sdk";
import { Static } from "@sinclair/typebox";
import { defaultsDeep } from "lodash";

import { CapabilityStatus } from "../schemas/GetOnboarding.types";
import { PersonType, TraderType } from "../schemas/Shared.types";
import {
  BusinessOwnershipSchema,
  OnboardingType,
  TradeProfileContactRole,
  TradeProfileResponse,
} from "../schemas/TradeProfileSchema";
import {
  findByPersonType,
  findPrimaryOwner,
  isValidString,
  mapProfileAddressToOnboardingAddress,
  onboardingAndCapabilitiesTransformer,
  preparePhoneNumber,
  prepareTaxNumber,
  problemsTransformer,
  taxInformationTransformer,
  tradeProfileOnboardingTransformer,
} from "./transformers";

type DeepPartial<T> = {
  [K in keyof T]?: T[K] extends object ? DeepPartial<T[K]> : T[K];
};

describe("isValidString", () => {
  it.each([
    ["test", true],
    ["", false],
    [" ", false],
  ])("should check %s and output %s", (input, output) => {
    expect(isValidString(input)).toBe(output);
  });
});

describe("preparePhoneNumber", () => {
  it.each([
    ["07*********", "+447*********"],
    ["+447*********", "+447*********"],
  ])("successfully converts %s to %s", (input, output) => {
    expect(preparePhoneNumber(input)).toBe(output);
  });
});

describe("prepareTaxNumber", () => {
  it.each([
    ["*********", "GB*********"],
    ["GB*********", "GB*********"],
  ])("successfully converts %s to %s", (input: string, output: string) => {
    expect(prepareTaxNumber(input)).toBe(output);
  });
});

describe("tradeProfileOnboardingTransformer", () => {
  const baseTraderInfo: Static<typeof TradeProfileResponse> = {
    payload: {
      core: {
        ltdCompanyRegisteredName: "Test Company Ltd",
        personalPhoneNumbers: ["*********0", "*********2"],
        name: "Bob Builder",
        personalEmails: ["<EMAIL>", "<EMAIL>"],
        vatRegistrationNo: "VAT123456",
        companyPrimaryPostalAddress: {
          postcode: "12345",
          city: "Test City",
          town: "Test Town",
          county: "Test County",
          country: "Test Country",
          street: "123 Test Street",
        },
        traderType: 0,
        contacts: [],
      },
      profile: {
        businessOwnership: [
          {
            id: 0,
            ownerName: "Mr Bob Builder",
            firstName: "Bob",
            lastName: "Builder",
            birthdate: new Date("1990-01-01").toISOString(),
            personTypeId: PersonType.PrimaryOwner,
            inactive: false,
            isDeleted: false,
          },
          {
            id: 0,
            ownerName: "Mr John Wick",
            firstName: "John",
            lastName: "Wick",
            birthdate: new Date("1988-01-01").toISOString(),
            personTypeId: PersonType.PrimaryOwner,
            inactive: false,
            isDeleted: false,
          },
          {
            id: 0,
            ownerName: "Mr John McClane",
            firstName: "John",
            lastName: "McClane",
            birthdate: new Date("1988-01-01").toISOString(),
            personTypeId: PersonType.SecondaryOwner,
            inactive: false,
            isDeleted: false,
          },
        ],
      },
    },
  };

  const extendsBaseTraderInfo = (
    partial: DeepPartial<typeof baseTraderInfo>,
  ) => {
    return defaultsDeep(partial, baseTraderInfo);
  };

  it.each([1, 3, 4])(
    `should transform data for an organisation (traderType  %s)`,
    (traderType) => {
      const traderInfo = extendsBaseTraderInfo({
        payload: {
          core: { traderType },
        },
      });

      const result = tradeProfileOnboardingTransformer({
        traderInfo,
        companyId: 123,
      });

      expect(result).toEqual({
        type: OnboardingType.Organisation,
        legalName: traderInfo.payload.core.ltdCompanyRegisteredName,
        vatNumber: prepareTaxNumber(traderInfo.payload.core.vatRegistrationNo),
        registeredAddress: mapProfileAddressToOnboardingAddress(
          traderInfo.payload.core.companyPrimaryPostalAddress,
        ),
        phoneNumber: preparePhoneNumber(
          traderInfo.payload.core.personalPhoneNumbers[0],
        ),
      });
    },
  );

  it.each([1, 3, 4])(
    `should transform data for an organisation (traderType  %s) with minimum data`,
    (traderType) => {
      const traderInfo = {
        ...baseTraderInfo,
        payload: {
          ...baseTraderInfo.payload,
          core: {
            name: "Fake Company Name",
            personalPhoneNumbers: [],
            personalEmails: [],
            companyPrimaryPostalAddress:
              baseTraderInfo.payload.core.companyPrimaryPostalAddress,
            traderType,
            contacts: [],
          },
        },
      };

      const result = tradeProfileOnboardingTransformer({
        traderInfo,
        companyId: 123,
      });

      expect(result).toEqual({
        type: OnboardingType.Organisation,
        legalName: traderInfo.payload.core.name,
        registeredAddress: mapProfileAddressToOnboardingAddress(
          traderInfo.payload.core.companyPrimaryPostalAddress,
        ),
        phoneNumber: "",
      });
    },
  );

  it("should transform data for SoleTrader (traderType 2)", () => {
    const traderInfo = extendsBaseTraderInfo({
      payload: {
        core: { traderType: 2 },
      },
    });

    const result = tradeProfileOnboardingTransformer({
      traderInfo,
      companyId: 123,
    });

    expect(result).toEqual({
      type: OnboardingType.SoleProprietorship,
      legalName: traderInfo.payload.core.ltdCompanyRegisteredName,
      vatNumber: prepareTaxNumber(traderInfo.payload.core.vatRegistrationNo),
      registeredAddress: mapProfileAddressToOnboardingAddress(
        traderInfo.payload.core.companyPrimaryPostalAddress,
      ),
      phoneNumber: preparePhoneNumber(
        traderInfo.payload.core.personalPhoneNumbers[0],
      ),
      individual: {
        name: {
          firstName: "Bob",
          lastName: "Builder",
        },
        birthData: {
          dateOfBirth: "1990-01-01",
        },
        email: traderInfo.payload.core.personalEmails[0],
        phone: {
          number: preparePhoneNumber(
            traderInfo.payload.core.personalPhoneNumbers[0],
          ),
        },
        residentialAddress: mapProfileAddressToOnboardingAddress(
          traderInfo.payload.core.companyPrimaryPostalAddress,
        ),
      },
    });
  });

  it("should transform data for SoleTrader (traderType 2) with minimum data", () => {
    const traderInfo = {
      ...baseTraderInfo,
      payload: {
        ...baseTraderInfo.payload,
        core: {
          name: "Fake Company Name",
          personalPhoneNumbers: [],
          personalEmails: [],
          companyPrimaryPostalAddress:
            baseTraderInfo.payload.core.companyPrimaryPostalAddress,
          traderType: 2,
          contacts: [],
        },
      },
    };

    const result = tradeProfileOnboardingTransformer({
      traderInfo,
      companyId: 123,
    });

    expect(result).toEqual({
      type: OnboardingType.SoleProprietorship,
      legalName: traderInfo.payload.core.name,
      registeredAddress: mapProfileAddressToOnboardingAddress(
        traderInfo.payload.core.companyPrimaryPostalAddress,
      ),
      phoneNumber: "",
      individual: {
        name: {
          firstName: "Bob",
          lastName: "Builder",
        },
        birthData: {
          dateOfBirth: "1990-01-01",
        },
        phone: {
          number: "",
        },
        residentialAddress: mapProfileAddressToOnboardingAddress(
          traderInfo.payload.core.companyPrimaryPostalAddress,
        ),
      },
    });
  });

  it.each(["phone", "mobilePhone"] as const)(
    "should use contact %s if no phone numbers available",
    (prop) => {
      // Arrange
      const phoneIn = "07*********";
      const phoneOut = preparePhoneNumber(phoneIn);
      const contact = {
        ...{
          firstName: null,
          lastName: null,
          email: null,
          dateOfBirth: null,
          mobilePhone: null,
          phone: null,
          roleId: TradeProfileContactRole.Owner,
        },
        [prop]: phoneIn,
      };
      const traderInfo = extendsBaseTraderInfo({
        payload: {
          core: { traderType: 2, contacts: [contact] },
        },
      });
      traderInfo.payload.core.personalPhoneNumbers = [];

      // Act
      const result = tradeProfileOnboardingTransformer({
        traderInfo,
        companyId: 123,
      });

      // Assert
      expect(result.phoneNumber).toEqual(phoneOut);
    },
  );

  it("should throw UnprocessableEntityError for missing address details", () => {
    const traderInfo = extendsBaseTraderInfo({
      payload: {
        core: {
          companyPrimaryPostalAddress: {},
          traderType: 0,
        },
      },
    });

    expect(() =>
      tradeProfileOnboardingTransformer({
        traderInfo,
        companyId: 123,
      }),
    ).toThrow(UnprocessableEntityError);
  });

  it("should throw UnprocessableEntityError for unknown traderType", () => {
    const traderInfo = extendsBaseTraderInfo({
      payload: {
        core: { traderType: 0 },
      },
    });

    expect(() =>
      tradeProfileOnboardingTransformer({
        traderInfo,
        companyId: 123,
      }),
    ).toThrow(UnprocessableEntityError);
  });
});

describe("mapProfileAddressToOnboardingAddress", () => {
  it("should return address in correct format", () => {
    const result = mapProfileAddressToOnboardingAddress({
      postcode: "12345",
      city: "Test City",
      town: "Test Town",
      county: "Test County",
      country: "Test Country",
      street: "123 Test Street",
    });

    expect(result.street).toBe("123 Test Street");
    expect(result.city).toBe("Test City");
    expect(result.postalCode).toBe("12345");
    expect(result.country).toBe("GB");
  });

  it("should return address in correct format with minimum data", () => {
    const result = mapProfileAddressToOnboardingAddress({
      postcode: "12345",
      city: "Test City",
      country: "Test Country",
      street: "123 Test Street",
    });

    expect(result.street).toBe("123 Test Street");
    expect(result.city).toBe("Test City");
    expect(result.postalCode).toBe("12345");
    expect(result.country).toBe("GB");
  });

  it("should map all address fields correctly when all fields are present", () => {
    const input = {
      postcode: "SW1A 1AA",
      city: "London",
      town: "Westminster",
      county: "Greater London",
      country: "United Kingdom",
      street: "10 Downing Street",
    };

    const result = mapProfileAddressToOnboardingAddress(input);

    expect(result).toEqual({
      postalCode: "SW1A 1AA",
      city: "London",
      country: "GB",
      street: "10 Downing Street",
    });
  });

  it("should handle undefined optional fields", () => {
    const input = {
      postcode: "SW1A 1AA",
      city: "London",
      street: "10 Downing Street",
      town: undefined,
      county: undefined,
      country: undefined,
    };

    const result = mapProfileAddressToOnboardingAddress(input);

    expect(result).toEqual({
      postalCode: "SW1A 1AA",
      city: "London",
      country: "GB",
      street: "10 Downing Street",
    });
  });

  it("should handle missing optional fields", () => {
    const input = {
      postcode: "SW1A 1AA",
      city: "London",
      street: "10 Downing Street",
    };

    const result = mapProfileAddressToOnboardingAddress(input);

    expect(result).toEqual({
      postalCode: "SW1A 1AA",
      city: "London",
      country: "GB",
      street: "10 Downing Street",
    });
  });

  it("should always return GB as country code regardless of input country", () => {
    const inputs = [
      { country: "United Kingdom" },
      { country: "England" },
      { country: "Great Britain" },
      { country: undefined },
      {},
    ];

    inputs.forEach((countryInput) => {
      const input = {
        postcode: "SW1A 1AA",
        city: "London",
        street: "10 Downing Street",
        ...countryInput,
      };

      const result = mapProfileAddressToOnboardingAddress(input);
      expect(result.country).toBe("GB");
    });
  });
});

describe("onboardingAndCapabilitiesTransformer", () => {
  const mockBaseCapability = {
    companyId: "1234",
    id: "capabilityId",
    disabled: false,
    status: CapabilityStatus.Valid,
    problems: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastEventDate: null,
  };

  const mockCapabilityData = [
    "receiveFromPlatformPayments",
    "receiveFromBalanceAccount",
    "sendToBalanceAccount",
    "sendToTransferInstrument",
  ].map((capabilityKey) => ({ ...mockBaseCapability, capabilityKey }));

  const missingCapability = {
    ...mockBaseCapability,
    capabilityKey: "receivePayments",
  };

  const mockCapabilities = {
    data: mockCapabilityData,
  };

  const mockOnboardingInfo = {
    providedTaxInformation: false,
    status: OnboardingStatus.Enrolled,
    type: OnboardingType.Organisation,
    soleProprietorshipId: null,
    legalEntityId: "legalEntityId",
    accountHolderId: "accountHolderId",
    balanceAccountId: "balanceAccountId",
    businessLineId: "businessLineId",
    storeId: null,
  };

  it("should return progress < 1 and status Start when not all required fields are filled", () => {
    const result = onboardingAndCapabilitiesTransformer({
      capabilities: mockCapabilities,
      onboardingInfo: mockOnboardingInfo,
    });

    expect(result.onboarding.progress).toBeLessThan(1);
    expect(result.onboarding.status).toBe(OnboardingStatus.Enrolled);
    expect(result.anyDisabledCapabilities).toBe(false);
  });

  it("should return progress = 1 and status ReVerify when all required fields are filled but some capabilities are disabled", () => {
    const completeOnboardingInfo = {
      ...mockOnboardingInfo,
      storeId: "storeId",
    };

    const capabiltiesWithOneDisabled = {
      data: [
        ...mockCapabilityData,
        { ...missingCapability, disabled: true, problems: null },
      ],
    };

    const result = onboardingAndCapabilitiesTransformer({
      capabilities: capabiltiesWithOneDisabled,
      onboardingInfo: completeOnboardingInfo,
    });

    expect(result.onboarding.progress).toBe(1);
    expect(result.onboarding.status).toBe(OnboardingStatus.Enrolled);
    expect(result.anyDisabledCapabilities).toBe(true);
  });

  it("should return progress = 1 and status Ready when all required fields are filled and no capabilities are disabled", () => {
    const completeOnboardingInfo = {
      ...mockOnboardingInfo,
      storeId: "storeId",
    };

    const completeEnabledCapabilities = {
      data: [...mockCapabilityData, missingCapability],
    };

    const result = onboardingAndCapabilitiesTransformer({
      capabilities: completeEnabledCapabilities,
      onboardingInfo: completeOnboardingInfo,
    });

    expect(result.onboarding.progress).toBe(1);
    expect(result.onboarding.status).toBe(OnboardingStatus.Enrolled);
    expect(result.anyDisabledCapabilities).toBe(false);
  });

  it("should include soleProprietorshipId in required fields when onboarding type is Sole Proprietorship", () => {
    const soleProprietorshipInfo = {
      ...mockOnboardingInfo,
      type: OnboardingType.SoleProprietorship,
      soleProprietorshipId: "soleProprietorshipId",
      storeId: "storeId",
    };

    const completeEnabledCapabilities = {
      data: [...mockCapabilityData, missingCapability],
    };

    const result = onboardingAndCapabilitiesTransformer({
      capabilities: completeEnabledCapabilities,
      onboardingInfo: soleProprietorshipInfo,
    });

    expect(result.anyDisabledCapabilities).toBe(false);
    expect(result.onboarding.progress).toBe(1);
    expect(result.onboarding.status).toBe(OnboardingStatus.Enrolled);
  });
});

describe("findByPersonType", () => {
  const businessOwners: Static<typeof BusinessOwnershipSchema>[] = [
    {
      firstName: "Bob",
      lastName: "Builder",
      birthdate: new Date("1990-01-01").toISOString(),
      personTypeId: PersonType.PrimaryOwner,
      inactive: false,
      isDeleted: false,
    },
    {
      firstName: "John",
      lastName: "Wick",
      birthdate: new Date("1988-01-01").toISOString(),
      personTypeId: PersonType.PrimaryOwner,
      inactive: false,
      isDeleted: false,
    },
    {
      firstName: "John",
      lastName: "McClane",
      birthdate: new Date("1988-01-01").toISOString(),
      personTypeId: PersonType.SecondaryOwner,
      inactive: false,
      isDeleted: false,
    },
  ];

  it("should find the primary owner with personTypeId 1", () => {
    const result = findByPersonType(businessOwners, PersonType.PrimaryOwner);
    expect(result?.firstName).toBe("Bob");
    expect(result?.lastName).toBe("Builder");
  });

  it("should find secondary owner with personType SecondaryOwner", () => {
    const result = findByPersonType(businessOwners, PersonType.SecondaryOwner);
    expect(result?.firstName).toBe("John");
    expect(result?.lastName).toBe("McClane");
  });

  it("should return undefined if no owner matches personTypeId", () => {
    const emptyOwners: Static<typeof BusinessOwnershipSchema>[] = [];
    const result = findByPersonType(emptyOwners, PersonType.PrimaryOwner);
    expect(result).toBeUndefined();
  });
});

describe("findPrimaryOwner", () => {
  const businessOwners: Static<typeof BusinessOwnershipSchema>[] = [
    {
      firstName: "Bob",
      lastName: "Builder",
      birthdate: new Date("1990-01-01").toISOString(),
      personTypeId: PersonType.PrimaryOwner,
      inactive: false,
      isDeleted: false,
    },
    {
      firstName: "John",
      lastName: "Wick",
      birthdate: new Date("1988-01-01").toISOString(),
      personTypeId: PersonType.PrimaryOwner,
      inactive: false,
      isDeleted: false,
    },
    {
      firstName: "John",
      lastName: "McClane",
      birthdate: new Date("1988-01-01").toISOString(),
      personTypeId: PersonType.SecondaryOwner,
      inactive: false,
      isDeleted: false,
    },
  ];

  it("should find the primary owner", () => {
    const result = findPrimaryOwner(businessOwners);
    expect(result?.firstName).toBe("Bob");
    expect(result?.lastName).toBe("Builder");
    expect(result?.birthdate).toStrictEqual(
      new Date("1990-01-01").toISOString(),
    );
    expect(result?.inactive).toBe(false);
    expect(result?.isDeleted).toBe(false);
  });

  it.each([
    ["", "Builder", false, false],
    ["Bob", "", false, false],
    ["Bob", "Builder", true, false],
    ["Bob", "Builder", false, true],
  ])(
    "should find the secondary owner if no primary owner is valid (Invalid: firstName='%s', lastName='%s', inactive=%s, isDeleted=%s)",
    (firstName, lastName, inactive, isDeleted) => {
      const businessOwners: Static<typeof BusinessOwnershipSchema>[] = [
        {
          firstName: firstName,
          lastName: lastName,
          birthdate: new Date("1990-01-01").toISOString(),
          personTypeId: PersonType.PrimaryOwner,
          inactive: inactive,
          isDeleted: isDeleted,
        },
        {
          firstName: "John",
          lastName: "McClane",
          birthdate: new Date("1988-01-01").toISOString(),
          personTypeId: PersonType.SecondaryOwner,
          inactive: false,
          isDeleted: false,
        },
      ];

      const result = findPrimaryOwner(businessOwners);
      expect(result?.firstName).toBe("John");
      expect(result?.lastName).toBe("McClane");
      expect(result?.birthdate).toStrictEqual(
        new Date("1988-01-01").toISOString(),
      );
      expect(result?.inactive).toBe(false);
      expect(result?.isDeleted).toBe(false);
    },
  );

  it("should return undefined if no valid owner exists", () => {
    const result = findPrimaryOwner([]);
    expect(result).toBeUndefined();
  });

  it("should ignore deleted or inactive owners", () => {
    const businessOwners: Static<typeof BusinessOwnershipSchema>[] = [
      {
        firstName: "Bob",
        lastName: "Builder",
        birthdate: new Date("1990-01-01").toISOString(),
        personTypeId: PersonType.PrimaryOwner,
        inactive: true,
        isDeleted: false,
      },
      {
        firstName: "John",
        lastName: "McClane",
        birthdate: new Date("1988-01-01").toISOString(),
        personTypeId: PersonType.SecondaryOwner,
        inactive: false,
        isDeleted: true,
      },
    ];
    const result = findPrimaryOwner(businessOwners);
    expect(result).toBeUndefined();
  });
});

describe("problemsTransformer", () => {
  const mockCapabilityProblem = [
    {
      entity: {
        id: "*************************",
        type: "LegalEntity",
      },
      verificationErrors: [
        {
          code: "2_902",
          message: "Terms Of Service forms are not accepted.",
          remediatingActions: [
            {
              code: "2_902",
              message: "Accept TOS",
            },
          ],
          type: "invalidInput",
        },
      ],
    },
  ];
  const mockCapabilityMultipleProblems = [
    {
      entity: {
        id: "*************************",
        type: "LegalEntity",
      },
      verificationErrors: [
        {
          code: "2_8036",
          message: "'bankAccount' was missing.",
          remediatingActions: [
            {
              code: "2_115",
              message: "Add bank account",
            },
          ],
          type: "dataMissing",
        },
      ],
    },
    ...mockCapabilityProblem,
  ];

  const mockOnboardingInfo = {
    providedTaxInformation: false,
    status: OnboardingStatus.RequiresFurtherAction,
    type: OnboardingType.Organisation,
    soleProprietorshipId: null,
    legalEntityId: "legalEntityId",
    accountHolderId: "accountHolderId",
    balanceAccountId: "balanceAccountId",
    businessLineId: "businessLineId",
    storeId: null,
  };

  const mockBaseCapability = {
    companyId: "1234",
    id: "capabilityId",
    disabled: false,
    status: CapabilityStatus.Valid,
    problems: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastEventDate: null,
  };

  const mockCapabilityData = [
    "receiveFromPlatformPayments",
    "receiveFromBalanceAccount",
    "sendToBalanceAccount",
    "sendToTransferInstrument",
  ].map((capabilityKey) => ({ ...mockBaseCapability, capabilityKey }));

  const capabiltiesWithOneProblematicData = [
    ...mockCapabilityData,
    {
      ...mockBaseCapability,
      status: CapabilityStatus.Invalid,
      capabilityKey: "receivePayments",
      problems: mockCapabilityProblem,
    },
  ];

  it("should return null if onboarding is not of type RequiresFurtherAction", () => {
    const result = problemsTransformer({
      onboardingInfo: {
        ...mockOnboardingInfo,
        status: OnboardingStatus.ReadyForPayment,
      },
      capabilities: { data: capabiltiesWithOneProblematicData },
    });

    // Assert
    expect(result).toEqual(null);
  });

  it("should be able to return one problem", () => {
    const result = problemsTransformer({
      onboardingInfo: mockOnboardingInfo,
      capabilities: {
        data: capabiltiesWithOneProblematicData,
      },
    });

    // Assert
    expect(result).toStrictEqual(["Sign services agreement"]);
  });

  it("should be able to return one problem if two capabilities have the same error", () => {
    const result = problemsTransformer({
      onboardingInfo: mockOnboardingInfo,
      capabilities: {
        data: [
          ...capabiltiesWithOneProblematicData,
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "sendToTransferInstrument",
            problems: mockCapabilityProblem,
          },
        ],
      },
    });

    // Assert
    expect(result).toStrictEqual(["Sign services agreement"]);
  });

  it("should be able to return multiple differing problems from the same capability", () => {
    const result = problemsTransformer({
      onboardingInfo: mockOnboardingInfo,
      capabilities: {
        data: [
          ...mockCapabilityData,
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "receivePayments",
            problems: mockCapabilityMultipleProblems,
          },
        ],
      },
    });

    // Assert
    expect(result).toStrictEqual([
      "Connect your bank account",
      "Sign services agreement",
    ]);
  });

  it("should be able to return multiple differing problems from different capabilities", () => {
    const result = problemsTransformer({
      onboardingInfo: mockOnboardingInfo,
      capabilities: {
        data: [
          ...mockCapabilityData,
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "receivePayments",
            problems: mockCapabilityMultipleProblems,
          },
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "sendToTransferInstrument",
            problems: mockCapabilityMultipleProblems,
          },
        ],
      },
    });

    // Assert
    expect(result).toStrictEqual([
      "Connect your bank account",
      "Sign services agreement",
    ]);
  });

  it("should not include a remediating which is doesn't match a general code check", () => {
    const result = problemsTransformer({
      onboardingInfo: mockOnboardingInfo,
      capabilities: {
        data: [
          ...mockCapabilityData,
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "receivePayments",
            problems: mockCapabilityProblem,
          },
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "sendToTransferInstrument",
            problems: [
              {
                entity: {
                  id: "*************************",
                  type: "LegalEntity",
                },
                verificationErrors: [
                  {
                    code: "1_90",
                    message: "Industry couldn't be verified",
                    remediatingActions: [
                      {
                        code: "1_9000",
                        message: "Upload a proof of industry",
                      },
                    ],
                    type: "invalidInput",
                  },
                ],
              },
            ],
          },
        ],
      },
    });

    // Assert
    expect(result).toStrictEqual(["Sign services agreement"]);
  });

  it("should not include a remediating which is matches a general code check, but isn't in the list", () => {
    const result = problemsTransformer({
      onboardingInfo: mockOnboardingInfo,
      capabilities: {
        data: [
          ...mockCapabilityData,
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "receivePayments",
            problems: mockCapabilityProblem,
          },
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "sendToTransferInstrument",
            problems: [
              ...mockCapabilityProblem,
              {
                entity: {
                  id: "*************************",
                  type: "LegalEntity",
                },
                verificationErrors: [
                  {
                    code: "1_3008",
                    message: "The ID document image was incomplete",
                    remediatingActions: [
                      {
                        code: "1_3003",
                        message: "Upload a different image of the ID document",
                      },
                    ],
                    type: "dataMissing",
                  },
                ],
              },
            ],
          },
        ],
      },
    });

    // Assert
    expect(result).toStrictEqual(["Sign services agreement"]);
  });

  it("should return null if the no matching problems are found", () => {
    const result = problemsTransformer({
      onboardingInfo: mockOnboardingInfo,
      capabilities: {
        data: [
          ...mockCapabilityData,
          {
            ...mockBaseCapability,
            status: CapabilityStatus.Invalid,
            capabilityKey: "receivePayments",
            problems: [
              {
                entity: {
                  id: "*************************",
                  type: "LegalEntity",
                },
                verificationErrors: [
                  {
                    code: "1_90",
                    message: "Industry couldn't be verified",
                    remediatingActions: [
                      {
                        code: "1_9000",
                        message: "Upload a proof of industry",
                      },
                    ],
                    type: "invalidInput",
                  },
                ],
              },
            ],
          },
        ],
      },
    });

    // Assert
    expect(result).toStrictEqual(null);
  });
});

describe("taxInformationTransformer", () => {
  it("should return trader type and VAT number when VAT number is present", () => {
    const traderInfo = {
      payload: {
        core: {
          traderType: TraderType.LtdCompany,
          vatRegistrationNo: "GB*********",
        },
      },
    } as Static<typeof TradeProfileResponse>;

    const result = taxInformationTransformer(traderInfo);

    expect(result).toEqual({
      traderType: TraderType.LtdCompany,
      vatRegistrationNo: "GB*********",
    });
  });

  it("should return only trader type when VAT number is missing", () => {
    const traderInfo = {
      payload: {
        core: {
          traderType: TraderType.SoleTrader,
          vatRegistrationNo: undefined,
        },
      },
    } as Static<typeof TradeProfileResponse>;

    const result = taxInformationTransformer(traderInfo);

    expect(result).toEqual({
      traderType: TraderType.SoleTrader,
      vatRegistrationNo: undefined,
    });
  });

  it.each([
    [TraderType.LtdCompany, "123456"],
    [TraderType.Partnership, undefined],
    [TraderType.LLP, "GB987654"],
    [TraderType.SoleTrader, null],
  ])(
    "should handle traderType %s with vatNumber %s",
    (traderType, vatRegistrationNo) => {
      const traderInfo = {
        payload: {
          core: {
            traderType,
            vatRegistrationNo,
          },
        },
      } as Static<typeof TradeProfileResponse>;

      const result = taxInformationTransformer(traderInfo);

      expect(result).toEqual({
        traderType,
        vatRegistrationNo,
      });
    },
  );
});
