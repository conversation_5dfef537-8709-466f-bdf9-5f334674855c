import { schemas } from "@checkatrade/payment-types";
import { Static } from "@sinclair/typebox";

import { OffPlatformJobCreatedEventType } from "../../../lib/api-common";
import { PostCreatePaymentRequestRequestOffPlatformJobSchema } from "../schemas/PostCreatePaymentRequestOffPlatformJob.types";

type PaymentRequest = Static<
  typeof schemas.api.payment.paymentRequest.response
>;
type Job = Static<
  typeof PostCreatePaymentRequestRequestOffPlatformJobSchema
>["job"];
type Consumer = Static<
  typeof PostCreatePaymentRequestRequestOffPlatformJobSchema
>["consumer"];

export const transform = (
  { paymentId, paymentRequest: { companyId } }: PaymentRequest,
  job: Job,
  { firstName, lastName, emailAddress, phoneNumber }: Consumer,
): OffPlatformJobCreatedEventType => {
  return {
    paymentRequestId: paymentId,
    companyId,
    consumer: {
      firstName,
      lastName,
      emailAddress,
      phoneNumber,
    },
    job,
  };
};
