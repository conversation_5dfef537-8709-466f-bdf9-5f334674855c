import { paymentSDK } from "@checkatrade/payment-sdk";
import { Nullable } from "@checkatrade/schemas";
import { Type } from "@sinclair/typebox";

import { PersonType, TraderType } from "./Shared.types";

const individualSchema = paymentSDK.schemas.api.payment.onboarding.individual;

// to come from payments when SDK exports it
export enum OnboardingType {
  SoleProprietorship = "soleProprietorship",
  Organisation = "organisation",
}

export const TradeProfileSchema = Type.Object({
  type: Type.Enum(OnboardingType),
  legalName: Type.String(),
  vatNumber: Type.Optional(Type.String()),
  registeredAddress: Type.Object({
    city: Type.String(),
    country: Type.String(),
    postalCode: Type.String(),
    street: Type.String(),
  }),
  phoneNumber: Type.String(),
  individual: Type.Optional(individualSchema),
});

export const BusinessOwnershipSchema = Type.Object({
  id: Type.Optional(Nullable(Type.Integer())),
  ownerName: Type.Optional(Nullable(Type.String())),
  firstName: Type.String(),
  lastName: Type.String(),
  birthdate: Type.String({ format: "date-time" }),
  personTypeId: Type.Enum(PersonType),
  inactive: Type.Boolean(),
  isDeleted: Type.Boolean(),
});

export const TradeProfileResponseAddress = Type.Object({
  postcode: Type.Optional(Type.String()),
  city: Type.Optional(Type.String()),
  town: Type.Optional(Type.String()),
  county: Type.Optional(Type.String()),
  country: Type.Optional(Type.String()),
  street: Type.Optional(Type.String()),
});

export enum TradeProfileContactRole {
  Owner = 1,
  AdminContact = 2,
  Director = 3,
}

export const TradeProfileContactSchema = Type.Object({
  firstName: Nullable(Type.String()),
  lastName: Nullable(Type.String()),
  email: Nullable(Type.String()),
  dateOfBirth: Nullable(Type.String()),
  mobilePhone: Nullable(Type.String()),
  phone: Nullable(Type.String()),
  roleId: Nullable(Type.Enum(TradeProfileContactRole)),
});

// for sole trader map to individual.residentialAddress, for org have as registeredAddress
// sole trader will probably have both fields populated
// org won't have individual given back
export const TradeProfileResponse = Type.Object({
  payload: Type.Object({
    core: Type.Object({
      name: Type.Optional(Type.String()),
      ltdCompanyRegisteredName: Type.Optional(Type.String()),
      personalPhoneNumbers: Type.Array(Type.String()),
      personalEmails: Type.Array(Type.String()),
      vatRegistrationNo: Type.Optional(Type.String()),
      companyPrimaryPostalAddress: TradeProfileResponseAddress,
      traderType: Type.Enum(TraderType),
      contacts: Nullable(Type.Array(TradeProfileContactSchema)),
    }),
    profile: Type.Object({
      businessOwnership: Type.Array(BusinessOwnershipSchema),
    }),
  }),
});
