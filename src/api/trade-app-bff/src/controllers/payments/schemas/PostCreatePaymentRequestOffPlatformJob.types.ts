import { schemas } from "@checkatrade/payment-types";
import { Type } from "@sinclair/typebox";

export const PostCreatePaymentRequestRequestOffPlatformJobSchema = Type.Object({
  paymentRequest: schemas.api.payment.paymentRequest.request,
  job: Type.Object({
    categoryId: Type.String(),
    description: Type.String({ minLength: 10, maxLength: 500 }),
    postcode: Type.String(),
  }),
  consumer: Type.Object({
    firstName: Type.String(),
    lastName: Type.String(),
    phoneNumber: Type.String(),
    emailAddress: Type.String(),
  }),
});
