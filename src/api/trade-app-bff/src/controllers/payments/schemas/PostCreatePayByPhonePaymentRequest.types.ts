import { schemas } from "@checkatrade/payment-types";
import { Type } from "@sinclair/typebox";

export const PostCreatePayByPhonePaymentRequestBodySchema = Type.Composite([
  schemas.api.payment.payByPhone.paymentRequest.body,
  Type.Object({
    job: Type.Optional(
      Type.Object({
        categoryId: Type.String(),
        description: Type.String({ minLength: 10, maxLength: 500 }),
        postcode: Type.String(),
      }),
    ),
    consumer: Type.Optional(
      Type.Object({
        firstName: Type.String(),
        lastName: Type.String(),
        phoneNumber: Type.String(),
        emailAddress: Type.String(),
      }),
    ),
  }),
]);
