import { PaymentRequestStatus } from "@checkatrade/payment-sdk";
import { PaymentType } from "@checkatrade/payment-types";
import { Nullable } from "@checkatrade/schemas";
import { Type } from "@sinclair/typebox";

export const GetPaymentRequestResponseSchema = Type.Object({
  id: Type.String(),
  status: Type.Enum(PaymentRequestStatus),
  paymentType: Type.Optional(Nullable(Type.Enum(PaymentType))),
  reference: Type.String(),
  description: Type.Optional(Nullable(Type.String())),
  jobReference: Type.Optional(Nullable(Type.String())),
  requestCreatedAt: Type.String({ format: "date-time" }),
  totalAmount: Type.Number(),
  commissionAmount: Type.Number(),
  tradeAmount: Type.Number(),
  companyId: Type.String(),
  jobId: Type.Optional(Type.String()),
  jobTitle: Type.String(),
  opportunityId: Type.Optional(Type.String()),
  consumerId: Type.Optional(Type.String()),
  consumerName: Type.String(),
  consumerEmail: Type.String(),
  paymentLinkId: Type.String(),
  paymentUrl: Type.String(),
  checkoutUrl: Type.Optional(Type.String()),
  expiryDate: Type.String({ format: "date-time" }),
  dueDate: Type.String({ format: "date-time" }),
  currency: Type.String(),
});
