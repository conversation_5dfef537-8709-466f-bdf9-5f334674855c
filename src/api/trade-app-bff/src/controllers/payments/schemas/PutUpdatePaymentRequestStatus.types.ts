import { PaymentRequestStatus } from "@checkatrade/payment-sdk";
import { Nullable } from "@checkatrade/schemas";
import { type TSchema, type TString, Type } from "@sinclair/typebox";

export const PutUpdatePaymentRequestStatusParamsSchema = Type.Object({
  paymentLinkId: Type.String(),
});

export const dateKind = Symbol("DateKind");

export const safeDate = Type.String({ format: "date-time" }) as TString | TDate;

/**
 * Reusable Typebox convenience type for handling converting dates to strings
 */
export interface TDate extends TSchema {
  type: "string";
  $static: Date;
  kind: typeof dateKind;
}

const PAYMENT_DETAIL_SCHEMA = Type.Object({
  id: Type.String({ format: "uuid" }),
  paymentLinkId: Type.String(),
  pspReference: Type.String(),
  paymentMethod: Type.String(),
  cardSummary: Type.String(),
  currency: Type.String(),
  amount: Type.Integer(),
  reason: Type.String(),
  eventCode: Type.Integer(),
  isSuccess: Type.Boolean(),
  createdAt: safeDate,
  updatedAt: safeDate,
});

export const PAYMENT_REQUEST_SCHEMA = Type.Object({
  id: Type.String({ format: "uuid" }),
  currency: Type.String(),
  totalAmount: Type.Integer(),
  tradeAmount: Type.Integer(),
  commissionAmount: Type.Integer(),
  companyId: Type.String(),
  jobId: Type.String({ format: "uuid" }),
  opportunityId: Type.String(),
  consumerId: Type.String({ format: "uuid" }),
  consumerName: Type.String(),
  consumerEmail: Type.String({ format: "email" }),
  paymentLinkId: Type.String(),
  paymentUrl: Nullable(Type.String()),
  reference: Type.String(),
  status: Type.Enum(PaymentRequestStatus),
  expiryDate: safeDate,
  dueDate: safeDate,
  createdAt: safeDate,
  updatedAt: safeDate,
  paymentDetail: Type.Optional(Type.Array(PAYMENT_DETAIL_SCHEMA)),
});
