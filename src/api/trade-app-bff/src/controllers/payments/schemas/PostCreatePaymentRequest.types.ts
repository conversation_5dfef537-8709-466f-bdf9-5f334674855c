import { schemas } from "@checkatrade/payment-types";
import { Type } from "@sinclair/typebox";

export const PostCreatePaymentRequestRequestSchema = Type.Object({
  paymentRequest: Type.Composite([
    Type.Omit(schemas.api.payment.paymentRequest.request, [
      "jobId",
      "opportunityId",
    ]),
    Type.Object({
      jobId: Type.String({ format: "uuid" }),
      opportunityId: Type.String(),
    }),
  ]),
});
