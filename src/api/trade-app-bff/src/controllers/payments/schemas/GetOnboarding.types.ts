import {
  OnboardingStatus,
  OnboardingType,
  paymentSDK,
} from "@checkatrade/payment-sdk";
import { Nullable } from "@checkatrade/schemas";
import { Type } from "@sinclair/typebox";

const individualSchema = paymentSDK.schemas.api.payment.onboarding.individual;

export enum CapabilityStatus {
  Invalid = "INVALID",
  Pending = "PENDING",
  Rejected = "REJECTED",
  Valid = "VALID",
}

export const CapabilityStatusExtendedSchema = Type.Object({
  status: Type.Enum(CapabilityStatus),
  disabled: Type.Boolean(),
});

export const GetOnboardingInformationSchema = Type.Object({
  onboarding: Type.Object({
    providedTaxInformation: Type.Boolean(),
    progress: Type.Number(),
    status: Type.Enum(OnboardingStatus),
  }),
  problems: Nullable(Type.Array(Type.String())),
  anyDisabledCapabilities: Type.Boolean(),
  capabilities: Type.Object({
    RECEIVE_FROM_PLATFORM_PAYMENTS: Nullable(CapabilityStatusExtendedSchema),
    RECEIVE_FROM_BALANCE_ACCOUNT: Nullable(CapabilityStatusExtendedSchema),
    SEND_TO_BALANCE_ACCOUNT: Nullable(CapabilityStatusExtendedSchema),
    SEND_TO_TRANSFER_INSTRUMENT: Nullable(CapabilityStatusExtendedSchema),
    RECEIVE_FROM_TRANSFER_INSTRUMENT: Nullable(CapabilityStatusExtendedSchema),
    RECEIVE_PAYMENTS: Nullable(CapabilityStatusExtendedSchema),
  }),
});

export const GetOnboardingResponseSchema = Type.Object({
  type: Type.Enum(OnboardingType),
  legalName: Type.String(),
  vatNumber: Type.String(),
  registeredAddress: Type.Object({
    city: Type.String(),
    country: Type.String(),
    postalCode: Type.String(),
    street: Type.String(),
  }),
  phoneNumber: Type.String(),
  individual: Type.Optional(individualSchema),
});
