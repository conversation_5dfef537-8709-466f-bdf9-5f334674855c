import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { COUNTRY_OF_GOVERNING_LAW, WEB_ADDRESS } from "./constants";
import ContentAPI from "./contentapi";
import { OnboardResponse } from "./schemas/Onboard.types";
import { tradeProfileOnboardingTransformer } from "./utils/transformers";

export enum ClientType {
  Web = "WEB",
  Mobile = "MOBILE",
}

const onboardQuery = Type.Object({
  clientType: Type.Enum(ClientType),
});

const onboardBody = Type.Object({
  primaryCategoryId: Type.Number(),
});

export const postOnboard: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof onboardQuery>;
  Body: Static<typeof onboardBody>;
  Reply: Static<typeof OnboardResponse>;
}> = {
  method: "POST",
  url: `/payments/onboard`,
  schema: {
    summary: "Fetch profile data and onboard",
    body: onboardBody,
    querystring: onboardQuery,
    headers: companyIdHeader,
    response: { 200: OnboardResponse },
    description:
      "Retrieves data from ContentApi which is used to seed Adyen, then returns onboard information",
    operationId: "postOnboard",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { clientType } = req.query;
    const { primaryCategoryId } = req.body;

    const traderInfo = await ContentAPI.getProfileData(companyId);

    const onboardingInfo = tradeProfileOnboardingTransformer({
      traderInfo,
      companyId,
    });

    const { hostedOnboardingUrl } =
      await paymentSDK.trade.onboarding.onboardSoleAndOrganisation({
        ...onboardingInfo,
        companyId: companyId.toString(),
        countryOfGoverningLaw: COUNTRY_OF_GOVERNING_LAW,
        primaryCategoryId,
        webAddress: WEB_ADDRESS,
        clientType,
      });

    return res.send({
      hostedOnboardingUrl,
      traderType: traderInfo.payload.core.traderType,
      ...onboardingInfo,
    });
  },
};
