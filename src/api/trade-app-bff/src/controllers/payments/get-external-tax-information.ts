import { authTrade } from "@checkatrade/auth-trade";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { paymentSDK } from "@checkatrade/payment-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import type { Static } from "@sinclair/typebox";

export const getExternalTaxInformation: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: Static<
    typeof paymentSDK.schemas.api.payment.onboarding.getExternalTaxInformationResponse
  >;
}> = {
  method: "get",
  url: `/payments/external-tax-information`,
  schema: {
    summary: "Fetch trade external tax information",
    headers: companyIdHeader,
    response: {
      200: paymentSDK.schemas.api.payment.onboarding
        .getExternalTaxInformationResponse,
    },
    description:
      "Retrieves external tax information from the KYC Service via the Payment API",
    operationId: "getExternalTaxInformation",
    tags: ["Payments"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const externalTaxInformation =
      await paymentSDK.trade.onboarding.getExternalTaxInformation(
        String(companyId),
      );

    return res.send(externalTaxInformation);
  },
};
