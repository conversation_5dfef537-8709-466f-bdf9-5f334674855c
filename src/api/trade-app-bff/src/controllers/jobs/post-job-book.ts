import { authTrade } from "@checkatrade/auth-trade";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { fetchMediaAttachments } from "../../lib/api-common";
import { formatJobDetailsResponse } from "./job.response";
import {
  type JobDetailsResponse,
  type JobParams,
  jobDetailsResponseSchema,
  jobParams,
} from "./job.schema";

export const postJobBook: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: JobDetailsResponse;
}> = {
  method: "POST",
  url: "/jobs/:jobId/book",
  schema: {
    summary: "Book opportunity",
    description:
      "Trade marks job as booked. Doesn't change status, but instead adds a flag under TradeMarkedBooked",
    operationId: "postJobBook",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: jobDetailsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const {
      params: { jobId },
    } = req;
    const { token, companyId } = await authTrade(req);

    const job = await jobsSDK.trade(token, companyId).bookJob(jobId);

    const [consumer, mediaAttachments] = await Promise.all([
      consumerSDK.trade(token).getConsumer(job.consumerId),
      fetchMediaAttachments(job.mediaAttachmentIds ?? []),
    ]);

    return res.send(
      await formatJobDetailsResponse(job, consumer, mediaAttachments),
    );
  },
};
