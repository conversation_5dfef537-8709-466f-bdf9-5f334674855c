import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { TradeUnreadJobsCountResponse, jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

export const getUnreadJobsCount: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: TradeUnreadJobsCountResponse;
}> = {
  method: "GET",
  url: "/jobs/count-unread",
  schema: {
    summary: "Unread jobs count",
    description: "Get count of jobs not yet viewed by trade",
    operationId: "getUnreadJobsCountJobs",
    tags: ["Job"],
    headers: companyIdHeader,
    response: {
      200: jobsSDK.schemas.trade.getUnreadJobsCount.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const response = await jobsSDK.trade(token, companyId).getUnreadJobsCount();

    return res.send(response);
  },
};
