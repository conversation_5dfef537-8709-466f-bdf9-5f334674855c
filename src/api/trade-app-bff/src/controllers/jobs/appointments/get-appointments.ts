import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  AppointmentQueryActive,
  schedulingSDK,
} from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { type JobParams, jobParams } from "../job.schema";

const appointmentsQuery = Type.Omit(
  schedulingSDK.schemas.api.trade.getAppointments.query,
  ["jobId"],
);

export const getJobAppointments: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Querystring: Static<typeof appointmentsQuery>;
  Reply: Static<
    typeof schedulingSDK.schemas.api.trade.getAppointments.response
  >;
}> = {
  method: "GET",
  url: "/jobs/:jobId/appointments",
  schema: {
    summary: "Get trade job appointments",
    description: "Get paginated appointments for a trade job",
    operationId: "getJobAppointments",
    tags: ["Job"],
    params: jobParams,
    querystring: appointmentsQuery,
    headers: companyIdHeader,
    response: {
      200: schedulingSDK.schemas.api.trade.getAppointments.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { jobId } = req.params;
    const { active } = req.query;

    const appointments = await schedulingSDK
      .trade(token, companyId)
      .getAppointments({
        ...req.query,
        jobId,
        active: active ?? AppointmentQueryActive.ALL,
      });

    return res.send(appointments);
  },
};
