import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CancelOpportunityRequest,
  FormattedOpportunityStatus,
  jobsSDK,
} from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { fetchMediaAttachments } from "../../lib/api-common";
import { formatJobDetailsResponse } from "./job.response";
import {
  type JobDetailsResponse,
  type JobParams,
  jobDetailsResponseSchema,
  jobParams,
} from "./job.schema";

export const postJobCancel: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Body?: CancelOpportunityRequest;
  Reply: JobDetailsResponse;
}> = {
  method: "POST",
  url: "/jobs/:jobId/cancel",
  schema: {
    summary: "Cancel opportunity",
    description:
      "Trade marks job as cancelled. Changes opportunity status to CANCELLED.",
    operationId: "postCancelJob",
    tags: ["Job"],
    params: jobParams,
    body: jobsSDK.schemas.trade.cancelJob.body,
    headers: companyIdHeader,
    response: {
      200: jobDetailsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const {
      log: logger,
      params: { jobId },
    } = req;
    const { companyId, token } = await authTrade(req);

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);

    const cancelledJob = await jobsSDK
      .trade(token, companyId)
      .cancelJob(jobId, req.body);

    //This check verifies that the channel is only closed when the opportunity wasn't already cancelled.
    //This is required because a cancellation reason is added to an already cancelled opportunity by making a second request.
    if (
      job.status !== FormattedOpportunityStatus.CANCELLED &&
      cancelledJob.status === FormattedOpportunityStatus.CANCELLED
    ) {
      try {
        await chatSDK.freezeChannel({
          channelId: cancelledJob.opportunityId,
          userId: companyId,
          message: {
            opportunityId: cancelledJob.opportunityId,
            smartType: SmartMessageType.OPPORTUNITY_CANCELLED,
            text: "The trade has cancelled this job",
          },
          logger,
        });
      } catch (error) {
        logger.error(
          {
            error,
            channelId: cancelledJob.opportunityId,
            jobId: cancelledJob.id,
            smartType: SmartMessageType.OPPORTUNITY_CANCELLED,
          },
          "Error freezing channel",
        );
      }
    }

    const [consumer, mediaAttachments] = await Promise.all([
      consumerSDK.trade(token).getConsumer(cancelledJob.consumerId),
      fetchMediaAttachments(cancelledJob.mediaAttachmentIds ?? []),
    ]);

    return res.send(
      await formatJobDetailsResponse(cancelledJob, consumer, mediaAttachments),
    );
  },
};
