import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { fetchMediaAttachments } from "../../lib/api-common";
import { formatJobDetailsResponse } from "./job.response";
import {
  type JobDetailsResponse,
  type JobParams,
  jobDetailsResponseSchema,
  jobParams,
} from "./job.schema";

export const postJobAccept: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: JobDetailsResponse;
}> = {
  method: "POST",
  url: "/jobs/:jobId/accept",
  schema: {
    summary: "Accept opportunity",
    description:
      "Trade raise his interest in the job. Changes job status to REQUEST_ACCEPTED.",
    operationId: "postAcceptJob",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: jobDetailsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const {
      log: logger,
      params: { jobId },
    } = req;
    const { companyId, token } = await authTrade(req);

    const job = await jobsSDK.trade(token, companyId).acceptJob(jobId);
    const { opportunityId: channelId } = job;

    try {
      await chatSDK.sendSmartMessage({
        channelId,
        jobId: job.id,
        senderId: companyId,
        smartType: SmartMessageType.JOB_ACCEPTED,
        text: "Trade is interested in your job",
        logger,
      });
    } catch (error) {
      logger.error(
        {
          error,
          jobId: job.id,
          channelId,
          smartType: SmartMessageType.JOB_ACCEPTED,
        },
        "Error sending smart message",
      );
    }

    const [consumer, mediaAttachments] = await Promise.all([
      consumerSDK.trade(token).getConsumer(job.consumerId),
      fetchMediaAttachments(job.mediaAttachmentIds ?? []),
    ]);

    return res.send(
      await formatJobDetailsResponse(job, consumer, mediaAttachments),
    );
  },
};
