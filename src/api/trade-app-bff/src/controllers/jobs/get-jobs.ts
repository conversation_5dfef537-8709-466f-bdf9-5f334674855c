import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { OpportunityStatus, TradeJobs, jobsSDK } from "@checkatrade/jobs-sdk";
import { schemas } from "@checkatrade/schemas";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { jobsFilterCorrection } from "../../helpers/transform-job-status";
import { fetchMediaAttachments } from "../../lib/api-common";
import { TabEnum, TabEnumSchema } from "../../lib/schemas/jobsSchema";
import { getConsumerMapByIds } from "../../services/consumer";
import { formatJobResponse } from "./job.response";
import { jobResponseSchema } from "./job.schema";

const getJobsQuery = Type.Intersect([
  Type.Object({ tab: Type.Optional(TabEnumSchema) }),
  schemas.pagination.query,
]);

const getJobsResponse = Type.Composite([
  schemas.pagination.response,
  Type.Object(
    {
      data: Type.Array(jobResponseSchema),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

const getFilter = (
  tab?: TabEnum,
): Exclude<Parameters<TradeJobs["getJobs"]>[0], undefined> => {
  switch (tab) {
    case TabEnum.Archive: {
      return {
        "filter[in]": [
          OpportunityStatus.REQUEST_REJECTED,
          OpportunityStatus.CANCELLED,
          OpportunityStatus.V2_WITHDRAWN,
          OpportunityStatus.V2_CANCELLED,
        ],
      };
    }
    case TabEnum.Completed: {
      return {
        "filter[in]": [
          OpportunityStatus.COMPLETED,
          OpportunityStatus.V2_COMPLETED,
          OpportunityStatus.V2_WORK_FINISHED,
        ],
      };
    }
    case TabEnum.Booked: {
      return {
        filter: OpportunityStatus.V2_BOOKED,
      };
    }
    case TabEnum.Interested: {
      return {
        "filter[in]": [
          OpportunityStatus.REQUEST_ACCEPTED,
          OpportunityStatus.SCHEDULED,
          OpportunityStatus.SCHEDULE_CANCELLED,
          OpportunityStatus.V2_INTERESTED,
        ],
      };
    }
    case TabEnum.Reviewable: {
      return {
        "filter[in]": [
          OpportunityStatus.REQUEST_ACCEPTED,
          OpportunityStatus.SCHEDULED,
          OpportunityStatus.SCHEDULE_CANCELLED,
          OpportunityStatus.V2_INTERESTED,
          OpportunityStatus.COMPLETED,
          OpportunityStatus.V2_WORK_FINISHED,
        ],
      };
    }
    case TabEnum.New: {
      return {
        "filter[in]": [
          OpportunityStatus.REQUESTED,
          OpportunityStatus.V2_REQUESTED,
        ],
      };
    }
    case TabEnum.All: {
      return {
        "filter[nin]": [
          OpportunityStatus.CANCELLED,
          OpportunityStatus.V2_CANCELLED,
          OpportunityStatus.V2_WITHDRAWN,
        ],
      };
    }
    default: {
      return {};
    }
  }
};

export const getJobs: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof getJobsQuery>;
  Reply: Static<typeof getJobsResponse>;
}> = {
  method: "GET",
  url: "/jobs",
  schema: {
    summary: "Get jobs",
    description: "Get paginated consumer jobs",
    operationId: "getJobs",
    tags: ["Job"],
    querystring: getJobsQuery,
    headers: companyIdHeader,
    response: {
      200: getJobsResponse,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { page, size, tab } = req.query;

    const jobs = await jobsSDK
      .trade(token, companyId)
      .getJobs({ page, size, ...getFilter(tab) });

    // Filter booked jobs via tradeMarkedBooked flag
    const filteredJobs = jobsFilterCorrection(tab, jobs.data);

    // Single pass to collect consumer IDs and media attachment IDs
    const consumerIds = new Set<string>();
    const allMediaAttachmentIds: string[] = [];
    for (const job of filteredJobs) {
      consumerIds.add(job.consumerId);
      if (job.mediaAttachmentIds) {
        allMediaAttachmentIds.push(...job.mediaAttachmentIds);
      }
    }

    // Parallel fetch of consumers and media attachments
    const [consumerMap, mediaAttachments] = await Promise.all([
      getConsumerMapByIds(consumerIds, token),
      fetchMediaAttachments(allMediaAttachmentIds),
    ]);

    // Create media lookup map in single pass
    const mediaAttachmentsById = Object.fromEntries(
      mediaAttachments.map((ma) => [ma.id, ma]),
    );

    // Single pass for final data transformation
    const data = await Promise.all(
      filteredJobs.map((job) => {
        const consumer = consumerMap[job.consumerId] ?? { id: job.consumerId };
        const jobMediaAttachments =
          job.mediaAttachmentIds
            ?.map((id) => mediaAttachmentsById[id])
            .filter(Boolean) ?? [];

        return formatJobResponse(job, consumer, jobMediaAttachments);
      }),
    );

    return res.send({ ...jobs, data });
  },
};
