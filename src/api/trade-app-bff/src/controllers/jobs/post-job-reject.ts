import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  FormattedOpportunityStatus,
  RejectOpportunityRequest,
  jobsSDK,
  rejectOpportunityRequestSchema,
} from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { config } from "../../config";
import { fetchMediaAttachments } from "../../lib/api-common";
import { redirect } from "../../services/redirect";
import { formatJobDetailsResponse } from "./job.response";
import {
  type JobDetailsResponse,
  type JobParams,
  jobDetailsResponseSchema,
  jobParams,
} from "./job.schema";

export const postJobReject: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: RejectOpportunityRequest;
  Params: JobParams;
  Reply: JobDetailsResponse;
}> = {
  method: "POST",
  url: "/jobs/:jobId/reject",
  schema: {
    summary: "Reject opportunity",
    description:
      "Trade is not interested in the job. Changes job status to REQUEST_REJECTED and locks the Stream channel.",
    operationId: "postRejectJob",
    tags: ["Job"],
    params: jobParams,
    body: rejectOpportunityRequestSchema,
    headers: companyIdHeader,
    response: {
      200: jobDetailsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const {
      log: logger,
      params: { jobId },
    } = req;
    const { companyId, token } = await authTrade(req);

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);

    const rejectedJob = await jobsSDK
      .trade(token, companyId)
      .rejectJob(jobId, req.body);

    // This check verifies that the channel is only closed when the opportunity wasn't already rejected.
    // This is required because a rejection reason is added to an already cancelled opportunity by making a second request.
    if (
      job.status !== FormattedOpportunityStatus.REQUEST_REJECTED &&
      rejectedJob.status === FormattedOpportunityStatus.REQUEST_REJECTED
    ) {
      if (config.redirection.enabled) {
        //we don't want to fail rejection if redirection fails
        try {
          const redirectedAt = new Date();
          await redirect.redirectOnReject(
            jobId,
            companyId,
            redirectedAt,
            token,
            logger,
          );
        } catch (error) {
          logger.error(
            error,
            `Failed to redirect job ${jobId} rejected by trade ${companyId}`,
          );
        }
      }

      try {
        await chatSDK.freezeChannel({
          channelId: rejectedJob.opportunityId,
          userId: companyId,
          message: {
            jobId: rejectedJob.id,
            smartType: SmartMessageType.JOB_REJECTED,
            text: "This job has been declined by the trade",
          },
          logger,
        });
      } catch (error) {
        logger.error(
          {
            error,
            channelId: rejectedJob.opportunityId,
            jobId: rejectedJob.id,
            smartType: SmartMessageType.JOB_REJECTED,
          },
          "Error freezing channel",
        );
      }
    }

    const [consumer, mediaAttachments] = await Promise.all([
      consumerSDK.trade(token).getConsumer(rejectedJob.consumerId),
      fetchMediaAttachments(rejectedJob.mediaAttachmentIds ?? []),
    ]);

    return res.send(
      await formatJobDetailsResponse(rejectedJob, consumer, mediaAttachments),
    );
  },
};
