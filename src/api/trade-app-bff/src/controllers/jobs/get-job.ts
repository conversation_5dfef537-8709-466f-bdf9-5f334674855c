import { authTrade } from "@checkatrade/auth-trade";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { fetchMediaAttachments } from "../../lib/api-common";
import { formatJobDetailsResponse } from "./job.response";
import {
  JobDetailsResponse,
  JobParams,
  jobDetailsResponseSchema,
  jobParams,
} from "./job.schema";

export const getJob: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: JobDetailsResponse;
}> = {
  method: "GET",
  url: "/jobs/:jobId",
  schema: {
    summary: "Get job",
    description: "Get by id",
    operationId: "getJob",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: jobDetailsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const {
      params: { jobId },
    } = req;

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);
    const [consumer, mediaAttachments] = await Promise.all([
      consumerSDK.trade(token).getConsumer(job.consumerId),
      fetchMediaAttachments(job.mediaAttachmentIds ?? []),
    ]);

    return res.send(
      await formatJobDetailsResponse(job, consumer, mediaAttachments),
    );
  },
};
