import { authTrade } from "@checkatrade/auth-trade";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { fetchMediaAttachments } from "../../lib/api-common";
import { formatJobDetailsResponse } from "./job.response";
import {
  type JobDetailsResponse,
  type JobParams,
  jobDetailsResponseSchema,
  jobParams,
} from "./job.schema";

export const postJobMarkAsRead: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: JobDetailsResponse;
}> = {
  method: "POST",
  url: "/jobs/:jobId/mark-as-read",
  schema: {
    summary: "Mark opportunity as read",
    description: "Trade views job in trade app or website.",
    operationId: "postViewedJob",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: jobDetailsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const {
      params: { jobId },
    } = req;
    const { token, companyId } = await authTrade(req);

    const job = await jobsSDK.trade(token, companyId).markJobAsRead(jobId);

    const [consumer, mediaAttachments] = await Promise.all([
      consumerSDK.trade(token).getConsumer(job.consumerId),
      fetchMediaAttachments(job.mediaAttachmentIds ?? []),
    ]);

    return res.send(
      await formatJobDetailsResponse(job, consumer, mediaAttachments),
    );
  },
};
