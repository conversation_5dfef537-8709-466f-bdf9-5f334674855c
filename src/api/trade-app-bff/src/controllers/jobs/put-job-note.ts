import { authTrade } from "@checkatrade/auth-trade";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { JobNoteRequest, JobParams, jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { fetchMediaAttachments } from "../../lib/api-common";
import { formatJobDetailsResponse } from "./job.response";
import { JobDetailsResponse, jobDetailsResponseSchema } from "./job.schema";

export const putJobNote: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: JobDetailsResponse;
  Body: JobNoteRequest;
}> = {
  method: "PUT",
  url: "/jobs/:jobId/note",
  schema: {
    summary: "Update job note",
    description: "Trade can add or update a note in a job.",
    operationId: "putJobNote",
    tags: ["Job"],
    params: jobsSDK.schemas.trade.putJobNote.params,
    body: jobsSDK.schemas.trade.putJobNote.body,
    headers: companyIdHeader,
    response: {
      200: jobDetailsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const {
      params: { jobId },
      body: { note },
    } = req;
    const { token, companyId } = await authTrade(req);

    const job = await jobsSDK
      .trade(token, companyId)
      .updateJobNote(jobId, { note });

    const [consumer, mediaAttachments] = await Promise.all([
      consumerSDK.trade(token).getConsumer(job.consumerId),
      fetchMediaAttachments(job.mediaAttachmentIds ?? []),
    ]);

    return res.send(
      await formatJobDetailsResponse(job, consumer, mediaAttachments),
    );
  },
};
