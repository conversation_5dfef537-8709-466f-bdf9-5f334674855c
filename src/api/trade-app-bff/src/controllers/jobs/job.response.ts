import { consumerSDK } from "@checkatrade/consumer-sdk";
import { TradeJobListResponse, TradeJobResponse } from "@checkatrade/jobs-sdk";
import { mediaServiceSDK } from "@checkatrade/media-service-sdk";
import { Static } from "@sinclair/typebox";

import { transformJobStatus } from "../../helpers/transform-job-status";
import { searchApi } from "../../lib/api-common";
import { JobDetailsResponse, JobResponse } from "./job.schema";

type ConsumerResponse = Static<
  typeof consumerSDK.schemas.api.consumers.getConsumer.response
>;

type MediaAttachmentsResponse = Static<
  typeof mediaServiceSDK.schemas.api.getMedia.response
>;

export const formatJobResponse = async (
  job: TradeJobResponse | TradeJobListResponse,
  consumer: Partial<ConsumerResponse> & { id: ConsumerResponse["id"] },
  mediaAttachments?: MediaAttachmentsResponse,
): Promise<JobResponse> => {
  return {
    id: job.id,
    description: job.description,
    category: {
      id: job.categoryId,
      label: await searchApi.getCategoryName(job.categoryId),
    },
    channelId: job.opportunityId,
    postcode: job.postcode,
    consumer: {
      id: consumer.id,
      firstName: consumer.firstName,
      lastName: consumer.lastName,
      email: consumer.email,
      phone: consumer.phone,
    },
    createdAt: job.createdAt,
    status: transformJobStatus(job.status, job?.tradeMarkedBooked),
    cancelReason: job.cancelReason,
    tradeViewed: job.tradeViewed,
    tradeMarkedBooked: job.tradeMarkedBooked,
    fulfilmentType: job.fulfilmentType,
    jobRefinementForm: job.jobRefinementForm,
    mediaAttachments: mediaAttachments ?? [],
  };
};

export const formatJobDetailsResponse = async (
  job: TradeJobResponse,
  consumer: ConsumerResponse,
  mediaAttachments: MediaAttachmentsResponse | undefined = undefined,
): Promise<JobDetailsResponse> => {
  return {
    ...(await formatJobResponse(job, consumer, mediaAttachments)),
    address: {
      uprn: job.address.uprn,
      city: job.address.city,
      postcode: job.address.postcode,
    },
    booking: job.booking,
    preferredStart: job.preferredStart,
    note: job?.note,
    details: job.details,
    rejectReason: job?.rejectReason,
    mediaAttachmentIds: job.mediaAttachmentIds,
    status: transformJobStatus(job.status, job?.tradeMarkedBooked),
    jobRefinementForm: job.jobRefinementForm,
  };
};
