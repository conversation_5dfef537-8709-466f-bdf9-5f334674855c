import { authTrade } from "@checkatrade/auth-trade";
import { SmartMessageType } from "@checkatrade/chat-types";
import { BadRequestError, ConflictError } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { JobParams, jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { searchApi } from "../../../../lib/api-common";
import {
  createJobReviewRequest,
  updateJobReviewRequest,
} from "../../../../services/firebase/firestore/job-review-request";
import { jobReviewRequestStatus } from "../../../../services/reviews";
import { jobParams } from "../../job.schema";
import { buildReviewRequestUrl } from "./helpers/build-review-request-url";
import { createSmartMessage } from "./helpers/create-smart-message";
import { isReviewable } from "./helpers/is-reviewable";
import { sendCommsMessage } from "./helpers/send-comms-message";

const postReviewResponse = Type.Object({
  success: Type.Boolean(),
});

type PostReviewResponse = Static<typeof postReviewResponse>;

export const postJobReviewRequest: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: PostReviewResponse;
}> = {
  method: "POST",
  url: "/jobs/:jobId/review/requests",
  schema: {
    summary: "Request a review for a job",
    description:
      "Publishes a pub/sub message for comms to send an email to the consumer and sends a smart message to the opportunity channel",
    operationId: "postJobReviewRequest",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: postReviewResponse,
    },
  },
  handler: async (req, res) => {
    const { jobId } = req.params;
    const { log: logger } = req;
    const { token, companyId } = await authTrade(req);
    const [job, trade] = await Promise.all([
      jobsSDK.trade(token, companyId).getJob(jobId),
      searchApi.getTrade({ companyId, logger: req.log }),
    ]);

    if (!isReviewable(job.status)) {
      throw new ConflictError("Job is not in a state that can be reviewed");
    }

    const statusFirestore = await jobReviewRequestStatus.getFromFirestore(
      companyId,
      jobId,
    );

    if (
      statusFirestore &&
      statusFirestore.hasSentReviewRequest &&
      statusFirestore.hasSentReviewRequestReminder
    ) {
      throw new BadRequestError(
        "Review reminder cannot be sent more than once",
      );
    }

    if (statusFirestore && !statusFirestore.canRequestReview) {
      throw new BadRequestError(
        "Review reminder cannot be sent in less than a day after the initial review request",
      );
    }

    const reviewUrl = buildReviewRequestUrl({
      uniqueName: trade.uniqueName,
      categoryId: job.categoryId,
      jobId,
    });

    if (statusFirestore) {
      //send reminder
      const text = "Trade reminded about review requested for this job";
      const reminderType = SmartMessageType.REVIEW_REQUEST_REMINDER;
      await createSmartMessage(
        job,
        reminderType,
        text,
        reviewUrl,
        companyId,
        logger,
      );
      await sendCommsMessage(job, reminderType, companyId, trade, logger);
      await updateJobReviewRequest(
        companyId,
        statusFirestore.jobReviewRequestId,
        {
          sent: true,
        },
      );
      return res.send({ success: true });
    }

    //jobReviewRequest does not exist, check in smartMessages
    const chatStatus = await jobReviewRequestStatus.getFromChat(
      job.opportunityId,
      logger,
    );

    if (
      chatStatus &&
      chatStatus.hasSentReviewRequest &&
      chatStatus.hasSentReviewRequestReminder
    ) {
      await createJobReviewRequest(companyId, { jobId, sent: true });
      throw new BadRequestError(
        "Review reminder cannot be sent more than once",
      );
    }

    let text = `Trade requested a review of this job: ${reviewUrl}`;
    let smartType = SmartMessageType.REVIEW_REQUESTED;
    let dateCreated: Date | undefined = undefined;

    if (chatStatus && chatStatus.hasSentReviewRequest) {
      if (!chatStatus.canRequestReview) {
        await createJobReviewRequest(companyId, {
          jobId,
          sent: false,
          dateCreated: chatStatus.dateCreated,
        });

        throw new BadRequestError(
          "Review reminder cannot be sent in less than a day after the initial review request",
        );
      }

      text = "Trade reminded about review requested for this job";
      smartType = SmartMessageType.REVIEW_REQUEST_REMINDER;
      dateCreated = chatStatus.dateCreated;
    }

    await createJobReviewRequest(companyId, {
      jobId,
      sent: smartType === SmartMessageType.REVIEW_REQUEST_REMINDER,
      ...(dateCreated ? { dateCreated: dateCreated } : {}),
    });

    await createSmartMessage(
      job,
      smartType,
      text,
      reviewUrl,
      companyId,
      logger,
    );
    await sendCommsMessage(job, smartType, companyId, trade, logger);

    return res.send({ success: true });
  },
};
