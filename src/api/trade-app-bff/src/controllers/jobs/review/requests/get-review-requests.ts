import { authTrade } from "@checkatrade/auth-trade";
import { SmartMessageType } from "@checkatrade/chat-types";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { jobReviewRequestStatus } from "../../../../services/reviews";
import { JobParams, jobParams } from "../../job.schema";

const ReviewRequestMessageTypes = {
  REVIEW_REQUESTED: SmartMessageType.REVIEW_REQUESTED,
  REVIEW_REQUEST_REMINDER: SmartMessageType.REVIEW_REQUEST_REMINDER,
};

const responseSchema = Type.Array(
  Type.Object({
    type: Type.Enum(ReviewRequestMessageTypes),
    createdAt: Type.String({ format: "date-time" }),
  }),
);

type GetJobReviewRequestsResponse = Static<typeof responseSchema>;

export const getJobReviewRequests: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: GetJobReviewRequestsResponse;
}> = {
  method: "GET",
  url: "/jobs/:jobId/review/requests",
  schema: {
    summary: "Job review requests",
    description:
      "Returns a list of review requests for a job, sent by the trade.",
    operationId: "getJobReviewRequests",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: responseSchema,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { jobId } = req.params;
    const { log: logger } = req;

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);

    const firestoreStatus = await jobReviewRequestStatus.getFromFirestore(
      companyId,
      jobId,
    );

    if (firestoreStatus) {
      const response: GetJobReviewRequestsResponse = [];

      response.push({
        type: ReviewRequestMessageTypes.REVIEW_REQUESTED,
        createdAt: firestoreStatus.dateCreated.toISOString(),
      });

      if (firestoreStatus.hasSentReviewRequestReminder) {
        response.push({
          type: ReviewRequestMessageTypes.REVIEW_REQUEST_REMINDER,
          createdAt: firestoreStatus.dateCreated.toISOString(),
        });
      }

      return res.send(response);
    }

    const chatStatus = await jobReviewRequestStatus.getFromChat(
      job.opportunityId,
      logger,
    );

    const response: GetJobReviewRequestsResponse = [];

    if (chatStatus) {
      response.push({
        type: ReviewRequestMessageTypes.REVIEW_REQUESTED,
        createdAt: chatStatus.dateCreated.toISOString(),
      });
      if (chatStatus.hasSentReviewRequestReminder) {
        response.push({
          type: ReviewRequestMessageTypes.REVIEW_REQUEST_REMINDER,
          createdAt: chatStatus.dateCreated.toISOString(),
        });
      }
    }

    return res.send(response);
  },
};
