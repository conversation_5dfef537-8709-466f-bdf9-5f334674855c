import { chatSD<PERSON> } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { TradeJobResponse } from "@checkatrade/jobs-sdk";
import { FastifyBaseLogger } from "fastify";

export async function createSmartMessage(
  job: TradeJobResponse,
  smartType:
    | SmartMessageType.REVIEW_REQUESTED
    | SmartMessageType.REVIEW_REQUEST_REMINDER,
  text: string,
  reviewUrl: string,
  companyId: number,
  logger: FastifyBaseLogger,
) {
  try {
    await chatSDK.sendSmartMessage({
      channelId: job.opportunityId,
      smartType,
      text,
      reviewRequest: {
        url: reviewUrl,
      },
      senderId: companyId,
      reviewId: "",
      logger,
    });
  } catch (error) {
    logger.error(
      {
        error,
        channelId: job.opportunityId,
        jobId: job.id,
        smartType,
      },
      "Error sending smart message",
    );
  }
}
