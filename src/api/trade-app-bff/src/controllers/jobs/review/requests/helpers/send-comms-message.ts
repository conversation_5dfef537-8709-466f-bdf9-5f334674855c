import { SmartMessageType } from "@checkatrade/chat-types";
import { TradeJobResponse } from "@checkatrade/jobs-sdk";
import { FastifyBaseLogger } from "fastify";

import { TradeCard, comms } from "../../../../../lib/api-common";

export async function sendCommsMessage(
  job: TradeJobResponse,
  smartType:
    | SmartMessageType.REVIEW_REQUESTED
    | SmartMessageType.REVIEW_REQUEST_REMINDER,
  companyId: number,
  trade: TradeCard,
  logger: FastifyBaseLogger,
) {
  await comms.reviewRequest(
    {
      consumerId: job.consumerId,
      type: smartType,
      job: {
        id: job.id,
        categoryId: job.categoryId,
      },
      trade: {
        companyId,
        name: trade.name,
        uniqueName: trade.uniqueName,
      },
    },
    logger,
  );
}
