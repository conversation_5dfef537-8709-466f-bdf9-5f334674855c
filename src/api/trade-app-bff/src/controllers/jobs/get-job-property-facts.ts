import { authTrade } from "@checkatrade/auth-trade";
import { consumerMyhomeSDK } from "@checkatrade/consumer-myhome-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import {
  PropertyFacts,
  getPropertyFactsForConsumer,
} from "../../services/property-facts/get-property-facts-for-consumer";
import { JobParams, jobParams } from "./job.customer.schema";

export const getJobPropertyFacts: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: JobParams;
  Reply: PropertyFacts;
}> = {
  method: "GET",
  url: "/jobs/:jobId/property-facts",
  schema: {
    summary: "Get job property facts",
    description: "Get property facts about job, including lat/lng, by job id",
    operationId: "getJobPropertyFacts",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: consumerMyhomeSDK.schemas.api.propertyFacts
        .getPropertyFactsForConsumer.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const {
      log: logger,
      params: { jobId },
    } = req;

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);
    const propertyFacts = await getPropertyFactsForConsumer(token, job, logger);

    return res.send(propertyFacts);
  },
};
