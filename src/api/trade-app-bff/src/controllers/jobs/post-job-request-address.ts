import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import {
  UnauthorizedError,
  UnprocessableEntityError,
} from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { type JobParams, jobParams } from "./job.schema";

const postRequestAddressResponse = Type.Object({});

export const postShareAddress: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: Static<typeof postRequestAddressResponse>;
  Params: JobParams;
}> = {
  method: "POST",
  url: "/jobs/:jobId/request-address",
  schema: {
    summary: "Request address",
    description: "Requests the consumers address.",
    operationId: "postRequestAddress",
    tags: ["Job"],
    params: jobParams,
    headers: companyIdHeader,
    response: {
      200: postRequestAddressResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId, token } = await authTrade(req);
    const { jobId } = req.params;

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);

    if (!job) {
      throw new UnauthorizedError("Job not found");
    }

    const [channel] = await chatSDK.getChannelsById(job.opportunityId);

    if (!channel) {
      throw new UnprocessableEntityError("No channel found for this job");
    }

    await chatSDK.sendSmartMessage({
      channel: channel,
      smartType: SmartMessageType.ADDRESS_REQUESTED,
      senderId: companyId,
      text: `The trade has requested the consumer's address.`,
      logger: logger,
      addressId: "", // Not provided.
    });

    return res.send({});
  },
};
