import { consumerSDK } from "@checkatrade/consumer-sdk";
import { FormattedOpportunityStatus, jobsSDK } from "@checkatrade/jobs-sdk";
import { mediaServiceSDK } from "@checkatrade/media-service-sdk";
import { Static, Type } from "@sinclair/typebox";

export const jobParams = Type.Object({
  jobId: Type.String({
    description: "Identifier of the job",
    format: "uuid",
  }),
});

export type JobParams = Static<typeof jobParams>;

export const jobDetailsResponse = Type.Composite(
  [
    Type.Omit(jobsSDK.schemas.consumer.getJob.response, [
      "categoryId",
      "opportunities",
      "trades",
    ]),
    Type.Object({
      category: Type.Object({
        id: Type.Number(),
        label: Type.String(),
      }),
      trades: Type.Array(
        Type.Object({
          name: Type.String(),
          uniqueName: Type.String(),
          id: Type.Number(),
          opportunityId:
            jobsSDK.schemas.trade.getJob.response.properties.opportunityId,
          logoUrl: Type.String(),
          channelId: Type.Optional(
            jobsSDK.schemas.trade.getJob.response.properties.opportunityId,
          ),
          status: Type.Enum(FormattedOpportunityStatus),
          tradeApp: Type.Boolean(),
          contact: Type.Optional(
            Type.Object({
              calloutable24hr: Type.Boolean(),
              label: Type.String(),
              number: Type.String(),
            }),
          ),
          reviews: Type.Optional(
            Type.Object({
              score: Type.Number(),
              count: Type.Number(),
            }),
          ),
        }),
      ),
      mediaAttachments: Type.Optional(
        mediaServiceSDK.schemas.api.getMedia.response,
      ),
    }),
  ],
  {
    additionalProperties: false,
    description: "Successful response",
  },
);

export const jobDetailsResponsePublic = Type.Composite(
  [
    jobDetailsResponse,
    Type.Object({
      token: Type.Optional(Type.String()),
    }),
  ],
  {
    additionalProperties: false,
    description: "Successful response",
  },
);

export const jobResponse = Type.Omit(
  jobDetailsResponse,
  ["address", "preferredStart"],
  {
    additionalProperties: false,
    description: "Successful response",
  },
);

export type JobDetailsResponse = Static<typeof jobDetailsResponse>;
export type JobDetailsResponsePublic = Static<typeof jobDetailsResponsePublic>;

export type JobResponse = Static<typeof jobResponse>;

export const publicJobRequest = Type.Composite([
  jobsSDK.schemas.consumer.postJob.body,
  consumerSDK.schemas.api.profile.postProfile.body,
  Type.Object({
    email: Type.String(),
  }),
]);

export type PublicJobRequest = Static<typeof publicJobRequest>;

export const postJobTradesRequest = Type.Object(
  {
    trades: Type.Array(Type.Number(), {
      minItems: 1,
      maxItems: 10,
      description: "List of companyId of trades to add to the job",
    }),
  },
  {
    additionalProperties: false,
  },
);

export type PostJobTradesRequest = Static<typeof postJobTradesRequest>;

export const publicJobTradesRequest = Type.Composite([
  postJobTradesRequest,
  Type.Object({
    email: Type.String(),
  }),
]);

export type PublicJobTradesRequest = Static<typeof publicJobTradesRequest>;
