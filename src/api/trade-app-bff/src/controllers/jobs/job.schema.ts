import { jobsSDK } from "@checkatrade/jobs-sdk";
import { mediaServiceSDK } from "@checkatrade/media-service-sdk";
import { Static, Type } from "@sinclair/typebox";

export const jobParams = Type.Object({
  jobId: Type.String({ format: "uuid" }),
});

export type JobParams = Static<typeof jobParams>;

export const jobDetailsResponseSchema = Type.Composite(
  [
    Type.Omit(jobsSDK.schemas.trade.getJob.response, [
      "consumerId",
      "opportunityId",
      "categoryId",
      "address",
    ]),
    Type.Object({
      category: Type.Object({
        id: Type.Number(),
        label: Type.String(),
      }),
      channelId: jobsSDK.schemas.trade.getJob.response.properties.opportunityId,
      consumer: Type.Object({
        id: Type.String(),
        firstName: Type.Optional(Type.String()),
        lastName: Type.Optional(Type.String()),
        email: Type.Optional(Type.String({ format: "email" })),
        phone: Type.Optional(Type.String()),
      }),
      address: Type.Object({
        uprn: Type.Optional(Type.Union([Type.String(), Type.Null()])),
        city: Type.Optional(Type.Union([Type.String(), Type.Null()])),
        postcode: Type.String(),
      }),
      mediaAttachments: Type.Optional(
        mediaServiceSDK.schemas.api.getMedia.response,
      ),
    }),
  ],
  { additionalProperties: false, description: "Successful response" },
);

export const jobResponseSchema = Type.Omit(
  jobDetailsResponseSchema,
  ["address", "preferredStart"],
  {
    additionalProperties: false,
    description: "Successful response",
  },
);

export type JobDetailsResponse = Static<typeof jobDetailsResponseSchema>;

export type JobResponse = Static<typeof jobResponseSchema>;
