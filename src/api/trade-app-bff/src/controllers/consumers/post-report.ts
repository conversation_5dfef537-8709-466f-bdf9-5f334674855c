import { authTrade } from "@checkatrade/auth-trade";
import { httpClient } from "@checkatrade/axios";
import { StreamUserType, chatSDK } from "@checkatrade/chat-sdk";
import { SmartMessageType } from "@checkatrade/chat-types";
import { NotFoundError } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { gcp } from "@checkatrade/gcp";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { ReportUserParams } from "./post-report-types";
import { config } from "./report-config";

const reportUserV2 = async ({ data, logger }: ReportUserParams) => {
  try {
    const url = `${config.api.url}/report-user`;
    const token = await gcp.generateBearerToken(url);
    const payload = {
      jobId: data.jobId,
      reporter: data.reporter,
      user: data.user,
    };

    const response = await httpClient.post(url, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response;
  } catch (error) {
    logger.error(error, "Failed to report user to Comms service");
    throw error;
  }
};

const reportUserV1 = async ({ data, logger }: ReportUserParams) => {
  try {
    const url = `${config.api.url}/chat/block`;
    const token = await gcp.generateBearerToken(url);
    const payload = {
      attributes: {
        schemaVersion: "1.0",
        timestamp: new Date().toISOString(),
      },
      data: {
        channelId: data.channelId,
        senderId: data.reporter.id,
        members: [data.reporter, data.user],
      },
    };

    const response = await httpClient.post(url, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response;
  } catch (error) {
    logger.error(error, "Failed to report user to Comms service");
    throw error;
  }
};

export const reportUser = async (params: ReportUserParams) => {
  const reportFn =
    // This will need to be added as a new environment variable.
    // As it stands it will always return false and call reportUserV2.
    process.env.COMMS_REPORT_USER_VERSION === "V1" ?
      reportUserV1
    : reportUserV2;

  await reportFn(params);
};

const postReportConsumerParams = Type.Object({
  consumerId: Type.String({ format: "uuid" }),
});

const postReportConsumerBody = Type.Object({
  jobId: Type.String({ format: "uuid" }),
});

const postReportConsumerResponse = Type.Object({});

export const postReportConsumer: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof postReportConsumerParams>;
  Body: Static<typeof postReportConsumerBody>;
  Reply: Static<typeof postReportConsumerResponse>;
}> = {
  method: "POST",
  url: "/consumers/:consumerId/report",
  schema: {
    summary: "Report consumer",
    description:
      "For trades reporting/blocking a consumer by job id and consumer id",
    operationId: "postReportConsumer",
    tags: ["Consumers"],
    headers: companyIdHeader,
    params: postReportConsumerParams,
    body: postReportConsumerBody,
    response: {
      200: postReportConsumerResponse,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { jobId } = req.body;
    const { consumerId } = req.params;

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);
    if (job.consumerId !== consumerId) {
      throw new NotFoundError("The requested resource was not found");
    }

    const channelId = job.opportunityId;
    const logger = req.log;

    await reportUser({
      data: {
        jobId,
        channelId,
        reporter: {
          id: String(companyId),
          type: StreamUserType.TRADE,
        },
        user: {
          id: consumerId,
          type: StreamUserType.CONSUMER,
        },
      },
      logger,
    });

    // @important Freeze and flag, AFTER event is sent to comms
    await chatSDK.freezeChannel({ channelId, logger });
    await chatSDK.flagLastMessage({
      userId: consumerId,
      reporterId: String(companyId),
      channelId,
      logger,
    });

    await chatSDK.updateChannel({
      channelId,
      data: { frozenFromBlock: true },
      logger,
    });

    await chatSDK
      .sendSmartMessage({
        channelId,
        jobId: job.id,
        senderId: String(companyId),
        smartType: SmartMessageType.BLOCKED,
        text: "Chat unavailable: You can no longer send or receive messages.",
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.send({});
  },
};
