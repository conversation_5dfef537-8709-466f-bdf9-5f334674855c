import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  SelfServiceHeaders,
  SelfServiceResponse,
  UpdateMemberPhoneBody,
  selfServiceHeaders,
  selfServiceResponseSchema,
  updateMemberPhoneBodySchema,
} from "@checkatrade/trade-bff-types";

import { verifyAccountOwner } from "../../helpers/verify-account-owner";
import { selfService } from "../../services/self-service";
import { SelfServiceRequestType } from "../../types/asyncapi-types";
import { getCorrelationId } from "./utils/utils";

export const updateMemberPhone: FastifyRouteOptions<{
  Header: SelfServiceHeaders;
  Body: UpdateMemberPhoneBody;
  Reply: SelfServiceResponse;
}> = {
  method: "POST",
  url: "/self-service/member-phone",
  schema: {
    summary: "Update member phone",
    description: "Update the member phone number",
    operationId: "updateMmeberPhone",
    tags: ["Self Service"],
    body: updateMemberPhoneBodySchema,
    headers: selfServiceHeaders,
    response: {
      200: selfServiceResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { email } = authTradeCapi(req);
    const { memberPhone } = req.body;

    await verifyAccountOwner(req, companyId);

    const correlationId = getCorrelationId(req.headers["x-correlation-id"]);

    await selfService.memberPhoneRequest({
      correlationId,
      data: {
        companyId: companyId.toString(),
        phone: memberPhone,
        requesterEmail: email,
      },
    });
    return res.status(200).send({
      requestType: SelfServiceRequestType.MEMBER_PHONE,
      correlationId,
    });
  },
};
