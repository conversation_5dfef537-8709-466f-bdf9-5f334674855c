import { v4 as UUIDv4, v7 as UUIDv7 } from "uuid";

import { CORRELATION_ID_ERROR, getCorrelationId } from "./utils";

describe("utils", () => {
  it("should return a correlation id if it's a valid UUIDv7", () => {
    const xCorrelationId = UUIDv7();
    const correlationId = getCorrelationId(xCorrelationId);

    expect(correlationId).toBe(xCorrelationId);
  });

  it("Should throw an error if the correlation id is not a valid UUIDv7", () => {
    const xCorrelationId = UUIDv4();
    expect(() => getCorrelationId(xCorrelationId)).toThrow(
      new Error(CORRELATION_ID_ERROR),
    );
  });

  describe("When the correlation id is an array", () => {
    it("Should take the first element of the array if the correlation id is an array, and a valid UUIDv7", () => {
      const xCorrelationId = [UUIDv7()];
      const correlationId = getCorrelationId(xCorrelationId);

      expect(correlationId).toBe(xCorrelationId[0]);
    });

    it("Should throw an error if the correlation id is an array, and not a valid UUIDv7", () => {
      const xCorrelationId = [UUIDv4()];
      expect(() => getCorrelationId(xCorrelationId)).toThrow(
        new Error(CORRELATION_ID_ERROR),
      );
    });
  });

  it("Should generate a new UUIDv7 if the correlation id is not provided", () => {
    const correlationId = getCorrelationId(undefined);

    expect(correlationId).toMatch(
      /^[a-f0-9]{8}-[a-f0-9]{4}-7[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$/,
    );
  });
});
