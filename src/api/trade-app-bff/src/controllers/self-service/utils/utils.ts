import { BadRequestError } from "@checkatrade/errors";
import { v7 as UUID7 } from "uuid";

import { validateUUIDv7 } from "../../../utilities/uuid";

export const CORRELATION_ID_ERROR = "Correlation ID must be a valid UUIDv7";

export const getCorrelationId = (
  xCorrelationId: string | string[] | undefined,
): string => {
  let correlationId: string;

  if (xCorrelationId) {
    correlationId =
      typeof xCorrelationId === "string" ? xCorrelationId : xCorrelationId[0];

    if (!validateUUIDv7(correlationId)) {
      throw new BadRequestError(CORRELATION_ID_ERROR);
    }
  } else {
    correlationId = UUID7();
  }

  return correlationId;
};
