import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { ForbiddenError, UnprocessableEntityError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  SelfServiceHeaders,
  SelfServiceResponse,
  UpdatePersonType,
  selfServiceHeaders,
  selfServiceResponseSchema,
  updatePersonSchema,
} from "@checkatrade/trade-bff-types";

import { address } from "../../services/address";
import { checkAccountOwner } from "../../services/member-details/member-details";
import { selfService } from "../../services/self-service";
import { SelfServiceRequestType } from "../../types/asyncapi-types";
import { getCorrelationId } from "./utils/utils";

export const updatePerson: FastifyRouteOptions<{
  Header: SelfServiceHeaders;
  Body: UpdatePersonType;
  Reply: SelfServiceResponse;
}> = {
  method: "POST",
  url: "/self-service/update-person",
  schema: {
    summary: "Update person details",
    description:
      "Update the details of a person in the company, which is a contact associate with their account",
    operationId: "updatePerson",
    tags: ["Self Service"],
    body: updatePersonSchema,
    headers: selfServiceHeaders,
    response: {
      200: selfServiceResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { email } = authTradeCapi(req);
    const person = req.body;

    const authToken = authTradeCapi(req);

    const isAccountOwner = await checkAccountOwner({
      companyId,
      userEmail: authToken.email,
    });

    if (!isAccountOwner) {
      throw new ForbiddenError("Only the account owner can update contacts");
    }

    const correlationId = getCorrelationId(req.headers["x-correlation-id"]);
    if (person.mailingAddress) {
      const validAddress = await address.validateAddress(
        {
          line1: person.mailingAddress.street,
          city: person.mailingAddress.city,
          postcode: person.mailingAddress.postcode,
        },
        req.log,
      );

      if (!validAddress) {
        throw new UnprocessableEntityError("The address provided is not valid");
      }
    }

    const { contactId, ...rest } = person;
    await selfService.updatePersonRequest({
      correlationId,
      data: {
        companyId: companyId.toString(),
        personId: contactId,
        ...rest,
        requesterEmail: email,
      },
    });

    return res.status(200).send({
      requestType: SelfServiceRequestType.UPDATE_PERSON,
      correlationId,
    });
  },
};
