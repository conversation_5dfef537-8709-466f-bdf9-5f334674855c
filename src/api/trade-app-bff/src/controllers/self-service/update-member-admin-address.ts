import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { UnprocessableEntityError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  SelfServiceHeaders,
  SelfServiceResponse,
  UpdateMemberAddressBody,
  selfServiceHeaders,
  selfServiceResponseSchema,
  updateMemberAddressSchema,
} from "@checkatrade/trade-bff-types";

import { verifyAccountOwner } from "../../helpers/verify-account-owner";
import { address } from "../../services/address";
import { selfService } from "../../services/self-service";
import { SelfServiceRequestType } from "../../types/asyncapi-types";
import { getCorrelationId } from "./utils/utils";

export const updateMemberAdminAddress: FastifyRouteOptions<{
  Header: SelfServiceHeaders;
  Body: UpdateMemberAddressBody;
  Reply: SelfServiceResponse;
}> = {
  method: "POST",
  url: "/self-service/member-admin-address",
  schema: {
    summary: "Update member admin address",
    description:
      "Update the member admin address, which is the secondary postal address associated with their account",
    operationId: "updateMemberAdminAddress",
    tags: ["Self Service"],
    body: updateMemberAddressSchema,
    headers: selfServiceHeaders,
    response: {
      200: selfServiceResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { email } = authTradeCapi(req);
    const { street, postcode, city, county } = req.body;

    await verifyAccountOwner(req, companyId);

    const correlationId = getCorrelationId(req.headers["x-correlation-id"]);
    const validAddress = await address.validateAddress(
      { line1: street, city, postcode },
      req.log,
    );

    if (!validAddress) {
      throw new UnprocessableEntityError("The address provided is not valid");
    }

    await selfService.memberAdminAddressRequest({
      correlationId,
      data: {
        companyId: companyId.toString(),
        address: { street, postcode, city, county },
        requesterEmail: email,
      },
    });

    return res.status(200).send({
      requestType: SelfServiceRequestType.MEMBER_ADMIN_ADDRESS,
      correlationId,
    });
  },
};
