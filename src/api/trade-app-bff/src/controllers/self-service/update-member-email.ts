import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  SelfServiceHeaders,
  SelfServiceResponse,
  UpdateMemberEmailBody,
  selfServiceHeaders,
  selfServiceResponseSchema,
  updateMemberEmailBodySchema,
} from "@checkatrade/trade-bff-types";

import { verifyAccountOwner } from "../../helpers/verify-account-owner";
import { selfService } from "../../services/self-service";
import { SelfServiceRequestType } from "../../types/asyncapi-types";
import { getCorrelationId } from "./utils/utils";

export const updateMemberEmail: FastifyRouteOptions<{
  Header: SelfServiceHeaders;
  Body: UpdateMemberEmailBody;
  Reply: SelfServiceResponse;
}> = {
  method: "POST",
  url: "/self-service/member-email",
  schema: {
    summary: "Update member email",
    description: "Update the member email address",
    operationId: "updateMmeberEmail",
    tags: ["Self Service"],
    body: updateMemberEmailBodySchema,
    headers: selfServiceHeaders,
    response: {
      200: selfServiceResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { email } = authTradeCapi(req);
    const { memberEmail } = req.body;

    await verifyAccountOwner(req, companyId);

    const correlationId = getCorrelationId(req.headers["x-correlation-id"]);

    await selfService.memberEmailRequest({
      correlationId,
      data: {
        companyId: companyId.toString(),
        email: memberEmail,
        requesterEmail: email,
      },
    });
    return res.status(200).send({
      requestType: SelfServiceRequestType.MEMBER_EMAIL,
      correlationId,
    });
  },
};
