import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { apiErrorSchema } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  DeletePersonType,
  SelfServiceHeaders,
  SelfServiceResponse,
  deletePersonSchema,
  selfServiceHeaders,
  selfServiceResponseSchema,
} from "@checkatrade/trade-bff-types";

import { verifyAccountOwner } from "../../helpers/verify-account-owner";
import { selfService } from "../../services/self-service";
import { SelfServiceRequestType } from "../../types/asyncapi-types";
import { getCorrelationId } from "./utils/utils";

export const updateMemberDeletePerson: FastifyRouteOptions<{
  Header: SelfServiceHeaders;
  Body: DeletePersonType;
  Reply: SelfServiceResponse;
}> = {
  method: "POST",
  url: "/self-service/delete-person",
  schema: {
    summary: "Delete Person",
    description:
      "Allows a member to make a request to delete a person from their account (contact)",
    operationId: "updateMemberDeletePerson",
    tags: ["Self Service"],
    body: deletePersonSchema,
    headers: selfServiceHeaders,
    response: {
      200: selfServiceResponseSchema,
      403: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { email } = authTradeCapi(req);
    const person = req.body;

    await verifyAccountOwner(req, companyId);

    const correlationId = getCorrelationId(req.headers["x-correlation-id"]);

    const { contactId, ...rest } = person;

    await selfService.deletePersonRequest({
      correlationId,
      data: {
        companyId: companyId.toString(),
        personId: contactId,
        requesterEmail: email,
        ...rest,
      },
    });
    return res.status(200).send({
      requestType: SelfServiceRequestType.REMOVE_PERSON,
      correlationId,
    });
  },
};
