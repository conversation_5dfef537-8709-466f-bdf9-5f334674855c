import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { apiErrorSchema } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  SelfServiceHeaders,
  SelfServiceResponse,
  UpdateCompanyNameBody,
  selfServiceHeaders,
  selfServiceResponseSchema,
  updateCompanyNameBodySchema,
} from "@checkatrade/trade-bff-types";

import { verifyAccountOwner } from "../../helpers/verify-account-owner";
import { selfService } from "../../services/self-service";
import { SelfServiceRequestType } from "../../types/asyncapi-types";
import { getCorrelationId } from "./utils/utils";

export const updateCompanyName: FastifyRouteOptions<{
  Header: SelfServiceHeaders;
  Body: UpdateCompanyNameBody;
  Reply: SelfServiceResponse;
}> = {
  method: "POST",
  url: "/self-service/company-name",
  schema: {
    summary: "Update company name",
    description: "Update the company name",
    operationId: "updateCompanyName",
    tags: ["Self Service"],
    body: updateCompanyNameBodySchema,
    headers: selfServiceHeaders,
    response: {
      200: selfServiceResponseSchema,
      403: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { email } = authTradeCapi(req);
    const { companyName } = req.body;

    await verifyAccountOwner(req, companyId);

    const correlationId = getCorrelationId(req.headers["x-correlation-id"]);

    await selfService.companyNameRequest({
      correlationId,
      data: {
        companyId: companyId.toString(),
        name: companyName,
        requesterEmail: email,
      },
    });
    return res.status(200).send({
      requestType: SelfServiceRequestType.COMPANY_NAME,
      correlationId,
    });
  },
};
