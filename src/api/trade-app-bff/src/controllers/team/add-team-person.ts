import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  AddTeamPersonsQuery,
  CompanyIdHeader,
  addTeamPersonsQuery,
  addTeamPersonsResponse,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const addTeamPersons: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: AddTeamPersonsQuery;
}> = {
  method: "POST",
  url: "/team",
  schema: {
    summary: "Add team persons",
    description: "Add team persons",
    operationId: "addTeamPersons",
    tags: ["Team"],
    headers: companyIdHeader,
    body: addTeamPersonsQuery,
    response: {
      201: addTeamPersonsResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const newPerson = req.body;

    try {
      const person = await team.addTeamPerson(newPerson, companyId, logger);
      return res.status(201).send(person);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Error adding person");
    }
  },
};
