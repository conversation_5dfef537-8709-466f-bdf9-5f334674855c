import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  NotFoundError,
  UnprocessableEntityError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetTeamMemberVettingDetailsQueryParams,
  companyIdHeader,
  getTeamMemberVettingDetailsQueryParams,
  getTeamPersonVettingResponse,
  mapToPersonVettingResponse,
} from "@checkatrade/trade-bff-types";
import { TypeBoxError } from "@sinclair/typebox";
import { ZodError } from "zod";

import { getMemberByCompanyId } from "../../services/team/get-member-by-company-id";
import { getTeamPerson } from "../../services/team/get-team-person";
import { vetting } from "../../services/vetting";

export const getTeamPersonsVettingDetails: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: GetTeamMemberVettingDetailsQueryParams;
}> = {
  method: "GET",
  url: "/team/persons/vetting-details",
  schema: {
    summary: "Get team person vetting details",
    description:
      "Based on the company ID & email, get the current person vetting details",
    operationId: "getTeamPersonsVettingDetails",
    tags: ["Team"],
    headers: companyIdHeader,
    querystring: getTeamMemberVettingDetailsQueryParams,
    response: {
      200: getTeamPersonVettingResponse,
      404: apiErrorSchema,
      422: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const {
      log: logger,
      query: { email },
    } = req;
    const { companyId: legacyCompanyId } = await authTrade(req);

    try {
      const { memberId } =
        (await getMemberByCompanyId(legacyCompanyId, logger)) ?? {};

      if (!memberId) {
        throw new NotFoundError("Member not found");
      }

      const personResponse = await getTeamPerson(memberId, { email }, logger);

      if (!personResponse) {
        logger.debug("Person not found", { personResponse });
        throw new NotFoundError("Person not found");
      }

      const personRes = personResponse.data.find(
        (person) => person.email === email,
      );

      if (!personRes?.id) {
        logger.debug("Person not found in the team", { email, personRes });
        throw new NotFoundError("Person not found");
      }

      const { id: personId } = personRes;

      const consentResponse = await vetting.getTradeVettingDetails(
        memberId,
        personId,
      );

      return res.status(200).send(mapToPersonVettingResponse(consentResponse));
    } catch (error) {
      if (error instanceof ApiError) {
        logger.error(error, "Failed to fetch vetting data");
        throw error;
      }
      if (error instanceof TypeBoxError || error instanceof ZodError) {
        throw new UnprocessableEntityError(error.message);
      }

      throw new InternalServerError("Error getting person vetting details");
    }
  },
};
