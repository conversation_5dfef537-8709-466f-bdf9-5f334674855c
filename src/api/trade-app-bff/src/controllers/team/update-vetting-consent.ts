import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  UnprocessableEntityError,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  TeamParams,
  UpdateTeamPersonVettingQuery,
  companyIdHeader,
  mapToTeamVettingConsentStatusResponse,
  teamParams,
  updateTeamPersonVettingQuery,
  updateTeamPersonVettingResponse,
} from "@checkatrade/trade-bff-types";
import { TypeBoxError } from "@sinclair/typebox";

import { vetting } from "../../services/vetting";

export const putTeamPersonVetting: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: TeamParams;
  Body: UpdateTeamPersonVettingQuery;
}> = {
  method: "PUT",
  url: "/team/:personId/vetting",
  schema: {
    summary: "Update team person's vetting consent",
    description: "Trade can update their vetting consent",
    operationId: "putTeamPersonVetting",
    tags: ["Team"],
    params: teamParams,
    body: updateTeamPersonVettingQuery,
    headers: companyIdHeader,
    response: {
      200: updateTeamPersonVettingResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);

    const {
      params: { personId },
      body: { consent },
    } = req;

    try {
      const vettingResponse = await vetting.updateTradeVetting({
        consent,
        companyId,
        personId,
      });
      return res.send(mapToTeamVettingConsentStatusResponse(vettingResponse));
    } catch (error) {
      logger.error(error, "updateTeamPersonVetting error");
      if (error instanceof ApiError) {
        throw error;
      }
      if (error instanceof TypeBoxError) {
        throw new UnprocessableEntityError(
          "Schema mismatch upon validation",
          error,
        );
      }

      throw new InternalServerError(
        "Error updating team person vetting consent",
      );
    }
  },
};
