import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  ConflictError,
  InternalServerError,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  UpdateTeamInviteBody,
  UpdateTeamInviteParams,
  companyIdHeader,
  updateTeamInviteBody,
  updateTeamInviteParams,
  updateTeamInviteResponse,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const putTeamInvite: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: UpdateTeamInviteParams;
  Body: UpdateTeamInviteBody;
}> = {
  method: "PUT",
  url: "/team/invite/:id",
  schema: {
    summary: "Update team invite",
    description: "Trade can update a team invite",
    operationId: "putTeamInvite",
    tags: ["Team"],
    params: updateTeamInviteParams,
    body: updateTeamInviteBody,
    headers: companyIdHeader,
    response: {
      200: updateTeamInviteResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const {
      params: { id },
    } = req;

    try {
      logger.info("updateTeamInvite request", {
        body: req.body,
        id,
        companyId,
      });
      const invite = await team.updateTeamInvite(
        req.body,
        id,
        companyId,
        logger,
      );
      return res.send(invite);
    } catch (error) {
      logger.error(error, "updateTeamInvite error");
      if (error instanceof ApiError) {
        if (error.status === 409) {
          throw new ConflictError("Invite already accepted");
        }
        throw error;
      }
      throw new InternalServerError("Error updating team invite");
    }
  },
};
