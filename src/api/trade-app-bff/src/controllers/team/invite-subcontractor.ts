import { authTrade } from "@checkatrade/auth-trade";
import { InternalServerError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  InviteSubcontractor<PERSON><PERSON>y,
  companyIdHeader,
  inviteSubcontractorQuery,
  inviteSubcontractorResponse,
} from "@checkatrade/trade-bff-types";

import { config } from "../../config";
import { team } from "../../services/team";

export const inviteSubcontractor: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: InviteSubcontractorQuery;
}> = {
  method: "POST",
  url: "/team/invites",
  schema: {
    summary: "Invite subcontractor",
    description: "Create a subcontractor invite link",
    operationId: "inviteSubcontractor",
    tags: ["Team"],
    headers: companyIdHeader,
    body: inviteSubcontractorQuery,
    response: {
      201: inviteSubcontractorResponse,
    },
  },
  handler: async (req, res) => {
    const { body, log: logger } = req;
    const { companyId } = await authTrade(req);

    const invite = await team.inviteSubcontractor(body, companyId, logger);

    if (!invite) {
      throw new InternalServerError("Failed to invite subcontractor");
    }

    return res.status(201).send({
      id: invite.id,
      inviteUrl: `${config.websites.tradeSiteUrl}/my-team?inviteId=${invite.id}`,
    });
  },
};
