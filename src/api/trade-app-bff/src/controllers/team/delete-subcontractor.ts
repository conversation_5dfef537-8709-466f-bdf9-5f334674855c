import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  DeleteSubcontractorParams,
  DeleteSubcontractorQuery,
  companyIdHeader,
  deleteSubcontractorParams,
  deleteSubcontractorQuery,
  deleteSubcontractorResponse,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const deleteSubcontractor: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: DeleteSubcontractorParams;
  Querystring: DeleteSubcontractorQuery;
}> = {
  method: "DELETE",
  url: "/team/subcontractor/:subcontractorId",
  schema: {
    summary: "Delete subcontractor",
    description: "Delete subcontractor",
    operationId: "deleteSubcontractor",
    tags: ["Team"],
    headers: companyIdHeader,
    params: deleteSubcontractorParams,
    querystring: deleteSubcontractor<PERSON><PERSON>y,
    response: {
      200: deleteSubcontractor<PERSON><PERSON>ponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const { inviteId } = req.query;

    try {
      const subcontractor = await team.deleteSubcontractor(
        companyId,
        req.params.subcontractorId,
        inviteId,
        logger,
      );
      return res.status(200).send(subcontractor);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Error deleting subcontractor");
    }
  },
};
