import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  CompanyRole,
  GetTeamInvitesTradeDataResponse,
  GetTeamPendingInvitesTradeDataResponse,
  GetTeamTradeDataResponse,
  companyIdHeader,
  getTeamCountersResponse,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const getTeamCounters: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
}> = {
  method: "GET",
  url: "/team/counters",
  schema: {
    summary: "Get team counters",
    description: "Get team counters",
    operationId: "getTeamCounters",
    tags: ["Team"],
    headers: companyIdHeader,
    response: {
      200: getTeamCountersResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);

    const defaultParams = {
      page: 1,
      pageSize: 1,
    };

    const [employees, subcontractors, contractors] = await Promise.allSettled([
      team.getTeam({
        companyId,
        relationshipType: [CompanyRole.Employee],
        logger,
        searchText: "",
        page: defaultParams.page,
        pageSize: defaultParams.pageSize,
      }),
      team.getTeamInvites(
        companyId,
        logger,
        defaultParams.page,
        defaultParams.pageSize,
      ),
      team.getTeamPendingInvites(
        companyId,
        logger,
        defaultParams.page,
        defaultParams.pageSize,
      ),
    ]);

    const getCount = (
      result: PromiseSettledResult<
        | GetTeamTradeDataResponse
        | GetTeamInvitesTradeDataResponse
        | GetTeamPendingInvitesTradeDataResponse
        | undefined
      >,
    ): number => {
      if (result.status === "fulfilled") {
        return result.value?.pagination?.totalRecordsCount ?? 0;
      }
      if (result.status === "rejected") {
        logger.error({ error: result.reason }, "Error fetching team data");
      }
      return 0;
    };

    const counters = {
      totalEmployees: getCount(employees),
      totalSubcontractors: getCount(subcontractors),
      totalInvites: getCount(contractors),
    };

    return res.send(counters);
  },
};
