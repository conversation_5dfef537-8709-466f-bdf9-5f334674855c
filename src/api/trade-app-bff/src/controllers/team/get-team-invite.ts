import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetTeamInviteParams,
  Member,
  companyIdHeader,
  getTeamInviteParams,
  getTeamInviteResponse,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";
import { tradeData } from "../../services/trade-data-service";

export const getTeamInvite: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: GetTeamInviteParams;
}> = {
  method: "GET",
  url: "/team/invite/:id",
  schema: {
    summary: "Get team invite",
    description: "Get team invite",
    operationId: "getTeamInvite",
    tags: ["Team"],
    params: getTeamInviteParams,
    headers: companyIdHeader,
    response: {
      200: getTeamInviteResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { id } = req.params;
    const { companyId } = await authTrade(req);

    const teamInvite = await team.getTeamInvite(id, companyId, logger);

    if (!teamInvite) {
      logger.error(`Error getting team invite ${id}`);
      throw new NotFoundError("Invite not found");
    }

    let parentCompanyInfo: Pick<Member, "name" | "membershipType"> | null =
      null;

    try {
      const parentMemberDetails = await tradeData.getMember(
        teamInvite.parentMemberId,
        logger,
      );
      parentCompanyInfo =
        parentMemberDetails ?
          {
            name: parentMemberDetails.name,
            membershipType: parentMemberDetails.membershipType,
          }
        : null;
    } catch (error) {
      logger.error(
        error,
        `Failed to get parent member details for member ${teamInvite.parentMemberId}`,
      );
    }

    return res.send({
      ...teamInvite,
      parentCompanyInfo,
    });
  },
};
