import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  NotFoundError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  GetVettingConsentTokenDecodeHeaders,
  getVettingConsentTokenDecodeHeaders,
  getVettingConsentTokenDecodeResponse,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const getVettingConsentTokenDecode: FastifyRouteOptions<{
  Headers: GetVettingConsentTokenDecodeHeaders;
}> = {
  method: "GET",
  url: "/team/vetting-consent-token-decode",
  schema: {
    summary: "Decode vetting consent token",
    description: "Decode vetting consent token",
    operationId: "getVettingConsentTokenDecode",
    tags: ["Team"],
    headers: getVettingConsentTokenDecodeHeaders,
    response: {
      200: getVettingConsentTokenDecodeResponse,
      404: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger, headers } = req;
    const { companyId } = await authTrade(req);

    const { memberId } = (await team.getTeamMemberId(companyId, logger)) ?? {};

    if (!memberId) {
      throw new NotFoundError(
        `Failed to get member id for company ${companyId}`,
      );
    }

    const token = headers["x-trade-time-limited-token"];

    try {
      const decodedToken = await team.getVettingConsentTokenDecode(
        token,
        logger,
      );
      return res.send(decodedToken);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Error decoding vetting consent token");
    }
  },
};
