import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetTeamInvitesTradeDataResponse,
  companyIdHeader,
  getTeamInvitesQuery,
  getTeamInvitesResponse,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { team } from "../../services/team/index";
import { mapToGetTeamInvitesResponse } from "./mappers/invite-mappers";

export const getTeamInvites: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof getTeamInvitesQuery>;
}> = {
  method: "GET",
  url: "/team/invites",
  schema: {
    summary: "Get team invites",
    description: "Get team invites",
    operationId: "getTeamInvites",
    tags: ["Team"],
    querystring: getTeamInvitesQuery,
    headers: companyIdHeader,
    response: {
      200: getTeamInvitesResponse,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const { page = 1, pageSize = 10 } = req.query;

    try {
      const teamInvites: GetTeamInvitesTradeDataResponse | undefined =
        await team.getTeamInvites(companyId, logger, page, pageSize);

      if (teamInvites) {
        const mappedTeamInvites = mapToGetTeamInvitesResponse(teamInvites);
        return res.send(mappedTeamInvites);
      } else {
        return res.send({
          data: [],
          pages: 0,
          total: 0,
        });
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Error getting team persons");
    }
  },
};
