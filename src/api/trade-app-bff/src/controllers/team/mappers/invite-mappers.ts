import {
  GetTeamInvitesResponse,
  GetTeamInvitesTradeDataResponse,
  GetTeamPendingInvitesResponse,
  GetTeamPendingInvitesTradeDataResponse,
} from "@checkatrade/trade-bff-types";

export function mapToGetTeamInvitesResponse(
  teamInvites: GetTeamInvitesTradeDataResponse,
): GetTeamInvitesResponse {
  return {
    data: teamInvites.data,
    pages: teamInvites.pagination.totalPagesCount,
    total: teamInvites.pagination.totalRecordsCount,
  };
}

export function mapToGetTeamPendingInvitesResponse(
  teamPendingInvites: GetTeamPendingInvitesTradeDataResponse,
): GetTeamPendingInvitesResponse {
  return {
    data: teamPendingInvites.data,
    pages: teamPendingInvites.pagination.totalPagesCount,
    total: teamPendingInvites.pagination.totalRecordsCount,
  };
}
