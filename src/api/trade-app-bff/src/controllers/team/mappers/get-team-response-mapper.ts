import {
  CompanyRole,
  GetTeamResponse,
  GetTeamTradeDataResponse,
  GetVettingStatusResponse,
  GetWorkersVettingStatusResponse,
  VettingConsentStatus,
  VettingStatus,
  VettingStatusTVS,
  vettingStatusTVSMapper,
} from "@checkatrade/trade-bff-types";

export function mapToGetTeamResponse(
  teamPersons: GetTeamTradeDataResponse,
  teamVettingWorkers: GetWorkersVettingStatusResponse["workers"],
  companyVettingStatus?: VettingStatus,
): GetTeamResponse {
  return {
    data: teamPersons.data.map((person) => {
      const vettingWorker = teamVettingWorkers.find(
        (worker) => worker.externalId === person.id,
      );

      const basePersonData = {
        ...person,
        email: person.email ?? "",
      };

      // Owner/Director with non-Essentials membership
      if (
        person.role !== CompanyRole.Employee &&
        person.membershipType !== "Essentials Member"
      ) {
        return {
          ...basePersonData,
          vettingStatus: companyVettingStatus,
          consentStatus: VettingConsentStatus.NotCreated,
        };
      }

      // Employee with vetting record
      if (vettingWorker) {
        return {
          ...basePersonData,
          vettingStatus: getVettingStatus(vettingWorker),
          consentStatus: getConsentStatus(vettingWorker),
        };
      }

      // Employee without vetting record
      return {
        ...basePersonData,
        vettingStatus: companyVettingStatus ?? VettingStatus.InProgress,
        consentStatus: VettingConsentStatus.Granted,
      };
    }),
    pages: teamPersons.pagination.totalPagesCount,
    total: teamPersons.pagination.totalRecordsCount,
  };
}

function getVettingStatus(worker: GetVettingStatusResponse): VettingStatus {
  return worker.vettingStatus ?
      vettingStatusTVSMapper(worker.vettingStatus as VettingStatusTVS)
    : VettingStatus.NotStarted;
}

function getConsentStatus(
  worker: GetVettingStatusResponse,
): VettingConsentStatus {
  return worker.consentStatus ?
      (worker.consentStatus as VettingConsentStatus)
    : VettingConsentStatus.NotCreated;
}
