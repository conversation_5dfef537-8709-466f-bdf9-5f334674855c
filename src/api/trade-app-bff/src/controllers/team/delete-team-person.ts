import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  DeleteTeamPersonQuery,
  TeamParams,
  companyIdHeader,
  deleteTeamPersonQuery,
  deleteTeamPersonResponse,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const deleteTeamPerson: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: TeamParams;
  Body: DeleteTeamPersonQuery;
}> = {
  method: "DELETE",
  url: "/team/:personId",
  schema: {
    summary: "Delete team person",
    description: "Delete team person",
    operationId: "deleteTeamPerson",
    tags: ["Team"],
    headers: companyIdHeader,
    body: deleteTeamPersonQuery,
    response: {
      200: deleteTeamPersonResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);

    try {
      const person = await team.deleteTeamPerson(
        req.body,
        companyId,
        req.params.personId,
        logger,
      );
      return res.status(200).send(person);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Error deleting person");
    }
  },
};
