import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  NotFoundError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetTeamPersonResponse,
  TeamParams,
  companyIdHeader,
  contractingType,
  getTeamPersonResponseSchema,
  teamParams,
  vettingStatusType,
} from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

// TODO: Integrate with Trade Data Service
const generateMockedTeamPersonData = (
  personId: string,
): GetTeamPersonResponse => {
  const categories = [
    {
      id: "cat1",
      name: "Plumbing",
      subCategories: [
        { id: "111", name: "Pi<PERSON>" },
        { id: "112", name: "Faucets" },
      ],
    },
    {
      id: "cat2",
      name: "Electrical",
      subCategories: [
        { id: "121", name: "Wiring" },
        { id: "122", name: "Lighting" },
      ],
    },
  ];

  const vettingStatuses = vettingStatusType.anyOf.map((type) => type.const);
  const contractingTypes = contractingType.anyOf.map((type) => type.const);

  return {
    id: personId,
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    dateOfBirth: "1990-01-01",
    email: faker.internet.email(),
    phoneNumber: faker.phone.number(),
    address: {
      line1: faker.location.streetAddress(),
      line2: faker.location.secondaryAddress(),
      city: faker.location.city(),
      county: faker.location.county(),
      postalCode: faker.location.zipCode(),
    },
    category: categories,
    vettingStatus: vettingStatuses[0],
    contractingType: contractingTypes[0],
  };
};

async function getTeamPersonRecord(
  personId: string,
): Promise<GetTeamPersonResponse> {
  return generateMockedTeamPersonData(personId);
}

export const getTeamPerson: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: TeamParams;
}> = {
  method: "GET",
  url: "/team/:personId",
  schema: {
    summary: "Get team person",
    description: "Get team person",
    operationId: "getTeamPerson",
    tags: ["Team"],
    params: teamParams,
    headers: companyIdHeader,
    response: {
      200: getTeamPersonResponseSchema,
      404: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    await authTrade(req);
    const {
      params: { personId },
    } = req;

    try {
      const teamPerson = await getTeamPersonRecord(personId);

      if (!teamPerson) {
        throw new NotFoundError("Team person not found");
      }
      return res.send(teamPerson);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Error getting team person");
    }
  },
};
