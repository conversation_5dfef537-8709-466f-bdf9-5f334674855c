import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  NotFoundError,
  UnprocessableEntityError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  vettingStatusOnlySchema,
} from "@checkatrade/trade-bff-types";
import { TypeBoxError } from "@sinclair/typebox";

import { getMemberByCompanyId } from "../../services/team/get-member-by-company-id";
import { vetting } from "../../services/vetting";

export const getTeamVettingStatus: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
}> = {
  method: "GET",
  url: "/team/vetting-details",
  schema: {
    summary: "Get team vetting status",
    description: "Get team (company) vetting status via companyId header",
    operationId: "getTeamVettingStatus",
    tags: ["Team"],
    response: {
      200: vettingStatusOnlySchema,
      404: apiErrorSchema,
      422: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId: legacyCompanyId } = await authTrade(req);

    try {
      const { memberId } =
        (await getMemberByCompanyId(legacyCompanyId, logger)) ?? {};

      if (!memberId) {
        throw new NotFoundError("Member not found");
      }
      const vettingStatus =
        (await vetting.getCompanyVettingDetails(memberId)) ?? {};

      return res.status(200).send({ vettingStatus });
    } catch (error) {
      if (error instanceof ApiError) {
        logger.error(error, "Failed to fetch company vetting data");
        throw error;
      }
      if (error instanceof TypeBoxError) {
        throw new UnprocessableEntityError(error.message);
      }

      throw new InternalServerError("Error getting team vetting status");
    }
  },
};
