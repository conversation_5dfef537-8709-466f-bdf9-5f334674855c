import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetTeamPendingInvitesTradeDataResponse,
  companyIdHeader,
  getTeamPendingInvitesQuery,
  getTeamPendingInvitesResponse,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { team } from "../../services/team/index";
import { mapToGetTeamPendingInvitesResponse } from "./mappers/invite-mappers";

export const getTeamPendingInvites: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof getTeamPendingInvitesQuery>;
}> = {
  method: "GET",
  url: "/team/pending-invites",
  schema: {
    summary: "Get team pending invites",
    description: "Get team pending invites",
    operationId: "getTeamPendingInvites",
    tags: ["Team"],
    querystring: getTeamPendingInvitesQuery,
    headers: companyIdHeader,
    response: {
      200: getTeamPendingInvitesResponse,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const { page = 1, pageSize = 10, relationshipStatus } = req.query;

    try {
      const teamPendingInvites:
        | GetTeamPendingInvitesTradeDataResponse
        | undefined = await team.getTeamPendingInvites(
        companyId,
        logger,
        page,
        pageSize,
        relationshipStatus,
      );

      if (teamPendingInvites) {
        const mappedTeamPendingInvites =
          mapToGetTeamPendingInvitesResponse(teamPendingInvites);
        return res.send(mappedTeamPendingInvites);
      } else {
        return res.send({
          data: [],
          pages: 0,
          total: 0,
        });
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error(error, "Error getting team pending invites");
      throw new InternalServerError("Error getting team pending invites");
    }
  },
};
