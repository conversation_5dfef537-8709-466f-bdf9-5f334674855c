import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  TeamParams,
  UpdateTeamPersonsQuery,
  companyIdHeader,
  teamParams,
  updateTeamPersonResponse,
  updateTeamPersonsQuery,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const putTeamPerson: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: TeamParams;
  Body: UpdateTeamPersonsQuery;
}> = {
  method: "PUT",
  url: "/team/:personId/persons",
  schema: {
    summary: "Update team person",
    description: "Trade can update a team person",
    operationId: "putTeamPerson",
    tags: ["Team"],
    params: teamParams,
    body: updateTeamPersonsQuery,
    headers: companyIdHeader,
    response: {
      200: updateTeamPersonResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    await authTrade(req);
    const {
      params: { personId },
    } = req;

    try {
      logger.info(
        {
          body: req.body,
          personId,
        },
        "updateTeamPerson request",
      );
      const person = await team.updateTeamPerson(req.body, personId, logger);
      return res.send(person);
    } catch (error) {
      logger.error(error, "updateTeamPerson error");
      if (error instanceof ApiError) {
        throw error;
      }
      throw new InternalServerError("Error updating team person");
    }
  },
};
