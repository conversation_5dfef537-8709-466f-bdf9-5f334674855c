import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  companyIdHeader,
  getIsFullMemberOrNationalAccountResponse,
} from "@checkatrade/trade-bff-types";

import { team } from "../../services/team";

export const getIsFullMemberOrNationalAccount: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
}> = {
  method: "GET",
  url: "/team/is-full-member-or-national-account",
  schema: {
    summary: "Get is full member or national account",
    description: "Get is full member or national account",
    operationId: "getIsFullMemberOrNationalAccount",
    tags: ["Team"],
    headers: companyIdHeader,
    response: {
      200: getIsFullMemberOrNationalAccountResponse,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);

    const isFullMemberOrNationalAccount =
      await team.getIsFullMemberOrNationalAccount(companyId, logger);

    return res.send(isFullMemberOrNationalAccount);
  },
};
