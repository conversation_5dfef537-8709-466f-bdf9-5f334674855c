import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError, apiErrorSchema } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  CompanyRole,
  GetWorkersVettingStatusResponse,
  companyIdHeader,
  getTeamQuery,
  getTeamResponse,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { team } from "../../services/team";
import { vetting } from "../../services/vetting";
import { mapToGetTeamResponse } from "./mappers/get-team-response-mapper";

export const getTeam: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof getTeamQuery>;
}> = {
  method: "GET",
  url: "/team/persons",
  schema: {
    summary: "Get team persons",
    description: "Get team persons",
    operationId: "getTeamPersons",
    tags: ["Team"],
    querystring: getTeamQuery,
    headers: companyIdHeader,
    response: {
      200: getTeamResponse,
      404: apiErrorSchema,
      422: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const {
      searchTerm,
      page,
      pageSize,
      sortBy,
      sortOrder,
      contractingType,
      status,
    } = req.query;

    const relationshipType: CompanyRole[] = [
      CompanyRole.Owner,
      CompanyRole.Director,
      CompanyRole.AdminContact,
    ];
    if (contractingType === "Employee") {
      relationshipType.push(CompanyRole.Employee);
    }

    const teamPersons = await team.getTeam({
      companyId,
      relationshipType,
      logger,
      searchText: searchTerm,
      page,
      pageSize,
      sortBy,
      sortOrder,
      status,
    });

    if (!teamPersons) {
      throw new NotFoundError("Team persons not found");
    }
    let teamVetting: GetWorkersVettingStatusResponse | undefined;

    try {
      teamVetting = await vetting.getTeamVettingDetails(
        companyId,
        contractingType,
      );
    } catch (error) {
      logger.error(error, "Error getting team vetting details");
    }

    const companyVettingStatus = await (async () => {
      const memberId = await getHandledMemberId(companyId, logger);
      return await vetting.getCompanyVettingDetails(memberId);
    })();

    const mappedTeamPerson = mapToGetTeamResponse(
      teamPersons,
      teamVetting?.workers ?? [],
      companyVettingStatus,
    );
    return res.send(mappedTeamPerson);
  },
};
