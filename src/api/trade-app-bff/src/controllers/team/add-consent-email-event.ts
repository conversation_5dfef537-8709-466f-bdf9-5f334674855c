import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  AddConsentEmailBody,
  AddConsentEmailParams,
  CompanyIdHeader,
  addConsentEmailBody,
  addConsentEmailParams,
  addConsentEmailResponse,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { team } from "../../services/team";

export const addConsentEmailEvent: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: AddConsentEmailParams;
  Body: AddConsentEmailBody;
}> = {
  method: "POST",
  url: "/team/:personId/consent-email",
  schema: {
    summary: "Create consent email",
    description: "Publish an event to generate a consent email in Braze",
    operationId: "addConsentEmailEvent",
    tags: ["Team"],
    headers: companyIdHeader,
    params: addConsentEmailParams,
    body: addConsentEmailBody,
    response: {
      200: addConsentEmailResponse,
      404: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId } = await authTrade(req);
    const memberId = await getHandledMemberId(companyId, logger);

    const { personId } = req.params;

    try {
      const email = await team.addConsentEmailEvent(
        req.body,
        companyId,
        personId,
        memberId,
        logger,
      );
      return res.send(email);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Error creating consent email event");
    }
  },
};
