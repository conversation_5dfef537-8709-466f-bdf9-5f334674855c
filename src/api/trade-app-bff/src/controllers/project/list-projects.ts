import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ProjectTypes, projectSDKsingleton } from "@checkatrade/project-sdk";
import { reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { getAlbumDocByCompanyId } from "../../services/firebase/firestore/get-albums";

export const listCompanyProjects: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: ProjectTypes.Api.List.Query;
  Reply: ProjectTypes.Api.List.Response;
}> = {
  method: "GET",
  url: "/projects",
  schema: {
    summary: "List company projects",
    description: "Retrieve a list of projects for a specific company",
    operationId: "listCompanyProjects",
    tags: ["Projects"],
    headers: companyIdHeader,
    querystring:
      projectSDKsingleton.schemas.project.api.project.list.querystring,
    response: {
      200: projectSDKsingleton.schemas.project.api.project.list.response,
    },
  },
  handler: async (req, res) => {
    const { log } = req;
    const { companyId } = await authTrade(req);

    try {
      const projects = await projectSDKsingleton.public.listCompanyProjects(
        companyId,
        req.query,
      );

      projects.data = await Promise.all(
        projects.data.map(async (project) => {
          if (project.album?.id) {
            const albumDoc = await getAlbumDocByCompanyId(
              companyId.toString(),
              project.album.id,
            );
            if (albumDoc) {
              project.album = albumDoc;
            }
          }
          if (project.review?.id) {
            project.review = await reviewSDK
              .public()
              .getReview(project.review.id, {
                companyId,
              });
          }
          return project;
        }),
      );

      return res.status(200).send(projects);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      log.error(
        `Unexpected error occurred while listing projects for company ${companyId}`,
        { error, query: req.query },
      );
      throw new InternalServerError("Failed to list projects");
    }
  },
};
