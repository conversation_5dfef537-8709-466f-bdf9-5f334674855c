import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ProjectTypes, projectSDKsingleton } from "@checkatrade/project-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

export const updateProject: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ProjectTypes.Api.Update.Params;
  Body: ProjectTypes.Api.Update.Body;
  Reply: ProjectTypes.Api.Update.Response;
}> = {
  method: "PATCH",
  url: "/project/:projectId",
  schema: {
    summary: "Update project",
    description: "Partially update a project by ID",
    operationId: "updateProject",
    tags: ["Projects"],
    headers: companyIdHeader,
    params: projectSDKsingleton.schemas.project.api.project.update.params,
    body: projectSDKsingleton.schemas.project.api.project.update.body,
    response: {
      200: projectSDKsingleton.schemas.project.api.project.update.response,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { projectId } = req.params;
    const updateData = {
      ...req.body,
      companyId,
    };

    try {
      const updatedProject = await projectSDKsingleton.public.updateProject(
        projectId,
        updateData,
      );

      return res.status(200).send(updatedProject);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new InternalServerError("Failed to update project");
    }
  },
};
