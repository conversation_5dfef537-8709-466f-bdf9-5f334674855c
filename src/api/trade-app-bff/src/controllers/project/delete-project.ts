import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ProjectTypes, projectSDKsingleton } from "@checkatrade/project-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

export const deleteProject: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ProjectTypes.Api.Delete.Params;
}> = {
  method: "DELETE",
  url: "/project/:projectId",
  schema: {
    summary: "Delete project",
    description: "Delete a project by ID",
    operationId: "deleteProject",
    tags: ["Projects"],
    headers: companyIdHeader,
    params: projectSDKsingleton.schemas.project.api.project.delete.params,
    response: {
      204: { type: "null", description: "No Content" },
    },
  },
  handler: async (req, res) => {
    try {
      await authTrade(req);

      const { projectId } = req.params;

      await projectSDKsingleton.public.deleteProject(projectId);

      return res.status(204).send();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      req.log.error("Unexpected error occurred while deleting project", error);
      throw new InternalServerError("Failed to delete project");
    }
  },
};
