import { authTrade } from "@checkatrade/auth-trade";
import { ApiError, InternalServerError } from "@checkatrade/errors";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ProjectTypes, projectSDKsingleton } from "@checkatrade/project-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

export const postProject: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: ProjectTypes.Api.Create.Body;
  Reply: ProjectTypes.Api.Create.Response;
}> = {
  method: "POST",
  url: "/project",
  schema: {
    summary: "Post project",
    description: "Create a new featured project",
    operationId: "postProject",
    tags: ["Projects"],
    headers: companyIdHeader,
    body: projectSDKsingleton.schemas.project.api.project.create.body,
    response: {
      201: projectSDKsingleton.schemas.project.api.project.create.response,
    },
  },
  handler: async (req, res) => {
    const { log } = req;
    try {
      const { companyId } = await authTrade(req);

      const projectData = {
        ...req.body,
        companyId,
      };

      const createdProject =
        await projectSDKsingleton.public.postProject(projectData);

      return res.status(201).send(createdProject);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      log.error("Unexpected error occurred while creating project", error);
      throw new InternalServerError("Failed to create project draft");
    }
  },
};
