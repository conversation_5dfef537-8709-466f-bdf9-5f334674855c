/* eslint-disable @typescript-eslint/ban-ts-comment */
import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  UnprocessableEntityError,
} from "@checkatrade/errors";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { ProjectTypes, projectSDKsingleton } from "@checkatrade/project-sdk";
import { reviewSDK } from "@checkatrade/review-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { ZodError } from "zod";

import { getAlbumDocByCompanyId } from "../../services/firebase/firestore/get-albums";

export const getProject: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ProjectTypes.Api.Get.Params;
  Reply: ProjectTypes.Api.Get.Response;
}> = {
  method: "GET",
  url: "/project/:projectId",
  schema: {
    summary: "Get project",
    description: "Retrieve a project by ID",
    operationId: "getProject",
    tags: ["Projects"],
    headers: companyIdHeader,
    params: projectSDKsingleton.schemas.project.api.project.get.params,
    // response: {
    //   200: projectSDKsingleton.schemas.project.api.project.get.response,
    // },
  },
  handler: async (req, res) => {
    const { log } = req;
    try {
      const { companyId } = await authTrade(req);
      const { projectId } = req.params;

      const project = await projectSDKsingleton.public.getProject(projectId);
      if (project.album?.id || project.review?.id) {
        const promises = [];
        if (project.album?.id) {
          const albumDocPromise = getAlbumDocByCompanyId(
            companyId.toString(),
            project.album.id,
          );
          promises[0] = albumDocPromise;
        }
        if (project.review?.id) {
          promises[1] = reviewSDK.public().getReview(project.review.id, {
            companyId,
          });
        }
        const [albumResponse, reviewResponse] =
          await Promise.allSettled(promises);

        if (albumResponse.status === "fulfilled") {
          // @ts-ignore
          project.album = albumResponse.value;
        } else if (albumResponse.status === "rejected") {
          log.error("Error fetching album", { error: albumResponse.reason });
        }
        if (reviewResponse.status === "fulfilled") {
          // @ts-ignore
          project.review = reviewResponse.value;
        } else if (reviewResponse.status === "rejected") {
          log.error("Error fetching review", { error: reviewResponse.reason });
        }
      }

      return res.status(200).send(project);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof ZodError) {
        log.error("Zod validation error", error);
        throw new UnprocessableEntityError("Invalid request data", {
          error: error.message,
        });
      }

      log.error("Unexpected error occurred while getting project", { error });
      throw new InternalServerError("Failed to retrieve project");
    }
  },
};
