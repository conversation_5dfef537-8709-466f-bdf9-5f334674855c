import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { schemas } from "@checkatrade/schemas";
import { Static } from "@sinclair/typebox";

export const getLiveness: FastifyRouteOptions<{
  Reply: Static<typeof schemas.healthCheck>;
}> = {
  method: "GET",
  url: "/_internal/health/liveness",
  schema: {
    summary: "Checks the liveness of the application",
    description:
      "Returns a simple status object indicating if the application is alive.",
    operationId: "getLiveness",
    tags: ["Health"],
    hide: true,
    security: [],
    response: {
      200: schemas.healthCheck,
    },
  },
  handler: async () => ({
    uptime: process.uptime(),
    message: "OK",
    timestamp: Date.now(),
  }),
};
