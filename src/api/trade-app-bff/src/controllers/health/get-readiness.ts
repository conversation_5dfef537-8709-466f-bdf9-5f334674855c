import { ServiceUnavailableError } from "@checkatrade/errors";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { schemas } from "@checkatrade/schemas";
import { Static } from "@sinclair/typebox";

import { firebase } from "../../services/firebase";

export const getReadiness: FastifyRouteOptions<{
  Reply: Static<typeof schemas.healthCheck>;
}> = {
  method: "GET",
  url: "/_internal/health/readiness",
  schema: {
    summary: "Checks the readiness of the application",
    description:
      "Checks if the database is accessible and returns valid status if yes",
    operationId: "getReadiness",
    tags: ["Health"],
    hide: true,
    security: [],
    response: {
      200: schemas.healthCheck,
    },
  },
  handler: async () => {
    try {
      await firebase.utils.isAccessible();
    } catch (error) {
      const { message } = error as Error;
      const errors = [
        {
          resource: "Firebase",
          message,
        },
      ];
      const resources = errors.map((e) => e.resource).join(", ");
      throw new ServiceUnavailableError(
        `Services inaccessible: ${resources}`,
        errors,
      );
    }

    return {
      uptime: process.uptime(),
      message: "OK",
      timestamp: Date.now(),
    };
  },
};
