import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { schemas } from "@checkatrade/schemas";
import { Static } from "@sinclair/typebox";

export const getStartup: FastifyRouteOptions<{
  Reply: Static<typeof schemas.healthCheck>;
}> = {
  method: "GET",
  url: "/_internal/health/startup",
  schema: {
    summary: "Checks the startup health of the application",
    description:
      "Checks if there are any pending migrations to be performed on the database.",
    operationId: "getStartup",
    tags: ["Health"],
    hide: true,
    security: [],
    response: {
      200: schemas.healthCheck,
    },
  },
  handler: async () => {
    return {
      uptime: process.uptime(),
      message: "OK",
      timestamp: Date.now(),
    };
  },
};
