import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { Static } from "@sinclair/typebox";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import utc from "dayjs/plugin/utc";

dayjs.extend(duration);
dayjs.extend(utc);

export const formatAppointmentAlternatives = (
  alternatives: Pick<
    Static<typeof schedulingSDK.schemas.api.trade.postAppointment.response>,
    "alternatives"
  >["alternatives"],
) =>
  alternatives
    .map(({ start, end }) => {
      const startTime = dayjs.utc(start);
      let durationString = "";

      if (end) {
        const endTime = dayjs.utc(end);
        const dur = dayjs.duration(endTime.diff(startTime));
        const hours = dur.hours();
        const minutes = dur.minutes();

        if (hours > 0 || minutes > 0) {
          const hourString = hours > 0 ? `${hours}h` : "";
          const minuteString = minutes > 0 ? `${minutes}min` : "";
          durationString = `${hourString} ${minuteString}`.trim();
        }
      }

      const formattedStartTime = startTime.format("D MMMM YYYY HH:mm");
      const formattedDuration = durationString ? ` (${durationString})` : "";
      return `${formattedStartTime}${formattedDuration}`;
    })
    .join(", ");
