import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { formatAppointmentAlternatives } from "./appointment.formatter";

export const postAppointment: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: Static<typeof schedulingSDK.schemas.api.trade.postAppointment.body>;
  Reply: Static<
    typeof schedulingSDK.schemas.api.trade.postAppointment.response
  >;
}> = {
  method: "POST",
  url: "/appointments",
  schema: {
    summary: "Offer a new appointment",
    description: "Trade offers a new appointment for a job with time options.",
    operationId: "postAppointment",
    tags: ["Appointments"],
    headers: companyIdHeader,
    body: schedulingSDK.schemas.api.trade.postAppointment.body,
    response: {
      201: schedulingSDK.schemas.api.trade.postAppointment.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { jobId } = req.body;
    const { log: logger } = req;

    const job = await jobsSDK.trade(token, companyId).getJob(jobId);
    const appointment = await schedulingSDK
      .trade(token, companyId)
      .createAppointment(req.body);

    await chatSDK
      .sendSmartMessage({
        channelId: job.opportunityId,
        senderId: companyId,
        // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        smartType: "APPOINTMENT" as any,
        text: `The trade has offered a new appointment: ${formatAppointmentAlternatives(appointment.alternatives)} for "${appointment.type.title}"`,
        appointmentId: appointment.id,
        appointment: {
          id: appointment.id,
          title: appointment.type.title,
          alternatives: appointment.alternatives,
          status: appointment.status,
        },
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.status(201).send(appointment);
  },
};
