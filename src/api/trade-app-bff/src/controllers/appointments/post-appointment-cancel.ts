import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import {
  AlternativeStatus,
  AppointmentCategory,
  schedulingSDK,
} from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export const postAppointmentCancel: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<
    typeof schedulingSDK.schemas.api.trade.cancelAppointment.params
  >;
  Reply: Static<
    typeof schedulingSDK.schemas.api.trade.cancelAppointment.response
  >;
  Body: Static<typeof schedulingSDK.schemas.api.trade.cancelAppointment.body>;
}> = {
  method: "POST",
  url: "/appointments/:appointmentId/cancel",
  schema: {
    summary: "Cancel existing appointment",
    description: "Trade wants to cancel appointment for a job.",
    operationId: "postAppointmentCancel",
    tags: ["Appointments"],
    headers: companyIdHeader,
    body: schedulingSDK.schemas.api.trade.cancelAppointment.body,
    params: schedulingSDK.schemas.api.trade.cancelAppointment.params,
    response: {
      200: schedulingSDK.schemas.api.trade.cancelAppointment.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { appointmentId } = req.params;
    const { log: logger } = req;

    const oldAppointment = await schedulingSDK
      .trade(token, companyId)
      .getAppointment(appointmentId);

    const appointment = await schedulingSDK
      .trade(token, companyId)
      .cancelAppointment(appointmentId, req.body);

    const job = await jobsSDK.trade(token, companyId).getJob(appointment.jobId);

    if (oldAppointment.type.category === AppointmentCategory.JOB_START) {
      await jobsSDK
        .trade(token, companyId)
        .cancelAppointment(appointment.jobId);
    }

    const data: { start?: string; end?: string } = {};

    if (oldAppointment.status === AlternativeStatus.ACCEPTED) {
      const acceptedAlternative = oldAppointment.alternatives.find(
        (alternative) => alternative.status === AlternativeStatus.ACCEPTED,
      );

      if (acceptedAlternative) {
        data.start = acceptedAlternative.start;
        data.end = acceptedAlternative.end;
      }
    }

    await chatSDK
      .sendSmartMessage({
        channelId: job.opportunityId,
        senderId: companyId,
        // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        smartType: "APPOINTMENT_CANCELLED" as any,
        text: `The trade has cancelled "${appointment.type.title}" appointment`,
        appointmentId: appointment.id,
        appointment: {
          id: appointment.id,
          title: appointment.type.title,
          status: oldAppointment.status,
          ...data,
        },
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.send(appointment);
  },
};
