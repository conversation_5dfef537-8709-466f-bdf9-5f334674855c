import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { formatAppointmentAlternatives } from "./appointment.formatter";

export const patchAppointment: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<
    typeof schedulingSDK.schemas.api.trade.patchAppointment.params
  >;
  Body: Static<typeof schedulingSDK.schemas.api.trade.patchAppointment.body>;
  Reply: Static<
    typeof schedulingSDK.schemas.api.trade.patchAppointment.response
  >;
}> = {
  method: "PATCH",
  url: "/appointments/:appointmentId",
  schema: {
    summary: "Update existing appointment",
    description:
      "Trade wants to change the existing appointment for a job with time options.",
    operationId: "patchAppointment",
    tags: ["Appointments"],
    params: schedulingSDK.schemas.api.trade.patchAppointment.params,
    headers: companyIdHeader,
    body: schedulingSDK.schemas.api.trade.patchAppointment.body,
    response: {
      200: schedulingSDK.schemas.api.trade.patchAppointment.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { appointmentId } = req.params;
    const { log: logger } = req;

    const appointment = await schedulingSDK
      .trade(token, companyId)
      .patchAppointment(appointmentId, req.body);

    const job = await jobsSDK.trade(token, companyId).getJob(appointment.jobId);

    await chatSDK
      .sendSmartMessage({
        channelId: job.opportunityId,
        senderId: companyId,
        // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        smartType: "APPOINTMENT_UPDATED" as any,
        text: `The trade has updated "${appointment.type.title}" appointment: ${formatAppointmentAlternatives(appointment.alternatives)}`,
        appointmentId: appointment.id,
        appointment: {
          id: appointment.id,
          title: appointment.type.title,
          alternatives: appointment.alternatives,
          status: appointment.status,
        },
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.status(200).send(appointment);
  },
};
