import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

export const getAppointment: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Params: Static<typeof schedulingSDK.schemas.api.trade.getAppointment.params>;
  Reply: Static<typeof schedulingSDK.schemas.api.trade.getAppointment.response>;
}> = {
  method: "GET",
  url: "/appointments/:appointmentId",
  schema: {
    summary: "Get an appointment details",
    description: "Trade gets an appointment details for a job.",
    operationId: "getAppointment",
    tags: ["Appointments"],
    headers: companyIdHeader,
    response: {
      200: schedulingSDK.schemas.api.trade.getAppointment.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { appointmentId } = req.params;

    const appointment = await schedulingSDK
      .trade(token, companyId)
      .getAppointment(appointmentId);

    return res.status(200).send(appointment);
  },
};
