import { authTrade } from "@checkatrade/auth-trade";
import { chatSDK } from "@checkatrade/chat-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { jobsSDK } from "@checkatrade/jobs-sdk";
import {
  AppointmentCategory,
  schedulingSDK,
} from "@checkatrade/scheduling-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { formatAppointmentAlternatives } from "./appointment.formatter";

export const postAppointmentReschedule: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: Static<typeof schedulingSDK.schemas.api.trade.postAppointment.body>;
  Params: Static<
    typeof schedulingSDK.schemas.api.trade.cancelAppointment.params
  >;
  Reply: Static<
    typeof schedulingSDK.schemas.api.trade.postAppointment.response
  >;
}> = {
  method: "POST",
  url: "/appointments/:appointmentId/reschedule",
  schema: {
    summary: "Reschedule existing appointment",
    description: "Trade wants to reschedule appointment for a job.",
    operationId: "postAppointmentReschedule",
    tags: ["Appointments"],
    params: schedulingSDK.schemas.api.trade.cancelAppointment.params,
    headers: companyIdHeader,
    body: schedulingSDK.schemas.api.trade.postAppointment.body,
    response: {
      200: schedulingSDK.schemas.api.trade.postAppointment.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);
    const { appointmentId } = req.params;
    const { log: logger } = req;

    const oldAppointment = await schedulingSDK
      .trade(token, companyId)
      .cancelAppointment(appointmentId);

    const appointment = await schedulingSDK
      .trade(token, companyId)
      .createAppointment(req.body);

    if (oldAppointment.type.category === AppointmentCategory.JOB_START) {
      await jobsSDK
        .trade(token, companyId)
        .cancelAppointment(appointment.jobId);
    }

    const job = await jobsSDK.trade(token, companyId).getJob(appointment.jobId);

    await chatSDK
      .sendSmartMessage({
        channelId: job.opportunityId,
        senderId: companyId,
        // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        smartType: "APPOINTMENT_RESCHEDULED" as any,
        text: `The trade has rescheduled "${appointment.type.title}" appointment: ${formatAppointmentAlternatives(appointment.alternatives)}`,
        appointmentId: appointment.id,
        appointment: {
          id: appointment.id,
          title: appointment.type.title,
          alternatives: appointment.alternatives,
          status: appointment.status,
        },
        logger,
      })
      .catch((error) => {
        logger.debug(error.message);
      });

    return res.send(appointment);
  },
};
