import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { CancelReasonListResponse, jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

export const getCancelReasons: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: CancelReasonListResponse;
}> = {
  method: "GET",
  url: "/job/cancel-reasons",
  schema: {
    summary: "Get cancel reasons",
    description: "Get list of cancel reasons",
    operationId: "getCancelReasons",
    tags: ["Job"],
    headers: companyIdHeader,
    response: {
      200: jobsSDK.schemas.trade.getCancelReasons.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const reasons = await jobsSDK.trade(token, companyId).getCancelReasons();

    return res.send(reasons);
  },
};
