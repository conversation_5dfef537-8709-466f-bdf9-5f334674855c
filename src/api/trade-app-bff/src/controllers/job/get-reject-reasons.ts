import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { RejectReasonListResponse, jobsSDK } from "@checkatrade/jobs-sdk";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

export const getRejectReasons: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: RejectReasonListResponse;
}> = {
  method: "GET",
  url: "/job/reject-reasons",
  schema: {
    summary: "Get reject reasons",
    description: "Get list of reject reasons",
    operationId: "getRejectReasons",
    tags: ["Job"],
    headers: companyIdHeader,
    response: {
      200: jobsSDK.schemas.trade.getRejectReasons.response,
    },
  },
  handler: async (req, res) => {
    const { token, companyId } = await authTrade(req);

    const reasons = await jobsSDK.trade(token, companyId).getRejectReasons();

    return res.send(reasons);
  },
};
