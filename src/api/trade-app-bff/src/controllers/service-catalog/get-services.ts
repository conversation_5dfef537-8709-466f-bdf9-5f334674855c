import { authTrade } from "@checkatrade/auth-trade";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { serviceCatalogSDK } from "@checkatrade/service-catalog-sdk";
import {
  OrderDirection,
  PaginationOrderingRequestSchema,
} from "@checkatrade/service-catalog-types";
import {
  CompanyIdHeader,
  GET_SERVICES_RESPONSE_SCHEMA,
  GetServicesResponseSchema,
  ServiceVersionWithDetails,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

import { getCategoryName } from "../../lib/api-common/services/search-api/category/get-category-name";

export const getServices: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring?: PaginationOrderingRequestSchema;
  Reply: GetServicesResponseSchema;
}> = {
  method: "GET",
  url: "/service-catalog/services",
  schema: {
    summary: "Retrieves a list of services for a company",
    headers: companyIdHeader,
    response: {
      200: GET_SERVICES_RESPONSE_SCHEMA,
    },
    description:
      "Retrieves a list of services for a company, including only the current service version",
    operationId: "getServices",
    tags: ["Trade", "ServiceCatalog"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { pageNumber, pageSize, orderBy, orderDirection } = req.query ?? {};

    const result = await serviceCatalogSDK.trade.serviceVersions.getServices(
      companyId.toString(),
      {
        pageNumber: pageNumber ?? 1,
        pageSize: pageSize ?? 10,
        orderBy,
        orderDirection: orderDirection ?? OrderDirection.Ascending,
      },
    );

    const enrichServiceData = result.data.map(async (service) => {
      try {
        const categoryName = await getCategoryName(service.categoryId);
        return {
          ...service,
          categoryName,
        } as ServiceVersionWithDetails;
      } catch {
        return {
          ...service,
          categoryName: service.categoryId.toString(),
        } as ServiceVersionWithDetails;
      }
    });

    const enrichedServiceDataResult = await Promise.all(enrichServiceData);

    res.send({
      data: enrichedServiceDataResult,
      pagination: result.pagination,
    });
  },
};
