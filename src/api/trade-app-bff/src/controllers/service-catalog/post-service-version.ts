import { authTrade } from "@checkatrade/auth-trade";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { serviceCatalogSDK } from "@checkatrade/service-catalog-sdk";
import { StatusType } from "@checkatrade/service-catalog-types";
import {
  CompanyIdHeader,
  POST_SERVICE_VERSION_RESPONSE_SCHEMA,
  PostServiceVersionResponseSchema,
  SERVICE_PARAMS,
  SERVICE_VERSION_BODY_SCHEMA,
  ServiceParams,
  ServiceVersionBodySchema,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

export const postServiceVersion: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ServiceParams;
  Body: ServiceVersionBodySchema;
  Reply: PostServiceVersionResponseSchema;
}> = {
  method: "POST",
  url: "/service-catalog/service/:serviceId/version",
  schema: {
    summary: "Creates a new service version for a company",
    headers: companyIdHeader,
    params: SERVICE_PARAMS,
    body: SERVICE_VERSION_BODY_SCHEMA,
    response: {
      200: POST_SERVICE_VERSION_RESPONSE_SCHEMA,
    },
    description:
      "Creates a new service version for a company with the provided details, or updates the current version if editable",
    operationId: "postServiceVersion",
    tags: ["Trade", "ServiceCatalog"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { serviceId } = req.params;
    const body = req.body;

    const serviceHistory =
      await serviceCatalogSDK.trade.serviceVersions.getServiceHistory(
        companyId.toString(),
        serviceId,
      );

    const currentVersion = serviceHistory.currentVersion;

    if (
      currentVersion &&
      [StatusType.Draft, StatusType.InReview].includes(currentVersion.status)
    ) {
      const updated =
        await serviceCatalogSDK.trade.serviceVersions.updateServiceVersion(
          companyId.toString(),
          serviceId,
          currentVersion.id,
          { ...body, status: StatusType.Draft },
        );
      return res.send(updated);
    }

    const created =
      await serviceCatalogSDK.trade.serviceVersions.createServiceVersion(
        companyId.toString(),
        serviceId,
        body,
      );
    return res.send(created);
  },
};
