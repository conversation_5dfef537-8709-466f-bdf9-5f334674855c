import { authTrade } from "@checkatrade/auth-trade";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { serviceCatalogSDK } from "@checkatrade/service-catalog-sdk";
import {
  CompanyIdHeader,
  PostServiceVersionResponseSchema,
  SERVICE_VERSION_PARAMS,
  SERVICE_VERSION_STATUS_UPDATE_BODY_SCHEMA,
  ServiceVersionParams,
  ServiceVersionStatusUpdateBodySchema,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

export const patchUpdateServiceVersionStatus: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ServiceVersionParams;
  Body: ServiceVersionStatusUpdateBodySchema;
  Reply: PostServiceVersionResponseSchema;
}> = {
  method: "PATCH",
  url: "/service-catalog/service/:serviceId/version/:versionId/status",
  schema: {
    summary: "Update status on service version",
    headers: companyIdHeader,
    params: SERVICE_VERSION_PARAMS,
    body: SERVICE_VERSION_STATUS_UPDATE_BODY_SCHEMA,
    response: {
      204: {},
    },
    description: "Updates the status of a service version.",
    operationId: "patchServiceVersionStatus",
    tags: ["Trade", "ServiceCatalog"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { serviceId, versionId } = req.params;

    const body = req.body;

    await serviceCatalogSDK.trade.serviceVersions.updateServiceVersionStatus(
      companyId.toString(),
      serviceId,
      versionId,
      body,
    );

    res.status(204).send();
  },
};
