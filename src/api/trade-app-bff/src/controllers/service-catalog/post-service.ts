import { authTrade } from "@checkatrade/auth-trade";
import type { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { serviceCatalogSDK } from "@checkatrade/service-catalog-sdk";
import {
  CompanyIdHeader,
  POST_SERVICE_VERSION_RESPONSE_SCHEMA,
  PostServiceVersionResponseSchema,
  SERVICE_VERSION_BODY_SCHEMA,
  ServiceVersionBodySchema,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

export const postService: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Body: ServiceVersionBodySchema;
  Reply: PostServiceVersionResponseSchema;
}> = {
  method: "POST",
  url: "/service-catalog/service",
  schema: {
    summary: "Creates a new service for a company",
    headers: companyIdHeader,
    body: SERVICE_VERSION_BODY_SCHEMA,
    response: {
      200: POST_SERVICE_VERSION_RESPONSE_SCHEMA,
    },
    description:
      "Creates a new service for a company with the provided details",
    operationId: "postService",
    tags: ["Trade", "ServiceCatalog"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);

    const body = req.body;

    const result = await serviceCatalogSDK.trade.serviceVersions.createService(
      companyId.toString(),
      body,
    );

    res.send(result);
  },
};
