import { authTrade } from "@checkatrade/auth-trade";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { serviceCatalogSDK } from "@checkatrade/service-catalog-sdk";
import {
  CompanyIdHeader,
  SERVICE_VERSION_PARAMS,
  SERVICE_VERSION_WITH_DETAILS_RESPONSE,
  ServiceVersionParams,
  ServiceVersionWithDetails,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";

import { getCategoryName } from "../../lib/api-common/services/search-api/category/get-category-name";

export const getServiceVersion: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: ServiceVersionParams;
  Reply: ServiceVersionWithDetails;
}> = {
  method: "GET",
  url: "/service-catalog/service/:serviceId/version/:versionId",
  schema: {
    summary: "Retrieves a service version for a company",
    headers: companyIdHeader,
    params: SERVICE_VERSION_PARAMS,
    response: {
      200: SERVICE_VERSION_WITH_DETAILS_RESPONSE,
    },
    description:
      "Retrieves a service version for all current services for a company",
    operationId: "getServiceVersion",
    tags: ["Trade", "Service"],
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { serviceId, versionId } = req.params;

    const data =
      await serviceCatalogSDK.trade.serviceVersions.getServiceVersion(
        companyId.toString(),
        serviceId,
        versionId,
      );

    const categoryName = await getCategoryName(data.categoryId);

    return res.send({ ...data, categoryName });
  },
};
