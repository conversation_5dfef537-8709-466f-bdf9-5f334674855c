import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { referralFactoryApi } from "../../lib/api-common";
import { ReferralData } from "../../lib/api-common/services/referral-factory-api/schema";
import { getCompanyDoc } from "../../services/firebase/firestore/get-company";
import { referralDataResponseSchema } from "./referral.schema";

const CAT_REFERRAL_URL = "https://referrals.checkatrade.com";

const queryStringSchema = Type.Object({
  campaignId: Type.Number(),
});

type ReferralDataResponse = Static<typeof referralDataResponseSchema>;

function mapReferralDataToResponse(
  referralDataResponse: ReferralData,
): ReferralDataResponse {
  return {
    code: referralDataResponse.code,
    campaignId: referralDataResponse.campaign_id,
    url: referralDataResponse.url,
    sharing: referralDataResponse.sharing,
  };
}

export const gerReferralCode: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: Static<typeof queryStringSchema>;
}> = {
  method: "GET",
  url: "/referral",
  schema: {
    summary: "Get a referral factory code",
    description: "Fetches a referral factory code for a trade",
    operationId: "getReferralCode",
    querystring: queryStringSchema,
    tags: ["Referral"],
    headers: companyIdHeader,
    response: {
      200: referralDataResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { email } = authTradeCapi(req);
    const { campaignId } = req.query;
    const { log: logger } = req;

    const referral = await referralFactoryApi.getUser({
      email,
      campaignId,
      logger,
    });

    if (referral) {
      referral.url = `${CAT_REFERRAL_URL}/${referral.code}`;
      return res.status(200).send(mapReferralDataToResponse(referral));
    }

    const company = await getCompanyDoc(companyId.toString());
    if (!company) {
      return res.status(404).send({ message: "Company not found" });
    }

    const newReferral = await referralFactoryApi.postUser({
      email,
      name: company?.name ?? "Tradesperson",
      campaignId,
      logger,
    });

    if (newReferral) {
      newReferral.url = `${CAT_REFERRAL_URL}/${newReferral.code}`;
      return res.status(200).send(mapReferralDataToResponse(newReferral));
    }

    return res.status(404).send({ message: "Referral Factory User not found" });
  },
};
