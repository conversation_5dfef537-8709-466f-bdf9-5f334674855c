import { Type } from "@sinclair/typebox";

export const referralSocialLinkSchema = Type.Object({
  social: Type.String(),
  url: Type.String({ format: "uri" }), // Validate URL format using 'format' option
});

export const referralDataResponseSchema = Type.Object({
  code: Type.String(),
  campaignId: Type.Number(),
  url: Type.String({ format: "uri" }),
  sharing: Type.Array(referralSocialLinkSchema),
});
