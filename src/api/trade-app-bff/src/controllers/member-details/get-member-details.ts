import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { apiErrorSchema } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  type GetMemberDetailsResponse,
  GetMemberDetailsResponseSchema, // Member,
  companyIdHeader,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { memberDetails } from "../../services/member-details/member-details";

export const getMemberDetails: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: GetMemberDetailsResponse | Static<typeof apiErrorSchema>;
}> = {
  method: "GET",
  url: "/member-details",
  schema: {
    summary: "Fetch member details",
    description:
      "Returns detailed member information for the authenticated user.",
    operationId: "getMemberDetails",
    tags: ["MemberDetails"],
    headers: companyIdHeader,
    response: {
      200: GetMemberDetailsResponseSchema,
      404: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const authToken = authTradeCapi(req);

    const details = await memberDetails({
      companyId,
      userEmail: authToken.email,
    });

    return res.send(details);
  },
};
