import dayjs from "dayjs";

import { Accreditation } from "../../../services/firebase/firestore/schemas/accreditation";
import {
  AccreditationStatusType,
  PersonAccreditation,
} from "../../../services/firebase/firestore/schemas/person-accreditations";
import {
  AccreditationStatus,
  PersonAccreditationResponse,
} from "../accreditations.schema";

const ACCREDITATIONS_EXPIRES_SOON_DAYS = 31;

export const daysUntilExpiration = (expiryDate: Date): number => {
  const now = Date.now();
  const then = expiryDate.getTime();

  const millisecondsInADay = 1000 * 60 * 60 * 24;
  return Math.round((then - now) / millisecondsInADay);
};

export const expiresSoon = (
  personAccreditation: PersonAccreditation,
): boolean => {
  if (!personAccreditation.expiryDate) {
    return false;
  }
  const expiresInDays = daysUntilExpiration(personAccreditation.expiryDate);
  return expiresInDays == null ? false : (
      expiresInDays < ACCREDITATIONS_EXPIRES_SOON_DAYS
    );
};

export const calculateAccreditationStatus = (
  personAccreditation: PersonAccreditation,
  accreditation?: Accreditation,
): AccreditationStatus => {
  const currentDate = new Date();

  if (personAccreditation.status === AccreditationStatusType.Pending) {
    return AccreditationStatus.PendingReview;
  }

  if (
    (!accreditation ||
      (accreditation.canExpire && accreditation.requiresApproval)) &&
    personAccreditation.status === AccreditationStatusType.Approved &&
    personAccreditation.expiryDate
  ) {
    if (currentDate > personAccreditation.expiryDate) {
      return AccreditationStatus.Expired;
    }

    if (personAccreditation.withFurtherAction) {
      return AccreditationStatus.FurtherActionRequired;
    }

    if (expiresSoon(personAccreditation)) {
      return AccreditationStatus.ExpiresSoon;
    }
  }

  switch (personAccreditation.status) {
    case AccreditationStatusType.Approved:
      return AccreditationStatus.Active;
    case AccreditationStatusType.Rejected:
      return AccreditationStatus.Rejected;
    default:
      throw new Error(`Unexpected status: ${personAccreditation.status}`);
  }
};

const getStatusDate = (
  status: AccreditationStatus,
  personAccreditationType: PersonAccreditation,
): Date => {
  switch (status) {
    case AccreditationStatus.Active:
      return (
        personAccreditationType.approvedDate ??
        personAccreditationType.modifiedDate!
      );
    case AccreditationStatus.Expired:
    case AccreditationStatus.ExpiresSoon:
      return personAccreditationType.expiryDate as Date;
    default: {
      const mostRecentUpdate = personAccreditationType.history.slice(-1)[0];
      return mostRecentUpdate.updatedDate;
    }
  }
};

const getStatusText = (status: AccreditationStatus, date: Date) => {
  const formattedDate =
    date ? dayjs(date).format("D MMM YYYY") : "Invalid Date";

  switch (status) {
    case AccreditationStatus.Active:
      return `Active from ${formattedDate}`;
    case AccreditationStatus.Rejected:
      return `Rejected ${formattedDate}`;
    case AccreditationStatus.Expired:
      return `Expired ${formattedDate}`;
    case AccreditationStatus.ExpiresSoon:
      return `Expires on ${formattedDate}`;
    case AccreditationStatus.PendingReview:
    default:
      return `Added ${formattedDate}`;
  }
};

export const mapDocumentToPersonAccreditationResponse = (
  document: PersonAccreditation,
  accreditation: Accreditation | undefined,
): PersonAccreditationResponse => {
  const status = calculateAccreditationStatus(document, accreditation);
  const statusDate = getStatusDate(status, document);
  const statusText = getStatusText(status, statusDate);

  return {
    id: document.id!,
    accreditationName: accreditation?.name,
    accreditationLogo: accreditation?.logoFilename,
    canExpire: accreditation?.canExpire ?? false,
    companyId: document.companyId,
    personId: document.personId,
    accreditationId: document.accreditationId,
    status: calculateAccreditationStatus(document, accreditation),
    statusDate: statusDate.toISOString(),
    proof:
      document.proof?.map((attachment) => ({
        ...attachment,
        uploadedDate: attachment.uploadedDate.toISOString(),
      })) || [],
    statusText,
  };
};
