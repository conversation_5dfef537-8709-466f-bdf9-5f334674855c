import { Accreditation } from "../../../services/firebase/firestore/schemas/accreditation";
import {
  AccreditationApprovalType,
  AccreditationMutationType,
  AccreditationPlatform,
  AccreditationStatusType,
  Attachment,
  PersonAccreditation,
} from "../../../services/firebase/firestore/schemas/person-accreditations";
import {
  AttachmentBody,
  PostAccreditationBody,
} from "../accreditations.schema";

export const mapPostAccreditationBodyToDocument = (
  companyId: number,
  personId: string,
  body: PostAccreditationBody,
  accreditationDefinition: Accreditation,
  existingAccreditation: PersonAccreditation | undefined,
): PersonAccreditation => {
  const status =
    accreditationDefinition.requiresApproval ?
      AccreditationStatusType.Pending
    : AccreditationStatusType.Approved;

  const mutationType =
    accreditationDefinition.requiresApproval ?
      AccreditationMutationType.Submitted
    : AccreditationMutationType.Approved;

  const historyItem = {
    platform: AccreditationPlatform.TradeApp,
    type: mutationType,
    updatedDate: new Date(),
  };

  const history = existingAccreditation?.history || [];
  history.push(historyItem);

  const result: PersonAccreditation = {
    ...(existingAccreditation || {}),
    accreditationId: body.accreditationId,
    withFurtherAction: false,
    personId: personId,
    companyId: companyId,
    isDeleted: false,
    status,
    history,
  };

  if (!accreditationDefinition.requiresApproval) {
    result.approvalType = AccreditationApprovalType.NotRequired;
    result.approvedDate = new Date();
  }

  if (accreditationDefinition.canExpire && body.expiryDate) {
    result.expiryDate = new Date(body.expiryDate);
  }

  if (body.proof) {
    result.proof = body.proof.map(
      (p: AttachmentBody): Attachment => ({
        fileName: p.fileName,
        fileSize: p.fileSize,
        reference: p.reference,
        uploadedDate: new Date(p.uploadedDate),
        mimeType: p.mimeType,
      }),
    );
  }

  return result;
};
