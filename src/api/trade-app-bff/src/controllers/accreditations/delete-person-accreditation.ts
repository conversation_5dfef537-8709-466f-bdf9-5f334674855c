import { authTrade } from "@checkatrade/auth-trade";
import { BadRequestError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  companyIdHeader,
  teamParams,
} from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { accreditations } from "../../services/accreditations";

const deleteAccreditationParams = Type.Composite([
  Type.Object({
    id: Type.Number(),
  }),
  teamParams,
]);

export const deletePersonAccreditation: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: Static<typeof deleteAccreditationParams>;
}> = {
  method: "DELETE",
  url: "/person/:personId/accreditations/:id",
  schema: {
    summary: "Delete accreditation",
    description: "Soft delete accreditation by accreditation",
    params: deleteAccreditationParams,
    tags: ["Accreditations"],
    operationId: "deletePersonAccreditation",
    headers: companyIdHeader,
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { id: accreditationId, personId } = req.params;
    const { log } = req;

    const isValidPerson = await accreditations.isPersonInCompany(
      companyId,
      personId,
      log,
    );
    if (!isValidPerson) {
      throw new BadRequestError(
        `Person ${personId} not found in company ${companyId}`,
      );
    }

    const existingAccreditation = await accreditations.getPersonAccreditation(
      companyId,
      personId,
      accreditationId,
    );

    if (existingAccreditation) {
      await accreditations.deletePersonAccreditation(existingAccreditation);
    }

    res.send(200);
  },
};
