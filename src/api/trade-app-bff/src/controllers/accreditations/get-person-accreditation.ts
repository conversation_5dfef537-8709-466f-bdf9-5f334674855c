import { authTrade } from "@checkatrade/auth-trade";
import { NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { accreditations } from "../../services/accreditations";
import {
  GetAccreditationParams,
  GetAccreditationParamsSchema,
  PersonAccreditationResponse,
  PersonAccreditationsResponseSchema,
} from "./accreditations.schema";
import { mapDocumentToPersonAccreditationResponse } from "./mappers";

export const getPersonAccreditation: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: GetAccreditationParams;
  Reply: PersonAccreditationResponse;
}> = {
  method: "GET",
  url: "/person/:personId/accreditations/:accreditationId",
  schema: {
    summary: "Get person accreditation",
    description: "Get accreditation details for a person.",
    tags: ["Accreditations"],
    operationId: "getPersonAccreditation",
    headers: companyIdHeader,
    params: GetAccreditationParamsSchema,
    response: {
      200: PersonAccreditationsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { personId, accreditationId } = req.params;
    const { log } = req;
    log.info(
      `Getting accreditations for company ${companyId} accreditationId ${accreditationId} personId ${personId}`,
    );

    const personAccreditation = await accreditations.getPersonAccreditation(
      companyId,
      personId,
      accreditationId,
    );

    if (!personAccreditation) {
      throw new NotFoundError(
        `Accreditation ${accreditationId} not found for person ${personId}`,
      );
    }

    const accreditation =
      await accreditations.getAccreditationById(accreditationId);
    if (!accreditation) {
      throw new NotFoundError(
        `Accreditation ${accreditationId} not found in accreditations collection`,
      );
    }

    res.send(
      mapDocumentToPersonAccreditationResponse(
        personAccreditation,
        accreditation,
      ),
    );
  },
};
