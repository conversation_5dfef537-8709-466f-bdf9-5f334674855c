import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  TeamParams,
  companyIdHeader,
  teamParams,
} from "@checkatrade/trade-bff-types";

import { getHandledMemberId } from "../../helpers/get-handled-member-id";
import { accreditations } from "../../services/accreditations";
import { getTeamPerson } from "../../services/team/get-team-person";
import {
  GetPersonRequiredAccreditationsResponse,
  GetPersonRequiredAccreditationsResponseSchema,
} from "./accreditations.schema";

export const getRequiredAccreditations: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: TeamParams;
  Reply: GetPersonRequiredAccreditationsResponse;
}> = {
  method: "GET",
  url: "/person/:personId/required-accreditations",
  schema: {
    summary: "Get person accreditations",
    description: "Get all accreditations for a person",
    tags: ["Accreditations"],
    operationId: "getPersonRequiredAccreditations",
    params: teamParams,
    headers: companyIdHeader,
    response: {
      200: GetPersonRequiredAccreditationsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { personId } = req.params;
    const { log } = req;
    const memberId = await getHandledMemberId(companyId, log);

    const person = await getTeamPerson(memberId, { personId }, log);

    log.info(
      `Getting accreditations required for company ${companyId} personId ${personId} categories`,
    );

    const personWorkCategories = person?.data[0].workCategories;

    if (!personWorkCategories) {
      log.info(`No work categories for person ${personId}`);
      res.send({ accreditationIds: [] });
      return;
    }

    const requiredAccreditations =
      await accreditations.getRequiredAccreditations(personWorkCategories, log);

    res.send({ accreditationIds: requiredAccreditations ?? [] });
  },
};
