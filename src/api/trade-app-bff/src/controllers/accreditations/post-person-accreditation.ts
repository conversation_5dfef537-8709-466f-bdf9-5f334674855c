import { authTrade } from "@checkatrade/auth-trade";
import { BadRequestError, NotFoundError } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  TeamParams,
  companyIdHeader,
  teamParams,
} from "@checkatrade/trade-bff-types";

import { accreditations } from "../../services/accreditations";
import { Accreditation } from "../../services/firebase/firestore/schemas/accreditation";
import {
  PersonAccreditationResponse,
  PersonAccreditationsResponseSchema,
  PostAccreditationBody,
  PostAccreditationBodySchema,
} from "./accreditations.schema";
import {
  mapDocumentToPersonAccreditationResponse,
  mapPostAccreditationBodyToDocument,
} from "./mappers";

function validatePostPersonAccreditationBody(
  body: PostAccreditationBody,
  accreditationDefinition: Accreditation,
) {
  const { expiryDate, proof } = body;

  if (accreditationDefinition.requiresApproval) {
    if (!proof || proof.length === 0) {
      throw new BadRequestError(
        "Proof is required when accreditation requires approval",
      );
    }

    if (accreditationDefinition.canExpire) {
      if (!expiryDate) {
        throw new BadRequestError("expiryDate is required");
      }

      if (new Date(expiryDate) < new Date()) {
        throw new BadRequestError("expiryDate cannot be in the past");
      }
    }
  }
}

export const postPersonAccreditation: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: TeamParams;
  Body: PostAccreditationBody;
  Reply: PersonAccreditationResponse;
}> = {
  method: "POST",
  url: "/person/:personId/accreditations",
  schema: {
    summary: "Post accreditation",
    description: "Create new accreditation or overwrite existing",
    tags: ["Accreditations"],
    operationId: "postAccreditation",
    headers: companyIdHeader,
    params: teamParams,
    body: PostAccreditationBodySchema,
    response: {
      201: PersonAccreditationsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { personId } = req.params;
    const { log } = req;
    const { accreditationId } = req.body;

    log.info({ data: req.body, companyId, personId }, `Posting accreditation`);

    const isValidPerson = await accreditations.isPersonInCompany(
      companyId,
      personId,
      log,
    );
    if (!isValidPerson) {
      throw new BadRequestError(
        `Person ${personId} not found in company ${companyId}`,
      );
    }

    const accreditationDefinition =
      await accreditations.getAccreditationById(accreditationId);
    if (!accreditationDefinition) {
      throw new NotFoundError(`Accreditation ${accreditationId} not found`);
    }

    validatePostPersonAccreditationBody(req.body, accreditationDefinition);

    const existingAccreditation = await accreditations.getPersonAccreditation(
      companyId,
      personId,
      accreditationId,
    );

    const document = mapPostAccreditationBodyToDocument(
      companyId,
      personId,
      req.body,
      accreditationDefinition,
      existingAccreditation,
    );

    const updatedDocument = await accreditations.setPersonAccreditation(
      document,
      existingAccreditation?.id,
    );

    res.send(
      mapDocumentToPersonAccreditationResponse(
        updatedDocument,
        accreditationDefinition,
      ),
    );
  },
};
