import { authTrade } from "@checkatrade/auth-trade";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  TeamParams,
  companyIdHeader,
  teamParams,
} from "@checkatrade/trade-bff-types";

import { accreditations } from "../../services/accreditations";
import {
  GetPersonAccreditationsResponse,
  GetPersonAccreditationsResponseSchema,
} from "./accreditations.schema";
import { mapDocumentToPersonAccreditationResponse } from "./mappers";

export const getPersonAccreditations: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: TeamParams;
  Reply: GetPersonAccreditationsResponse;
}> = {
  method: "GET",
  url: "/person/:personId/accreditations",
  schema: {
    summary: "Get person accreditations",
    description: "Get all accreditations for a person",
    tags: ["Accreditations"],
    operationId: "getPersonAccreditations",
    params: teamParams,
    headers: companyIdHeader,
    response: {
      200: GetPersonAccreditationsResponseSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const { personId } = req.params;

    const { log } = req;
    log.info(
      `Getting accreditations for company ${companyId} personId ${personId}`,
    );

    const personAccreditations = await accreditations.getPersonAccreditations(
      companyId,
      personId,
      log,
    );

    const response = await Promise.all(
      personAccreditations
        .filter((pa) => !pa.isDeleted)
        .map(async (pa) => {
          const accreditation = await accreditations.getAccreditationById(
            pa.accreditationId,
          );
          return mapDocumentToPersonAccreditationResponse(pa, accreditation);
        }),
    );

    res.send(response);
  },
};
