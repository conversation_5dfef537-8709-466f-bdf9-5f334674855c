import { teamParams } from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { AttachmentSchema } from "../../services/firebase/firestore/schemas/person-accreditations";

export enum AccreditationStatus {
  Active = "Active",
  Rejected = "Rejected",
  ExpiresSoon = "Expires Soon",
  Expired = "Expired",
  PendingReview = "Pending Review",
  FurtherActionRequired = "Further Action Required",
}

export const AttachmentResponseSchema = Type.Composite([
  Type.Omit(AttachmentSchema, ["uploadedDate"]),
  Type.Object({
    uploadedDate: Type.String({ format: "date-time" }),
  }),
]);

export const PersonAccreditationsResponseSchema = Type.Object({
  id: Type.String(),
  accreditationId: Type.Number(),
  personId: Type.String(),
  companyId: Type.Number(),
  accreditationName: Type.Optional(Type.String()),
  accreditationLogo: Type.Optional(Type.String()),
  canExpire: Type.Boolean(),
  status: Type.Enum(AccreditationStatus),
  proof: Type.Optional(Type.Array(AttachmentResponseSchema)),
  statusDate: Type.String({ format: "date-time" }),
  statusText: Type.String(),
});

export const AttachmentBodySchema = Type.Composite([
  Type.Omit(AttachmentSchema, ["uploadedDate"]),
  Type.Object({
    uploadedDate: Type.String({ format: "date-time" }),
  }),
]);

export const PostAccreditationBodySchema = Type.Object({
  accreditationId: Type.Number(),
  expiryDate: Type.Optional(Type.String({ format: "date-time" })),
  proof: Type.Optional(Type.Array(AttachmentBodySchema)),
});

export const GetAccreditationParamsSchema = Type.Composite([
  Type.Object({
    accreditationId: Type.Number(),
  }),
  teamParams,
]);

export const GetPersonAccreditationsResponseSchema = Type.Array(
  PersonAccreditationsResponseSchema,
);

export const GetPersonRequiredAccreditationsResponseSchema = Type.Object({
  accreditationIds: Type.Array(Type.Optional(Type.Number())),
});

export type PostAccreditationBody = Static<typeof PostAccreditationBodySchema>;
export type AttachmentBody = Static<typeof AttachmentBodySchema>;
export type AttachmentResponse = Static<typeof AttachmentResponseSchema>;
export type PersonAccreditationResponse = Static<
  typeof PersonAccreditationsResponseSchema
>;
export type GetAccreditationParams = Static<
  typeof GetAccreditationParamsSchema
>;
export type GetPersonAccreditationsResponse = Static<
  typeof GetPersonAccreditationsResponseSchema
>;

export type GetPersonRequiredAccreditationsResponse = Static<
  typeof GetPersonRequiredAccreditationsResponseSchema
>;
