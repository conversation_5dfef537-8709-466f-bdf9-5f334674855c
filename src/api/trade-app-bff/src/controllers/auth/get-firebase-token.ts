import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { UnauthorizedError, apiErrorSchema } from "@checkatrade/errors";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  companyIdHeader,
  getFirebaseTokenResponseSchema,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { hooks } from "../../lib/api-common";
import { firebase } from "../../services/firebase";

type CheckatradeClaims = {
  "cat.org": { [key: number]: string };
  "custom_user_info": {
    uid: string;
    email: string;
  };
};

export const getFirebaseToken: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: string | Static<typeof apiErrorSchema>;
}> = {
  method: "GET",
  url: "/auth/createCustomFirebaseToken",
  schema: {
    summary: "Return firebase token from an authenticated request.",
    description:
      "Handles an authenticated request, uses the claims in the auth token to create a token to use with firebase.",
    operationId: "getFirebaseToken",
    headers: companyIdHeader,
    response: {
      200: getFirebaseTokenResponseSchema,
      401: apiErrorSchema,
    },
  },
  onSend: hooks.onSend.noCache, // NOSONAR
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const {
      accounts,
      id: capiUserId,
      email: capiUserEmail,
    } = authTradeCapi(req);

    const account = accounts.find((account) => account.companyId === companyId);
    if (!account) {
      throw new UnauthorizedError("Invalid auth token");
    }

    // Generate a custom firebase token with additional claims
    const additionalClaims: CheckatradeClaims = {
      "cat.org": { [companyId]: account.salesforceVettingStatus },
      // "uid" will appear in two places:
      // 1. Firebase will create uid at the root level of the token related to the Firebase user id,
      // 2. `additionalClaims` will be placed into the `claims` field, where "uid" will point to a `capiUserId` value.
      // Notice that the token will be sent to API gateway proxy, which merges incoming `claims` to the root level of a new token,
      //   thus, `"uid": "firebaseUserId", { "claims": { "uid": "capiUserId", "cat.org": {}, "email: "string"} }` after API Gateway processing
      //   will appear as `{ "uid": "capiUserId", "cat.org": {}, "email: "string"} }` in the final destination.
      "custom_user_info": { uid: capiUserId, email: capiUserEmail },
    };
    const firebaseToken = await firebase.auth.createCustomToken(
      account.id,
      additionalClaims,
    );

    return res.send(firebaseToken);
  },
};
