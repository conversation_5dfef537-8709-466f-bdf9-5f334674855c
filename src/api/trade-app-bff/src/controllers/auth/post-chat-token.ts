import { authTrade } from "@checkatrade/auth-trade";
import { ChatTokenData, chatSDK, chatTokenData } from "@checkatrade/chat-sdk";
import { type FastifyRouteOptions } from "@checkatrade/fastify-five";
import { CompanyIdHeader, companyIdHeader } from "@checkatrade/trade-bff-types";

import { hooks } from "../../lib/api-common";

export const postChatToken: FastifyRouteOptions<{
  Header: CompanyIdHeader;
  Reply: ChatTokenData;
}> = {
  method: "POST",
  url: "/auth/chat",
  schema: {
    summary: "Fetch chat configuration",
    description:
      "Returns an Stream API key and access token for the current user.",
    operationId: "postChatToken",
    tags: ["Chat"],
    headers: companyIdHeader,
    response: {
      200: chatTokenData,
    },
  },
  onSend: hooks.onSend.noCache, // NOSONAR
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    return res.send(chatSDK.generateNewToken(companyId));
  },
};
