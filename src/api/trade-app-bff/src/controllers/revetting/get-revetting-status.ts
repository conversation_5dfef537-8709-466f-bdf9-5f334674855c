import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  NotFoundError,
  UnprocessableEntityError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  companyIdHeader,
  revettingStatusSchema,
} from "@checkatrade/trade-bff-types";
import { TypeBoxError } from "@sinclair/typebox";

import { getMemberByCompanyId } from "../../services/team/get-member-by-company-id";
import { getMember } from "../../services/trade-data-service/get-member";
import { vetting } from "../../services/vetting";

export const getRevettingStatus: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
}> = {
  method: "GET",
  url: "/revetting/status",
  schema: {
    summary: "Get re-vetting status",
    description: "Get re-vetting status",
    operationId: "getRevettingStatus",
    tags: ["Revetting"],
    headers: companyIdHeader,
    response: {
      200: revettingStatusSchema,
      404: apiErrorSchema,
      422: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId: legacyCompanyId } = await authTrade(req);

    try {
      const { memberId } =
        (await getMemberByCompanyId(legacyCompanyId, logger)) ?? {};
      const member = memberId ? await getMember(memberId, logger) : undefined;

      if (!member) {
        throw new NotFoundError("Member not found");
      }

      const revettingStatus = await vetting.getRevettingStatus(member);

      return res.status(200).send({ revettingStatus });
    } catch (error) {
      if (error instanceof ApiError) {
        logger.error(error, "Failed to fetch re-vetting data");
        throw error;
      }
      if (error instanceof TypeBoxError) {
        throw new UnprocessableEntityError(error.message);
      }

      throw new InternalServerError("Error getting re-vetting status");
    }
  },
};
