import { authTrade } from "@checkatrade/auth-trade";
import {
  ApiError,
  InternalServerError,
  UnprocessableEntityError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  companyIdHeader,
  revettingUrlSchema,
} from "@checkatrade/trade-bff-types";
import { TypeBoxError } from "@sinclair/typebox";

import { vetting } from "../../services/vetting";

export const getRevettingUrl: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
}> = {
  method: "GET",
  url: "/revetting/url",
  schema: {
    summary: "Get re-vetting url",
    description: "Get re-vetting url",
    operationId: "getRevettingUrl",
    tags: ["Revetting"],
    headers: companyIdHeader,
    response: {
      200: revettingUrlSchema,
      404: apiErrorSchema,
      422: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { log: logger } = req;
    const { companyId: legacyCompanyId } = await authTrade(req);

    try {
      const url = await vetting.getRevettingRequestLink(
        legacyCompanyId,
        logger,
      );

      return res.status(200).send({ url });
    } catch (error) {
      if (error instanceof ApiError) {
        logger.error(error, "Failed to fetch re-vetting data");
        throw error;
      }
      if (error instanceof TypeBoxError) {
        throw new UnprocessableEntityError(error.message);
      }

      throw new InternalServerError("Error getting re-vetting status");
    }
  },
};
