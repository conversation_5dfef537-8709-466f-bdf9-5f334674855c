import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { ForbiddenError, apiErrorSchema } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetCampaignStatsQuery,
  GetCampaignStatsResponse,
  companyIdHeader,
  getCampaignStatsQuery,
  getCampaignStatsResponse,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { hasAccessToCompany } from "../../helpers/auth-helpers";
import { adManager } from "../../services/ad-manager";

export const getCampaignStats: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Querystring: GetCampaignStatsQuery;
  Reply: GetCampaignStatsResponse | Static<typeof apiErrorSchema>;
}> = {
  method: "GET",
  url: "/campaigns/stats",
  schema: {
    summary: "Get campaign statistics",
    description: "Returns statistics summary for all campaigns",
    operationId: "getCampaignStats",
    tags: ["Campaigns"],
    headers: companyIdHeader,
    querystring: getCampaignStatsQuery,
    response: {
      200: getCampaignStatsResponse,
      401: apiErrorSchema,
      403: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    await authTrade(req);
    const authToken = authTradeCapi(req);
    const { from, to, dimensions, companyId } = req.query;

    if (!hasAccessToCompany(authToken, companyId)) {
      throw new ForbiddenError(
        "Only the account owner can get campaign statistics",
      );
    }

    const { log: logger } = req;

    try {
      const stats = await adManager.getCampaignStats(
        logger,
        from,
        to,
        dimensions,
        companyId,
      );

      return res.send(stats);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
