import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import {
  BadRequestError,
  ForbiddenError,
  ServiceUnavailableError,
  apiErrorSchema,
} from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  CreateCampaignsRequest,
  CreateCampaignsResponse,
  companyIdHeader,
  createCampaignsRequestSchema,
  createCampaignsResponseSchema,
} from "@checkatrade/trade-bff-types";
import { Static, Type } from "@sinclair/typebox";

import { hasAccessToCompany } from "../../helpers/auth-helpers";
import { adManager } from "../../services/ad-manager";

/**
 * Define the URL parameters for the create campaigns endpoint
 */
export const campaignsParams = Type.Object({
  companyId: Type.Integer({
    description: "Company ID for which to create campaigns",
    minimum: 1,
  }),
});

/**
 * Type for the URL parameters
 */
export type CampaignsParams = Static<typeof campaignsParams>;

/**
 * Controller for creating Sponsored Listings campaigns
 *
 * This endpoint allows the Trade Experience app to create new Sponsored Listings
 * campaigns in the ad-manager service. It validates the request, checks authorization,
 * and forwards the request to the ad-manager service.
 *
 */
export const createCampaigns: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Params: CampaignsParams;
  Body: CreateCampaignsRequest;
  Reply: CreateCampaignsResponse | Static<typeof apiErrorSchema>;
}> = {
  method: "POST",
  url: "/ad-manager/advertisers/:companyId/campaigns",
  schema: {
    summary: "Create Sponsored Listings campaigns",
    description:
      "Creates new Sponsored Listings campaigns for a company and returns the campaign IDs",
    operationId: "createCampaigns",
    tags: ["Ad Manager", "Campaigns"],
    headers: companyIdHeader,
    params: campaignsParams,
    body: createCampaignsRequestSchema,
    response: {
      200: createCampaignsResponseSchema,
      400: apiErrorSchema,
      401: apiErrorSchema,
      403: apiErrorSchema,
      500: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId: headerCompanyId } = await authTrade(req);
    const authToken = authTradeCapi(req);
    const { log: logger } = req;
    const { companyId: urlCompanyId } = req.params;

    // Convert URL param to number for comparison
    const companyIdFromUrl = Number(urlCompanyId);

    // Validate that companyId is a positive integer
    if (!Number.isInteger(companyIdFromUrl) || companyIdFromUrl <= 0) {
      logger.warn({ urlCompanyId }, "Invalid company ID format");
      throw new BadRequestError("Company ID must be a positive integer");
    }

    // Validate that the companyId in the URL matches the one in the header
    if (headerCompanyId !== companyIdFromUrl) {
      logger.warn(
        { headerCompanyId, urlCompanyId },
        "Company ID in URL does not match the one in the header",
      );
      throw new ForbiddenError("Company ID mismatch between URL and header");
    }

    // Use the companyId from the URL for all subsequent operations
    const companyId = companyIdFromUrl;

    if (!hasAccessToCompany(authToken, companyId)) {
      logger.warn(
        { companyId },
        "User attempted to create campaigns for a company they don't have access to",
      );
      throw new ForbiddenError("Only the account owner can create campaigns");
    }

    if (!req.body || req.body.length === 0) {
      logger.warn(
        { companyId },
        "Attempted to create campaigns with empty payload",
      );
      throw new BadRequestError("At least one campaign is required");
    }

    try {
      logger.info(
        {
          companyId,
          campaignCount: req.body.length,
          // Log campaign types but not sensitive data
          campaignTypes: req.body.map((c) => c.type),
        },
        "Creating campaigns",
      );

      const { email, id: userId } = authToken;

      const campaignIds = await adManager.createCampaigns(
        logger,
        companyId,
        req.body,
        email,
        userId,
      );

      logger.info({ companyId, campaignIds }, "Successfully created campaigns");

      return res.code(200).send(campaignIds);
    } catch (error) {
      // Enhanced error handling
      const err = error as Error & {
        response?: { status: number; data: unknown };
        request?: unknown;
      };

      if (error instanceof BadRequestError) {
        logger.warn(
          { error: err, companyId },
          "Bad request when creating campaigns",
        );
        throw error;
      }

      if (error instanceof ForbiddenError) {
        logger.warn(
          { error: err, companyId },
          "Forbidden when creating campaigns",
        );
        throw error;
      }

      if (error instanceof ServiceUnavailableError) {
        logger.error(
          { error: err, companyId },
          "Service unavailable when creating campaigns",
        );
        throw error;
      }

      if (err.response) {
        // Pass through the status code from the ad-manager service
        const status = err.response.status;
        const data = err.response.data;

        logger.error(
          { error: err, companyId, status, data },
          "Error from ad-manager service when creating campaigns",
        );

        // Create a proper error response
        const errorResponse = {
          type:
            "https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/" +
            status,
          status,
          title: "Ad Manager Error",
          instance: req.url,
          detail:
            typeof data === "object" && data && "message" in data ?
              String(data.message)
            : "Error from ad-manager service",
        };

        return res.code(status).send(errorResponse);
      }

      // Generic error handling
      logger.error(
        { error: err, companyId },
        "Unexpected error when creating campaigns",
      );
      throw error;
    }
  },
};
