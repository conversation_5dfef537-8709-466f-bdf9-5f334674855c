import { authTrade, authTradeCapi } from "@checkatrade/auth-trade";
import { ForbiddenError, apiErrorSchema } from "@checkatrade/errors";
import { FastifyRouteOptions } from "@checkatrade/fastify-five";
import {
  CompanyIdHeader,
  GetExperiencesResponse,
  companyIdHeader,
  getExperiencesResponse,
} from "@checkatrade/trade-bff-types";
import { Static } from "@sinclair/typebox";

import { hasAccessToCompany } from "../../helpers/auth-helpers";
import { adManager } from "../../services/ad-manager";

export const getExperiences: FastifyRouteOptions<{
  Headers: CompanyIdHeader;
  Reply: GetExperiencesResponse | Static<typeof apiErrorSchema>;
}> = {
  method: "GET",
  url: "/ad-manager/experiences",
  schema: {
    summary: "Get experiences",
    description:
      "Returns available experiences for ad manager including experience IDs, names, currency types, bid ranges, and bid strategies",
    operationId: "getExperiences",
    tags: ["Ad Manager", "Experiences"],
    headers: companyIdHeader,
    response: {
      200: getExperiencesResponse,
      401: apiErrorSchema,
      403: apiErrorSchema,
    },
  },
  handler: async (req, res) => {
    const { companyId } = await authTrade(req);
    const authToken = authTradeCapi(req);

    if (!hasAccessToCompany(authToken, companyId)) {
      throw new ForbiddenError("Only the account owner can get experiences");
    }

    const { log: logger } = req;

    try {
      const experiences = await adManager.getExperiences(logger);
      return res.send(experiences);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
