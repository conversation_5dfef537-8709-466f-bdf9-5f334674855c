import { plugins } from "@checkatrade/fastify-five";

import { config } from "./config";

export const openApiPluginConfig: plugins.OpenApiPluginOptions = {
  port: config.app.httpPort,
  openapi: {
    info: {
      title: "Trade BFF App API",
      version: "1.0.1",
      description:
        "Backend Trade BFF API. This API follows [CAPI API standard](https://checkatrade.atlassian.net/wiki/spaces/CAPI/pages/**********/Checkatrade+API+CAPI+Standard+V1.0.0).",
      contact: { name: "Trade Services Team" },
    },
    servers: [
      {
        url: "https://api.staging.checkatrade.com/v2/trade-app",
        description: "Staging",
      },
      {
        url: "https://api.checkatrade.com/v2/trade-app",
        description: "Production",
      },
    ],
    tags: [
      {
        name: "Health",
        description:
          "Health checks based on [Kubernetes liveness, readiness and startup probes](https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/). These endpoints are only available inside Kubernetes cluster are are not accessible publicly.",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
