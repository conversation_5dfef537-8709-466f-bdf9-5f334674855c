import { NodeEnvironment, env } from "@checkatrade/env";

const defaultLogLevel: Record<NodeEnvironment, string> = {
  development: "debug",
  test: "silent",
  production: "info",
};

const environment = env.get("NODE_ENV") as NodeEnvironment;

export const config = {
  env: environment,
  app: {
    httpPort: env.getNumber("PORT", 3000),
  },
  logger: {
    level: env.get("LOG_LEVEL", defaultLogLevel[environment] || "info"),
  },
  tradeFirebaseApp: {
    projectId: env.get("TRADE_FIREBASE_APP_PROJECT_ID"),
    firebaseAuthServiceAccountId: env.get("GCP_SA_FIREBASE_AUTH"),
  },
  redirection: {
    enabled: env.getBoolean("REDIRECTION_ENABLED", false),
  },
  websites: {
    consumerSiteUrl: env.get("CONSUMER_WEB_URL"),
    tradeSiteUrl: env.get("TRADE_WEB_URL"),
  },
  contentApi: {
    url: env.get("CONTENT_API_URL"),
  },
};
