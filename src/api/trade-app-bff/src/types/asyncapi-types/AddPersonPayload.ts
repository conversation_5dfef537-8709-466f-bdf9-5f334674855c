import PersonCompanyRoleDto from "./PersonCompanyRoleDto";
import PostalAddressDto from "./PostalAddressDto";

interface AddPersonPayload {
  companyId?: string | null;
  requesterEmail?: string | null;
  firstName?: string | null;
  lastName?: string;
  email?: string | null;
  dateOfBirth?: string;
  mobilePhone?: string | null;
  phone?: string | null;
  companyRole?: PersonCompanyRoleDto;
  mailingAddress?: PostalAddressDto | null;
}
export default AddPersonPayload;
