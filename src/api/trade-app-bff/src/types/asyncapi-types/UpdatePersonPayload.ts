import PersonCompanyRoleDto from "./PersonCompanyRoleDto";
import PostalAddressDto from "./PostalAddressDto";

interface UpdatePersonPayload {
  companyId?: string | null;
  requesterEmail?: string | null;
  personId?: string;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  dateOfBirth?: string | null;
  mobilePhone?: string | null;
  phone?: string | null;
  companyRole?: PersonCompanyRoleDto | null;
  mailingAddress?: PostalAddressDto | null;
}
export default UpdatePersonPayload;
