import { Channel } from "@checkatrade/chat-sdk";
import { ConsumerJobResponse } from "@checkatrade/jobs-sdk";

import { TradeCard } from "../services";

type JobTrade = Pick<ConsumerJobResponse["trades"][0], "id" | "companyId">;

type PartialTradeCard = Pick<
  TradeCard,
  "companyId" | "name" | "logoUrl" | "tradeApp"
>;

export const formatTradeChannels = ({
  jobId,
  jobTrades,
  searchTrades,
}: {
  jobId: string;
  jobTrades: JobTrade[];
  searchTrades: PartialTradeCard[];
}): Channel[] =>
  jobTrades.reduce<Channel[]>((acc, { id, companyId }) => {
    const { name, logoUrl, tradeApp } = searchTrades.find(
      (t) => t.companyId === companyId,
    )!;

    if (tradeApp) {
      acc.push({
        id,
        trade: {
          companyId,
          name,
          image: logoUrl,
        },
        data: { correlationId: jobId },
      });
    }

    return acc;
  }, []);
