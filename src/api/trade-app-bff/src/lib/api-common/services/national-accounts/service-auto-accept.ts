import {
  ServiceJobResponse,
  ServiceOpportunityResponse,
  jobsSDK,
} from "@checkatrade/jobs-sdk";

import { TradeCard } from "../search-api";

export const serviceAutoAccept = async (
  { id, trades }: ServiceJobResponse,
  nationalAccountTrades: TradeCard[],
) => {
  const nationalAccountOpportunities = trades.filter((trade) =>
    nationalAccountTrades.some((nat) => nat.companyId === trade.companyId),
  );

  const result: ServiceOpportunityResponse[] = [];

  for (const opp of nationalAccountOpportunities) {
    result.push(await jobsSDK.service().acceptOpportunity(id, opp.id));
  }

  return result;
};
