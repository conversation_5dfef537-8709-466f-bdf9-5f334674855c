import {
  ConsumerJobResponse,
  ConsumerOpportunityResponse,
  jobsSDK,
} from "@checkatrade/jobs-sdk";

import { TradeCard } from "../search-api";

export const autoAccept = async (
  token: string,
  { id, trades }: ConsumerJobResponse,
  nationalAccountTrades: TradeCard[],
) => {
  const nationalAccountOpportunities = trades.filter((trade) =>
    nationalAccountTrades.some((nat) => nat.companyId === trade.companyId),
  );

  const result: ConsumerOpportunityResponse[] = [];

  for (const opp of nationalAccountOpportunities) {
    result.push(await jobsSDK.consumer(token).acceptOpportunity(id, opp.id));
  }

  return result;
};
