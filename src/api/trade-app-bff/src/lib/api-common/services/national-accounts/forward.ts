import { httpClient } from "@checkatrade/axios";
import { consumerSDK } from "@checkatrade/consumer-sdk";
import { gcp } from "@checkatrade/gcp";
import { OpportunityType } from "@checkatrade/jobs-sdk";
import { Static } from "@sinclair/typebox";

import { TradeCard } from "..";
import { JobDetailsResponse } from "../../../../controllers/jobs/job.customer.schema";
import { mapPreferredStart } from "../job-api/map-preferred-start";
import { config } from "./config";

type Consumer = Omit<
  Static<typeof consumerSDK.schemas.api.profile.getProfile.response>,
  "communicationPreferences" | "postcode" | "addresses"
>;
type PreferredStart = JobDetailsResponse["preferredStart"] & {
  legacyOption: string;
};

type ForwardRequest = Pick<
  JobDetailsResponse,
  "id" | "description" | "details" | "preferredStart" | "createdAt"
> & {
  consumer: Consumer;
  companyId: number;
  type: OpportunityType;
  category: Omit<JobDetailsResponse["category"], "id"> & {
    id: string;
  };
  address: Omit<JobDetailsResponse["address"], "postcode"> & {
    postCode: JobDetailsResponse["address"]["postcode"];
  };
  preferredStart: PreferredStart;
};
type JobProperties =
  | "id"
  | "address"
  | "category"
  | "description"
  | "details"
  | "createdAt"
  | "preferredStart";

type JobDetails = Pick<JobDetailsResponse, JobProperties>;

export const forward = async (
  job: JobDetails,
  consumer: Static<
    typeof consumerSDK.schemas.api.consumers.getConsumer.response
  >,
  type: OpportunityType,
  trades: TradeCard[],
): Promise<unknown> => {
  if (!trades.length) return;

  const gcpIdToken = await gcp.generateBearerToken(
    config.nationalAccountsAPIUrl,
  );

  const {
    id: jobId,
    address,
    category,
    createdAt,
    description,
    details,
    preferredStart,
  } = job;
  const { line1, line2, line3, city, postcode } = address;
  const { id: consumerId, firstName, lastName, email, phone } = consumer;

  return Promise.all(
    trades.map(({ companyId }) => {
      const requestBody: ForwardRequest = {
        id: jobId,
        address: {
          line1,
          line2,
          line3,
          city,
          postCode: postcode,
        },
        consumer: {
          id: consumerId,
          firstName,
          lastName,
          email,
          phone,
        },
        companyId,
        category: {
          ...category,
          id: String(category.id),
        },
        description,
        details: details ?? [],
        preferredStart: {
          ...preferredStart,
          legacyOption: mapPreferredStart(preferredStart).option,
        },
        type,
        createdAt,
      };

      const legacyRequest = httpClient.post(
        `${config.nationalAccountsAPIUrl}`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${gcpIdToken}`,
          },
          params: {
            type: "jobs",
          },
        },
      );

      const enterpriseRequest = async () => {
        try {
          if (config.enterpriseAPIEnabled) {
            await httpClient.post(
              `${config.enterpriseAPIUrl}/leads`,
              requestBody,
            );
          }
        } catch (error) {
          // temporary solution for integrating with the new enterprise API
          console.error("Error forwarding job to enterprise API:", error);
          return;
        }
      };

      return Promise.all([legacyRequest, enterpriseRequest()]);
    }),
  );
};
