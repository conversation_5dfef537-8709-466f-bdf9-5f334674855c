import { RedirectDuePagedJobResponse } from "@checkatrade/jobs-sdk";
import { Logger } from "@checkatrade/logging";

import { searchApi } from "../search-api";

type Trade = RedirectDuePagedJobResponse["data"][number]["trades"][number];

type Id = Trade["id"];

export const findInitialChannelId = async (
  trades: Trade[],
  logger: Logger,
): Promise<Id | undefined> => {
  const tradeCards = await searchApi.getTrades({
    companyIds: trades.map((o) => o.companyId),
    logger,
  });

  //find first trade which is tradeApp
  const initialTrade = tradeCards.find((t) => t.tradeApp);

  return initialTrade ?
      trades.find((t) => initialTrade.companyId === t.companyId)?.id
    : undefined;
};
