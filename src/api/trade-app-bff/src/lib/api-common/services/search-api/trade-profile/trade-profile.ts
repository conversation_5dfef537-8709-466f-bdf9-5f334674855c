/**
 * Partial response typed (some unknowns). For full response go to:
 * https://github.com/cat-home-experts/gcp-search-service/blob/main/api-gateway/spec.yaml
 * Spec itself is also outdated, so this type is not fully accurate, but based on the response
 */
export type TradeProfile = {
  description: string;
  galleries: Array<{
    id: string;
    title: string;
    items: Array<{
      id: string;
      albumId: string;
      dateFormatted: string;
      description: string;
      label: string | null;
      type: string;
      title: string;
      url: string;
      urlTemplate: string;
      youTubeId: string | null;
    }>;
  }>;
  services: Array<{
    label: string;
    icon: string;
  }>;
  address: {
    locality: string;
    region: string;
  };
  displayState: {
    catIcon: boolean;
  };
  primaryCategory: {
    id: number | null;
    label: string;
  } | null;
  accreditation: {
    summary: string | null;
    accreditations: Array<{
      label: string;
      logoUrl: string | null;
      url: string | null;
    }>;
  };
  tradeApp?: boolean;
  vettingInformation: Array<{
    active: boolean;
    error: boolean;
    label: string;
    status: string;
    infos: Array<{
      alert: boolean;
      label: string;
      passed: boolean;
      value: string;
    }>;
  }>;
  verificationSummary: {
    fullyVetted: boolean;
    hasWarning: boolean;
    titleOnly: boolean;
    summaryTitle: string;
    items: Array<{
      label: string;
      value: string;
    }>;
  };
  reviewsSummary: {
    meanScore: number;
    totalRecentReviews: number;
    totalReviews: number;
    recentMeanScore: number;
    recentMeanScoreDescription: string;
    recentMeanScorePercent: number;
    breakdowns: Array<{
      label: string;
      score: number;
    }>;
  };
  contact: {
    name: string;
    contacts: Array<{
      calloutable24hr: boolean;
      label: string;
      href: string;
      secure: boolean;
    }>;
  };
  payment?: {
    status?: string;
  };
};
