import { Logger } from "@checkatrade/logging";

import { getTrade } from "./get-trade";
import type { TradeCard } from "./trade-card";

export const getTrades = ({
  companyIds,
  logger,
}: {
  companyIds: number[];
  logger: Logger;
}): Promise<TradeCard[]> => {
  const uniqCompanyIds = [...new Set<number>(companyIds)];

  return Promise.all(
    uniqCompanyIds.map((companyId) => getTrade({ companyId, logger })),
  );
};

export const getTradesMap = async ({
  companyIds,
  logger,
}: {
  companyIds: number[];
  logger: Logger;
}): Promise<Record<number, TradeCard>> => {
  const trades = await getTrades({ companyIds, logger });
  return trades.reduce(
    (map, trade) => ({ ...map, [trade.companyId]: trade }),
    {} as Record<number, TradeCard>,
  );
};
