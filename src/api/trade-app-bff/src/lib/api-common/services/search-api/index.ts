import { getCategoryNames } from "./category/get-categories-names";
import { getCategory } from "./category/get-category";
import { getCategoryName } from "./category/get-category-name";
import { config } from "./config";
import { getUrl } from "./get-url";
import { findTrades } from "./trade-card/find-trades";
import { getTrade } from "./trade-card/get-trade";
import { getTrades, getTradesMap } from "./trade-card/get-trades";
import { getTradeProfile } from "./trade-profile/get-trade-profile";
import { findTradesByName } from "./trade/find-trades-by-name";

export * from "./trade-card/trade-card";
export * from "./trade-profile/trade-profile";
export * from "./trade/trade";
export * from "./category/category";

export const searchApi = {
  config,
  findTrades,
  findTradesByName,
  getCategory,
  getCategoryName,
  getCategoryNames,
  getTrade,
  getTrades,
  getTradesMap,
  getUrl,
  getTradeProfile,
};
