import { searchApiGet } from "../request";
import { Category } from "./category";

type CategoryNames = Record<number, string>;

const flattenCategories = (input: Category[]) => {
  const flatList: Category[] = [];

  const flattenCategory = (category: Category) => {
    flatList.push({ ...category, subcategories: [] });
    category.subcategories.forEach(flattenCategory);
  };

  input.forEach(flattenCategory);

  return flatList;
};

export const getCategoryNames = async (
  categoryId: number[],
): Promise<CategoryNames> => {
  const { data } = await searchApiGet<Category[]>(`/category`);
  const categories = flattenCategories(data);

  return categoryId.reduce<CategoryNames>((acc, id) => {
    const category = categories.find((c) => c.id === id);
    acc[id] = category?.label ?? "";

    return acc;
  }, {});
};
