export type Trade = {
  companyId: number;
  name: string;
  uniqueName: string;
  tradeId: string;
  location: string;
  tradeProfileUrl: string;
  searchByCategory: boolean;
  basedInAddress: string;
  logoUrl: string;
  logoUrlTemplate: string;
  skills: Array<{
    label: string;
    id: number;
    iconName: string;
    subSkills: Array<{
      label: string;
      id: number;
      iconName: string;
    }>;
  }>;
};

export type TradeSearchResponse = {
  items: Trade[];
  meta: {
    currentPage: number;
    itemsPerPage: number;
    totalPages: number;
    totalResults: number;
    requestId: string;
  };
};
