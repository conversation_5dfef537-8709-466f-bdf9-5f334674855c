import { AxiosError } from "@checkatrade/axios";
import { BadRequestError } from "@checkatrade/errors";

import { searchApiGet } from "../request";
import type { TradeCardSearch } from "./trade-card";

export const findTrades = async ({
  categoryId,
  postcode,
  page = 1,
  size = 10,
  excludeCompanyIds,
}: {
  categoryId: number;
  postcode: string;
  page?: number;
  size?: number;
  excludeCompanyIds?: number[];
}): Promise<TradeCardSearch> => {
  const filterParams = [
    `categoryId[eq]:${categoryId}`,
    `postcode[eq]:${postcode}`,
  ];

  if (excludeCompanyIds?.length) {
    filterParams.push(`companyId[nin]:${excludeCompanyIds.join(",")}`);
  }

  const filter = filterParams.join(";");

  try {
    const { data } = await searchApiGet<TradeCardSearch>("/trade-card/search", {
      params: { filter, page, size },
    });

    return data;
  } catch (error) {
    const response = (error as AxiosError).response;

    if (response?.status === 400) {
      const { detail } = (response?.data as Record<string, string>) || {};
      const message = detail || "Invalid name provided";

      throw new BadRequestError(message, response?.data);
    }

    throw error;
  }
};
