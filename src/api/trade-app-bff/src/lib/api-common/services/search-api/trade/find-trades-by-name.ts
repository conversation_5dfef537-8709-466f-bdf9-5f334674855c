import { AxiosError } from "@checkatrade/axios";
import { BadRequestError } from "@checkatrade/errors";

import { searchApiGet } from "../request";
import { type TradeSearchResponse } from "./trade";

export const findTradesByName = async ({
  name,
  page = 1,
  size = 10,
}: {
  name: string;
  page?: number;
  size?: number;
}): Promise<TradeSearchResponse> => {
  const filterParams = [`name[like]:${name}`, `view[eq]:profile`];
  const filter = filterParams.join(";");

  try {
    const { data } = await searchApiGet<TradeSearchResponse>("/trade/search", {
      params: { filter, page, size },
    });

    return data;
  } catch (error) {
    const response = (error as AxiosError).response;

    if (response?.status === 400) {
      const { detail } = (response?.data as Record<string, string>) || {};
      const message = detail || "Invalid name provided";

      throw new BadRequestError(message, response?.data);
    }

    throw error;
  }
};
