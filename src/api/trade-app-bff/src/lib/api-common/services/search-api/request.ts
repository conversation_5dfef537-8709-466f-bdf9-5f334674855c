import { AxiosRequestConfig, AxiosResponse, axios } from "@checkatrade/axios";

import { getUrl } from "./get-url";

export const searchApiGet = async <T>(
  path: string,
  requestConfig?: AxiosRequestConfig,
): Promise<AxiosResponse<T, unknown>> => {
  const url = getUrl(path);

  const httpClient = axios.getInstance({ retries: 2 });

  const response = await httpClient<T>(url, requestConfig);

  return response;
};
