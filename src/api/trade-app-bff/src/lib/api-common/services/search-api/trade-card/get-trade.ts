import { AxiosError } from "@checkatrade/axios";
import { BadRequestError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";

import { searchApiGet } from "../request";
import type { TradeCard } from "./trade-card";

export const getTrade = async ({
  companyId,
  logger,
}: {
  companyId: number;
  logger: Logger;
}): Promise<TradeCard> => {
  try {
    const { data } = await searchApiGet<TradeCard>(`/trade-card/${companyId}`);
    return data;
  } catch (err) {
    const error = err as AxiosError;
    if (error.response?.status === 400 || error.status === 400) {
      const { detail } = (error.response?.data as Record<string, string>) || {};
      const message = detail || "Invalid or unknown company ID";
      throw new BadRequestError(message, error.response?.data);
    }
    logger.error(error, "Failed fetching trade card data");
    throw error;
  }
};
