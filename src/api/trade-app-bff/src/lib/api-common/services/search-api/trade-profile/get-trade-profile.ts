import { AxiosError } from "@checkatrade/axios";
import { BadRequestError } from "@checkatrade/errors";
import { Logger } from "@checkatrade/logging";

import { searchApiGet } from "../request";
import type { TradeProfile } from "./trade-profile";

export const getTradeProfile = async ({
  uniqueName,
  logger,
}: {
  uniqueName: string;
  logger: Logger;
}): Promise<TradeProfile> => {
  try {
    const { data } = await searchApiGet<TradeProfile>(
      `/trade-profile/${uniqueName}`,
    );
    return data;
  } catch (err) {
    const error = err as AxiosError;
    if (error.response?.status === 400 || error.status === 400) {
      const { detail } = (error.response?.data as Record<string, string>) || {};
      const message = detail || "Invalid or unknown company unique name";
      throw new BadRequestError(message, error.response?.data);
    }
    logger.error(error, "Failed fetching trade profile data");
    throw error;
  }
};
