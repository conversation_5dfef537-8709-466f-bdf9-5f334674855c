type TradeContact = {
  calloutable24hr: boolean;
  secure: boolean;
  label: string;
  number: string;
};

/**
 * Partial response typed (some unknowns). For full response go to:
 * https://github.com/cat-home-experts/gcp-search-service/blob/main/api-gateway/spec.yaml
 */
export type TradeCard = {
  companyId: number;
  name: string;
  uniqueName: string;
  summary: string;
  operatingLocationText: string;
  logoUrl: string;
  logoUrlTemplate: string;
  distanceFromSearch: string;
  trackingBadges: string[];
  displayState: {
    text: string;
    useModal: boolean;
    warning: boolean;
    catIcon: boolean;
    modalContent: string;
    modalKey: string;
  };
  address: {
    locality: string;
    region: string | null;
  };
  reviewsSummary: {
    recentMeanScore: number;
    totalRecentReviews: number;
    totalReviews: number;
  };
  contact: {
    primary: TradeContact | null;
    contacts: TradeContact[];
  };
  view: {
    showSummary: boolean;
    showLogo: boolean;
    showMessage: boolean;
    showShortlist: boolean;
    showNewToCheckatrade: boolean;
    showClaim: boolean;
  };
  skills: Array<{
    id: number;
    iconName: string;
    label: string;
    subSkills: Array<{
      id: number;
      iconName: string;
      label: string;
    }>;
  }>;
  reviews: Array<{
    body: string;
    reviewId: string;
    location: string;
    openOnLoad: boolean;
    reviewedDate: string;
    score: number;
    scorePercent: number;
    showBreakdowns: boolean;
    title: string;
    tradeResponse: string;
    verified: boolean;
    breakdowns: Array<{
      label: string;
      value: number;
    }>;
  }>;
  galleries: Array<{
    id: string;
    title: string;
    items: Array<{
      id: string;
      albumId: string;
      dateFormatted: string;
      description: string | null;
      label: string | null;
      type: string;
      title: string | null;
      url: string;
      urlTemplate: string;
      youTubeId: string | null;
    }>;
  }>;
  badges: unknown[];
  isSponsored: boolean;
  tradeApp: boolean;
  heroBannerUrl: string | null;
  payment?: {
    status?: string;
  };
};

export type TradeCardSearch = {
  view: {
    showMessage: boolean;
    showRAQForm: boolean;
  };
  items: TradeCard[];
  meta: {
    currentPage: number;
    itemsPerPage: number;
    totalPages: number;
    totalResults: number;
    requestId: string;
    experiments: {
      algorithm: string[];
    };
    category: {
      id: number;
      referenceId: number;
      label: string;
      name: string;
      parentId: number;
      isParent: boolean;
      isAlias: boolean;
    };
    seoData: {
      metaTitle: string;
      metaDescription: string;
      noIndex: boolean;
      noFollow: boolean;
      localBusiness: {
        "@type": string;
        "@context": string;
        "name": string;
        "address": {
          "@type": string;
          "@context": string;
          "streetAddress": string;
          "addressLocality": string;
          "addressRegion": string | null;
          "postalCode": string;
          "addressCountry": string;
        };
        "telephone": string | null;
        "url": string;
        "logo": string | null;
        "image": string | null;
        "priceRange": string | null;
        "aggregateRating": {
          "@type": string;
          "ratingValue": number;
          "ratingCount": number;
          "bestRating": number;
          "worstRating": number;
        };
        "description": string | null;
        "areaServed": string | null;
        "knowsAbout": string | null;
        "openingHours": string;
      };
    };
    tradeApp: boolean;
  };
};
