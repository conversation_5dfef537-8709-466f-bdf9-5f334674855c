import { AxiosResponse, httpClient } from "@checkatrade/axios";
import { gcp } from "@checkatrade/gcp";
import { type FastifyBaseLogger } from "fastify";

import { config } from "./config";

export type CommsReviewRequestData = {
  consumerId: string;
  type: string;
  job: {
    id: string;
    categoryId: number;
  };
  trade: {
    companyId: number;
    name: string;
    uniqueName: string;
  };
  consumerReviewId?: string;
};

export const reviewRequest = async (
  data: CommsReviewRequestData,
  logger: FastifyBaseLogger,
): Promise<AxiosResponse> => {
  try {
    const url = `${config.api.url}/reviews/request`;
    const token = await gcp.generateBearerToken(url);
    return await httpClient.post(url, data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  } catch (error) {
    logger.error(
      error,
      "Failed to send review request email request to Comms service",
    );
    throw error;
  }
};
