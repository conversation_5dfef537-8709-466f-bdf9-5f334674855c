import { capiPubsubClient } from "./capi-pubsub-client";
import { config } from "./config";

export type VettingConsentRequestData = {
  companyId: number;
  personId: string;
  email: string;
  phoneNumber: string;
  firstName: string;
  employer: string;
  url: string;
};

export const vettingConsentRequest = async (
  data: VettingConsentRequestData,
) => {
  await capiPubsubClient.topic(config.gcp.employeeConsentTopic).publishMessage({
    data: Buffer.from(
      JSON.stringify({
        companyId: data.companyId.toString(),
        personId: data.personId,
        email: data.email,
        phoneNumber: data.phoneNumber,
        firstName: data.firstName,
        employer: data.employer,
        url: data.url,
      }),
    ),
    attributes: {
      schemaVersion: "1.0",
      eventType: "EmployeeAdded",
      timestamp: Date.now().toString(),
    },
  });
};
