import { QuoteResponseCommsEventType } from "./QuoteResponseCommsEventTypeEnum";
import { publicConsumerQuoteResponse } from "./public-consumer-quote-response";
import { publishOffPlatformJobCreated } from "./publish-off-platform-job-created";
import { reportUser } from "./report-user";
import { reviewRequest } from "./review-request";
import { vettingConsentRequest } from "./vetting-consent-request";
import { workOpportunityRequest } from "./work-opportunity-request";

export { OffPlatformJobCreatedEventType } from "./publish-off-platform-job-created";
export { CommsReportUserData } from "./report-user";
export { CommsReviewRequestData } from "./review-request";

type Comms = {
  reviewRequest: typeof reviewRequest;
  reportUser: typeof reportUser;
  publicConsumerQuoteResponse: typeof publicConsumerQuoteResponse;
  QuoteResponseCommsEventType: typeof QuoteResponseCommsEventType;
  workOpportunityRequest: typeof workOpportunityRequest;
  publishOffPlatformJobCreated: typeof publishOffPlatformJobCreated;
  vettingConsentRequest: typeof vettingConsentRequest;
};

export const comms: Comms = {
  reviewRequest,
  reportUser,
  publicConsumerQuoteResponse,
  QuoteResponseCommsEventType,
  workOpportunityRequest,
  publishOffPlatformJobCreated,
  vettingConsentRequest,
};
