import { commsPubsubClient } from "./comms-pubsub-client";

export type WorkOpportunityRequestData = {
  description: string;
  applicantFirstName: string;
  applicantLastName: string;
  applicantPhone: string;
  applicantEmail: string;
  applicantPostcode: string;
  companyId: number;
};

export const workOpportunityRequest = async (
  data: WorkOpportunityRequestData,
) => {
  await commsPubsubClient
    .topic("apprentice-work-opportunity-requests")
    .publishMessage({
      data: Buffer.from(
        JSON.stringify({
          companyId: data.companyId.toString(),
          description: data.description,
          applicantName: data.applicantFirstName + " " + data.applicantLastName,
          applicantPhone: data.applicantPhone,
          applicantEmail: data.applicantPostcode,
          applicantPostcode: data.applicantPostcode,
        }),
      ),
      attributes: {
        schemaVersion: "1.0",
        timestamp: Date.now().toString(),
      },
    });
};
