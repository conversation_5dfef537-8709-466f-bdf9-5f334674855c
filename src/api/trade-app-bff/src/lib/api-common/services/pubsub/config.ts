import { env } from "@checkatrade/env";

export const config = {
  api: {
    url: env.get("COMMS_API_URL"),
  },
  gcp: {
    commsProjectId: env.get("COMMS_GCP_PROJECT_ID"),
    capiProjectId: env.get("CAPI_GCP_PROJECT_ID"),
    offPlatformJobTopic: env.get("COMMS_GCP_OFF_PLATFORM_JOB_TOPIC"),
    employeeConsentTopic: env.get("EMPLOYEE_CONSENT_TOPIC"),
  },
  matching: {
    spam: {
      url: env.get("MATCHING_SPAM_API_URL"),
    },
  },
};
