import { QuoteResponseCommsEventType } from "./QuoteResponseCommsEventTypeEnum";
import { commsPubsubClient } from "./comms-pubsub-client";

type PublishQuoteResponseEventParams = {
  companyId: number;
  quoteTitle: string;
  responseType: QuoteResponseCommsEventType;
  tradeAppDeepLink: string;
  correlationId: string; //uuid
};

export const publicConsumerQuoteResponse = async ({
  companyId,
  quoteTitle,
  responseType,
  tradeAppDeepLink,
  correlationId,
}: PublishQuoteResponseEventParams) =>
  await commsPubsubClient.topic("quote-events").publishMessage({
    data: Buffer.from(
      JSON.stringify({
        companyId: companyId.toString(),
        quoteTitle,
        url: tradeAppDeepLink,
      }),
    ),
    attributes: {
      eventType: responseType,
      schemaVersion: "1.0",
      correlationId: correlationId,
      timestamp: Date.now().toString(),
    },
  });
