import { httpClient } from "@checkatrade/axios";
import { StreamUserType } from "@checkatrade/chat-sdk";
import { gcp } from "@checkatrade/gcp";
import { type FastifyBaseLogger } from "fastify";

import { config } from "./config";

type ReportUserParams = {
  data: CommsReportUserData;
  logger: FastifyBaseLogger;
};

type User = {
  id: string;
  type: StreamUserType;
};

export type CommsReportUserData = {
  jobId: string;
  channelId: string;
  reporter: User;
  user: User;
};

const reportUserV1 = async ({ data, logger }: ReportUserParams) => {
  try {
    const url = `${config.api.url}/chat/block`;
    const token = await gcp.generateBearerToken(url);
    const payload = {
      attributes: {
        schemaVersion: "1.0",
        timestamp: new Date().toISOString(),
      },
      data: {
        channelId: data.channelId,
        senderId: data.reporter.id,
        members: [data.reporter, data.user],
      },
    };

    return await httpClient.post(url, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  } catch (error) {
    logger.error(error, "Failed to report user to Comms service");
    throw error;
  }
};

const reportUserV2 = async ({ data, logger }: ReportUserParams) => {
  try {
    const url = `${config.api.url}/report-user`;
    const token = await gcp.generateBearerToken(url);
    const payload = {
      jobId: data.jobId,
      reporter: data.reporter,
      user: data.user,
    };

    return await httpClient.post(url, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  } catch (error) {
    logger.error(error, "Failed to report user to Comms service");
    throw error;
  }
};

export const reportUser = async (params: ReportUserParams) => {
  const reportFn =
    process.env.COMMS_REPORT_USER_VERSION === "V1" ?
      reportUserV1
    : reportUserV2;

  await reportFn(params);
};
