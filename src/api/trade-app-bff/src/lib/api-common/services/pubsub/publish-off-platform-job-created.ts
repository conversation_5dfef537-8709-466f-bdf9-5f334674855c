import { randomUUID } from "crypto";

import { capiPubsubClient } from "./capi-pubsub-client";
import { config } from "./config";

export type OffPlatformJobCreatedEventType = {
  paymentRequestId: string;
  companyId: string;
  consumer: {
    firstName: string;
    lastName: string;
    emailAddress: string;
    phoneNumber: string;
  };
  job: {
    postcode: string;
    description: string;
    categoryId: string;
  };
};

export const publishOffPlatformJobCreated = async (
  data: OffPlatformJobCreatedEventType,
) =>
  await capiPubsubClient.topic(config.gcp.offPlatformJobTopic).publishMessage({
    data: Buffer.from(JSON.stringify(data)),
    attributes: {
      eventType: "payment-for-off-platform-job-created",
      schemaVersion: "1.0",
      correlationId: randomUUID(),
      timestamp: Date.now().toString(),
    },
  });
