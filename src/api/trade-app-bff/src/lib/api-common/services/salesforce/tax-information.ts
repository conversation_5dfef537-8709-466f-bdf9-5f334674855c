import { pubsubClient } from "./pubsub-client";

type TaxInformationParams = {
  companyId: number;
  vatNumber?: string;
  uniqueTaxpayerReference: string;
};

export const taxInformation = async ({
  companyId,
  vatNumber,
  uniqueTaxpayerReference,
}: TaxInformationParams) =>
  await pubsubClient.topic("tax-information-update").publishMessage({
    data: Buffer.from(
      JSON.stringify({
        companyId: companyId.toString(),
        vatNumber,
        uniqueTaxpayerReference,
      }),
    ),
    attributes: {
      eventType: "TaxInformationUpdateMessage",
      schemaVersion: "1.0",
      timestamp: Date.now().toString(),
      messageType: "TaxInformationUpdateMessage",
      messageVersion: "v1",
    },
  });
