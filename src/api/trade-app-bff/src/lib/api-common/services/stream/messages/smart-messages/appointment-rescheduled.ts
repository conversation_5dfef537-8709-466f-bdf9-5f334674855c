import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { Static } from "@sinclair/typebox";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

export type AppointmentRescheduledMessageData = Pick<
  Static<typeof schedulingSDK.schemas.api.trade.postAppointment.response>,
  "id" | "alternatives" | "status"
> & {
  title: Static<
    typeof schedulingSDK.schemas.api.trade.postAppointment.response
  >["type"]["title"];
};

export type AppointmentRescheduledMessage = SmartMessageGeneric<
  SmartMessageGroup.APPOINTMENT,
  SmartMessageType.APPOINTMENT_RESCHEDULED,
  AppointmentRescheduledMessageData
>;
