import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

export type ReviewRequestedMessage = SmartMessageGeneric<
  SmartMessageGroup.REVIEW,
  SmartMessageType.REVIEW_REQUESTED
>;

export type ReviewRequestReminderMessage = SmartMessageGeneric<
  SmartMessageGroup.REVIEW,
  SmartMessageType.REVIEW_REQUEST_REMINDER
>;
