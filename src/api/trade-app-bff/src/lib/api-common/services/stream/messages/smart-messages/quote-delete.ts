import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

export type QuoteDeleteMessageData = {
  id: string;
  title: string;
  total: number;
};

export type QuoteDeleteMessage = SmartMessageGeneric<
  SmartMessageGroup.QUOTE,
  SmartMessageType.QUOTE_DELETE,
  QuoteDeleteMessageData
>;
