import { SmartMessageType } from "./smart-message-type";

export enum SmartMessageGroup {
  APPOINTMENT = "appointment",
  BLOCKED = "blocked",
  OPPORTUNITY = "opportunity",
  JOB = "job",
  PAYMENT_REQUEST = "paymentRequest",
  QUOTE = "quote",
  REVIEW = "review",
}

export const smartGroupMap = {
  [SmartMessageType.APPOINTMENT]: SmartMessageGroup.APPOINTMENT,
  [SmartMessageType.APPOINTMENT_CANCELLED]: SmartMessageGroup.APPOINTMENT,
  [SmartMessageType.APPOINTMENT_ACCEPTED]: SmartMessageGroup.APPOINTMENT,
  [SmartMessageType.APPOINTMENT_UPDATED]: SmartMessageGroup.APPOINTMENT,
  [SmartMessageType.APPOINTMENT_REJECTED]: SmartMessageGroup.APPOINTMENT,
  [SmartMessageType.APPOINTMENT_RESCHEDULED]: SmartMessageGroup.APPOINTMENT,

  [SmartMessageType.BLOCKED]: SmartMessageGroup.BLOCKED,

  [SmartMessageType.JOB]: SmartMessageGroup.JOB,
  [SmartMessageType.JOB_ACCEPTED]: SmartMessageGroup.JOB,
  [SmartMessageType.JOB_REJECTED]: SmartMessageGroup.JOB,
  [SmartMessageType.JOB_CANCELLED]: SmartMessageGroup.JOB,
  [SmartMessageType.JOB_COMPLETED]: SmartMessageGroup.JOB,

  [SmartMessageType.OPPORTUNITY_CANCELLED]: SmartMessageGroup.OPPORTUNITY,

  [SmartMessageType.REVIEW_REQUESTED]: SmartMessageGroup.REVIEW,
  [SmartMessageType.REVIEW_REQUEST_REMINDER]: SmartMessageGroup.REVIEW,

  [SmartMessageType.PAYMENT_REQUEST_NEW]: SmartMessageGroup.PAYMENT_REQUEST,
  [SmartMessageType.PAYMENT_REQUEST_CHARGEBACK_REVERSED]:
    SmartMessageGroup.PAYMENT_REQUEST,
  [SmartMessageType.PAYMENT_REQUEST_CHARGEBACK]:
    SmartMessageGroup.PAYMENT_REQUEST,
  [SmartMessageType.PAYMENT_REQUEST_CANCELLED]:
    SmartMessageGroup.PAYMENT_REQUEST,
  [SmartMessageType.PAYMENT_REQUEST_REFUNDED]:
    SmartMessageGroup.PAYMENT_REQUEST,
  [SmartMessageType.PAYMENT_REQUEST_AUTHORIZED]:
    SmartMessageGroup.PAYMENT_REQUEST,
  [SmartMessageType.PAYMENT_REQUEST_PAID]: SmartMessageGroup.PAYMENT_REQUEST,
  [SmartMessageType.PAYMENT_REQUEST_EXPIRED]: SmartMessageGroup.PAYMENT_REQUEST,

  [SmartMessageType.QUOTE]: SmartMessageGroup.QUOTE,
  [SmartMessageType.QUOTE_DRAFT]: SmartMessageGroup.QUOTE,
  [SmartMessageType.QUOTE_DELETE]: SmartMessageGroup.QUOTE,
};
