import { QuoteStatus, QuoteType } from "@checkatrade/quoting-sdk";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

export type QuotesMessageData = {
  id: string;
  status: QuoteStatus;
  total: number;
  title: string;
  cardType: QuoteType;
  dueDate?: Date;
  pdfFileUrl: string;
  isDeleted?: boolean;
};

export type QuoteMessage = SmartMessageGeneric<
  SmartMessageGroup.QUOTE,
  SmartMessageType.QUOTE,
  QuotesMessageData
>;
