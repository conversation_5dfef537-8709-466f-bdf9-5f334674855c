import { schedulingSDK } from "@checkatrade/scheduling-sdk";
import { Static } from "@sinclair/typebox";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

type AppointmentCancelledMessageData = Partial<
  Static<
    typeof schedulingSDK.schemas.api.consumer.cancelAppointment.response
  >["alternatives"][number]
> & {
  title: Static<
    typeof schedulingSDK.schemas.api.consumer.cancelAppointment.response
  >["type"]["title"];
};

export type AppointmentCancelledMessage = SmartMessageGeneric<
  SmartMessageGroup.APPOINTMENT,
  SmartMessageType.APPOINTMENT_CANCELLED,
  AppointmentCancelledMessageData
>;
