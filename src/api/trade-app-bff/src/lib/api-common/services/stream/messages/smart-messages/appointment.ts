import { scheduling<PERSON>K } from "@checkatrade/scheduling-sdk";
import { Static } from "@sinclair/typebox";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

type AppointmentMessageData = Pick<
  Static<typeof schedulingSDK.schemas.api.trade.postAppointment.response>,
  "id" | "alternatives" | "status"
> & {
  title: Static<
    typeof schedulingSDK.schemas.api.trade.postAppointment.response
  >["type"]["title"];
};

export type AppointmentMessage = SmartMessageGeneric<
  SmartMessageGroup.APPOINTMENT,
  SmartMessageType.APPOINTMENT,
  AppointmentMessageData
>;
