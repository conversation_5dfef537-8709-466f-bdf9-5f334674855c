export enum SmartMessageType {
  APPOINTMENT = "APPOINTMENT",
  APPOINTMENT_ACCEPTED = "APPOINTMENT_ACCEPTED",
  APPOINTMENT_REJECTED = "APPOINTMENT_REJECTED",
  APPOINTMENT_UPDATED = "APPOINTMENT_UPDATED",
  APPOINTMENT_CANCELLED = "APPOINTMENT_CANCELLED",
  APPOINTMENT_RESCHEDULED = "APPOINTMENT_RESCHEDULED",
  BLOCKED = "BLOCKED",
  JOB = "JOB",
  JOB_ACCEPTED = "JOB_ACCEPTED",
  JOB_CANCELLED = "JOB_CANCELLED",
  JOB_REJECTED = "JOB_REJECTED",
  JOB_COMPLETED = "JOB_COMPLETED",
  OPPORTUNITY_CANCELLED = "OPPORTUNITY_CANCELLED",
  REVIEW_REQUESTED = "REVIEW_REQUESTED",
  REVIEW_REQUEST_REMINDER = "REVIEW_REQUEST_REMINDER",
  PAYMENT_REQUEST_NEW = "PAYMENT_REQUEST_NEW",
  PAYMENT_REQUEST_AUTHORIZED = "PAYMENT_REQUEST_AUTHORIZED",
  PAYMENT_REQUEST_PAID = "PAYMENT_REQUEST_PAID",
  PAYMENT_REQUEST_EXPIRED = "PAYMENT_REQUEST_EXPIRED",
  PAYMENT_REQUEST_REFUNDED = "PAYMENT_REQUEST_REFUNDED",
  PAYMENT_REQUEST_CANCELLED = "PAYMENT_REQUEST_CANCELLED",
  PAYMENT_REQUEST_CHARGEBACK = "PAYMENT_REQUEST_CHARGEBACK",
  PAYMENT_REQUEST_CHARGEBACK_REVERSED = "PAYMENT_REQUEST_CHARGEBACK_REVERSED",
  QUOTE = "QUOTE",
  QUOTE_DRAFT = "QUOTE_DRAFT",
  QUOTE_DELETE = "QUOTE_DELETE",
}
