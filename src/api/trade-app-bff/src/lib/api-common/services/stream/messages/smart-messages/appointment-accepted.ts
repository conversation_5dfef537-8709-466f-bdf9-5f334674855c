import { scheduling<PERSON>K } from "@checkatrade/scheduling-sdk";
import { Static } from "@sinclair/typebox";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

type AppointmentAcceptedMessageData = Partial<
  Static<
    typeof schedulingSDK.schemas.api.consumer.acceptAppointment.response
  >["alternatives"][number]
> & {
  title: Static<
    typeof schedulingSDK.schemas.api.consumer.acceptAppointment.response
  >["type"]["title"];
};

export type AppointmentAcceptedMessage = SmartMessageGeneric<
  SmartMessageGroup.APPOINTMENT,
  SmartMessageType.APPOINTMENT_ACCEPTED,
  AppointmentAcceptedMessageData
>;
