import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

export interface PaymentRequestCommonData {
  id: string;
  dueDate: Date;
  reference: string;
  jobReference?: string;
  description?: string;
  amount: {
    currency: string;
    value: number;
  };
  status: string;
  paymentLinkId: string;
  consumerName: string;
}

export interface PaymentRequestNewData extends PaymentRequestCommonData {
  paymentUrl: string;
}

/**
 * New Payment Request
 */
export type PaymentRequestNewMessage = SmartMessageGeneric<
  SmartMessageGroup.PAYMENT_REQUEST,
  SmartMessageType.PAYMENT_REQUEST_NEW,
  PaymentRequestNewData
>;

/**
 * Existing Payment Request
 */
export type PaymentRequestExistingMessage = SmartMessageGeneric<
  SmartMessageGroup.PAYMENT_REQUEST,
  | SmartMessageType.PAYMENT_REQUEST_AUTHORIZED
  | SmartMessageType.PAYMENT_REQUEST_PAID
  | SmartMessageType.PAYMENT_REQUEST_EXPIRED
  | SmartMessageType.PAYMENT_REQUEST_REFUNDED
  | SmartMessageType.PAYMENT_REQUEST_CANCELLED
  | SmartMessageType.PAYMENT_REQUEST_CHARGEBACK
  | SmartMessageType.PAYMENT_REQUEST_CHARGEBACK_REVERSED,
  PaymentRequestCommonData
>;
