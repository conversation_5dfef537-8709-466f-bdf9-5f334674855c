import { AppointmentMessage } from "./appointment";
import { AppointmentAcceptedMessage } from "./appointment-accepted";
import { AppointmentCancelledMessage } from "./appointment-cancelled";
import { AppointmentRejectedMessage } from "./appointment-rejected";
import { AppointmentRescheduledMessage } from "./appointment-rescheduled";
import { AppointmentUpdatedMessage } from "./appointment-updated";
import { BlockedMessage } from "./blocked";
import { JobMessage } from "./job";
import { JobAcceptedMessage } from "./job-accepted";
import { JobCancelledMessage } from "./job-cancelled";
import { JobRejectedMessage } from "./job-rejected";
import { OpportunityCancelledMessage } from "./opportunity-cancelled";
import {
  PaymentRequestExistingMessage,
  PaymentRequestNewMessage,
} from "./payment-request";
import { QuoteMessage } from "./quote";
import { QuoteDeleteMessage } from "./quote-delete";
import { QuoteDraftMessage } from "./quote-draft";
import { ReviewRequestReminderMessage, ReviewRequestedMessage } from "./review";

export { SmartMessageType } from "./smart-message-type";
export { SmartMessageGroup } from "./smart-message-group";

export type SmartMessage =
  | AppointmentMessage
  | AppointmentCancelledMessage
  | AppointmentAcceptedMessage
  | AppointmentRejectedMessage
  | AppointmentUpdatedMessage
  | AppointmentRescheduledMessage
  | BlockedMessage
  | JobMessage
  | JobAcceptedMessage
  | JobCancelledMessage
  | JobRejectedMessage
  | OpportunityCancelledMessage
  | ReviewRequestedMessage
  | ReviewRequestReminderMessage
  | PaymentRequestNewMessage
  | PaymentRequestExistingMessage
  | QuoteMessage
  | QuoteDraftMessage
  | QuoteDeleteMessage;

export {
  AppointmentMessage,
  AppointmentCancelledMessage,
  AppointmentAcceptedMessage,
  AppointmentRejectedMessage,
  AppointmentUpdatedMessage,
  AppointmentRescheduledMessage,
  BlockedMessage,
  JobMessage,
  JobAcceptedMessage,
  JobCancelledMessage,
  JobRejectedMessage,
  OpportunityCancelledMessage,
  ReviewRequestedMessage,
  ReviewRequestReminderMessage,
  PaymentRequestNewMessage,
  PaymentRequestExistingMessage,
  QuoteMessage,
};
