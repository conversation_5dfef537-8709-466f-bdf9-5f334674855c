import { QuoteStatus, QuoteType } from "@checkatrade/quoting-sdk";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

export type QuoteDraftMessageData = {
  id: string;
  status: QuoteStatus;
  total: number;
  title: string;
  cardType: QuoteType;
  dueDate?: Date;
  // pdfFileUrl may or may not be there, this is different to QuoteMessageData, which will definitely have the field populated
  pdfFileUrl?: string;
};

export type QuoteDraftMessage = SmartMessageGeneric<
  SmartMessageGroup.QUOTE,
  SmartMessageType.QUOTE_DRAFT,
  QuoteDraftMessageData
>;
