import { scheduling<PERSON>K } from "@checkatrade/scheduling-sdk";
import { Static } from "@sinclair/typebox";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

type AppointmentUpdatedMessageData = Pick<
  Static<typeof schedulingSDK.schemas.api.trade.patchAppointment.response>,
  "id" | "alternatives" | "status"
> & {
  title: Static<
    typeof schedulingSDK.schemas.api.trade.patchAppointment.response
  >["type"]["title"];
};

export type AppointmentUpdatedMessage = SmartMessageGeneric<
  SmartMessageGroup.APPOINTMENT,
  SmartMessageType.APPOINTMENT_UPDATED,
  AppointmentUpdatedMessageData
>;
