import { scheduling<PERSON>K } from "@checkatrade/scheduling-sdk";
import { Static } from "@sinclair/typebox";

import { SmartMessageGeneric } from "./smart-message-generic";
import { SmartMessageGroup } from "./smart-message-group";
import { SmartMessageType } from "./smart-message-type";

type AppointmentRejectedMessageData = {
  id: Static<
    typeof schedulingSDK.schemas.api.consumer.rejectAppointment.response
  >["id"];
  title: Static<
    typeof schedulingSDK.schemas.api.consumer.rejectAppointment.response
  >["type"]["title"];
};

export type AppointmentRejectedMessage = SmartMessageGeneric<
  SmartMessageGroup.APPOINTMENT,
  SmartMessageType.APPOINTMENT_REJECTED,
  AppointmentRejectedMessageData
>;
