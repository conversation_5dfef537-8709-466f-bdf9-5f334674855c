import { ConsumerJobResponse } from "@checkatrade/jobs-sdk";

export const PreferredStartIds = {
  Flexible: "018f809e-31fe-7ccd-b906-b4876df0c556",
  Urgent: "018f809e-5354-74c8-85e7-9aa6dead0de6",
  Within2Weeks: "018f809e-721e-71fb-b52d-3312dffdc86f",
  Within1Month: "018f809e-ad12-7811-ad8d-1d138f0e211d",
  PlanningAndBudgeting: "018f809e-8be9-7d90-837e-3a16b6d017f2",
  ReadyToBook: "018f809e-bdf9-4c32-a918-f52ccf194d78",
} as const;

type PreferredStartIdsType =
  (typeof PreferredStartIds)[keyof typeof PreferredStartIds];
const preferredStartsMap: Record<
  PreferredStartIdsType,
  { option: string; level?: number; label: string }
> = {
  [PreferredStartIds.Flexible]: {
    option: "FLEXIBLE",
    label: "I'm flexible on the start date",
  },
  [PreferredStartIds.Urgent]: {
    option: "ASAP",
    level: 48,
    label: "As soon as possible",
  },
  [PreferredStartIds.Within2Weeks]: {
    option: "WITHIN2WEEKS",
    label: "Within 2 weeks",
  },
  [PreferredStartIds.Within1Month]: {
    option: "WITHIN1MONTH",
    label: "Within 1 month",
  },
  [PreferredStartIds.PlanningAndBudgeting]: {
    option: "INPLANNING",
    label: "I'm budgeting / researching",
  },
  [PreferredStartIds.ReadyToBook]: {
    option: "READYTOBOOK",
    label: "I'm ready to book",
  },
};

const preferredStartDefault = {
  option: "FLEXIBLE",
  label: "I'm flexible on the start date",
};

export const mapPreferredStart = (
  jobPreferredStart?: ConsumerJobResponse["preferredStart"],
) => {
  if (
    !jobPreferredStart ||
    !preferredStartsMap[jobPreferredStart?.id as PreferredStartIdsType]
  ) {
    return preferredStartDefault;
  }

  return preferredStartsMap[jobPreferredStart.id as PreferredStartIdsType];
};
