import { AxiosError, axios } from "@checkatrade/axios";
import { FastifyBaseLogger } from "fastify";

import { config } from "./config";
import { getUrl } from "./get-url";
import { ReferralData, ReferralGetUsersResponse } from "./schema";

export type GetUserProps = {
  email: string;
  campaignId: number;
  logger: FastifyBaseLogger;
};
export const getUser = async (
  props: GetUserProps,
): Promise<ReferralData | null> => {
  const url = getUrl("/users");
  const { email, campaignId, logger } = props;
  const httpClient = axios.getInstance({ retries: 2 });

  try {
    const response = await httpClient<ReferralGetUsersResponse>(url, {
      method: "GET",
      params: {
        filters: [
          { field: "email", value: email },
          { field: "campaign_id", value: campaignId },
        ],
      },
      headers: {
        "Authorization": `Bearer ${config.referralFactoryApiKey}`,
        "Content-Type": "application/json",
      },
    });

    return response.data.data[0];
  } catch (error) {
    const response = (error as AxiosError).response;

    if (response?.status === 404) {
      return null;
    }

    logger.error(error, "ReferralFactory API get-users request failed");
    throw error;
  }
};
