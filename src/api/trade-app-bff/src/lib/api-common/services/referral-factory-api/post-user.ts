import { axios } from "@checkatrade/axios";
import { FastifyBaseLogger } from "fastify";

import { config } from "./config";
import { getUrl } from "./get-url";
import { ReferralData, ReferralPostUsersResponse } from "./schema";

export type PostUserProps = {
  email: string;
  name: string;
  campaignId: number;
  logger: FastifyBaseLogger;
};
export const postUser = async (
  props: PostUserProps,
): Promise<ReferralData | null> => {
  const url = getUrl("/users");
  const { email, campaignId, name, logger } = props;
  const httpClient = axios.getInstance({ retries: 2 });

  try {
    const response = await httpClient<ReferralPostUsersResponse>(url, {
      method: "POST",
      data: {
        campaign_id: campaignId,
        first_name: name,
        email,
      },
      headers: {
        "Authorization": `Bearer ${config.referralFactoryApiKey}`,
        "Content-Type": "application/json",
      },
    });

    return response.data.data;
  } catch (error) {
    logger.error(error, "ReferralFactory API post-users request failed");
    throw error;
  }
};
