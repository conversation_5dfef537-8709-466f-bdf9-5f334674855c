import { Static, Type } from "@sinclair/typebox";

export const socialLinkSchema = Type.Object({
  social: Type.String(),
  url: Type.String({ format: "uri" }),
});

export const referralDataSchema = Type.Object({
  code: Type.String(),
  campaign_id: Type.Number(),
  url: Type.String({ format: "uri" }),
  sharing: Type.Array(socialLinkSchema),
});

export const referralGetUsersResponseSchema = Type.Object({
  data: Type.Array(referralDataSchema),
});

export const referralPostUsersResponseSchema = Type.Object({
  data: referralDataSchema,
});

export type ReferralSocialLink = Static<typeof socialLinkSchema>;
export type ReferralData = Static<typeof referralDataSchema>;
export type ReferralGetUsersResponse = Static<
  typeof referralGetUsersResponseSchema
>;
export type ReferralPostUsersResponse = Static<
  typeof referralPostUsersResponseSchema
>;
