import { mediaServiceSDK } from "@checkatrade/media-service-sdk";

export async function fetchMediaAttachments(
  ids: string[],
  removeCorruptedUrls: boolean = true,
) {
  if (ids.length === 0) {
    return [];
  }

  let attachments = await mediaServiceSDK
    .media()
    .getByIds(ids)
    .then((response) => response.data);

  // Media service may return url = empty string if not in viewable state
  if (removeCorruptedUrls) {
    attachments = attachments.filter((attachment) => Boolean(attachment?.url));
  }

  return attachments;
}

export type MediaAttachment =
  typeof mediaServiceSDK.schemas.api.getMedia.response.static;
