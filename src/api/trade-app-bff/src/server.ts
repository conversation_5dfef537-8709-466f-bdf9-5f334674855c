import {
  type Logger,
  fastify<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  getLoggerConfig,
  plugins,
} from "@checkatrade/fastify-five";
import { type FastifyInstance, fastify } from "fastify";

import { config, openApiPluginConfig } from "./config";
import * as controllers from "./controllers";

let instance: FastifyInstance | undefined = undefined;
let logger: Logger = console;

const buildApp = async () => {
  const app = fastify({
    logger: getLoggerConfig(config.logger),
  });

  app.register(plugins.allowEmptyJsonBody);
  app.register(plugins.setHealthAndMetricsLogLevel);
  app.register(plugins.setValidatorCompiler);
  app.register(plugins.openApi, openApiPluginConfig);
  app.register(plugins.router, controllers);

  app.setErrorHandler(fastifyErrorHandler);

  await app.ready();
  logger = app.log;

  return app;
};

const start = async () => {
  instance = await buildApp();

  instance.listen({ port: config.app.httpPort, host: "0.0.0.0" }, (err) => {
    if (err) {
      logger.error(err, "Failed to start server");
      process.exit(1);
    }
  });
};

const stop = async (): Promise<void> => {
  instance?.server.close((err) => {
    if (err) {
      logger.error(err, "Failed to stop server");
      process.exit(1);
    }
  });
};

export const server = { buildApp, start, stop, logger };
