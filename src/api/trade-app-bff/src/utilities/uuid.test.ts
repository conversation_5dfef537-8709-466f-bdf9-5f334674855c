import { v4 as UUID4, v6 as UUID6, v7 as UUID7 } from "uuid";

import { validateUUIDv7 } from "./uuid";

describe("uuid", () => {
  it("should return true for a valid UUID v7", () => {
    const validUUID7 = UUID7();
    expect(validateUUIDv7(validUUID7)).toBe(true);
  });

  it("should return false for a non UUIDv7 UUID", () => {
    const validUUID6 = UUID6();
    expect(validateUUIDv7(validUUID6)).toBe(false);

    const validUUID4 = UUID4();
    expect(validateUUIDv7(validUUID4)).toBe(false);
  });

  it("should return false if string is not a UUID", () => {
    const nonUUIDString = "invalid-uuid";
    expect(validateUUIDv7(nonUUIDString)).toBe(false);
  });
});
