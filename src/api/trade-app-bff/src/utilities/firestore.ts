const firestoreTimestampToISOString = (timestamp: {
  _seconds: number;
  _nanoseconds: number;
}): string => {
  return new Date(timestamp._seconds * 1000).toISOString();
};

export const transformFirestoreTimestamps = <T>(
  data: T,
): ReplaceFirestoreTimestampsWithISO<T> => {
  if (Array.isArray(data)) {
    return data.map(
      transformFirestoreTimestamps,
    ) as ReplaceFirestoreTimestampsWithISO<T>;
  } else if (data && typeof data === "object") {
    const transformed: any = {}; // eslint-disable-line @typescript-eslint/no-explicit-any
    for (const key in data) {
      const value = (data as any)[key]; // eslint-disable-line @typescript-eslint/no-explicit-any
      if (
        value &&
        typeof value === "object" &&
        "_seconds" in value &&
        "_nanoseconds" in value
      ) {
        transformed[key] = firestoreTimestampToISOString(value);
      } else {
        transformed[key] = transformFirestoreTimestamps(value);
      }
    }
    return transformed as ReplaceFirestoreTimestampsWithISO<T>;
  }
  return data as ReplaceFirestoreTimestampsWithISO<T>;
};

type FirestoreTimestamp = {
  _seconds: number;
  _nanoseconds: number;
};

// Recursive utility type to replace FirestoreTimestamp with ISO string
export type ReplaceFirestoreTimestampsWithISO<T> =
  T extends FirestoreTimestamp ? string
  : T extends Array<infer U> ? Array<ReplaceFirestoreTimestampsWithISO<U>>
  : T extends object ?
    { [K in keyof T]: ReplaceFirestoreTimestampsWithISO<T[K]> }
  : T;
