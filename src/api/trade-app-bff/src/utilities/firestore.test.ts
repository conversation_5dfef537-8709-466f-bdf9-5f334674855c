import { transformFirestoreTimestamps } from "./firestore";

describe("transformFirestoreTimestamps", () => {
  it("transforms flat object with timestamp", () => {
    const input = {
      createdAt: { _seconds: 1609459200, _nanoseconds: 0 },
    };
    const result = transformFirestoreTimestamps(input);
    expect(result).toEqual({
      createdAt: "2021-01-01T00:00:00.000Z",
    });
  });

  it("transforms nested object with timestamps", () => {
    const input = {
      user: {
        createdAt: { _seconds: 1609459200, _nanoseconds: 0 },
        profile: {
          updatedAt: { _seconds: 1612137600, _nanoseconds: 0 },
        },
      },
    };
    const result = transformFirestoreTimestamps(input);
    expect(result).toEqual({
      user: {
        createdAt: "2021-01-01T00:00:00.000Z",
        profile: {
          updatedAt: "2021-02-01T00:00:00.000Z",
        },
      },
    });
  });

  it("transforms arrays with timestamps", () => {
    const input = [
      { createdAt: { _seconds: 1609459200, _nanoseconds: 0 } },
      { createdAt: { _seconds: 1612137600, _nanoseconds: 0 } },
    ];
    const result = transformFirestoreTimestamps(input);
    expect(result).toEqual([
      { createdAt: "2021-01-01T00:00:00.000Z" },
      { createdAt: "2021-02-01T00:00:00.000Z" },
    ]);
  });

  it("handles null and primitive values", () => {
    const input = {
      name: "John",
      createdAt: null,
      count: 3,
    };
    const result = transformFirestoreTimestamps(input);
    expect(result).toEqual({
      name: "John",
      createdAt: null,
      count: 3,
    });
  });

  test("leaves non-timestamp objects unchanged", () => {
    const input = { something: { foo: "bar" } };
    const result = transformFirestoreTimestamps(input);
    expect(result).toEqual({ something: { foo: "bar" } });
  });
});
