import { ContactRole } from "@checkatrade/trade-bff-types";

import { ContactsType } from "../services/firebase/firestore/schemas/company";

export const isAccountOwner = (
  authdEmail: string,
  contacts: Array<Pick<ContactsType, "roleId" | "email">>,
): boolean => {
  return contacts.some(
    (contact) =>
      contact.roleId &&
      contact.roleId === ContactRole.Owner &&
      contact.email === authdEmail,
  );
};
