import { ContactRole } from "@checkatrade/trade-bff-types";
import { faker } from "@faker-js/faker";

import { ContactsType } from "../services/firebase/firestore/schemas/company";
import { isAccountOwner } from "./member";

describe("Member", () => {
  const MOCK_AUTHD_EMAIL = faker.internet.email();

  it("should return TRUE if the authenticated user IS the account owner", () => {
    const mockContacts: ContactsType[] = [
      {
        roleId: ContactRole.Owner,
        email: MOCK_AUTHD_EMAIL,
      },
    ];

    expect(isAccountOwner(MOCK_AUTHD_EMAIL, mockContacts)).toBe(true);
  });

  it("should return FALSE if the authenticated user is NOT the account owner", () => {
    const mockContacts: ContactsType[] = [
      {
        roleId: ContactRole.Owner,
        email: faker.internet.email(),
      },
      {
        roleId: ContactRole.AdminContact,
        email: MOCK_AUTHD_EMAIL,
      },
      {
        roleId: ContactRole.Owner,
        email: faker.internet.email(),
      },
    ];

    expect(isAccountOwner(MOCK_AUTHD_EMAIL, mockContacts)).toBe(false);
  });

  it("should return FALSE if roleId is not present", () => {
    const mockContacts: ContactsType[] = [
      {
        email: faker.internet.email(),
      },
      {
        email: MOCK_AUTHD_EMAIL,
      },
      {
        email: faker.internet.email(),
      },
    ];

    expect(isAccountOwner(MOCK_AUTHD_EMAIL, mockContacts)).toBe(false);
  });
});
