FROM node:22.14-alpine AS base

RUN npm i -g corepack@latest && corepack enable

ARG PACKAGE_NAME
ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ARG DD_VERSION

ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}
ENV DD_VERSION=${DD_VERSION}

WORKDIR /app

COPY deps meta pkg ./

RUN --mount=type=bind,source=.pnpm-store,target=/root/.local/share/pnpm/store/v10 \
	pnpm --filter "${PACKAGE_NAME}..." install --frozen-lockfile --prefer-offline && \
	pnpm --if-present --filter "${PACKAGE_NAME}^..." build && \
	pnpm --if-present --filter "${PACKAGE_NAME}" run build

CMD [ "pnpm", "--filter", "trade-app-bff", "run", "start"]
