import { AxiosRequestConfig } from "axios";

import { getAxiosRequestHeaders } from "./get-axios-headers.helper";

/** @internal **/
export interface AxiosRequestConfiguration {
  token?: string;
  headers?: Record<string, string> | [string, string][];
}

/** @internal **/
export const getAxiosConfig = (
  config: AxiosRequestConfiguration,
): AxiosRequestConfig => {
  return {
    headers: getAxiosRequestHeaders(config.headers, config.token),
  };
};
