import { AxiosHeaders, RawAxiosRequestHeaders } from "axios";

/**
 * @internal
 * Returns headers for Axios (and other similar system)
 * **/
export const getAxiosRequestHeaders = (
  incomingHeaders?: Record<string, string> | [string, string][],
  bearerToken?: string,
): RawAxiosRequestHeaders => {
  const axiosHeaders = new AxiosHeaders();
  const headers: [string, string][] = [];

  // map object to [string, string][];
  if (incomingHeaders !== undefined) {
    if (!Array.isArray(incomingHeaders)) {
      headers.push(...Object.entries(incomingHeaders));
    }
  }

  // add headers
  for (const [key, value] of headers) {
    axiosHeaders.set(key, value);
  }

  // override Authorization is token given
  if (bearerToken) {
    axiosHeaders.Authorization = `Bearer ${bearerToken}`;
  }

  return axiosHeaders;
};
