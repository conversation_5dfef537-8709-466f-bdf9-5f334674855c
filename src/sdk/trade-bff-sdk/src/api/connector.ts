import {
  BadRequestError,
  ConflictError,
  ForbiddenError,
  InternalServerError,
  NotFoundError,
  ServiceUnavailableError,
  UnauthorizedError,
} from "@checkatrade/errors";
import axios, { AxiosError } from "axios";
import axiosBetterStacktrace from "axios-better-stacktrace";

import { config } from "../config";

const serviceUnavailableErrors = new Set([
  AxiosError.ERR_NETWORK,
  AxiosError.ECONNABORTED,
  AxiosError.ETIMEDOUT,
]);

export const createApiConnector = (baseURL?: string) => {
  const instance = axios.create({
    baseURL: baseURL ?? config.bffTradeAPIUrl,
  });
  axiosBetterStacktrace(instance);

  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error instanceof AxiosError) {
        if (error.code === AxiosError.ERR_INVALID_URL) {
          throw new BadRequestError("Invalid URL provided");
        }
        if (error.code && serviceUnavailableErrors.has(error.code)) {
          throw new ServiceUnavailableError(error.message);
        }

        switch (error?.response?.status) {
          case 400:
            throw new BadRequestError(error?.response?.data?.detail);
          case 401:
            throw new UnauthorizedError(error?.response?.data?.detail);
          case 403:
            throw new ForbiddenError(error?.response?.data?.detail);
          case 404:
            throw new NotFoundError(error?.response?.data?.detail);
          case 409:
            throw new ConflictError(error?.response?.data?.detail);
          case 503:
            throw new ServiceUnavailableError(error?.response?.data?.detail);
        }

        throw new InternalServerError(
          error?.response?.data?.detail || "Reason unknown",
        );
      }
    },
  );

  return instance;
};
