import { AxiosInstance, AxiosRequestConfig } from "axios";

import { getAxiosConfig } from "../helpers/get-axios-config.helper";

export class BaseApi {
  connector: AxiosInstance;
  config: AxiosRequestConfig;

  constructor(connector: AxiosInstance, token?: string) {
    this.connector = connector;
    this.config = getAxiosConfig({ token });
  }

  setConfig(connector: AxiosInstance) {
    // NOSONAR
    this.connector = connector;
  }
}
