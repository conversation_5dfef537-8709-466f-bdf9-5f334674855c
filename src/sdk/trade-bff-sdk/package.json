{"name": "@checkatrade/trade-bff-sdk", "version": "0.0.1", "description": "BFF Trade SDK easing usage of BFF Trade API", "homepage": "https://github.com/checkatrade/core-trade-bff", "author": "Checkatrade.com", "license": "UNLICENSED", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "*.md"], "scripts": {"prebuild": "rimraf ./dist", "build": "tsc", "dotenv": "dotenvx run -f ../../../.env", "test": "jest --runInBand", "lint": "eslint .", "lint:fix": "eslint --fix ."}, "peerDependencies": {"@checkatrade/errors": "^0.3.0"}, "dependencies": {"@checkatrade/errors": "^0.8.1", "@checkatrade/env": "^0.2.0", "@checkatrade/trade-bff-types": "workspace:^", "@sinclair/typebox": "^0.32.16", "axios-better-stacktrace": "^2.1.6", "axios": "^1.8.2"}, "devDependencies": {"@types/eslint": "~8.56.2", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0"}}