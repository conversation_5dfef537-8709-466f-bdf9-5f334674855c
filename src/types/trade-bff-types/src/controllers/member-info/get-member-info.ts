import { Static, Type } from "@sinclair/typebox";

import { Nullish } from "../../helpers";

export const GetMemberInfoResponseSchema = Type.Object({
  data: Type.Object({
    memberId: Nullish(Type.String()),
    companyId: Type.Integer(),
    name: <PERSON><PERSON><PERSON>(Type.String()),
    isMembershipFlexible: Type.Boolean(),
    joinedDate: Nullish(Type.String()),
    legacyProductType: Null<PERSON>(Type.String()),
    membershipType: Nullish(Type.String()),
    traderId: Nullish(Type.Integer()),
    uniqueName: Nullish(Type.String()),
    accountBalance: Type.Number(),
    hasAcceptedEssentialsTerms: Type.Boolean(),
    isAccountOwner: Type.Boolean(),
  }),
});
export type GetMemberInfoResponse = Static<typeof GetMemberInfoResponseSchema>;
