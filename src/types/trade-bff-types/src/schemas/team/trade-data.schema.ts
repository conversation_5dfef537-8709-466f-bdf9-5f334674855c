import { z } from "zod";

import { VettingStatus } from "../vetting";
import {
  CompanyRole,
  MembershipType,
  RelationshipStatus,
  RelationshipType,
} from "./team-enums";

const teamPersonAddressSchema = z.object({
  line1: z.string().nullable(),
  line2: z.string().nullable(),
  city: z.string().nullable(),
  county: z.string().nullable(),
  postalCode: z.string().nullable(),
});

const workCategoryMinimalSchema = z.array(
  z.object({
    id: z.string(),
    subCategories: z.array(z.object({ id: z.string() })),
  }),
);

const paginationSchema = z.object({
  currentPage: z.number(),
  pageSize: z.number(),
  totalRecordsCount: z.number(),
  totalPagesCount: z.number(),
});

export const addTeamPersonTradeDataBody = z.object({
  fullName: z.string().max(120),
  firstName: z.string().max(40),
  lastName: z.string().max(80),
  dateOfBirth: z.iso.datetime().nullable(),
  lastUpdated: z.iso.datetime().nullable(),
  email: z.email().nullable(),
  mobilePhone: z.string().max(15).nullable(),
  role: z.enum(CompanyRole),
  nickname: z.string().optional(),
  address: teamPersonAddressSchema,
  workCategories: workCategoryMinimalSchema,
});

export type AddTeamPersonTradeDataBody = z.infer<
  typeof addTeamPersonTradeDataBody
>;

export const updateTeamPersonTradeDataBody = z.object({
  firstName: z.string().max(100),
  lastName: z.string().max(100),
  dateOfBirth: z.iso.datetime(),
  email: z.email(),
  phone: z.string().max(15),
  mobilePhone: z.string().max(15).optional(),
  nickname: z.string().optional(),
  address: teamPersonAddressSchema,
  workCategories: workCategoryMinimalSchema,
});

export type UpdateTeamPersonTradeDataBody = z.infer<
  typeof updateTeamPersonTradeDataBody
>;

const nullishString = z.string().nullable().optional();

const personSchema = z.object({
  id: z.string(),
  fullName: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  dateOfBirth: z.string(),
  lastUpdated: z.string(),
  email: nullishString,
  mobilePhone: nullishString,
  phone: nullishString,
  address: teamPersonAddressSchema,
  nickname: nullishString,
  role: z.enum(CompanyRole),
  workCategories: workCategoryMinimalSchema.optional(),
  membershipType: z.enum(MembershipType).optional(),
});

export type TeamPerson = z.infer<typeof personSchema>;

export const getTeamTradeDataResponse = z.object({
  data: z.array(personSchema),
  pagination: paginationSchema,
});

export type GetTeamTradeDataResponse = z.infer<typeof getTeamTradeDataResponse>;

export const inviteSchema = z.object({
  id: z.string(),
  relationshipStatus: z.enum(RelationshipStatus).nullable(),
  expiryDate: z.string(),
  relationshipType: z.enum(RelationshipType),
  emailAddress: z.email(),
  dateCreated: z.string(),
  dateUpdated: z.string(),
  dateAccepted: z.string().optional(),
  childMemberId: z.string().nullable(),
  parentMemberId: z.string(),
});

export const companySchema = z.object({
  companyId: z.number(),
  traderId: z.number(),
  name: z.string(),
  uniqueName: z.string(),
  id: z.string(),
});

export const inviteWithChildCompanySchema = z.object({
  invite: inviteSchema,
  childCompany: companySchema,
});

export const inviteWithChildCompanySchemaAndVettingStatus = z.object({
  invite: inviteSchema.extend({
    vettingStatus: z.enum(VettingStatus),
  }),
  childCompany: companySchema,
});

export const inviteWithParentCompanySchema = z.object({
  invite: inviteSchema,
  parentCompany: companySchema,
});

export const getTeamInvitesTradeDataResponse = z.object({
  data: z.array(inviteWithChildCompanySchemaAndVettingStatus),
  pagination: paginationSchema,
});

export const getTeamMemberInviteTVSResponse = z.object({
  data: z.array(inviteWithChildCompanySchema),
  pagination: paginationSchema,
});

export type GetTeamInvitesTradeDataResponse = z.infer<
  typeof getTeamInvitesTradeDataResponse
>;

export const inviteSubcontractorTradeDataResponse = z.object({
  id: z.string(),
  relationshipStatus: z.enum(RelationshipStatus).nullable(),
  expiryDate: z.string(),
  relationshipType: z.enum(RelationshipType),
  emailAddress: z.string(),
  dateCreated: z.string(),
  dateUpdated: z.string(),
  parentMemberId: z.string(),
  childMemberId: z.string().nullable().optional(),
});

export type InviteSubcontractorTradeDataResponse = z.infer<
  typeof inviteSubcontractorTradeDataResponse
>;

export const getMemberByLegacyCompanyIdResponse = z.object({
  memberId: z.string(),
});

export type GetMemberByLegacyCompanyIdResponse = z.infer<
  typeof getMemberByLegacyCompanyIdResponse
>;

export const getTeamPendingInvitesTradeDataResponse = z.object({
  data: z.array(inviteWithParentCompanySchema),
  pagination: paginationSchema,
});

export type GetTeamPendingInvitesTradeDataResponse = z.infer<
  typeof getTeamPendingInvitesTradeDataResponse
>;
