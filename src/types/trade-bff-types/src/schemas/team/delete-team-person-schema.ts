import { Static, Type } from "@sinclair/typebox";

export const deleteTeamPersonResponse = Type.Composite([
  Type.Object(
    {
      id: Type.String(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type DeleteTeamPersonResponse = Static<typeof deleteTeamPersonResponse>;

export const deleteTeamPersonQuery = Type.Intersect([
  Type.Object({
    type: Type.String(),
  }),
]);

export type DeleteTeamPersonQuery = Static<typeof deleteTeamPersonQuery>;
