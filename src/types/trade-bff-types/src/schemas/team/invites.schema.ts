import { Type } from "@sinclair/typebox";

import { VettingStatus } from "../vetting";
import { RelationshipStatus, RelationshipType } from "./team-enums";

export const inviteSchema = Type.Object({
  id: Type.String(),
  relationshipStatus: Type.Union([Type.Enum(RelationshipStatus), Type.Null()]),
  expiryDate: Type.String(),
  relationshipType: Type.Enum(RelationshipType),
  emailAddress: Type.String(),
  dateCreated: Type.String(),
  dateUpdated: Type.String(),
  dateAccepted: Type.Optional(Type.String()),
  childMemberId: Type.Union([Type.String(), Type.Null()]),
  parentMemberId: Type.String(),
});

export const companySchema = Type.Object({
  companyId: Type.Number(),
  traderId: Type.Number(),
  name: Type.String(),
  uniqueName: Type.String(),
  id: Type.String(),
});

export const inviteWithChildCompanySchemaAndVettingStatus = Type.Object({
  invite: Type.Composite([
    inviteSchema,
    Type.Object({
      vettingStatus: Type.Enum(VettingStatus),
    }),
  ]),
  childCompany: companySchema,
});

export const inviteWithParentCompanySchema = Type.Object({
  invite: inviteSchema,
  parentCompany: companySchema,
});
