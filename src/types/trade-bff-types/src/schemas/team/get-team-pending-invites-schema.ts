import { schemas } from "@checkatrade/schemas";
import { Static, Type } from "@sinclair/typebox";

import { inviteWithParentCompanySchema } from "./invites.schema";

export type GetTeamPendingInvitesResponse = Static<
  typeof getTeamPendingInvitesResponse
>;

export const getTeamPendingInvitesResponse = Type.Composite([
  Type.Object(
    {
      data: Type.Array(inviteWithParentCompanySchema),
      pages: Type.Number(),
      total: Type.Number(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export enum TeamPendingInvitesRelationshipStatus {
  Pending = "Pending",
  Accepted = "Accepted",
}

export const getTeamPendingInvitesQuery = Type.Intersect([
  Type.Object({
    page: Type.Optional(Type.Number({ minimum: 1 })),
    pageSize: Type.Optional(Type.Number({ minimum: 1 })),
    relationshipStatus: Type.Optional(
      Type.Enum(TeamPendingInvitesRelationshipStatus),
    ),
  }),
  schemas.pagination.query,
]);

export type GetTeamPendingInvitesQuery = Static<
  typeof getTeamPendingInvitesQuery
>;
