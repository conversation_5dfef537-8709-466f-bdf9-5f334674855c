import { Static, Type } from "@sinclair/typebox";

import { teamPersonAddressSchema, workCategoryMinimalSchema } from "./team";

export const updateTeamPersonsQuery = Type.Intersect([
  Type.Object({
    firstName: Type.String(),
    lastName: Type.String(),
    nickname: Type.Optional(Type.String()),
    dateOfBirth: Type.String({ format: "date-time" }),
    address: teamPersonAddressSchema,
    email: Type.String(),
    phoneNumber: Type.String(),
    mobilePhone: Type.Optional(Type.String()),
    workCategories: workCategoryMinimalSchema,
  }),
]);

export type UpdateTeamPersonsQuery = Static<typeof updateTeamPersonsQuery>;

export const updateTeamPersonResponse = Type.Composite([
  Type.Object(
    {
      id: Type.String(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type UpdateTeamPersonResponse = Static<typeof updateTeamPersonResponse>;
