import { Static, Type } from "@sinclair/typebox";

import {
  contractingType,
  teamPersonAddressSchema,
  workCategoryMinimalSchema,
} from "./team";

export const addTeamPersonsResponse = Type.Composite([
  Type.Object(
    {
      id: Type.String(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type AddTeamPersonsResponse = Static<typeof addTeamPersonsResponse>;

export const addTeamPersonsQuery = Type.Intersect([
  Type.Object({
    firstName: Type.String(),
    lastName: Type.String(),
    nickname: Type.Optional(Type.String()),
    dateOfBirth: Type.String({ format: "date-time" }),
    address: teamPersonAddressSchema,
    email: Type.String(),
    phoneNumber: Type.String(),
    workCategories: workCategoryMinimalSchema,
    contractingType: contractingType,
    id: Type.Optional(Type.String()),
  }),
]);

export type AddTeamPersonsQuery = Static<typeof addTeamPersonsQuery>;
