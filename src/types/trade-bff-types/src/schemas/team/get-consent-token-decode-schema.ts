import { Static, Type } from "@sinclair/typebox";

import {
  companyIdHeader,
  timeLimitedTokenHeader,
} from "../common/request-headers";

export const getVettingConsentTokenDecodeResponse = Type.Object({
  personId: Type.String(),
  memberId: Type.String(),
  companyId: Type.Number(),
});

export const getVettingConsentTokenDecodeHeaders = Type.Composite([
  companyIdHeader,
  timeLimitedTokenHeader,
]);
export type GetVettingConsentTokenDecodeHeaders = Static<
  typeof getVettingConsentTokenDecodeHeaders
>;

export type GetVettingConsentTokenDecodeResponse = Static<
  typeof getVettingConsentTokenDecodeResponse
>;
