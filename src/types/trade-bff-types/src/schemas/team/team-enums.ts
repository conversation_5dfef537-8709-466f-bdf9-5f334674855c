// TODO: Import these from @checkatrade/core-trade-shared-types when available in future SDK implementation - https://checkatrade.atlassian.net/browse/DSU-3326
export enum CompanyRole {
  Owner = "Owner",
  Director = "Director",
  AdminContact = "Admin Contact",
  Employee = "Employee",
}

export enum RelationshipStatus {
  Pending = "Pending",
  Accepted = "Accepted",
}

export enum RelationshipType {
  Subcontractor = "Subcontractor",
}

export enum MembershipType {
  FullMember = "Full Member",
  AffiliateMember = "Affiliate Member",
  NonMember = "Non Member",
  ExMember = "Ex-Member",
  Claimed = "Claimed",
  Paused = "Paused",
  NationalAccounts = "National Accounts",
  FreeMember = "Free Member",
  EssentialsMember = "Essentials Member",
}
