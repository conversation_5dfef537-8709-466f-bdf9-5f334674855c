import { Nullable } from "@checkatrade/schemas";
import { Static, Type } from "@sinclair/typebox";

const accreditationNeededSchema = Type.Object({
  id: Type.Number(),
  deleted: Type.Boolean(),
  name: Type.String(),
});

export const subCategorySchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  accreditationsNeeded: Type.Optional(Type.Array(accreditationNeededSchema)),
});

export const workCategorySchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  subCategories: Type.Array(subCategorySchema),
});

export const workCategoryMinimalSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    subCategories: Type.Array(Type.Object({ id: Type.String() })),
  }),
);

export type WorkCategoryMinimal = Static<typeof workCategoryMinimalSchema>;

export const contractingType = Type.Enum({
  Employee: "Employee",
  SubContractor: "Subcontractor",
});

export type ContractingType = Static<typeof contractingType>;

export const vettingStatusType = Type.Enum({
  ISSUE: "ISSUE",
  IN_PROGRESS: "IN_PROGRESS",
  NOT_STARTED: "NOT_STARTED",
  ACTIVE: "ACTIVE",
  CONSENT_PENDING: "CONSENT_PENDING",
});

export const teamPersonAddressSchema = Type.Object({
  line1: Nullable(Type.String()),
  line2: Nullable(Type.String()),
  city: Nullable(Type.String()),
  county: Nullable(Type.String()),
  postalCode: Nullable(Type.String()),
});

export const teamParams = Type.Object({
  personId: Type.String({ format: "uuid" }),
});

export type TeamParams = Static<typeof teamParams>;
