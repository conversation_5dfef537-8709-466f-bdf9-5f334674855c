import { Nullable } from "@checkatrade/schemas";
import { Static, Type } from "@sinclair/typebox";

import { MemberMembershipTypeSchema } from "../trade-data";
import { inviteSchema } from "./invites.schema";

export const getTeamInviteResponseTDS = Type.Composite([
  Type.Object(inviteSchema.properties, {
    additionalProperties: false,
    description: "Successful response",
  }),
  Type.Object(
    {
      currentMemberId: Type.String(),
    },
    {
      additionalProperties: false,
      description: "Additional properties",
    },
  ),
]);
export type GetTeamInviteResponseTDS = Static<typeof getTeamInviteResponseTDS>;

export const getTeamInviteResponse = Type.Object(
  {
    ...getTeamInviteResponseTDS.properties,
    parentCompanyInfo: Nullable(
      Type.Object({
        name: Type.String(),
        membershipType: MemberMembershipTypeSchema,
      }),
    ),
  },
  {
    additionalProperties: false,
    description: "Successful response",
  },
);
export type GetTeamInviteResponse = Static<typeof getTeamInviteResponse>;

export const getTeamInviteParams = Type.Object({
  id: Type.String({ format: "uuid" }),
});

export type GetTeamInviteParams = Static<typeof getTeamInviteParams>;
