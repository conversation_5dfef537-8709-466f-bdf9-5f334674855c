import { Static, Type } from "@sinclair/typebox";

import {
  contractingType,
  teamPersonAddressSchema,
  vettingStatusType,
  workCategorySchema,
} from "./team";

export const getTeamPersonResponseSchema = Type.Composite([
  Type.Object(
    {
      id: Type.String(),
      firstName: Type.String(),
      lastName: Type.String(),
      dateOfBirth: Type.String(),
      email: Type.String(),
      phoneNumber: Type.String(),
      address: teamPersonAddressSchema,
      category: Type.Array(workCategorySchema),
      vettingStatus: vettingStatusType,
      contractingType: contractingType,
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type GetTeamPersonResponse = Static<typeof getTeamPersonResponseSchema>;
