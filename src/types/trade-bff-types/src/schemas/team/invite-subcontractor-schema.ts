import { Static, Type } from "@sinclair/typebox";

export const inviteSubcontractorResponse = Type.Composite([
  Type.Object(
    {
      id: Type.String(),
      inviteUrl: Type.String(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type InviteSubcontractorResponse = Static<
  typeof inviteSubcontractorResponse
>;

export const inviteSubcontractorQuery = Type.Intersect([
  Type.Object({
    email: Type.String(),
  }),
]);

export type InviteSubcontractorQuery = Static<typeof inviteSubcontractorQuery>;
