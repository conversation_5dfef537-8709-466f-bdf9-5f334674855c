import { schemas } from "@checkatrade/schemas";
import { Static, Type } from "@sinclair/typebox";

import { VettingConsentStatus, VettingStatus } from "../vetting";
import { contractingType, workCategoryMinimalSchema } from "./team";
import { CompanyRole } from "./team-enums";

export type GetTeamResponse = Static<typeof getTeamResponse>;

export const getTeamResponse = Type.Composite([
  Type.Object(
    {
      data: Type.Array(
        Type.Object({
          id: Type.String(),
          fullName: Type.String(),
          firstName: Type.String(),
          lastName: Type.String(),
          dateOfBirth: Type.String(),
          lastUpdated: Type.String(),
          email: Type.String(),
          mobilePhone: Type.Optional(Type.Union([Type.Null(), Type.String()])),
          phone: Type.Optional(Type.Union([Type.Null(), Type.String()])),
          role: Type.Enum(CompanyRole),
          workCategories: Type.Optional(workCategoryMinimalSchema),
          vettingStatus: Type.Optional(Type.Enum(VettingStatus)),
          consentStatus: Type.Optional(Type.Enum(VettingConsentStatus)),
        }),
      ),
      pages: Type.Number(),
      total: Type.Number(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export const getTeamQuery = Type.Intersect([
  Type.Object({
    contractingType: contractingType,
    searchTerm: Type.Optional(Type.String()),
    page: Type.Optional(Type.Number({ minimum: 1 })),
    pageSize: Type.Optional(Type.Number({ minimum: 1 })),
    sortBy: Type.Optional(Type.String()),
    sortOrder: Type.Optional(Type.String()),
    status: Type.Optional(Type.String()),
  }),
  schemas.pagination.query,
]);
