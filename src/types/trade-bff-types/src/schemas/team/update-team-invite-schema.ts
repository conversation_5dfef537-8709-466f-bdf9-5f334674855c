import { Static, Type } from "@sinclair/typebox";

import { inviteSchema } from "./invites.schema";
import { RelationshipStatus, RelationshipType } from "./team-enums";

export const updateTeamInviteBody = Type.Intersect([
  Type.Object({
    relationshipStatus: Type.Enum(RelationshipStatus),
    expiryDate: Type.Optional(Type.String()),
    hasAccepted: Type.Optional(Type.Boolean()),
  }),
]);

export type UpdateTeamInviteBody = Static<typeof updateTeamInviteBody>;

export const updateTeamInviteParams = Type.Object({
  id: Type.String({ format: "uuid" }),
});

export type UpdateTeamInviteParams = Static<typeof updateTeamInviteParams>;

export const updateTeamInviteResponse = Type.Composite([
  Type.Intersect(
    [
      inviteSchema,
      Type.Object({
        relationshipType: Type.Enum(RelationshipType),
      }),
    ],
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type UpdateTeamInviteResponse = Static<typeof updateTeamInviteResponse>;
