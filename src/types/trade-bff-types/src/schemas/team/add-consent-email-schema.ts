import { Static, Type } from "@sinclair/typebox";

export const addConsentEmailParams = Type.Object({
  personId: Type.String({ format: "uuid" }),
});

export type AddConsentEmailParams = Static<typeof addConsentEmailParams>;

export const addConsentEmailResponse = Type.Object({
  Email: Type.String(),
});

export type AddConsentEmailResponse = Static<typeof addConsentEmailResponse>;

export const addConsentEmailBody = Type.Object({
  email: Type.String(),
  phoneNumber: Type.String(),
  firstName: Type.String(),
  employer: Type.String(),
});

export type AddConsentEmailBody = Static<typeof addConsentEmailBody>;
