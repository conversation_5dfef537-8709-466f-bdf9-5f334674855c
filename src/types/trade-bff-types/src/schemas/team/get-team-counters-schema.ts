import { Static, Type } from "@sinclair/typebox";

export type GetTeamCountersResponse = Static<typeof getTeamCountersResponse>;

export const getTeamCountersResponse = Type.Composite([
  Type.Object(
    {
      totalEmployees: Type.Number(),
      totalSubcontractors: Type.Number(),
      totalInvites: Type.Number(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);
