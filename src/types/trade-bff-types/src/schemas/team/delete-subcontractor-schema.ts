import { Static, Type } from "@sinclair/typebox";

export const deleteSubcontractorResponse = Type.Composite([
  Type.Object(
    {
      subcontractorId: Type.String(),
      memberId: Type.String(),
      inviteId: Type.String(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type DeleteSubcontractorResponse = Static<
  typeof deleteSubcontractorResponse
>;

export const deleteSubcontractorParams = Type.Object({
  subcontractorId: Type.String({ format: "uuid" }),
});

export type DeleteSubcontractorParams = Static<
  typeof deleteSubcontractorParams
>;

export const deleteSubcontractorQuery = Type.Object({
  inviteId: Type.String({ format: "uuid" }),
});

export type DeleteSubcontractorQuery = Static<typeof deleteSubcontractorQuery>;
