import { schemas } from "@checkatrade/schemas";
import { Static, Type } from "@sinclair/typebox";

import { inviteWithChildCompanySchemaAndVettingStatus } from "./invites.schema";

export type GetTeamInvitesResponse = Static<typeof getTeamInvitesResponse>;

export const getTeamInvitesResponse = Type.Composite([
  Type.Object(
    {
      data: Type.Array(inviteWithChildCompanySchemaAndVettingStatus),
      pages: Type.Number(),
      total: Type.Number(),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export const getTeamInvitesQuery = Type.Intersect([
  Type.Object({
    page: Type.Optional(Type.Number({ minimum: 1 })),
    pageSize: Type.Optional(Type.Number({ minimum: 1 })),
  }),
  schemas.pagination.query,
]);
