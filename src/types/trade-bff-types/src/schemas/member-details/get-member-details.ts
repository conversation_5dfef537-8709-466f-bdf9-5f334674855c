import { Optional, Static, Type } from "@sinclair/typebox";

import { ContactRole } from "../../enums";
import { Nullish } from "../../helpers";

const MailingAddressSchema = Type.Object({
  city: Nullish(Type.String()),
  country: Nullish(Type.String()),
  county: Nullish(Type.String()),
  postcode: <PERSON>ull<PERSON>(Type.String()),
  street: Nullish(Type.String()),
  town: Nullish(Type.String()),
});

export const ContactsSchema = Type.Object({
  contactId: Nullish(Type.String()),
  dateOfBirth: Nullish(
    Type.String({ description: "ISO 8601 date-time format" }),
  ),
  email: <PERSON>ullish(Type.String()),
  firstName: Nullish(Type.String()),
  lastName: Nullish(Type.String()),
  mailingAddress: Nullish(MailingAddressSchema),
  mobilePhone: Nullish(Type.String()),
  phone: Nullish(Type.String()),
  roleId: <PERSON><PERSON><PERSON>(Type.Number()),
  role: <PERSON>ull<PERSON>(
    Type.Union(Object.keys(ContactRole).map((key) => Type.Literal(key))),
  ),
});

export const GetMemberDetailsResponseSchema = Type.Object({
  accountEmail: Optional(Type.String()),
  accountPhone: Optional(Type.String()),
  isAccountOwner: Type.Boolean(),
  contacts: Optional(Type.Array(ContactsSchema)),
  companyAdminAddress: Optional(MailingAddressSchema),
  companyPrimaryPostalAddress: Optional(MailingAddressSchema),
});

export type GetMemberDetailsResponse = Static<
  typeof GetMemberDetailsResponseSchema
>;
