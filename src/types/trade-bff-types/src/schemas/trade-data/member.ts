import { Nullable } from "@checkatrade/schemas";
import { Static, Type } from "@sinclair/typebox";

import { Nullish } from "../../helpers";

export const MemberMembershipTypeSchema = Type.Union([
  Type.Literal("Full Member"),
  Type.Literal("Affiliate Member"),
  Type.Literal("Non Member"),
  Type.Literal("Ex-Member"),
  Type.Literal("Claimed"),
  Type.Literal("Paused"),
  Type.Literal("National Accounts"),
  Type.Literal("Free Member"),
  Type.Literal("Essentials Member"),
]);

export enum MemberProfileStatus {
  Live = "Live",
  Hidden = "Hidden",
  Blind = "Blind",
}

// Member schema
export const MemberSchema = Type.Object({
  companyId: Type.Number(),
  traderId: Type.Number(),
  name: Type.String(),
  legacyProductType: Nullable(Type.String()),
  isMembershipFlexible: Nullable(Type.Boolean()),
  uniqueName: Type.String(),
  isCampaignMember: Nullable(Type.Boolean()),
  membershipType: Nullable(MemberMembershipTypeSchema),
  joinedDate: Nullable(Type.String()),
  registeredName: Nullable(Type.String()),
  isOnProbation: Nullable(Type.Boolean()),
  acceptedEssentialsTermsDate: Nullish(Type.Date({ format: "date-time" })),
  profileStatus: Type.Optional(
    Type.Union([Type.Enum(MemberProfileStatus), Type.Null()]),
  ),
});

// Export types
export type Member = Static<typeof MemberSchema>;
export type MemberMembershipType = Static<typeof MemberMembershipTypeSchema>;
