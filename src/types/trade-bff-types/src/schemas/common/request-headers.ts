import { Static, Type } from "@sinclair/typebox";

export const companyIdHeader = Type.Object({
  "x-trade-company-id": Type.Integer({ minimum: 1 }),
});

export const selfServiceHeaders = Type.Composite([
  Type.Object({
    "x-correlation-id": Type.Optional(Type.String({ description: "UUIDv7" })),
  }),
  companyIdHeader,
]);

export const timeLimitedTokenHeader = Type.Object({
  "x-trade-time-limited-token": Type.String(),
});

export type TimeLimitedTokenHeader = Static<typeof timeLimitedTokenHeader>;
export type SelfServiceHeaders = Static<typeof selfServiceHeaders>;
export type CompanyIdHeader = Static<typeof companyIdHeader>;
