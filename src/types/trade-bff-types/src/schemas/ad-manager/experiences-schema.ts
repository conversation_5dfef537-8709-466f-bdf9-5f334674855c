import { Static, Type } from "@sinclair/typebox";

export const experienceSchema = Type.Object({
  experienceId: Type.Number(),
  currencyType: Type.Number(),
  name: Type.String(),
  minBid: Type.Number(),
  maxBid: Type.Number(),
  bidStrategyPlayer: Type.Number(),
  bidStrategyLeader: Type.Number(),
  bidStrategyCompetitor: Type.Number(),
});

export const getExperiencesResponse = Type.Object({
  experiences: Type.Array(experienceSchema),
});

export type GetExperiencesResponse = Static<typeof getExperiencesResponse>;
