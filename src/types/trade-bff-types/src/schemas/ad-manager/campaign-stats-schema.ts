import { Static, Type } from "@sinclair/typebox";

export enum Dimensions {
  COMPANY_ID = "companyId",
  CAMPAIGN_ID = "campaignId",
  SEARCH_TYPE = "searchType",
}

export const getCampaignStatsQuery = Type.Object({
  from: Type.String({ format: "date" }),
  to: Type.String({ format: "date" }),
  dimensions: Type.Optional(
    Type.Union([Type.Array(Type.Enum(Dimensions)), Type.Enum(Dimensions)]),
  ),
  companyId: Type.Number(),
});

export const getCampaignStatsResponse = Type.Array(
  Type.Object({
    impressions: Type.Number(),
    clicks: Type.Number(),
    cpc: Type.Number(),
    clickThroughRate: Type.Number(),
    spend: Type.Number(),
  }),
);

export type GetCampaignStatsQuery = Static<typeof getCampaignStatsQuery>;
export type GetCampaignStatsResponse = Static<typeof getCampaignStatsResponse>;
