export enum VettingStatusTVS {
  AwaitingConsent = "AwaitingConsent",
  InFlight = "InFlight",
  Successful = "Successful",
  UnderQuery = "UnderQuery",
  Failed = "Failed",
  NotStarted = "NotStarted",
}

export enum VettingStatusSF {
  RVM = "RVM", // Fully vetted, full member
  EV = "EV", // Essentials vetting
  E = "E", // Essentials (not vetted)
  U = "U", // unclassified = no vetting done on account
  F = "F", // Failed vetting
  D = "D", // Declined
  R = "R", // Recommended (ex members use this)
  IE = "IE", // Business Ceased (Unsaveable)
  SC = "SC", // Suspended (Complaints)
  X = "X", // Expelled
}

export enum VettingConsentStatus {
  NotCreated = "NotCreated", // default
  NotProvided = "NotProvided",
  Granted = "Granted",
  Denied = "Denied",
}

export enum VettingStatus {
  InProgress = "IN_PROGRESS",
  NotStarted = "NOT_STARTED",
  Active = "ACTIVE", // default
  Failed = "FAILED",
  NeedMoreInfo = "NEED_MORE_INFO",
  ConsentPending = "CONSENT_PENDING",
}
