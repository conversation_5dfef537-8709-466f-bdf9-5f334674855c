import { Static, Type } from "@sinclair/typebox";

import { vettingConsentStatusLiteral } from "./vetting-consent-schema";

const vettingStatusTVSLiteral = Type.Union([
  Type.Literal("AwaitingConsent"),
  Type.Literal("InFlight"),
  Type.Literal("Successful"),
  Type.Literal("UnderQuery"),
  Type.Literal("Failed"),
  Type.Literal("NotStarted"),
]);

const relationshipTypeLiteral = Type.Union([
  Type.Literal("Employee"),
  Type.Literal("Subcontractor"),
]);

export const getVettingStatusResponse = Type.Object({
  consentStatus: vettingConsentStatusLiteral,
  externalId: Type.String(), // TDS ID
  tradePersonId: Type.String(), // Vetting ID
  vettingStatus: vettingStatusTVSLiteral,
});

export const getWorkersVettingStatusResponse = Type.Object({
  companyId: Type.Number(),
  companyUuid: Type.String(),
  relationshipType: relationshipTypeLiteral,
  workers: Type.Array(getVettingStatusResponse),
});

export type GetWorkersVettingStatusResponse = Static<
  typeof getWorkersVettingStatusResponse
>;
export type GetVettingStatusResponse = Static<typeof getVettingStatusResponse>;
