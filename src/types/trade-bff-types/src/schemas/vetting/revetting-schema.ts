import { Static, Type } from "@sinclair/typebox";

export enum RevettingStatus {
  REQUIRED = "REQUIRED",
  NOT_REQUIRED = "NOT_REQUIRED",
}

export const revettingStatusSchema = Type.Object({
  revettingStatus: Type.Enum(RevettingStatus),
});

export const createMitekRequestSchema = Type.Object({
  name: Type.String(),
  phone: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  scope: Type.String(),
  address: Type.Optional(
    Type.Object({
      address1: Type.Optional(Type.String()),
      address2: Type.Optional(Type.String()),
      town: Type.Optional(Type.String()),
      region: Type.Optional(Type.String()),
      postcode: Type.Optional(Type.String()),
    }),
  ),
  mandatory: Type.Optional(Type.String()),
  min_docs: Type.Optional(Type.Number()),
  environment: Type.String(),
});

export type CreateMitekRequestSchema = Static<typeof createMitekRequestSchema>;

export const createMitekRequestResponseSchema = Type.Object({
  reference: Type.String(),
  link: Type.String(),
  id: Type.String(),
  client_key: Type.Optional(Type.String()),
  min_id_docs: Type.Optional(Type.Number()),
  min_docs: Type.Optional(Type.Number()),
  mandatory: Type.Optional(Type.String()),
  tabs_order: Type.Optional(Type.String()),
  min_poa_docs: Type.Optional(Type.Number()),
  environment: Type.Optional(Type.String()),
  scope: Type.Optional(Type.String()),
  callback_uri: Type.Optional(Type.String()),
  redirect_uri: Type.Optional(Type.String()),
});

export type CreateMitekResponseSchema = Static<
  typeof createMitekRequestResponseSchema
>;

export const revettingUrlSchema = Type.Object({
  url: Type.String(),
});

export type RevettingUrlSchema = Static<typeof revettingUrlSchema>;

export const mitekRequestStatus = Type.Union([
  Type.Literal("SENT"),
  Type.Literal("CANCELLED"),
  Type.Literal("CONFIRMATION"),
  Type.Literal("EXPIRED"),
  Type.Literal("COMPLETED"),
  Type.Literal("ABANDONED"),
]);

export const mitekRequestResult = Type.Union([
  Type.Literal("PASS"),
  Type.Literal("FAIL"),
  Type.Literal("FRAUD"),
  Type.Literal("REFER"),
]);

export const mitekRequestStatusResponseSchema = Type.Object({
  status: mitekRequestStatus,
  created: Type.String(),
  started: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  completed: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  result: Type.Optional(Type.Union([mitekRequestResult, Type.Null()])),
  expiry: Type.String(),
  link: Type.String(),
  lastReminderSent: Type.Optional(Type.Union([Type.String(), Type.Null()])),
});

export type MitekRequestStatusResponse = Static<
  typeof mitekRequestStatusResponseSchema
>;
