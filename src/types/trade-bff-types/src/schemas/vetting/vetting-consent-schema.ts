import { Static, Type } from "@sinclair/typebox";

import { Nullish } from "../../helpers";
import { CompanyRole } from "../team/team-enums";
import { GetVettingStatusResponse } from "./vetting-details-schema";
import {
  VettingConsentStatus,
  VettingStatus,
  VettingStatusSF,
  VettingStatusTVS,
} from "./vetting-enums";

export const vettingConsentStatusLiteral = Type.Union([
  Type.Literal("NotCreated"),
  Type.Literal("NotProvided"),
  Type.Literal("Granted"),
  Type.Literal("Denied"),
]);

export const updateTeamPersonVettingQuery = Type.Object({
  consent: Type.Union([
    Type.Literal(VettingConsentStatus.Granted),
    Type.Literal(VettingConsentStatus.Denied),
  ]),
});

export type UpdateTeamPersonVettingQuery = Static<
  typeof updateTeamPersonVettingQuery
>;

export const updateTeamPersonVettingResponse = Type.Composite([
  Type.Object(
    {
      vettingId: Nullish(Type.String()),
      externalId: Nullish(Type.String()), // TDS ID
      consentStatus: Type.Enum(VettingConsentStatus),
    },
    {
      additionalProperties: false,
      description: "Successful response",
    },
  ),
]);

export type UpdateTeamPersonVettingResponse = Static<
  typeof updateTeamPersonVettingResponse
>;

export const getTeamPersonVettingResponse = Type.Object({
  vettingId: Type.String(),
  externalId: Type.String(), // TDS ID
  consentStatus: Type.Enum(VettingConsentStatus),
  vettingStatus: Type.Enum(VettingStatus),
});
export type GetTeamPersonVettingResponse = Static<
  typeof getTeamPersonVettingResponse
>;

// legacy
export const getVettingConsentQueryParams = Type.Optional(
  Type.Object({ relationshipType: Type.Optional(Type.Enum(CompanyRole)) }),
);
// legacy
export type GetVettingConsentQueryParams = Static<
  typeof getVettingConsentQueryParams
>;

export const getTeamMemberVettingDetailsQueryParams = Type.Optional(
  Type.Object({
    email: Type.String({ format: "email" }),
  }),
);
export type GetTeamMemberVettingDetailsQueryParams = Static<
  typeof getTeamMemberVettingDetailsQueryParams
>;

export const vettingTradePersonSchema = Type.Object({
  id: Nullish(Type.String()),
  externalId: Nullish(Type.String()),
  consent: vettingConsentStatusLiteral,
  dateCreated: Nullish(Type.String()),
  dateModified: Nullish(Type.String()),
  dateConsentUpdated: Nullish(Type.String()),
});

export const vettingConsentOnlySchema = Type.Object({
  consentStatus: Type.Enum(VettingConsentStatus),
});

export const vettingStatusOnlySchema = Type.Object({
  vettingStatus: Type.Enum(VettingStatus),
});

export type VettingTradePerson = Static<typeof vettingTradePersonSchema>;

export function mapToTeamVettingConsentStatusResponse(
  vettingData: VettingTradePerson,
): UpdateTeamPersonVettingResponse {
  return {
    vettingId: vettingData.id,
    externalId: vettingData.externalId,
    consentStatus: vettingData.consent as VettingConsentStatus,
  };
}

export const vettingStatusTVSMapper = (
  status: VettingStatusTVS,
): VettingStatus => {
  switch (status) {
    case VettingStatusTVS.AwaitingConsent:
      return VettingStatus.ConsentPending;
    case VettingStatusTVS.NotStarted:
      return VettingStatus.NotStarted;
    case VettingStatusTVS.InFlight:
      return VettingStatus.InProgress;
    case VettingStatusTVS.UnderQuery:
      return VettingStatus.NeedMoreInfo;
    case VettingStatusTVS.Successful:
      return VettingStatus.Active;
    case VettingStatusTVS.Failed:
      return VettingStatus.Failed;
  }
};

export const vettingStatusSFMapper = (status: VettingStatusSF) => {
  const vettingMap = {
    [VettingStatusSF.RVM]: VettingStatus.Active,
    [VettingStatusSF.EV]: VettingStatus.Active,
    [VettingStatusSF.E]: VettingStatus.NotStarted,
    [VettingStatusSF.U]: VettingStatus.NotStarted,
    [VettingStatusSF.F]: VettingStatus.Failed,
    [VettingStatusSF.D]: VettingStatus.Failed,
    [VettingStatusSF.R]: VettingStatus.Failed,
    [VettingStatusSF.IE]: VettingStatus.Failed,
    [VettingStatusSF.SC]: VettingStatus.Failed,
    [VettingStatusSF.X]: VettingStatus.Failed,
  };
  const vettingStatus = vettingMap[status];
  if (!vettingStatus) {
    return VettingStatus.NotStarted;
  }
  return vettingStatus;
};

export function mapToPersonVettingResponse({
  tradePersonId: vettingId,
  externalId,
  consentStatus,
  vettingStatus,
}: GetVettingStatusResponse): GetTeamPersonVettingResponse {
  return {
    vettingId,
    externalId,
    consentStatus: consentStatus as VettingConsentStatus,
    vettingStatus: vettingStatusTVSMapper(vettingStatus as VettingStatusTVS),
  };
}
