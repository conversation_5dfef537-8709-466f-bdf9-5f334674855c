import { Static, Type } from "@sinclair/typebox";

export const accountSchema = Type.Object({
  id: Type.String(),
  updated_by_id: Type.String(),
  updated_time: Type.String(),
  created_by_id: Type.String(),
  created_time: Type.String(),
  account_number: Type.String(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  default_payment_method_id: Type.Optional(Type.String()),
  bill_cycle_day: Type.Optional(Type.Number()),
});

export const updateAccountParamsSchema = Type.Object({
  accountId: Type.String(),
});

export const updateAccountBodySchema = Type.Object({
  paymentMethodId: Type.String(),
});

export type UpdateAccountParams = Static<typeof updateAccountParamsSchema>;
export type UpdateAccountBody = Static<typeof updateAccountBodySchema>;
export type Account = Static<typeof accountSchema>;
