import { Static, Type } from "@sinclair/typebox";

export const addressSchema = Type.Object({
  line1: Type.Optional(Type.String()),
  line2: Type.Optional(Type.String()),
  city: Type.Optional(Type.String()),
  country: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  postal_code: Type.Optional(Type.String()),
});

export const paymentMethodSchema = Type.Object({
  id: Type.String(),
  updated_by_id: Type.String(),
  updated_time: Type.String(),
  created_by_id: Type.String(),
  created_time: Type.String(),
  account_id: Type.String(),
  billing_details: Type.Optional(
    Type.Object({
      name: Type.Optional(Type.String()),
      address: Type.Optional(addressSchema),
      email: Type.Optional(Type.String()),
      phone: Type.Optional(Type.String()),
    }),
  ),
  bacs_debit: Type.Optional(
    Type.Object({
      account_number: Type.String(),
      bank_code: Type.String(),
      mandate: Type.Optional(
        Type.Object({
          id: Type.String(),
          reason: Type.Optional(Type.String()),
          state: Type.Optional(Type.String()),
        }),
      ),
    }),
  ),
  state: Type.Optional(Type.String()),
  existing_mandate: Type.Optional(Type.Boolean()),
  total_number_of_processed_payments: Type.Optional(Type.Number()),
  total_number_of_error_payments: Type.Optional(Type.Number()),
  last_transaction_time: Type.Optional(Type.String()),
  last_transaction_status: Type.Optional(Type.String()),
});

export const paymentMethodParamsSchema = Type.Object({
  paymentMethodId: Type.String(),
});

export type PaymentMethodParams = Static<typeof paymentMethodParamsSchema>;
export type PaymentMethod = Static<typeof paymentMethodSchema>;
