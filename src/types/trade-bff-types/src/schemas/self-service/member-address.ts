import { Static, Type } from "@sinclair/typebox";

import { Nullish } from "../../helpers";

//Uppercase letters, digits, and spaces - no trailing or leading spaces
const POSTCODE_REGEX = "^[A-Z]{1,2}\\d{1,2}[A-Z]?\\s?\\d[A-Z]{2}$";

export const updateMemberAddressSchema = Type.Object({
  street: Type.String(),
  postcode: Type.String({
    pattern: POSTCODE_REGEX,
  }),
  city: Type.String(),
  town: Nullish(Type.String()),
  county: Nullish(Type.String()),
  country: Nullish(Type.String()),
});

export type UpdateMemberAddressBody = Static<typeof updateMemberAddressSchema>;
