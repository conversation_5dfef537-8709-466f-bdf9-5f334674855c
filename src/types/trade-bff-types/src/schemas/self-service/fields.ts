import { Static, Type } from "@sinclair/typebox";

enum SelfServiceRequestType {
  COMPANY_NAME = "CompanyName",
  MEMBER_EMAIL = "MemberEmail",
  MEMBER_PHONE = "MemberPhone",
  MEMBER_TRADING_ADDRESS = "MemberTradingAddress",
  MEMBER_ADMIN_ADDRESS = "MemberAdminAddress",
  ADD_PERSON = "AddPerson",
  UPDATE_PERSON = "UpdatePerson",
  REMOVE_PERSON = "RemovePerson",
}
export enum PersonCompanyRoleDto {
  OWNER = "Owner",
  ADMIN_CONTACT = "AdminContact",
  DIRECTOR = "Director",
  EMPLOYEE = "Employee",
}
export const selfServiceResponseSchema = Type.Object({
  requestType: Type.Enum(SelfServiceRequestType),
  correlationId: Type.Optional(Type.String()),
});

export type SelfServiceResponse = Static<typeof selfServiceResponseSchema>;
