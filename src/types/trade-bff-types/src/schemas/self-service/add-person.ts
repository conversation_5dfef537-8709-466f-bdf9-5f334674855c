import { Static, Type } from "@sinclair/typebox";

import { Nullish } from "../../helpers";
import { PersonCompanyRoleDto } from "./fields";
import { updateMemberAddressSchema } from "./member-address";

export const addPersonSchema = Type.Object({
  firstName: Type.String(),
  lastName: Type.String(),
  email: Type.String(),
  dateOfBirth: Type.String({ format: "date-time" }),
  mobilePhone: Type.String(),
  phone: Nullish(Type.String()),
  mailingAddress: Nullish(updateMemberAddressSchema),
  companyRole: Type.Enum(PersonCompanyRoleDto),
});

export type AddPersonType = Static<typeof addPersonSchema>;
