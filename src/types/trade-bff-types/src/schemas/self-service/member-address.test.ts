import { Value } from "@sinclair/typebox/value";

import {
  UpdateMemberAddressBody,
  updateMemberAddressSchema,
} from "./member-address";

describe("member-trading-address schema", () => {
  describe("Valid schema", () => {
    it("valid postcode with space in between", () => {
      const validSchema: UpdateMemberAddressBody = {
        street: "123 Main St",
        city: "London",
        postcode: "NW7 1LD",
      };

      const isValid = Value.Check(updateMemberAddressSchema, validSchema);

      expect(isValid).toBe(true);
    });

    it("valid postcode with NO space in between", () => {
      const validSchema: UpdateMemberAddressBody = {
        street: "123 Main St",
        city: "London",
        postcode: "PO161TT",
      };

      const isValid = Value.Check(updateMemberAddressSchema, validSchema);

      expect(isValid).toBe(true);
    });
  });

  describe("Invalid schema", () => {
    it("invalid postcode with lowercase letters", () => {
      const invalidSchema: UpdateMemberAddressBody = {
        street: "123 Main St",
        city: "London",
        postcode: "nw7 1LD",
      };

      const isValid = Value.Check(updateMemberAddressSchema, invalidSchema);

      expect(isValid).toBe(false);
    });

    it("invalid postcode with trailing space", () => {
      const invalidSchema: UpdateMemberAddressBody = {
        street: "123 Main St",
        city: "London",
        postcode: "PO16 1TT ",
      };

      const isValid = Value.Check(updateMemberAddressSchema, invalidSchema);

      expect(isValid).toBe(false);
    });

    it("invalid postcode with leading space", () => {
      const invalidSchema: UpdateMemberAddressBody = {
        street: "123 Main St",
        city: "London",
        postcode: " PO16 1TT",
      };

      const isValid = Value.Check(updateMemberAddressSchema, invalidSchema);

      expect(isValid).toBe(false);
    });
  });
});
