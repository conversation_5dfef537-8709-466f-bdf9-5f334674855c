import { Static, Type } from "@sinclair/typebox";

import { Nullish } from "../../helpers";
import { PersonCompanyRoleDto } from "./fields";
import { updateMemberAddressSchema } from "./member-address";

export const updatePersonSchema = Type.Object({
  contactId: Type.String(),
  dateOfBirth: Nullish(Type.String({ format: "date-time" })),
  email: Type.String(),
  firstName: Type.String(),
  lastName: Type.String(),
  mailingAddress: Nullish(updateMemberAddressSchema),
  mobilePhone: Type.String(),
  phone: Nullish(Type.String()),
  companyRole: Type.Enum(PersonCompanyRoleDto),
});

export type UpdatePersonType = Static<typeof updatePersonSchema>;
