import { Static, Type } from "@sinclair/typebox";

import { SERVICE_VERSION_WITH_DETAILS_RESPONSE } from "./response";

export const GET_SERVICES_RESPONSE_SCHEMA = Type.Object({
  pagination: Type.Object({
    total: Type.Optional(Type.Number()),
    pageCount: Type.Optional(Type.Number()),
    page: Type.Number(),
    size: Type.Number(),
    hasNext: Type.Optional(Type.Boolean()),
    hasPrevious: Type.Optional(Type.Boolean()),
  }),
  data: Type.Array(SERVICE_VERSION_WITH_DETAILS_RESPONSE),
});

export type GetServicesResponseSchema = Static<
  typeof GET_SERVICES_RESPONSE_SCHEMA
>;
