import {
  DayOfTheWeek,
  Quantifier,
  StatusType,
} from "@checkatrade/service-catalog-types";
import { Static, Type } from "@sinclair/typebox";

const StrongUUID = Type.String({ format: "uuid" });

const SERVICE_VERSION_SCHEMA = Type.Object({
  id: StrongUUID,
  categoryId: Type.Number(),
  categoryName: Type.String(),
  serviceId: StrongUUID,
  companyId: Type.Number(),
  name: Type.String(),
  description: Type.String(),
  homeownerNotes: Type.Optional(Type.String()),
  whatIsIncluded: Type.Array(Type.String()),
  whatIsNotIncluded: Type.Array(Type.String()),
  status: Type.Enum(StatusType),
});

const SERVICE_DAY_SCHEMA = Type.Object({
  serviceVersionId: StrongUUID,
  dayOfTheWeek: Type.Enum(DayOfTheWeek),
  startTime: Type.String({ format: "time" }),
  endTime: Type.String({ format: "time" }),
});

const LINE_ITEM_SCHEMA = Type.Object({
  serviceVersionId: StrongUUID,
  priceInPence: Type.Number(),
  quantifier: Type.Enum(Quantifier),
  notes: Type.String(),
});

export const SERVICE_VERSION_WITH_DETAILS_RESPONSE = Type.Intersect([
  SERVICE_VERSION_SCHEMA,
  Type.Object({
    days: Type.Array(SERVICE_DAY_SCHEMA),
    lineItems: Type.Array(LINE_ITEM_SCHEMA),
  }),
]);

export type ServiceVersionWithDetails = Static<
  typeof SERVICE_VERSION_WITH_DETAILS_RESPONSE
>;

export const SERVICE_VERSION_PARAMS = Type.Object({
  serviceId: StrongUUID,
  versionId: StrongUUID,
});

export type ServiceVersionParams = Static<typeof SERVICE_VERSION_PARAMS>;
