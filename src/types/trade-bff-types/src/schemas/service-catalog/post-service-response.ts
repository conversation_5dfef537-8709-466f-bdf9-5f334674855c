import {
  DayOfTheWeek,
  Quantifier,
  StatusType,
} from "@checkatrade/service-catalog-types";
import { Static, Type } from "@sinclair/typebox";

const StrongUUID = Type.String({ format: "uuid" });

const SERVICE_DAY_SCHEMA = Type.Object({
  day: Type.Enum(DayOfTheWeek),
  startTime: Type.String({ format: "time" }),
  endTime: Type.String({ format: "time" }),
});

const LINE_ITEM_SCHEMA = Type.Object({
  priceInPence: Type.Number(),
  unit: Type.Enum(Quantifier),
  homeownerPriceRelatedNotes: Type.String(),
});

export const POST_SERVICE_VERSION_RESPONSE_SCHEMA = Type.Object({
  id: StrongUUID,
  serviceId: StrongUUID,
  categoryId: Type.Number(),
  name: Type.String(),
  description: Type.String(),
  companyId: Type.Number(),
  homeownerNotes: Type.Optional(Type.String()),
  whatIsIncluded: Type.Array(Type.String()),
  whatIsNotIncluded: Type.Array(Type.String()),
  status: Type.Enum(StatusType),
  lineItems: Type.Array(LINE_ITEM_SCHEMA),
  days: Type.Optional(Type.Array(SERVICE_DAY_SCHEMA)),
});

export type PostServiceVersionResponseSchema = Static<
  typeof POST_SERVICE_VERSION_RESPONSE_SCHEMA
>;
