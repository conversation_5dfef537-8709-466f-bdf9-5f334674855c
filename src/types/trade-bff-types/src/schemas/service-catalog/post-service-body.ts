import { DayOfTheWeek, Quantifier } from "@checkatrade/service-catalog-types";
import { Static, Type } from "@sinclair/typebox";

const SERVICE_DAYS_BODY_SCHEMA = Type.Object({
  day: Type.Enum(DayOfTheWeek),
  startTime: Type.String({ format: "time" }),
  endTime: Type.String({ format: "time" }),
});

export const SERVICE_VERSION_BODY_SCHEMA = Type.Object({
  categoryId: Type.Number(),
  name: Type.String({
    minLength: 15,
    maxLength: 80,
  }),
  description: Type.String({
    minLength: 100,
    maxLength: 1200,
  }),
  days: Type.Optional(Type.Array(SERVICE_DAYS_BODY_SCHEMA)),
  whatIsIncluded: Type.Array(Type.String()),
  whatIsNotIncluded: Type.Array(Type.String()),
  homeownerNotes: Type.Optional(Type.String()),
  priceInPence: Type.Number({ minimum: 100 }),
  quantifier: Type.Enum(Quantifier),
  homeownerPriceRelatedNotes: Type.String(),
});

export type ServiceVersionBodySchema = Static<
  typeof SERVICE_VERSION_BODY_SCHEMA
>;
