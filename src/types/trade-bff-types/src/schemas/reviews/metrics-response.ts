import { Static, Type } from "@sinclair/typebox";

export const reviewMetricPropertySchema = Type.Object({
  scoreAverage: Type.Number(),
  scoreCount: Type.Number(),
});

export const externalMetricSchema = Type.Object({
  totalReviewCount: Type.Number(),
  publishedCount: Type.Number(),
  complaintCount: Type.Number(),
  complaintPercentage: Type.Number(),
  reviewReplyRate: Type.Number(),
  connectedJobRate: Type.Number(),
  verifiedCount: Type.Number(),
  score: Type.Optional(Type.Number()),
  courtesy: reviewMetricPropertySchema,
  qualityOfWorkmanship: reviewMetricPropertySchema,
  reliabilityAndTimekeeping: reviewMetricPropertySchema,
  tidiness: reviewMetricPropertySchema,
  communication: reviewMetricPropertySchema,
});

export const externalMetricResponseSchema = Type.Object({
  lifeTime: Type.Optional(externalMetricSchema),
  year: Type.Optional(externalMetricSchema),
  month: Type.Optional(externalMetricSchema),
});

export type ExternalMetricResponse = Static<
  typeof externalMetricResponseSchema
>;
