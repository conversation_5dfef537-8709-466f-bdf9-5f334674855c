{"name": "@checkatrade/trade-bff-types", "version": "1.0.0", "description": "BFF Trade API types", "homepage": "https://github.com/checkatrade/core-trade-bff", "author": "Checkatrade.com", "license": "UNLICENSED", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "*.md"], "scripts": {"prebuild": "rimraf ./dist", "build": "tsc", "typecheck": "tsc -noEmit", "lint": "eslint .", "lint:fix": "eslint --fix .", "test": "NODE_OPTIONS=\"--max-old-space-size=8192\" jest --runInBand --colors"}, "dependencies": {"@checkatrade/schemas": "^0.6.0", "@checkatrade/service-catalog-types": "^2.0.21", "@sinclair/typebox": "^0.33.16", "zod": "4.0.0-beta.20250424T163858"}}