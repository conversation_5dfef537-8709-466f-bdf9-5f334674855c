###########################################
# Firestore emulator
# only for local development, picked up automatically by the google clients
###########################################
FIRESTORE_EMULATOR_PORT=8080
FIRESTORE_EMULATOR_HOST=localhost:${FIRESTORE_EMULATOR_PORT}

###########################################
# src/api/*
###########################################
LOG_LEVEL=fatal

###########################################
# src/api/trade-app-bff
###########################################
TRADE_BFF_PORT=5001
TRADE_FIREBASE_APP_PROJECT_ID=default
REPORT_RECORDS_PER_PAGE=1000

###########################################
# src/api/trade-app-bff/**/consumers
###########################################
ADDRESS_API_URL=https://api.staging.checkatrade.com/v1/address
CONSUMER_API_URL=https://api.staging.checkatrade.com/v1/consumer
CONTENT_API_URL=https://content-api-inhqerqcdq-nw.a.run.app
GCP_SA_FIREBASE_AUTH: <EMAIL>
JOBS_API_URL=https://api.staging.checkatrade.com/v1/jobs
MATCHING_SPAM_API_URL=https://ham-spam-f3ewssbvtq-nw.a.run.app/api/v1
MEDIA_SERVICE_API_URL: https://api.staging.checkatrade.com/v1/media-service
PAYMENT_API_URL=https://api.staging.checkatrade.com/v1/payment
QUOTING_API_URL=https://api.staging.checkatrade.com/v1/quoting
REVIEW_API_URL=https://api.staging.checkatrade.com/v1/review
SCHEDULING_API_URL=https://api.staging.checkatrade.com/v1/scheduling
SEARCH_API_URL=https://search-dev.checkatrade.com/api/v1
CONSUMER_WEB_URL=https://frontend-staging.checkatrade.com
TRADE_WEB_URL=https://membersapp-staging.checkatrade.com
TRADE_SIGNUP_WEB_URL=https://member-onboarding.checkatrade.dev
SERVICE_CATALOG_API_URL=https://api.staging.checkatrade.com/v1/service-catalog
REVIEWS_SUMMARY_API_URL=https://api.staging.checkatrade.com/v1/reviews-summariser

CAPI_GCP_PROJECT_ID=capi-staging-27323

COMMS_API_URL=https://communication-api-66w7cgdjcq-nw.a.run.app
COMMS_REPORT_USER_VERSION=V1
COMMS_GCP_PROJECT_ID=communication-stg-24665

STREAM_CHAT_API_KEY=stream-chat-api-key
STREAM_CHAT_API_SECRET=stream-chat-api-secret
STREAM_CHAT_TOKEN_EXPIRATION=3600

###########################################
# Redirection (src/job/auto-redirect)
###########################################
REDIRECTION_ENABLED=true
REDIRECTION_MAX_ACCEPTS=2
REDIRECTION_MAX_OPPORTUNITIES=6
REDIRECTION_JOB_DUE_HOURS=24
REDIRECTION_JOB_EXPIRY_HOURS=168
REDIRECTION_OPPORTUNITY_DUE_HOURS=24
REDIRECTION_OPPORTUNITY_EXPIRY_HOURS=168

###########################################
# Referral Factory (src/api/**/referral)
###########################################
REFERRAL_FACTORY_URL=https://referral-factory.com/api/v1
REFERRAL_FACTORY_API_KEY=supersecretkey

###########################################
# Trade Data Service
###########################################
TRADE_DATA_SERVICE_API_URL=http://core-trade-data.core-trade-data.svc.cluster.local:3000

###########################################
# Trade Vetting Service
###########################################
VETTING_SERVICE_API_URL=https://api.staging.checkatrade.com/v1/vetting

###########################################
# Mitek
###########################################
MITEK_API_URL=mitek-base-url
MITEK_CLIENT_ID=mitek-client-id
MITEK_CLIENT_SECRET=mitek-client-secret

###########################################
# Ad Manager
###########################################
AD_MANAGER_API_URL=https://api.staging.checkatrade.com/v1/ad-manager
