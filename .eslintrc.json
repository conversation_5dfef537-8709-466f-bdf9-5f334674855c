{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "env": {"es2022": true, "jest": true, "node": true}, "plugins": ["import", "@typescript-eslint"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/quotes": ["error", "double", {"avoidEscape": true, "allowTemplateLiterals": true}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}, "ignorePatterns": ["dist/", "node_modules/"]}