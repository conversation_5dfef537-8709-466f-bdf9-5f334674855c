# Core Trade BFF Service [![Coverage](https://sonarqube.checkatrade.dev/api/project_badges/measure?project=checkatrade_core-trade-bff_3eaba8bb-d07a-4f0c-a1c8-e3bab1bf7a55&metric=coverage&token=sqb_34c82923e287fe7a03efa7759090ef44e2b6a647)](https://sonarqube.checkatrade.dev/dashboard?id=checkatrade_core-trade-bff_3eaba8bb-d07a-4f0c-a1c8-e3bab1bf7a55) [![Quality Gate Status](https://sonarqube.checkatrade.dev/api/project_badges/measure?project=checkatrade_core-trade-bff_3eaba8bb-d07a-4f0c-a1c8-e3bab1bf7a55&metric=alert_status&token=sqb_34c82923e287fe7a03efa7759090ef44e2b6a647)](https://sonarqube.checkatrade.dev/dashboard?id=checkatrade_core-trade-bff_3eaba8bb-d07a-4f0c-a1c8-e3bab1bf7a55)

The `core-trade-bff` contains a BFF service for the trade app. This replaces the consumer-trade-bff service. This service requires SlashId Auth.

# Architecture

## High Level Architecture
```mermaid
flowchart LR
  A[Trade App Frontend iOS/Android/Web]
  B[Trade App BFF]
  C[(Trade App Firestore)]
  D[GCP Projects]
  E[K8 Services]

  A --> B
  B --> C
  B --> D
  B --> E
```

## Dependant K8 Services

| Service | Description |
| --- | --- |
| `core-address` | Address service |
| `core-chat` | Chat service |
| `core-consumer-myhome` | Consumer MyHome service |
| `core-consumer` | Consumer service |
| `core-job` | Job service |
| `core-matching` | Matching service |
| `core-opportunity` | Opportunity service |
| `core-payment` | Payment service |
| `core-quoting` | Quoting service |
| `core-review` | Review service |
| `core-scheduling` | Scheduling service |

## Google Cloud Projects (cat-home-experts)

| Service | Description |
| --- | --- |
| `trade-experience Firestore` | Firestore database for trade-experience |
| `communication` | Communication service |
| `content-api` | Content API service |
| `search-service` | Search service |
| `salesforce-integ` | Salesforce integration service |
| `national-accounts` | National accounts service |


# Running locally

Before you run the example project make sure to:

1. Install dependencies

```bash
pnpm install
```

2. Set the required environment variables by running:

```bash
cp .env.sample .env && source ./.env
```

3. Build all the dependencies, including the shared library:

```bash
pnpm build
```

4. Run the trade-app-bff:

```bash
pnpm --filter trade-app-bff run start:dev
```

# Running with Docker

The project has a `docker-compose.yml` file configured to run the entire stack with a single command.

```bash
docker-compose build
docker-compose up
```

# Using pnpm

Due to monorepo structure of project is advised to perform all pnpm actions from root folder of project.
Actions on projects can be done using `--filter` argument of pnpm.


# Testing

You can run all tests within the project using the following command:

# Running locally

Setup env vars

```bash
cp .env.sample.local .env && source ./.env
```

## Setting up firebase emulator to run test script locally

TODO

## Running core services locally

- Ask Andrew/Simon to add you to the <NAME_EMAIL>
- Follow this guide to authenticate into gcp, and setup your local kube context https://checkatrade.atlassian.net/wiki/spaces/PT/pages/5054431319/Configure+cluster+access+using+kubectl+and+kubeconfig
- Install cat-kube-proxy https://github.com/checkatrade/platform-cat-kube-proxy?tab=readme-ov-file#installing

1. Ensure you're authenticated with gcloud cli and your kubecontext has been set
2. In a terminal from the root of core-trade-bff project run 
```bash
cat-kube-proxy
```

If successful, you should see the services you are connected to in the terminal.

# Further Documentation

- [Root Docs](/docs)
- [BFF Trade Docs](/src/api/trade-app-bff/README.md)
