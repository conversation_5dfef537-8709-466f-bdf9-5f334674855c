PNPM_STORE_PATH ?= $(shell pnpm store path)

ensure-%:
	@if [ "${${*}}" = "" ]; then \
		echo "Environment variable $* not set"; \
		exit 1; \
	fi

.PHONY: docker-archive
docker-archive: ensure-ARCHIVE_DIR ensure-DOCKERFILE
	@mkdir -p $(ARCHIVE_DIR)
ifdef CI
	@cp -r "$(PNPM_STORE_PATH)" $(ARCHIVE_DIR)/.pnpm-store
endif
	pnpm run \
		--if-present \
		--silent \
		-w pnpm-context \
		-p tsconfig.json \
		-p nx.json \
		-p bin/ \
		-- \
		"$(DOCKERFILE)" | tar xz -C "$(ARCHIVE_DIR)"

.PHONY: docker-build-archive
docker-build-archive: ensure-ARCHIVE_DIR ensure-PACKAGE
	@docker build --build-arg='PACKAGE_NAME=$(PACKAGE)' "$(ARCHIVE_DIR)"

.PHONY: install
install: ensure-PACKAGE
	pnpm --filter=$(PACKAGE) install

.PHONY: install-all
install-all:
	pnpm --recursive install

.PHONY: lint-all
lint-all:
	pnpm run lint

.PHONY: test-all
test-all:
	pnpm run test

.PHONY: services-start
services-start:
	@docker compose up -d

.PHONY: services-stop
services-stop:
	docker compose down