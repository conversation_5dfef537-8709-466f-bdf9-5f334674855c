{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "YtmKRC4tPo57SkSICDRFm", "type": "rectangle", "x": 368.59375, "y": 585.53125, "width": 818.24609375, "height": 362.09375, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": {"type": 3}, "seed": 1116126031, "version": 530, "versionNonce": 416541551, "isDeleted": false, "boundElements": [{"id": "7UkiXmF1p7eiJ0x4JHR6D", "type": "arrow"}], "updated": 1690266424826, "link": null, "locked": false}, {"id": "u5xDDk4c6gI7R79SAvNXE", "type": "rectangle", "x": 366.109375, "y": 223.5703125, "width": 827.55078125, "height": 163.6953125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": {"type": 3}, "seed": 1455530497, "version": 107, "versionNonce": 501395023, "isDeleted": false, "boundElements": [{"id": "7UkiXmF1p7eiJ0x4JHR6D", "type": "arrow"}], "updated": 1690222183228, "link": null, "locked": false}, {"id": "YkQAP0Owzg5QYwXkL4Dj5", "type": "rectangle", "x": 390.59375, "y": 259.72265625, "width": 232.5546875, "height": 93.2890625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": {"type": 3}, "seed": 33928623, "version": 208, "versionNonce": 211341665, "isDeleted": false, "boundElements": null, "updated": 1690222308219, "link": null, "locked": false}, {"type": "rectangle", "version": 211, "versionNonce": 1653776815, "isDeleted": false, "id": "DSmLmVd63k3vslLNPR8Oq", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 661.265625, "y": 259.69140625, "strokeColor": "#000000", "backgroundColor": "#228be6", "width": 232.5546875, "height": 93.2890625, "seed": 33928623, "groupIds": [], "roundness": {"type": 3}, "boundElements": [], "updated": 1690222360005, "link": null, "locked": false}, {"type": "rectangle", "version": 237, "versionNonce": 377023375, "isDeleted": false, "id": "Ldp8V0fd6EtZQnlJTRlhq", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 930.62890625, "y": 258.66796875, "strokeColor": "#000000", "backgroundColor": "#fa5252", "width": 232.5546875, "height": 93.2890625, "seed": 33928623, "groupIds": [], "roundness": {"type": 3}, "boundElements": [], "updated": 1690222316799, "link": null, "locked": false}, {"id": "ebWQkFwEE6xZPVqC06GBc", "type": "text", "x": 442.9609375, "y": 291.20703125, "width": 123.97987365722656, "height": 50, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": null, "seed": 177332001, "version": 258, "versionNonce": 45655695, "isDeleted": false, "boundElements": null, "updated": 1690222183229, "link": null, "locked": false, "text": "Development\nconfiguration", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "top", "baseline": 43, "containerId": null, "originalText": "Development\nconfiguration", "lineHeight": 1.25}, {"type": "text", "version": 391, "versionNonce": 1159920815, "isDeleted": false, "id": "NA1JX10bWEP4O-75YNbcp", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 716.04052734375, "y": 289.6015625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 123.97987365722656, "height": 50, "seed": 177332001, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690222183229, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Staging\nconfiguration", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Staging\nconfiguration", "lineHeight": 1.25, "baseline": 43}, {"type": "text", "version": 440, "versionNonce": 1343990209, "isDeleted": false, "id": "Yp5jzIqccFWMIdoXxivTI", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 985.6848754882812, "y": 287.171875, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 123.97987365722656, "height": 50, "seed": 177332001, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690222183229, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Production\nconfiguration", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Production\nconfiguration", "lineHeight": 1.25, "baseline": 43}, {"id": "6Kca1EslR6xt5jzaZErf0", "type": "text", "x": 403.375, "y": 240.6640625, "width": 107.24790954589844, "height": 20, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": null, "seed": 1659479425, "version": 45, "versionNonce": 270372559, "isDeleted": false, "boundElements": null, "updated": 1690222183229, "link": null, "locked": false, "text": "terragrunt.hcl", "fontSize": 16, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 14, "containerId": null, "originalText": "terragrunt.hcl", "lineHeight": 1.25}, {"type": "text", "version": 115, "versionNonce": 895666593, "isDeleted": false, "id": "bAZLKgUqZs55UW6rpDHq4", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 675.1377639770508, "y": 240.0625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 107.24790954589844, "height": 20, "seed": 1659479425, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690222183229, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "terragrunt.hcl", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "terragrunt.hcl", "lineHeight": 1.25, "baseline": 14}, {"type": "text", "version": 126, "versionNonce": 1581309167, "isDeleted": false, "id": "SYN8ZIzhiYnCl5cLwmAuq", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 941.0987014770508, "y": 239.12890625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 107.24790954589844, "height": 20, "seed": 1659479425, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690222183229, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "terragrunt.hcl", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "terragrunt.hcl", "lineHeight": 1.25, "baseline": 14}, {"id": "xH9r3o3Ui0W8SBlnFd9yg", "type": "text", "x": 378.44005584716797, "y": 199.0234375, "width": 165.11988830566406, "height": 20, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": null, "seed": 225891329, "version": 113, "versionNonce": 1216015745, "isDeleted": false, "boundElements": null, "updated": 1690222183229, "link": null, "locked": false, "text": "Terragrunt perimeter", "fontSize": 16, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 14, "containerId": null, "originalText": "Terragrunt perimeter", "lineHeight": 1.25}, {"type": "text", "version": 270, "versionNonce": 1640988385, "isDeleted": false, "id": "q2oCFbQ7CgJuPwah_w7D_", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 385.23693084716797, "y": 557.70703125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 158.27188110351562, "height": 20, "seed": 225891329, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690266424827, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "Terraform perimeter", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Terraform perimeter", "lineHeight": 1.25, "baseline": 14}, {"id": "ZKXKPf4EUx7cW3Z8mB1pE", "type": "rectangle", "x": 411.953125, "y": 633.03125, "width": 724.6953125, "height": 292.953125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": {"type": 3}, "seed": 422625761, "version": 317, "versionNonce": 68492207, "isDeleted": false, "boundElements": null, "updated": 1690266424827, "link": null, "locked": false}, {"id": "9qu2aAjk5h9TFgp2H7cqz", "type": "text", "x": 433.9140625, "y": 607.5859375, "width": 593.87158203125, "height": 20, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": null, "seed": 570151599, "version": 256, "versionNonce": 1209989825, "isDeleted": false, "boundElements": null, "updated": 1690266424827, "link": null, "locked": false, "text": "Cross-environment unique codebase aka \"your project Terraform templates\"", "fontSize": 16, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 14, "containerId": null, "originalText": "Cross-environment unique codebase aka \"your project Terraform templates\"", "lineHeight": 1.25}, {"id": "7UkiXmF1p7eiJ0x4JHR6D", "type": "arrow", "x": 793.6901839842183, "y": 407.56005389375616, "width": 1.2499974159146632, "height": 157.58057110624384, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": {"type": 2}, "seed": 1049636015, "version": 951, "versionNonce": 1025271023, "isDeleted": false, "boundElements": null, "updated": 1690266635221, "link": null, "locked": false, "points": [[0, 0], [1.2499974159146632, 157.58057110624384]], "lastCommittedPoint": null, "startBinding": {"elementId": "BgTaF4Hn9IYvA-A9X-NCF", "focus": -1.1289365468375818, "gap": 21.307371484218265}, "endBinding": {"elementId": "YtmKRC4tPo57SkSICDRFm", "focus": 0.045843022425883936, "gap": 20.390625}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "BgTaF4Hn9IYvA-A9X-NCF", "type": "text", "x": 441.5390625, "y": 417.39453125, "width": 330.84375, "height": 110.39999999999999, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": null, "seed": 886912175, "version": 707, "versionNonce": 582133903, "isDeleted": false, "boundElements": [{"id": "7UkiXmF1p7eiJ0x4JHR6D", "type": "arrow"}], "updated": 1690266593511, "link": null, "locked": false, "text": "\"North-South\" variable injection:\n   - to be considered only for the configuration\n     which varies across the 3 stages\n   - injected as input variable, must be declared\n     in variables.tf\n   - more for static content", "fontSize": 16, "fontFamily": 2, "textAlign": "left", "verticalAlign": "top", "baseline": 106, "containerId": null, "originalText": "\"North-South\" variable injection:\n   - to be considered only for the configuration\n     which varies across the 3 stages\n   - injected as input variable, must be declared\n     in variables.tf\n   - more for static content", "lineHeight": 1.15}, {"id": "b9Mt8VSSnPTnrs7Ix2Eoe", "type": "rectangle", "x": 448.546875, "y": 669.6328125, "width": 149.75, "height": 237.89062499999997, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#82c91e", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": {"type": 3}, "seed": 310368353, "version": 332, "versionNonce": 158584271, "isDeleted": false, "boundElements": [{"id": "1wNbEgQb3Zp0WFf7KDdM7", "type": "arrow"}], "updated": 1690266424827, "link": null, "locked": false}, {"type": "rectangle", "version": 443, "versionNonce": 1389623969, "isDeleted": false, "id": "Q7XWIDJk01w1V4lzKy6z9", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 948.2734375, "y": 668.060546875, "strokeColor": "#000000", "backgroundColor": "#fd7e14", "width": 149.75, "height": 239.01953124999994, "seed": 310368353, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"id": "1wNbEgQb3Zp0WFf7KDdM7", "type": "arrow"}], "updated": 1690266424827, "link": null, "locked": false}, {"id": "Jy-MvywQWpg51m7H3sQJr", "type": "text", "x": 466.6953125, "y": 649.0703125, "width": 68.63995361328125, "height": 20, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": null, "seed": 1763798063, "version": 97, "versionNonce": 1886862319, "isDeleted": false, "boundElements": null, "updated": 1690266424827, "link": null, "locked": false, "text": "module A", "fontSize": 16, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 14, "containerId": null, "originalText": "module A", "lineHeight": 1.25}, {"type": "text", "version": 224, "versionNonce": 1654521473, "isDeleted": false, "id": "GlK48r2KGCqyy423BdCWp", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 966.8323669433594, "y": 647.15234375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 69.77595520019531, "height": 20, "seed": 1763798063, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690266424827, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "module B", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "module B", "lineHeight": 1.25, "baseline": 14}, {"id": "1wNbEgQb3Zp0WFf7KDdM7", "type": "arrow", "x": 626.078452297737, "y": 702.2475131886627, "width": 293.43164415644014, "height": 0.24070464957537752, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "roundness": {"type": 2}, "seed": 1075143663, "version": 811, "versionNonce": 274918991, "isDeleted": false, "boundElements": [], "updated": 1690266424845, "link": null, "locked": false, "points": [[0, 0], [293.43164415644014, -0.24070464957537752]], "lastCommittedPoint": null, "startBinding": {"elementId": "j8QEIRXC6wkjXOVlAkD15", "focus": -1.3922167305545088, "gap": 25.31108056133735}, "endBinding": {"elementId": "j8QEIRXC6wkjXOVlAkD15", "focus": 1.3890107728168346, "gap": 25.816767823333066}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "text", "version": 853, "versionNonce": 373065313, "isDeleted": false, "id": "j8QEIRXC6wkjXOVlAkD15", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 621.94140625, "y": 727.55859375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 304.15625, "height": 128.79999999999998, "seed": 886912175, "groupIds": [], "roundness": null, "boundElements": [{"id": "1wNbEgQb3Zp0WFf7KDdM7", "type": "arrow"}], "updated": 1690266424827, "link": null, "locked": false, "fontSize": 16, "fontFamily": 2, "text": "\"West-East\" variable injection:\n  - to be considered each time a module\n    output can be passed as another\n    module input\n  - more for dynamic content as opposed to\n    hardcoded\n", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"West-East\" variable injection:\n  - to be considered each time a module\n    output can be passed as another\n    module input\n  - more for dynamic content as opposed to\n    hardcoded\n", "lineHeight": 1.15, "baseline": 125}, {"type": "text", "version": 961, "versionNonce": 142545473, "isDeleted": false, "id": "jUMwt8-16EQ-Ce9fpoOhS", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 628.3515625, "y": 850.7328124999999, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 234.375, "height": 57.599999999999994, "seed": 886912175, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690266424827, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "module \"B\" {\n  input = module.A.output\n}", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "module \"B\" {\n  input = module.A.output\n}", "lineHeight": 1.2, "baseline": 53}, {"type": "text", "version": 1048, "versionNonce": 541650319, "isDeleted": false, "id": "RPfZKPb4pyC97UUDJE4tl", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 812.421875, "y": 467.24296875000005, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 206.25, "height": 57.599999999999994, "seed": 886912175, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1690266610198, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "module \"A\" {\n  input = var.an_input\n}", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "module \"A\" {\n  input = var.an_input\n}", "lineHeight": 1.2, "baseline": 53}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}