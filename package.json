{"private": true, "scripts": {"preinstall": "npx only-allow pnpm", "build": "nx run-many --target build", "test": "nx run-many --target test --output-style=static", "prepare": "husky install", "typecheck": "nx run-many --target typecheck", "lint": "nx run-many --target lint", "lint:fix": "nx run-many --target lint:fix", "format:fix": "prettier . --write", "format:check": "prettier . --check", "pnpm-context": "bin/pnpm-context.mjs", "generate-openapi": "pnpm build && nx run-many --target generate-openapi"}, "devDependencies": {"@checkatrade/generate-package-hashes": "0.2.1", "@checkatrade/jest": "^0.5.0", "@dotenvx/dotenvx": "^1.6.4", "@nx/js": "20.4.6", "@pnpm/filter-workspace-packages": "^7.2.11", "@stoplight/spectral-cli": "^6.11.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@tsconfig/node22": "^22.0.0", "@tsconfig/strictest": "^2.0.5", "@types/jest": "^29.5.12", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "globby": "14.0.2", "husky": "^8.0.3", "jest": "^29.7.0", "jest-extended": "^4.0.2", "lint-staged": "^15.2.10", "meow": "^13.2.0", "mississippi": "^4.0.0", "nx": "^20.4.6", "prettier": "^3.3.3", "rimraf": "^6.0.1", "tar": "^6.2.0", "typescript": "^5.5.4"}, "pnpm": {"overrides": {"@sinclair/typebox": "0.33.16", "axios": ">=1.8.2", "fastify": ">=5.3.2", "firebase-admin": "13.0.2", "jsonpath-plus": ">=10.3.0", "next": ">=14.2.25"}}}